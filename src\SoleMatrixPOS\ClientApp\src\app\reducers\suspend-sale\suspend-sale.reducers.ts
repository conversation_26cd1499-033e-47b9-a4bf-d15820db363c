import { Action, createReducer, on } from "@ngrx/store";
import * as SuspendSaleActions from "./suspend-sale.actions";
import { SuspendSaleHdrDto, SuspendTransToCartDto } from "src/app/pos-server.generated";

export interface SuspendSaleState {
	isLoading: boolean;
	headers: SuspendSaleHdrDto[];
	selectedSaleLines: SuspendTransToCartDto[];
	currentSuspendSaleNo: number | null;
	error: any;
}

export const initialState: SuspendSaleState = {
	isLoading: false,
	headers: [],
	selectedSaleLines: [],
	currentSuspendSaleNo: null,
	error: null,
};

const suspendSaleReducer = createReducer(
	initialState,
	on(SuspendSaleActions.loadSuspendSaleHeaders, (state) => ({
		...state,
		isLoading: true,
	})),
	on(
		SuspendSaleActions.loadSuspendSaleHeadersSuccess,
		(state, { headers }) => ({
			...state,
			isLoading: false,
			headers,
		})
	),
	on(
		SuspendSaleActions.loadSuspendSaleHeadersFailure,
		(state, { error }) => ({
			...state,
			isLoading: false,
			error,
		})
	),
	on(SuspendSaleActions.deleteSuspendSaleSuccess, (state, { suspendNo }) => ({
		...state,
		headers: state.headers.filter(
			(header) => header.suspendNo !== suspendNo
		),
	})),
	on(SuspendSaleActions.deleteSuspendSaleFailure, (state, { error }) => ({
		...state,
		error,
	})),
	on(SuspendSaleActions.selectSuspendSale, (state, { suspendNo }) => ({
		...state,
		isLoading: true,
		currentSuspendSaleNo: suspendNo,
		error: null
	})),
	on(SuspendSaleActions.selectSuspendSaleSuccess, (state, { lines }) => ({
		...state,
		isLoading: false,
		selectedSaleLines: lines,
		error: null
	})),
	on(SuspendSaleActions.selectSuspendSaleFailure, (state, { error }) => ({
		...state,
		isLoading: false,
		selectedSaleLines: [],
		error
	}))
);
export function reducer(state: SuspendSaleState | undefined, action: Action) {
	return suspendSaleReducer(state, action);
}
