import {createAction, props} from '@ngrx/store';
import {StockItemDto, StockSearchRequestDto} from '../../pos-server.generated';

export const clearSearchItems = createAction('[StockSearch] Clear Search Items');
export const search = createAction('[StockSearch] Search', props<{searchParams: StockSearchRequestDto}>());
export const searchResponse = createAction('[StockSearch] Response', props<{payload: StockItemDto[] }>());
export const searchMore = createAction('[StockSearch] SearchMore');
export const searchMoreResponse = createAction('[StockSearch] SearchMore Response', props<{payload: StockItemDto[] }>());
export const searchStockEnquiry = createAction(
  '[Stock Search] Search Stock Enquiry',
  props<{ searchParams: StockSearchRequestDto }>()
);
export const searchStockEnquiryResponse = createAction('[StockSearch] Search Stock Enquiry Response', props<{payload: StockItemDto[] }>());
export const searchStockEnquiryMore = createAction(
  '[Stock Search] Search Stock Enquiry More'
);
export const searchStockEnquiryMoreResponse = createAction('[StockSearch] Search Stock Enquiry More Response', props<{payload: StockItemDto[] }>());
export const searchStockEnquirySuccess = createAction(
  '[Stock Search] Search Stock Enquiry Success',
  props<{ items: StockItemDto[] }>()
);
export const searchStockEnquiryMoreSuccess = createAction(
  '[Stock Search] Search Stock Enquiry More Success',
  props<{ items: StockItemDto[] }>()
);


