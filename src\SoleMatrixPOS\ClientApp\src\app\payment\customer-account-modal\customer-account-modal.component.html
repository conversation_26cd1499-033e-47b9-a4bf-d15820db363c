<div class="container">
	<div class="container-fluid p-5">
		<div class="content-wrapper flex-grow-1 pt-10">
			<div class="container-fluid">
				<div class="row text-align-center mt-2 mb-4">
					<h3 class="mr-2 text-danger">
						Search Customers by
					</h3>
					<div class="col-2 pr-0">
						<select class="form-control form-control-special" [(ngModel)]="field" (change)="search()">
							<option value="Name" selected>NAME</option>
							<option value="Phone">PHONE</option>
							<option value="Email">EMAIL</option>
							<option value="AccountCode">ACCOUNT CODE</option>
						</select>
					</div>

					<div class="col-3 pl-0">
						<!--(keyup)='keyUp.next($event)'-->
						<input type="text" class="form-control" [(ngModel)]="term" (keyup)="search()" autofocus />
					</div>


				</div>
				<div *ngIf="loading$ | async">
					<mat-spinner style="margin:0 auto;" mode="indeterminate"></mat-spinner>
				</div>
				<div class="row m-2" *ngIf="!(loading$ | async)">
					<div class="table-responsive">
						<table class="table table-striped ml-2 mr-2 table-hover">
							<thead>
								<tr>
									<th>Phone</th>
									<th>Name</th>
									<th>Street</th>
									<th>Suburb</th>
									<th>State</th>
									<th>Post Code</th>
									<th>Email</th>
									<th>Contact Name</th>
									<th>Department</th>
								</tr>
							</thead>
							<tbody>
								<tr *ngFor="let cust of customers$ | async" (click)="selectCust(cust)">
									<td>{{cust.mobileNo}}</td>
									<td>{{cust.customerName}}</td>
									<td>{{cust.street}}</td>
									<td>{{cust.suburb}}</td>
									<td>{{cust.state}}</td>
									<td>{{cust.postCode}}</td>
									<td>{{cust.email}}</td>
									<td>{{cust.contactName}}</td>
									<td>{{cust.department}}</td>
								</tr>

							</tbody>
						</table>
					</div>
				</div>

			</div>
		</div>
	</div>
</div>