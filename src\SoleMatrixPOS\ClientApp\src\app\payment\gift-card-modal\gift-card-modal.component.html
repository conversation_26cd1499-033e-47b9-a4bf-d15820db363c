<div class="container">
    <div class="container-fluid p-5">
        <div class="row align-items-center">
            <div class="col-1">
                <i class="fas fa-2x text-danger align-items-center"
                    [ngClass]="isCreditNote ? 'fa-sticky-note' : 'fa-gift'"></i>
            </div>
            <div class="col-1">
                <!-- <h2 class="text-secondary align-items-center">{{this.name}}</h2> -->
            </div>
            <div class="col-10">
                <button type="button" class="btn btn-circle float-right" (click)="dismiss('Cross click')">
                    <i class="fas fa-times fa-2x"></i>
                </button>
            </div>
        </div>
        <hr style="height: 2px;">
        <form [formGroup]="form">

            <div class="form-group">
                <label for="voucherCode">
                    {{ isCreditNote ? 'Credit Note Code' : 'Gift Voucher Code' }}
                </label>
                <div class="input-group">
                    <input autofocus autocomplete="off" name="cardCode" id="cardCode"
                        class="form-control form-control-lg  ng-untouched ng-pristine ng-valid"
                        [ngClass]=" { 'is-invalid': fieldValidate(cardCode) }" formControlName="CardCode">

                    <div class="input-group-append">
                        <button id="remainder" type="submit" class="btn btn-outline-default"
                            (click)="applyVoucherCode()" [disabled]="cardCode.invalid">
                            <i class="fas fa-lg fa-fw fa-check-circle text-success mr-2"></i>
                            Apply
                        </button>
                    </div>
                </div>

                <div *ngIf="(errored$ | async)" class="alert alert-danger mt-2">
                    The {{voucherTypeDisplay}} you entered either doesn't exist or was already redeemed.
                </div>

                <br>
                <label *ngIf="voucher" for="amount" [translate]="'payment.modal.labels.TenderedAmount'">Tendered Amount
                    $</label>
                <div class="input-group">
                    <input *ngIf="voucher" autofocus name="amount" id="amount" type="text"
                        class="form-control form-control-lg  ng-untouched ng-pristine ng-valid"
                        [ngClass]=" {'is-invalid': fieldValidate(amount)}" formControlName="Amount">

                    <div *ngIf="voucher" class="input-group-append">
                        <button id="remainder" type="submit" class="btn btn-outline-default" (click)="useRemainder()"
                            [translate]="'payment.modal.buttons.UseRemainder'">Use Remainder</button>
                    </div>

                    <div class="invalid-feedback" *ngIf="amount.invalid && (amount.dirty || amount.touched)">
                        <div *ngIf="amount.errors?.min" [translate]="'payment.modal.validation.Eftpos.AmountMin'">
                            Amount must be greater than or equal to 0.01
                        </div>
                        <div *ngIf="amount.errors?.required"
                            [translate]="'payment.modal.validation.Eftpos.AmountRequired'">
                            Amount required
                        </div>
                        <div *ngIf="amount.errors?.pattern"
                            [translate]="'payment.modal.validation.Eftpos.AmountPattern'">
                            Amount must be a number
                        </div>
                    </div>
                </div>
                <div *ngIf="voucher" class="mt-3">
                    <p>Total Amount on {{toTitleCase(voucherTypeDisplay)}}: {{ voucher.voucherFunds | currency }}</p>
                </div>
            </div>

            <div *ngIf="errorMessage" class="alert alert-danger" role="alert">
                {{errorMessage}}
            </div>

            <div class="row justify-content-center p-4">
                <div><button class="btn btn-outline-default" type="button" (click)="apply()"
                        [translate]="'payment.modal.buttons.ApplyAmount'">
                        <i class="fas fa-lg fa-fw fa-check-circle text-success mr-2"></i>
                        Apply Amount</button>
                </div>
            </div>
        </form>

    </div>
</div>