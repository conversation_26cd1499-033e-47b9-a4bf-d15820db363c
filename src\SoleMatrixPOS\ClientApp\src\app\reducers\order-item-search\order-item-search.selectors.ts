// src/app/reducers/order-item-search/order-item-search.selectors.ts

import { createSelector } from '@ngrx/store';
import { AppState } from '../index';
import { OrderSearchState } from './order-item-search.reducer';

export const selectOrderItemSearchState = (state: AppState) => state.customerOrderSearch;

export const searchedOrders = createSelector(
  selectOrderItemSearchState,
  (state: OrderSearchState) => state.orders
);

export const searchOptions = createSelector(
  selectOrderItemSearchState,
  (state: OrderSearchState) => state.options
);

export const searchLoading = createSelector(
  selectOrderItemSearchState,
  (state: OrderSearchState) => state.isLoading
);

export const selectedOrder = createSelector(
  selectOrderItemSearchState,
  (state: OrderSearchState) => state.selected
);

export const orderError = createSelector(
  selectOrderItemSearchState,
  (state: OrderSearchState) => state.error
);
