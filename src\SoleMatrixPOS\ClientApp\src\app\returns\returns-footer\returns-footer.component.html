<div class="footer-wrapper">


	<div class="container-fluid">

		<div class="row align-items-center">

			<div class="col-auto back-button">

				<button type="button" class="btn-hidden">
					<div class="btn btn-lg btn-circle btn-default" (click)="goToLoginPage()">
						<i class="fas fa-caret-left fa-4x color-gradient-grey mr-2"></i>
					</div>
				</button>


			</div>

			<div class="col-auto">

				<button *ngIf="selectedCustomerClubMember != null" type="button" (click)="launchCustomerClubModal()"
					class="btn-lg btn-default btn d-flex align-items-center">
					<i class="fas fa-crown fa-lg text-danger"></i>
					<h4 class="ml-3">{{selectedCustomerClubMember.firstname}}<br />
						{{selectedCustomerClubMember.surname}}</h4>
				</button>
				<button *ngIf="selectedCustomerClubMember == null" type="button" (click)="launchCustomerClubModal()"
					class="btn-lg btn-default btn d-flex align-items-center">
					<i class="fas fa-crown fa-lg"></i>
					<h4 class="ml-3">Customer<br />
						Club</h4>
				</button>

			</div>

			<div class="col-auto ml-auto">

				<div class="total-container">

					<div class="total-items">
						<h6 class="items-label">
							{{noItems$ | async}} items
						</h6>
						<h6 class="total-label">
							Total
						</h6>
					</div>
					<h1 class="total">
						<span>
							{{(totalValue$ | async) | currency}}
						</span>
					</h1>

				</div>


			</div>

			<div class="col-auto next-button">

				<button type="button" class="btn-hidden" (click)="processBtnClick()">
					<div class="btn btn-lg btn-circle btn-default">
						<i class="fas fa-caret-right fa-4x color-gradient-green ml-2"></i>
					</div>
					<h4 class="text-light">Payment</h4>
				</button>


			</div>

		</div>


	</div>

</div>