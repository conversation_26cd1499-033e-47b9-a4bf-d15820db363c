import { createReducer, on } from '@ngrx/store';
import * as orderActions from './order.actions';
import { CustOrderHeaderDTO, CustOrderLineDTO } from 'src/app/pos-server.generated';

export interface OrderState {
    active: boolean;
    orderSubmitting: boolean;
    orderCreated: boolean;
    orderCancelled: boolean;
    orderHeader: CustOrderHeaderDTO | null;
    orderLines: CustOrderLineDTO[] | null;
    deposit: number | null; // Add deposit to the state
    error: any | null;
    uploadedOrderCode: string | null;
    orderNo: string | null;
}

export const initialState: OrderState = {
    active: false,
    orderSubmitting: false,
    orderCreated: false,
    orderCancelled: false,
    orderHeader: null,
    orderLines: null,
    deposit: null, // Initialize deposit as null
    error: null,
    uploadedOrderCode: null,
    orderNo: null,
};

export const orderReducer = createReducer(
    initialState,
    on(orderActions.init, () => initialState),

    on(orderActions.startOrder, (state) => {
        return { ...state, active: true };
    }),

    on(orderActions.setOrderHeader, (state, { payload }) => {
        return { ...state, orderHeader: payload };
    }),

    on(orderActions.setOrderLines, (state, { payload }) => {
        return { ...state, orderLines: payload };
    }),

    on(orderActions.submitOrderWithTransaction, (state) => {
        return { ...state, orderSubmitting: true, error: null };
    }),

    on(orderActions.orderWithTransactionCreated, (state) => {
        return { ...state, orderSubmitting: false, orderCreated: true, error: null };
    }),

    on(orderActions.orderSubmissionFailed, (state, { error }) => {
        return { ...state, orderSubmitting: false, error };
    }),

    on(orderActions.cancelOrder, (state) => {
        return { ...state, orderSubmitting: true, error: null };
    }),

    on(orderActions.orderCancelled, (state) => {
        return { ...state, orderSubmitting: false, orderCancelled: true, error: null };
    }),

    on(orderActions.orderCancellationFailed, (state, { error }) => {
        return { ...state, orderSubmitting: false, error };
    }),

    on(orderActions.setDeposit, (state, { deposit }) => {
        console.log(deposit);
        return { ...state, deposit };
    }),

    on(orderActions.clearUploadedOrderCode, (state) => ({
        ...state,
        uploadedOrderCode: null
      })),

    on(orderActions.clearDeposit, (state) => ({
        ...state,
        deposit: null
      })),

    on(orderActions.uploadOrderCode, (state, { orderCode }) => ({
    ...state,
    uploadedOrderCode: orderCode
    })),
    
    // Reset the uploaded order code when resetting the state
    on(orderActions.resetOrderState, (state) => ({
    ...initialState,
    uploadedOrderCode: state.uploadedOrderCode // Preserve the uploaded order code if needed
    })),

    on(orderActions.completeOrder, (state) => {
        return { ...state, orderSubmitting: true, error: null };
    }),

    on(orderActions.orderCompleted, (state) => {
        return { ...state, orderSubmitting: false, active: false, error: null };
    }),

    on(orderActions.orderCompletionFailed, (state, { error }) => {
        return { ...state, orderSubmitting: false, error };
    }),

    on(orderActions.resetOrderState, () => initialState),

    on(orderActions.getOrderNo, (state) => ({
        ...state,
        error: null
    })),

    on(orderActions.getOrderNoSuccess, (state, { orderNo }) => ({
        ...state,
        orderNo: orderNo,
        error: null
    })),

    on(orderActions.getOrderNoFailure, (state, { error }) => ({
        ...state,
        orderNo: null,
        error
    }))
);
