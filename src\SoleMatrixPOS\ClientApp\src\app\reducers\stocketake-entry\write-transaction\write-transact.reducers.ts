import { createReducer, on } from "@ngrx/store";
import { TransactRequestDto } from "src/app/pos-server.generated";
import * as transactAction from '../write-transaction/write-transact.actions'


export class WriteTransactionState {
    transactionCompleted: boolean;

}

export const initialState: WriteTransactionState = {
    transactionCompleted: false,

} 

export const TransactReducer = createReducer(initialState,
    on(transactAction.init, state => initialState),

    on(transactAction.submitTransConfirmation, (state, action) => {
        return {...state, transactionCompleted: true}
    }) 

);
