
import { Injectable } from "@angular/core";
import { Actions, ofType, createEffect, act } from '@ngrx/effects';
import { StockSearchClient, BestPriceSearchRequestDto, TransactionClient, AccountPaymentClient } from 'src/app/pos-server.generated';
import * as paymentActions from './payment.actions';
import { mergeMap, map, catchError, tap, mapTo } from 'rxjs/operators';
import { EMPTY } from 'rxjs';
import { Transaction } from "src/app/payment/payment.service";

@Injectable()
export class PaymentEffects {
    insertCtrans$ = createEffect(() => this.actions$.pipe(
		ofType(paymentActions.commitCtrans),
		mergeMap(
			(action) => this.accountClient.insertCtrans(action.payload)
		.pipe(
				map(
					(response) => {
                    	console.log(response)
						return paymentActions.ctransResponse()
					},catchError(() => EMPTY)
				)
			)
		)
	));

    constructor(
        private actions$: Actions, 
        private transactionClient: TransactionClient,
        private accountClient:AccountPaymentClient){}
    
}