using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;

namespace SoleMatrixPOS.Identity.Interface
{
	public class EmailPasswordResetTokenProvider<TUser>
							  : DataProtectorTokenProvider<TUser> where TUser : class
	{
		public EmailPasswordResetTokenProvider(
			IDataProtectionProvider dataProtectionProvider,
			IOptions<EmailPasswordResetTokenProviderOptions> options,
			ILogger<DataProtectorTokenProvider<TUser>> logger)
										   : base(dataProtectionProvider, options, logger)
		{

		}
	}
	public class EmailPasswordResetTokenProviderOptions : DataProtectionTokenProviderOptions
	{
		public EmailPasswordResetTokenProviderOptions()
		{
			Name = "EmailDataProtectorTokenProvider";
			TokenLifespan = TimeSpan.FromMinutes(15);
		}
	}
}
