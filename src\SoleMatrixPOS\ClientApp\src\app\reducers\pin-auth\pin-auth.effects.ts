import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, map, mergeMap, tap } from 'rxjs/operators';
import * as PINAuthActions from './pin-auth.actions';
import { EftposService } from '../../eftpos/eftpos.service';

@Injectable()
export class PINAuthEffects {
	constructor(
		private actions$: Actions,
		private eftposService: EftposService
	) { }

	// Effect to read PINAuth
	readPINAuth$ = createEffect(() =>
		this.actions$.pipe(
			ofType(PINAuthActions.readPINAuth),
			mergeMap(() =>
				this.eftposService.getTyroAuth().pipe(
					tap(result => console.log('Tyro auth response:', result)), // log the network response
					map(pinAuth => PINAuthActions.readPINAuthSuccess({ pinAuth })),
					catchError(error => of(PINAuthActions.readPINAuthFailure({ error })))
				)
			)
		)
	);
	
	//// Effect to update PINAuth
	//updatePINAuth$ = createEffect(() =>
	//	this.actions$.pipe(
	//		ofType(PINAuthActions.updatePINAuth),
	//		mergeMap(action =>
	//			this.houseKeepingClient.updatePINAuth(action.updatePINAuth).pipe(
	//				map(() => PINAuthActions.updatePINAuthSuccess()),
	//				catchError(error =>
	//					of(PINAuthActions.updatePINAuthFailure({ error }))
	//				)
	//			)
	//		)
	//	)
	//);

	//// Effect to fetch updated PINAuth after successful update
	//updatePINAuthSuccess$ = createEffect(() =>
	//	this.actions$.pipe(
	//		ofType(PINAuthActions.updatePINAuthSuccess),
	//		map(() => PINAuthActions.readPINAuth({ storeId: 'BA' })) // Replace 'BA' with the appropriate value
	//	)
	//);
}
