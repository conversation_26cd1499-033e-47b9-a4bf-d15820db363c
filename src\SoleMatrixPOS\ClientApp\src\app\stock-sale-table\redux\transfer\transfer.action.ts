import { Action, Store } from '@ngrx/store';
import { Transfer } from './transfer.model';

export const ADD_TRANSFER: string = "[TRANSFER] add";
export const REMOVE_TRANSFER: string = "[TRANSFER] remove";
export const LOAD_TRANSFERS: string = "[TRANSFER] load";
export const HIGHLIGHT_TRANSFER: string = "[TRANSFER] highlight";
import * as TransferSelector from './transfer.selector';

/**
 * Appends a transfer to the list of transfers in the
 * transfer state slice
 * 
 * NOTE: A non repeating id generation method is required
 */
export class AddTransferAction implements Action {
    readonly type = ADD_TRANSFER;
    public id: number;
    public highlighted: string;

    constructor(
        public from: string,
        public to: string,
        public name: string,
        public size: string,
        public qty: number
    ){

        this.id = Math.random(); // Need to think of a non-repeating rng
        
    }
}

/**
 * Removes a transfer from the transfer state slice by id
 */
export class RemoveTransferAction implements Action {
    readonly type = REMOVE_TRANSFER;

    constructor(
        public id: number,
        public from: string,
        public to: string,
        public name: string,
        public size: string,
        public qty: number
    ){}
}

/**
 * Loads existing transfers
 */
export class LoadTransfersAction implements Action {
    readonly type = LOAD_TRANSFERS;

    constructor(
        public transfers: Transfer[]
    ){}
}

/**
 * Switches the highlight state of a transfer
 */
export class HighlightTransferAction implements Action {
    readonly type = HIGHLIGHT_TRANSFER;

    constructor(
        public id: number,
        public from: string,
        public to: string,
        public name: string,
        public size: string,
        public qty: number
    ){}
}

export type TransferActionType = 
AddTransferAction |
RemoveTransferAction | 
LoadTransfersAction | 
HighlightTransferAction;