import { Component, OnInit } from '@angular/core';
import { CustomerClubDto } from 'src/app/pos-server.generated';
import { Store } from '@ngrx/store';
import * as customerClubSearchActions from '../../reducers/customer-club/club-search/customer-club.actions';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';
import { AppState } from 'src/app/reducers';

@Component({
  selector: 'pos-customer-club-modal',
  templateUrl: './customer-club-modal.component.html',
  styleUrls: ['./customer-club-modal.component.scss']
})
export class CustomerClubModalComponent implements OnInit {
  member: CustomerClubDto = null;
  editing: boolean = false;
  showMemberDetails: boolean = false;

  constructor(private store: Store<AppState>, private modal: NgbActiveModal) {}

  ngOnInit() {
    this.store.select(customerClubSearchSelectors.selectedCustomerClubMember)
      .subscribe(member => {
        if (member) {
          this.member = member;
          this.showMemberDetails = true;
        }
      });
  }

  isEditing(status: boolean) {
    this.editing = status;
  }

  onMemberSelected(member: CustomerClubDto) {
    this.member = member;
  }

  onUseSelected() {
    if (this.member != null) {
      this.store.dispatch(customerClubSearchActions.selectCustomerClubMember({ payload: this.member }));
    }
    this.modal.close(this.member != null && this.member != undefined);
  }

  onDeselect() {
    this.store.dispatch(customerClubSearchActions.selectCustomerClubMember({ payload: null }));
  }

  onBack() {
    this.showMemberDetails = false;
  }
}
