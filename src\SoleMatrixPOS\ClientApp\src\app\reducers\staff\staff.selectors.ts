import { AppState } from '../index';
import { createSelector } from '@ngrx/store';

export const select = (state: AppState) => state.staff;

export const selectStaffLoginDto = createSelector(select, (s) => s.staffDto);

export interface StaffAccess {
	noAccess: boolean;
	stocktakeEntry: boolean;
	customerClub: boolean;
	stockEnquiry: boolean;
	sales: boolean;
	returns: boolean;
	exchange: boolean;
	accountPayment: boolean;
	layby: boolean;
	giftVoucher: boolean;
	customerSpecials: boolean;
	history: boolean;
	sendStock: boolean;
	receiveStock: boolean;
	openTill: boolean;
	endOfDay: boolean;
	houseKeeping: boolean;
}

export const staffAccess = createSelector(selectStaffLoginDto, (s) => ({

	// level 0
	noAccess: s && s.privilegeLevel <= 0,

	// level 1
	stocktakeEntry: s && s.privilegeLevel >= 1,

	// level 2-3
	customerClub: s && s.privilegeLevel >= 2,
	stockEnquiry: s && s.privilegeLevel >= 2,

	// level 4-5
	sales: s && s.privilegeLevel >= 4,
	returns: s && s.privilegeLevel >= 4,
	exchange: s && s.privilegeLevel >= 4,
	accountPayment: s && s.privilegeLevel >= 4,
	layby: s && s.privilegeLevel >= 4,
	giftVoucher: s && s.privilegeLevel >= 4,
	customerSpecials: s && s.privilegeLevel >= 4,
	history: s && s.privilegeLevel >= 4,

	// level 6-7
	sendStock: s && s.privilegeLevel >= 6,
	receiveStock: s && s.privilegeLevel >= 6,

	// level 8-9
	openTill: s && s.privilegeLevel >= 8,

	// level 10
	endOfDay: s && s.privilegeLevel >= 10,
	houseKeeping: s && s.privilegeLevel >= 10

} as StaffAccess));
