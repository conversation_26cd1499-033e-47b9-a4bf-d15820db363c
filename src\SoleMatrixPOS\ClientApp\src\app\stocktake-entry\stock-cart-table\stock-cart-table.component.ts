import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { StockItemDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import * as stocktakeSelectors from '../../reducers/stocketake-entry/itemCart/itemCart.selectors';
import * as stocktakeAction from '../../reducers/stocketake-entry/itemCart/itemCart.actions';
import { StockCartItem } from 'src/app/reducers/stocketake-entry/itemCart/itemCart.reducers';

@Component({
  selector: 'pos-stock-cart-table',
  templateUrl: './stock-cart-table.component.html',
  styleUrls: ['./stock-cart-table.component.scss']
})
export class StockCartTableComponent implements OnInit {

  cartItem$: Observable<StockCartItem[]>;
  cartItem: StockCartItem[];
  reasons: Map<string, string[]>;
  tableLoading$: Observable<boolean>;
  public tableLoading: boolean = true;

  constructor(private store: Store<AppState>) { }

  getReasons(item: StockItemDto){
    let v = this.reasons.get(item.barcode);
    return v == undefined ? [] : v;
  }

  ngOnInit() {

    this.store.select(stocktakeSelectors.reasons).subscribe(
      value => this.reasons = value
    );

    this.cartItem$ = this.store.select(stocktakeSelectors.cartItem);
    this.cartItem$.subscribe(value => this.cartItem = value);

    this.tableLoading$ = this.store.select(stocktakeSelectors.tableLoading);
    this.tableLoading$.subscribe((s) => { this.tableLoading = s });

  }

  onTableItemDeleteClicked(itemIndex: number){
    console.log("Value ", itemIndex, " to be deleted.");
    
    let sItem = this.cartItem[itemIndex].stockItem;
    this.store.dispatch(stocktakeAction.removeItem({stockItem: sItem}))
    this.store.dispatch(stocktakeAction.removeAllReasons({barcode: sItem.barcode}))

  }

  onTableReasonDeleteClicked(itemIndex: number, reasonIndex: number){
	console.log("Reason", reasonIndex, " of Value ", itemIndex, " to be deleted.")
	let cartItem = this.cartItem[itemIndex];
    this.store.dispatch(stocktakeAction.setNumberOfItems({stockItem: cartItem.stockItem, quantity: cartItem.quantity - 1}))
    this.store.dispatch(stocktakeAction.removeReason({barcode: cartItem.stockItem.barcode, reasonId: reasonIndex}));
  }

}

