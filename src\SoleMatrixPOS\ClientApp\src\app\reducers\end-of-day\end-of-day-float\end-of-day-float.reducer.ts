import { createReducer, on } from '@ngrx/store';
import { storeEndFloatData } from './end-of-day-float.actions';
import { FloatDto } from 'src/app/pos-server.generated';

export interface EndFloatState {
  endFloatData: FloatDto | null;
}

export const initialState: EndFloatState = {
  endFloatData: null
};

export const endFloatReducer = createReducer(
  initialState,
  on(storeEndFloatData, (state, { payload }) => ({
    ...state,
    endFloatData: payload
  }))
);
