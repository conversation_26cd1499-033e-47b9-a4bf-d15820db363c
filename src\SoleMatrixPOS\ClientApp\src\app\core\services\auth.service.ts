import { Injectable, Optional, Inject } from "@angular/core";
import { HttpClient, HttpBackend } from "@angular/common/http";
import { Observable, of } from "rxjs";
import { environment } from "../../../environments/environment";
import { TokenStore } from "../store/token.store";
import { API_BASE_URL } from "../../pos-server.generated";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";

@Injectable()
export class AuthService {
	
	constructor(
		private handler: HttpBackend,
		private http: HttpClient,
		private tokenStore: TokenStore,
		private router: Router,
		private ngbModal: NgbModal,
		@Optional()
		@Inject(API_BASE_URL)
		baseUrl: string,
	) {
		this.baseUrl = baseUrl || ""
	}
	private baseUrl: string;
	private httpClientRefresh: HttpClient = new HttpClient(this.handler);

	login(payload: LoginPayload): Observable<LoginData> {
		return this.http.post<LoginData>(
			`${this.baseUrl}/api/Identity/login`,
			payload,
		);
	}

	refresh(payload: RefreshPayload): Observable<RefreshData> {
		return this.httpClientRefresh.post<RefreshData>(
			`${this.baseUrl}/api/Identity/refresh`,
			payload,
		);
	}

	logout() {
		this.tokenStore.removeAccessToken();
		this.tokenStore.removeRefreshToken();
		this.ngbModal.dismissAll()
		this.router.navigateByUrl('/login')
	}
	
	resetPassword(payload: ResetPasswordPayload): Observable<null> {
		return this.http.post<null>(`${this.baseUrl}/api/Identity/reset`, payload)
	}
}

export interface LoginPayload {
	id: string;
	password: string;
}

export interface LoginData {
	accessToken: string;
	refreshToken: string;
}

export interface RefreshPayload {
	refreshToken: string;
}

export interface RefreshData {
	accessToken: string;
	refreshToken: string;
}

export interface ResetPasswordPayload {
	id: string,
	token: string,
	password: string,
}