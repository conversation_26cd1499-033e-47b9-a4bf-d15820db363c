// src/app/reducers/order-item-search/order-item-search.effects.ts

import { Injectable } from '@angular/core';
import { Actions, ofType, createEffect } from '@ngrx/effects';
import * as quoteSearchActions from './quote-item-search.actions';
import { catchError, map, mergeMap, tap } from 'rxjs/operators';
import { EMPTY, of } from 'rxjs';
import { OrderSearchClient, CreateOrderDto, CreateQuoteDto, QuoteSearchClient } from '../../pos-server.generated';
import { HttpClient } from '@angular/common/http'; // Assuming you're using HttpClient

@Injectable()
export class QuoteSearchEffects {
  matchedOrders$ = createEffect(() =>
    this.actions$.pipe(
      ofType(quoteSearchActions.search),
      tap((action) => console.log('Dispatching QuoteSearchActions.search with:', action.searchParams)),
      mergeMap((action) =>
        this.quoteSearchClinet.get(action.searchParams).pipe(
          tap((foundQuotes) => console.log('Received quotes from API:', foundQuotes)),
          map((foundQuotes: CreateQuoteDto[]) =>
            quoteSearchActions.searchResponse({ payload: foundQuotes })
          ),
          catchError((error) => {
            console.error('Error fetching quotes:', error);
            return EMPTY;
          })
        )
      )
    )
  );

  // Effect for Cancelling Order
  // cancelOrder$ = createEffect(() =>
  //   this.actions$.pipe(
  //     ofType(orderSearchActions.cancelOrder),
  //     mergeMap((action) =>
  //       this.orderSearchClient.cancelOrder(action.orderCode).pipe(
  //         map(() => orderSearchActions.cancelOrderSuccess({ orderCode: action.orderCode })),
  //         catchError((error) => of(orderSearchActions.cancelOrderFailure({ error })))
  //       )
  //     )
  //   )
  // );

  constructor(
    private actions$: Actions,
    private quoteSearchClinet: QuoteSearchClient,
  ) {}
}
