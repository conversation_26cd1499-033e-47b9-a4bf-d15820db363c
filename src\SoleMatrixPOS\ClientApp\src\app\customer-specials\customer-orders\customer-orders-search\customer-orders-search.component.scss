/* src/app/components/customer-orders-search/customer-orders-search.component.scss */

/* Style for main order rows */
table tr:hover {
    background-color: #f1f1f1;
    cursor: pointer;
  }
  
  /* Style for selected order */
  .selectedOrder {
    background-color: #d1ecf1 !important; /* Light blue background */
  }
  
  /* Define styles for the sub-row */
  .sub-row {
    background-color: #f9f9f9; /* Light gray background */
    font-style: italic;        /* Italic text */
  }
  
  /* Indent the sub-row content for hierarchy */
  .sub-row td {
    padding-left: 40px;        /* Indent the sub-row content */
  }
  
  /* Hover effect for sub-rows */
  .sub-row:hover {
    background-color: #e9e9e9; /* Slightly darker gray on hover */
  }
  
  /* Style the icon within sub-rows */
  .sub-row td i {
    margin-right: 8px;
    color: #007bff; /* Optional: Change icon color */
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .sub-row td {
      padding-left: 20px;        /* Reduced padding for smaller screens */
      font-size: 14px;            /* Adjust font size if necessary */
    }
  }
  
 /* Ensure the table fits within its container */
.table-responsive {
  overflow-x: auto; /* Allow horizontal scrolling for wide content */
  width: 100%; /* Ensure responsiveness */
}

/* Flexible table layout */
.table-fit {
  table-layout: fixed; /* Ensures all columns adjust to fit the table width */
  width: 100%;
}

/* Prevent truncation by enabling word wrap */
.table-fit th, 
.table-fit td {
  white-space: normal; /* Allow text to wrap */
  word-wrap: break-word; /* Prevent overflow for long words */
  overflow-wrap: break-word; /* Support for modern browsers */
  text-overflow: ellipsis; /* Add ellipsis for very long text */
  max-width: 150px; /* Optional: Set a max width for each cell */
}

/* Adjust columns for better readability */
th, td {
  padding: 8px 12px; /* Add padding for readability */
  vertical-align: middle; /* Center content vertically */
}

/* Minimum column widths for critical fields */
th:nth-child(1), td:nth-child(1) { /* Order Code */
  min-width: 120px;
}

th:nth-child(2), td:nth-child(2) { /* Client Code */
  min-width: 120px;
}

th:nth-child(3), td:nth-child(3) { /* Full Name */
  min-width: 180px; /* Slightly larger for potentially longer names */
}

th:nth-child(10), td:nth-child(10) { /* Selling Price */
  min-width: 100px;
}

/* Highlight selected rows */
.selectedOrder {
  background-color: rgba(0, 123, 255, 0.1) !important;
}

/* Remove indent for sub-rows */
.sub-row td {
  padding-left: 0 !important;
}

/* Styling for buttons */
button.btn-danger {
  width: 100%;
  text-align: center;
}
