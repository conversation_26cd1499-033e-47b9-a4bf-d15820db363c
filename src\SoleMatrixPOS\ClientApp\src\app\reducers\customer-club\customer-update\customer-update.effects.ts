import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { tap } from 'rxjs/operators';
import * as customerUpdateActions from './customer-update.actions';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { of } from 'rxjs';
import { ClientUpdateClient } from '../../../pos-server.generated';
import { HttpClient } from '@angular/common/http';

@Injectable()
export class CustomerUpdateEffects {
  updateMember$ = createEffect(() => this.actions$.pipe(
    ofType(customerUpdateActions.updateMember),
    mergeMap((action) => this.clientUpdateClient.updateMember(action.payload)
      .pipe(
        map(result => customerUpdateActions.updateMemberSuccess({ result })),
        catchError(err => of(customerUpdateActions.updateMemberError({ error: err })))
      )
    )
  ));

  updatePoints$ = createEffect(() => this.actions$.pipe(
    ofType(customerUpdateActions.updatePoints),
    mergeMap((action) => {
      const pointsUpdateDto = {
        clientCode: action.payload.clientCode,
        pointsToAdjust: action.payload.pointsToAdjust
      };
      
      return this.http.put<number>('api/ClientUpdate/points', pointsUpdateDto)
        .pipe(
          map(newPointsTotal => customerUpdateActions.updatePointsSuccess({
            clientCode: action.payload.clientCode,
            newPointsTotal
          })),
          catchError(err => of(customerUpdateActions.updatePointsError({ 
            error: err && err.message ? err.message : 'Failed to update points' 
          })))
        );
    })
  ));

  constructor(
    private actions$: Actions, 
    private clientUpdateClient: ClientUpdateClient,
    private http: HttpClient
  ) {}
}