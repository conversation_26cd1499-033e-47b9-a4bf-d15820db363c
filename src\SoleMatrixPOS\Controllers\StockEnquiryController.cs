using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using SoleMatrixPOS.Application.StockEnquiry;
using System.Collections.Generic;
using SoleMatrixPOS.Application.StockEnquiry.Queries;
using SoleMatrixPOS.Application.Stock;
using SoleMatrixPOS.Application.Stock.Dtos;
using System.Threading;
using SoleMatrixPOS.Dal.Interface.Models.SMate;
using Microsoft.AspNetCore.Authentication.JwtBearer;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
    public class StockEnquiryController : ControllerBase
    {
        private readonly IMediator _mediator;

        public StockEnquiryController(IMediator mediator)
        {
            _mediator = mediator;
        }

		[HttpPost("GetStockEnquiry")]
		public async Task<StockEnquiryResultDto> GetStockEnquiry([FromBody] StockEnquiryRequestDto stockEnquiryFilterDto)
		{
			return await _mediator.Send(new StockEnquiryResultQuery(stockEnquiryFilterDto));
		}


	}
}
