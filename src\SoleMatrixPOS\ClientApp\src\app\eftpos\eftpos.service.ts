import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { EftposClient, EftPairPinPadDto, LinklyPurchaseRequestDto, LinklyPurchaseResponseDto, LinklyBasketItem, LinklyRefundResponseDto, GetReceiptDto, GetPINAuthDto, GetLast } from 'src/app/pos-server.generated';
import { CartItem } from '../reducers/sales/cart/cart.reducer';
import { map } from 'rxjs/operators';
import { HttpClient } from "@angular/common/http";
import { TyroIClientService } from './tyro-iclient.service';
import { receipt } from '../reducers/house-keeping/updateReceipt/receipt.selector';

export interface TyroQuestion {
	text: string;
	options: string[];
	answerCallback: (answer: string) => void;
}

@Injectable({
	providedIn: 'root'
})

export class EftposService {

	private questionSubject = new Subject<TyroQuestion>();
	public question$: Observable<TyroQuestion> = this.questionSubject.asObservable();

	private statusMessageSubject = new Subject<string>();
	public statusMessage$: Observable<string> = this.statusMessageSubject.asObservable();

	constructor(
		private eftposClient: EftposClient,
		private http: HttpClient,
		private tyroIClientService: TyroIClientService
	) {
	}

	/**
	 * Pair the PIN pad with the backend.
	 * @param username Username for pairing.
	 * @param password Password for pairing.
	 * @param pairCode Pairing code.
	 * @returns Observable containing the pairing secret.
	 */
	pairPINPad(username: string, password: string, pairCode: string): void {
		const pairPinPadDto: EftPairPinPadDto = {
			username,
			password,
			pairCode
		};
		this.eftposClient.pairPINPad(pairPinPadDto).subscribe(
		);
	}

	getTyroAuth(): Observable<GetPINAuthDto> {
		console.log('getTyroAuth() called');
		return this.eftposClient.getTyroAuth();
	}

	tyroPairPINPad(mid: string, tid: string,): void {
		const iclient = this.tyroIClientService.getClient();
		iclient.pairTerminal(mid, tid, (response) => {  // arrow function to preserve 'this'
			if ("success" == response.status) {
				console.log("Pairing success: " + JSON.stringify(response));
				const pairPinPadDto: EftPairPinPadDto = {
					pinPadId: tid,
					storeId: mid,
					pairCode: response.integrationKey
				};
				this.eftposClient.pairPINPad(pairPinPadDto).subscribe((result => {
					console.log(result);
				}));
			} else if ("failure" == response.status) {
				console.log("Pairing failure: " + JSON.stringify(response));
			}
		});

	}
	

	///**
	// * Get the authentication token from the backend.
	// * @param posId Point of sale ID.
	// * @returns Observable containing the auth token and its expiry time.
	// */
	//getAuthToken(posId: string): Observable<AuthTokenResponseDto> {
	//	return this.eftposClient.getAuthToken(posId);
	//}

	/**
	 * Process a purchase transaction.
	 * @param amtPurchase Amount for the purchase.
	 * @param taxAmt Tax amount.
	 * @param discountAmt Discount amount.
	 * @param surchargeAmt Surcharge amount.
	 * @param transNo Transaction number.
	 * @param currencyCode Currency code (default: 'AUD').
	 * @param cutReceipt Cut receipt flag.
	 * @param receiptAutoPrint Auto print receipt flag.
	 * @param operatorReference Operator reference.
	 * @param hasBarCodeScanner Barcode scanner availability flag.
	 * @param items List of items in the basket.
	 * @param authToken Authorization token.
	 * @returns Observable containing the purchase response.
	 */
	processPurchase(
		totalAmount: number,
		tenderAmount: number,
		totalEftAmount: number,
		taxAmt: number,
		discountAmt: number,
		surchargeAmt: number,
		transNo: number,
		currencyCode: string = 'AUD',
		cutReceipt: string,
		receiptAutoPrint: string,
		operatorReference: string,
		hasBarCodeScanner: string,
		items: LinklyBasketItem[],
		posId: string): Observable<{ sessionId: string; status: string }> {
		const purchaseRequestDto: LinklyPurchaseRequestDto = {
			totalAmount,
			tenderAmount,
			totalEftAmount,
			taxAmt,
			discountAmt,
			surchargeAmt,
			transNo,
			currencyCode,
			cutReceipt,
			receiptAutoPrint,
			operatorReference,
			hasBarCodeScanner,
			items,
			posId
		};

		return this.eftposClient.processPurchase(purchaseRequestDto).pipe(
			map((result: any) => {
				return {
					sessionId: result.sessionId,
					status: result.status,
				};
			})
		);
	}

	tyroProcessPayment(
		tenderAmount: number,
		mid: string,
		tid: string,
		integrationKey: string,
		transNo: number,
	) {
		const iclient = this.tyroIClientService.getClient();
		const purchaseParams = {
			amount: (tenderAmount * 100).toString(), // string in cents
			integratedReceipt: true, // indicate whether receipts will be printed on the POS (true) or on the terminal (false).
			mid: parseInt(mid),
			tid: parseInt(tid),
			integrationKey,
			transactionId: transNo.toString(),
			enableSurcharge: true // if we do surcharging on the POS, set to false
		}
		const callbacks = {
			receiptCallback: (response: any) => {
				// transNo is captured from the outer scope and passed into callback.
				this.tyroReceiptCallback(transNo.toString(), response);
			},
			transactionCompleteCallback: (response: any) => {
				this.tyroTransactionCompleteCallback(transNo.toString(), response);
			},
			questionCallback: this.tyroQuestionCallback,
			statusMessageCallback: this.tyroStatusMessageCallback
		}

		this.eftposClient.createTyroSession(transNo.toString()).subscribe();

		iclient.initiatePurchase(purchaseParams, callbacks);
		return this.eftposClient.getTransactionStatus(transNo, "Tyro").pipe(
			map((result: any) => {
				return {
					sessionId: result.sessionId,
					status: result.status,
					surchargeAmount: result.surchargeAmount
				};
			})
		);
	}

	tyroProcessRefund(
		tenderAmount: number,
		mid: string,
		tid: string,
		integrationKey: string,
		transNo: number,
	) {
		const iclient = this.tyroIClientService.getClient();
		const refundParams = {
			amount: (tenderAmount * 100).toString(), // string in cents
			integratedReceipt: true, // indicate whether receipts will be printed on the POS (true) or on the terminal (false).
			mid: parseInt(mid),
			tid: parseInt(tid),
			integrationKey,
			transactionId: transNo.toString(),
		}
		const callbacks = {
			receiptCallback: (response: any) => {
				// transNo is captured from the outer scope and passed into callback.
				this.tyroReceiptCallback(transNo.toString(), response);
			},
			transactionCompleteCallback: (response: any) => {
				this.tyroTransactionCompleteCallback(transNo.toString(), response);
			},
			questionCallback: this.tyroQuestionCallback,
			statusMessageCallback: this.tyroStatusMessageCallback
		}

		this.eftposClient.createTyroSession(transNo.toString()).subscribe();

		iclient.initiateRefund(refundParams, callbacks);
		return this.eftposClient.getTransactionStatus(transNo, "Tyro").pipe(
			map((result: any) => {
				return {
					sessionId: result.sessionId,
					status: result.status,
					surchargeAmount: result.surchargeAmount
				};
			})
		);
	}

	tyroReceiptCallback = (transNo: string, response: any) => {
		// Send receipt in a linklysession with the transno
		if (response.merchantReceipt) {
			this.eftposClient.saveTyroReceipt(response.merchantReceipt, true, transNo).subscribe();
		}
	}


	tyroTransactionCompleteCallback = (transNo: string, response: any) => {
		const final_responses = ["APPROVED", "SIGNATURE APPROVED", "CANCELLED", "REVERSED", "DECLINED", "SYSTEM ERROR", "NOT STARTED", "UNKNOWN"];
		if (response.surchargeAmount) {
			this.eftposClient.updateSurcharge(parseFloat(response.surchargeAmount), transNo).subscribe();
		}
		// Send receipt in a linklysession with the transno
		if (response.result && final_responses.includes(response.result)) {
			this.eftposClient.updateStatus(response.result, transNo).subscribe();
		}
		if (response.customerReceipt) {
			this.eftposClient.saveTyroReceipt(response.customerReceipt, false, transNo).subscribe();
		}
	}

	tyroQuestionCallback = (question: any, answerCallback: (answer: string) => void): void => {
    console.log("Tyro question callback received:", question);
    // Publish the question details so components can display the UI.
    this.questionSubject.next({
      text: question.text,
      options: question.options,
      answerCallback: answerCallback
    });
  };

	tyroStatusMessageCallback = (status: any) => {
		console.log("status", status);
		this.statusMessageSubject.next(status);

	}

	/**
 * Process a refund transaction.
 * @param amtRefund Refund amount.
 * @param txnRef Transaction reference (transNo).
 * @param currencyCode Currency code (default: 'AUD').
 * @param receiptAutoPrint Receipt auto-print flag.
 * @param posId Point-of-sale identifier.
 * @returns Observable containing an object with sessionId and the refund response.
 */
	processRefund(
		amtRefund: number,
		txnRef: number,
		currencyCode: string = 'AUD',
		receiptAutoPrint: string,
		posId: string
	): Observable<{ sessionId: string; status: string }> {
		const refundRequestDto = {
			amtRefund,
			txnRef,
			currencyCode,
			receiptAutoPrint,
			posId
		};

		// Call the EFTPOS client's processRefund method.
		// We assume that eftposClient has a method processRefund that accepts your DTO.
		return this.eftposClient.processRefund(refundRequestDto).pipe(
			map((result: any) => {
				return {
					sessionId: result.sessionId,
					status: result.status,
				};
			})
		);
	}

	getTransactionStatus(transNo: number, integratedEFTProvider: string) {
		return this.eftposClient.getTransactionStatus(transNo, integratedEFTProvider);
	}

	getReceipts(transNo: number, historical: boolean) {
		return this.eftposClient.getReceipts(transNo, "Linkly", historical)
	}

	sendKey(transNo: number, key: string) {
		console.log(transNo);
		var a;
		this.eftposClient.sendKey(transNo, "Linkly", key).subscribe(result => {
			a = result;
		});
		return a
	}

	logon() {
		var a; this.eftposClient.logon("Linkly").subscribe(result => {
			a = result;
		});
		return a
	}

	getLastTrans(integratedEftProvider: string) {
		if (integratedEftProvider == "Tyro") {
			return new Observable<GetLast>(observer => {
				const iclient = this.tyroIClientService.getClient();
				iclient.continueLastTransaction({
					receiptCallback: (receipt) => {},
					transactionCompleteCallback: (response: any) => {
						if (response.result === "APPROVED" || response.result === "SIGNATURE APPROVED") {
							observer.next({amount: response.transactionAmount, success: true}); // TODO JASON surcharge amount?
							observer.complete();
						} else {
							observer.next({ amount: 0, success: false });
							observer.complete();						}
					},
					questionCallback: (question) => {},
					statusMessageCallback: (message) => {}
				});
			});
		} else {
			return this.eftposClient.getLast(integratedEftProvider);
		}
	}

	cancelTyroTransaction() {
		const iclient = this.tyroIClientService.getClient();
		iclient.cancelCurrentTransaction();
	}


	logToBackend(message: string) {
		this.eftposClient.log(message).subscribe();
	}
}



export function mapCartToLinklyBasket(cart: CartItem[]): LinklyBasketItem[] {
	return cart.map(item => {
		const stockItem = item.stockItem;
		console.log(item);
		return {
			id: stockItem.barcode,
			sku: stockItem.barcode,
			qty: item.quantity,
			amt: (stockItem.price || 0) * item.quantity,
			tax: 0, // TODO choose amount in config?
			dis: item.bestValue || 0,
			name: `${stockItem.styleDescription || ''} ${stockItem.colourName || ''} ${stockItem.size || ''}`.trim()
		};
	});
}
