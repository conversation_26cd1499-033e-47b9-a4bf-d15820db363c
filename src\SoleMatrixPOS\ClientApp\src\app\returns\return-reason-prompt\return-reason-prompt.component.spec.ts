import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ReturnReasonPromptComponent } from './return-reason-prompt.component';

describe('ReturnReasonPromptComponent', () => {
  let component: ReturnReasonPromptComponent;
  let fixture: ComponentFixture<ReturnReasonPromptComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ReturnReasonPromptComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ReturnReasonPromptComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
