import { createAction, props } from '@ngrx/store';
import { CustomerClubMember } from './customer-club-member';

import { CustomerClubDto } from '../../../pos-server.generated';
import { ClientSearchRequestDto } from '../../../pos-server.generated';
import { CustomerSuburbDto } from '../../../pos-server.generated';

export const init = createAction('[CustomerClubSearch] Init');
export const search = createAction('[CustomerClubSearch] Search', props<{ searchParams: ClientSearchRequestDto }>());
export const searchResponse = createAction('[CustomerClubSearch] Response', props<{ payload: CustomerClubDto[] }>());
export const selectCustomerClubMember = createAction('[CustomerClubSearch] SelectCustomerClubMember', props<{ payload: CustomerClubDto }>());
export const searchBarcode = createAction(
    '[CustomerClubSearch] SearchBarcode',
    props<{ barcode: string }>()
);

export const searchBarcodeResponse = createAction(
    '[CustomerClubSearch] BarcodeResponse',
    props<{ payload: CustomerClubDto }>()
);

// New action for updating points
export const updateCustomerPoints = createAction(
    '[CustomerClubSearch] UpdateCustomerPoints',
    props<{ customerId: string | number, points: number }>()
);

export const searchSuburbs = createAction(
    '[CustomerClubSearch] SearchSuburbs',
    props<{ search: string }>()
);

export const searchSuburbsResponse = createAction(
    '[CustomerClubSearch] SearchSuburbsResponse',
    props<{ suburbs: CustomerSuburbDto[] }>()
);

export const selectSuburb = createAction(
    '[CustomerClubSearch] SelectSuburb',
    props<{ suburb: CustomerSuburbDto }>()
);

export const clearSuburbSearch = createAction(
    '[Customer Club Search] Clear Suburb Search'
  );

export const searchBarcodeFailure = createAction(
    '[Customer Club Search] Search Barcode Failure',
    props<{ error: any }>()
);

export const setSearchCriteria = createAction(
    '[Customer Club Search] Set Search Criteria',
    props<{ term: string, field: string }>()
);

export const setSelectedCustomerClubMember = createAction(
    '[Customer Club Search] Set Selected Customer Club Member',
    props<{ member: CustomerClubDto }>()
);