import { AppState } from '../index';
import { createSelector } from '@ngrx/store';
import { LaybyState } from './layby.reducer';

export const select = (state: AppState) => state.layby;

export const active = createSelector(select, (s) => s.active);
export const created = createSelector(select, (s) => s.laybyCreated);
export const isLaybyOrderInProgress = createSelector(
    select,
    (state: LaybyState) => state.laybyOrderInProgress
);
export const laybyNumber = createSelector(select, (s) => s.laybyNumber);