using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR; 
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.History;
using System.Collections;
using SoleMatrixPOS.Application.History.Queries;
using SoleMatrixPOS.Application.Daily;
using SoleMatrixPOS.Application.Daily.Queries;
using SoleMatrixPOS.Application.Infrastructure;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController]
	public class HistoryController : ControllerBase
	{
		private readonly IMediator _mediator;
		private readonly StaffCodeContext _staffCodeContext;

        public HistoryController(IMediator mediator, StaffCodeContext staffCodeContext)
        {
            _mediator = mediator;
			_staffCodeContext = staffCodeContext;
        }

		[HttpPost]
		public async Task<IEnumerable<HistoryDto>> Get([FromBody] HistorySearchRequestDto historySearchRequestDto)
		{
			// GET STOREID OF TRANSACTION
			// GET TILLNO OF TRANSACTION

			return await _mediator.Send(new SearchHistoryQuery(historySearchRequestDto, _staffCodeContext.StoreDetailsDto.StoreId, _staffCodeContext.PINPadNo));
		}

		[Route("ClientNo")]
		[HttpPost]
		public async Task<IEnumerable<HistoryDto>> GetByClientNo([FromBody] HistorySearchRequestDto historySearchRequestDto){
			return await _mediator.Send(new SearchHistoryForClientQuery(historySearchRequestDto));
		}

		[Route("DailyTotal")]
		[HttpGet]
		public async Task<DailyDto> GetDailyTotal([FromQuery] DateTime date) {
			return await _mediator.Send(new GetTodayDailyRecordQuery(date));
		}



	}
}
