<!--Solemate Header logo etc..-->
<pos-nav-header pageName="HOUSE KEEPING"></pos-nav-header>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-5">
            <div class="card shadow-sm mt-5">
                <div class="card-header text-ng-primary from-group">
                    <button type="button" class="close" aria-label="Close" (click)="dismiss($event)">
                        <i class="fa fa-times" style="color: red;"></i>
                    </button>
                    <h5>Password Entry</h5>
                </div>
                <div class="card-body from-group">
                    <form [formGroup]="loginForm" (ngSubmit)="onSubmit(loginForm.value)">
                        <div class="form-floating mb-3">
                            <input type="password" formControlName ="password" autocomplete="password"
                                class="form-control form-control-sm" placeholder="********"
                                [ngClass]="{ 'is-invalid': submitted && f.password.errors }" autofocus />
                            <div *ngIf="submitted && f.password.errors" class="invalid-feedback">
                                <div *ngIf="f.password.errors.required">Password is required</div>
                            </div>
                        </div>
                        <div class="from-group">
                            <button [disabled]="loading" class="btn btn-info btn-sm btn-block" type="submit">OK</button>

                        </div>
                    </form>

                    <div class="alert alert-warning" role="alert" *ngIf="managerLoginMessage$ | async as message">
                        {{message}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>