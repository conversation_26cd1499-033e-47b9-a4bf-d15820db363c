using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting.Internal;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using SoleMatrixPOS.Domain.Identity.Models;
using SoleMatrixPOS.Domain.Identity.Service;
using SoleMatrixPOS.Identity.Interface;
using SoleMatrixPOS.Identity.Models;

namespace SoleMatrixPOS.Controllers
{
	
	[Route("api/[controller]")]
    [ApiController]
    public class IdentityController: ControllerBase
    {
        private readonly IMediator _mediator;
		private readonly IMapper _mapper;
		private readonly TokenService _tokenService;
		private readonly UserManager<RegisteredTill> _tillManager;
		private readonly TokenValidationParameters _tokenValidationParameters;
		private readonly RegisteredTillService _regTillService;

        public IdentityController(RegisteredTillService regTillService, IConfiguration config, IMediator mediator, IMapper mapper, TokenService tokenService, UserManager<RegisteredTill> tillManager, TokenValidationParameters tokenValidationParameters)
        {
            _mediator = mediator;
			_tokenService = tokenService;
			_tillManager = tillManager;
			_regTillService = regTillService;
			_mapper = mapper;

			// TODO: move this somewhere
			_tokenValidationParameters = tokenValidationParameters;
		}

		private async Task<TokenPairResponseDto> GenerateTokenPairAsync(RegisteredTill till)
		{
			// Generate a JWT and refresh pair
			TokenDto tokenAccess = _tokenService.GenerateToken(till, await _tillManager.GetClaimsAsync(till));
			TokenDto tokenRefresh = _tokenService.GenerateRefreshToken(till);

			// Update user refresh token data
			till.RefreshTokenHash = _tokenService.HashRefreshToken(tokenRefresh.Payload);
			till.RefreshTokenExpiry = tokenRefresh.ExpiryUtc;

			// Ensure refresh token is saved to the backing store
			await _tillManager.UpdateAsync(till);

			// Send back the tokens
			return new TokenPairResponseDto()
			{
				AccessToken = tokenAccess.Payload,
				RefreshToken = tokenRefresh.Payload
			};
		}

		[HttpPost("Reset")]
		public async Task<ActionResult> ResetPassword([FromBody] PasswordResetDto passwordResetDto)
		{
			RegisteredTill till = await _tillManager.FindByIdAsync(passwordResetDto.Id);

			if (till == null)
			{
				return NotFound(new
				{
					Message = $"Login <{passwordResetDto.Id}> is invalid"
				});
			}

			IdentityResult result = await _tillManager.ResetPasswordAsync(till, passwordResetDto.Token, passwordResetDto.Password);

			if (!result.Succeeded)
			{
				return Unauthorized(new
				{
					Message = "Couldn't reset password",
					Errors = result.Errors.ToArray()
				});
			}

			return Ok();

		}

		[HttpPost("Refresh")]
		public async Task<ActionResult<TokenPairResponseDto>> Refresh([FromBody] TillRefreshDto tillRefreshDto)
		{
			var refreshToken = tillRefreshDto.RefreshToken;
			// First, confirm if the JWT is valid
			var jwtHdl = new JwtSecurityTokenHandler();

			TokenValidationResult validationResult = await jwtHdl.ValidateTokenAsync(refreshToken, _tokenValidationParameters);

			// Send back 403 forbidden if jwt wasn't valid
			if (!validationResult.IsValid) { return Forbid(); }

			// Extract the till id from the token
			string tillId = jwtHdl.ReadJwtToken(refreshToken).Claims.Where((v) => v.Type == ClaimTypes.NameIdentifier).First().Value;

			// Find this till
			RegisteredTill till = await _tillManager.FindByIdAsync(tillId);
            var hashRefreshToken = _tokenService.HashRefreshToken(refreshToken);
            // Then, confirm that the refresh token hasn't been revoked (check with _tillManager)
            if (hashRefreshToken == till.RefreshTokenHash)
			{
				return Ok(await GenerateTokenPairAsync(till));
			}

			else
			{
				return StatusCode(StatusCodes.Status403Forbidden, "Invalid refresh token");
			}
		}

		[HttpPost("Login")]
		public async Task<ActionResult<TokenPairResponseDto>> Login([FromBody] TillLoginDto loginDto)
		{
			RegisteredTill till = await _tillManager.FindByIdAsync(loginDto.Id);

			if (till == null)
			{
				// Not a valid username
				return StatusCode(StatusCodes.Status403Forbidden, "Invalid credentials");
			}

			if(await _tillManager.CheckPasswordAsync(till, loginDto.Password))
			{
				return Ok(await GenerateTokenPairAsync(till));
			}

			else
			{
				// Incorrect password
				return StatusCode(StatusCodes.Status403Forbidden, "Invalid credentials");
			}
		}

		[HttpGet("Me")]
		public async Task<ActionResult<RegisteredTillDto>> Me()
		{
			var me = await _regTillService.GetContextualTillAsync();

			if (me == null) return NotFound("Could not obtain till information from context");

			return _mapper.Map<RegisteredTillDto>(me);
		}
    }
}
