import { Component, OnInit, EventEmitter, Output, Input, ViewChild, ElementRef, AfterViewInit, ViewChildren, QueryList } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AppState } from '../../reducers';
import { Observable, Subject, combineLatest } from 'rxjs';

import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';
import * as customerClubSearchActions from '../../reducers/customer-club/club-search/customer-club.actions';
import { takeWhile, debounceTime, distinctUntilChanged, map, first, tap, startWith } from 'rxjs/operators';

import { CustomerClubDto } from '../../pos-server.generated';
import { ClientSearchRequestDto } from '../../pos-server.generated';
import { ClientSearchKeywordColumnDto } from '../../pos-server.generated';
import { ClientSearchOrderByColumnDto } from '../../pos-server.generated';
import { StockSearchOrderByDirectionEnumDto } from '../../pos-server.generated';

export interface SearchOptions {
	Term: string;
	Field: string;
}

@Component({
	selector: 'pos-customer-club-search',
	templateUrl: './customer-club-search.component.html',
	styleUrls: ['./customer-club-search.component.scss']
})
export class CustomerClubSearchComponent implements OnInit, AfterViewInit {

	term = '';
	field = 'Phone';

	customerClubMembers$: Observable<CustomerClubDto[]>;
	searchOptions$: Observable<ClientSearchRequestDto>;
	searchLoading$: Observable<boolean>;
	collapsed: boolean;

	// keep track of loading state
	public loading: boolean = true;
	isLoadingMore = false;
	noMoreResultsToLoad = false;
	previousMemberCount = 0;
	customerClubMembers: CustomerClubDto[] = [];

	private searchTerm$ = new Subject<string>();
	private alive = true;

	@Input() members: CustomerClubDto[];
	@Input() options: ClientSearchRequestDto;

	@Input() selectedMember;

	@Output() onSelectedMember = new EventEmitter<CustomerClubDto>();
	@Output() onEditedMember = new EventEmitter<CustomerClubDto>();

	@Output() searchChanged = new EventEmitter<ClientSearchRequestDto>();

	@Output() onDoubleClickMember = new EventEmitter<CustomerClubDto>();

	barcode: string = '';
	@ViewChild('barcodeInput', { static: false }) barcodeInput: ElementRef;
	@ViewChild('searchInput', { static: false }) searchInput: ElementRef;
	@ViewChild('scrollContainer', { static: false }) scrollContainer: ElementRef;
	@ViewChildren('memberItem') memberItems: QueryList<ElementRef>;

	currentSkip = 0;

	// Need a variable to track previous field for distinctUntilChanged logic
	private previousField: string = this.field;

	constructor(private store: Store<AppState>, private router: Router) { }

	ngOnInit() {
		const savedField = localStorage.getItem('customerClubSearchField');
		if (savedField) {
			this.field = savedField;
		}

		combineLatest([
			this.store.select(customerClubSearchSelectors.selectCustomerClubSearchTerm),
			this.store.select(customerClubSearchSelectors.selectCustomerClubSearchField),
			this.store.select(customerClubSearchSelectors.searchedCustomerClubMembers)
		]).pipe(
			first(), // take only the initial values
			takeWhile(() => this.alive)
		).subscribe(([storedTerm, storedField, initialMembers]) => {
			console.log("initialMembers", initialMembers);
			// Restore term and field from store
			if (storedTerm !== undefined && storedTerm !== null) {
				this.term = storedTerm;
			}

			// If no members are currently in the store, perform an initial search immediately
			if (!initialMembers || initialMembers.length === 0) {
				// Dispatch action to save search criteria (important for consistency)
				this.store.dispatch(customerClubSearchActions.setSearchCriteria({ term: this.term, field: this.field }));

				// Reset pagination/load state for a new search
				this.currentSkip = 0;
				this.noMoreResultsToLoad = false;
				this.previousMemberCount = 0;
				this.isLoadingMore = false;

				// Perform the search directly (bypassing debounce for initial load)
				this.doSearch({
					searchString: this.term,
					first: 25,
					skip: 0,
					customerClubOrderByDirectionEnumDto: StockSearchOrderByDirectionEnumDto.ASC,
					customerClubSearchKeywordColumnDto: ClientSearchKeywordColumnDto[this.field],
					customerClubSearchOrderByColumnDto: ClientSearchOrderByColumnDto[this.field],
				});
			}
			// If members are already present, this block is skipped, and existing members will be displayed.
		});

		this.store.select(customerClubSearchSelectors.searchedCustomerClubMembers)
			.pipe(takeWhile(() => this.alive))
			.subscribe(members => {
				this.customerClubMembers = members;
				if (this.selectedMember && this.customerClubMembers && this.customerClubMembers.length > 0) {
					this.scrollToSelectedMember();
				}
			});

		this.customerClubMembers$ = this.store.select(customerClubSearchSelectors.searchedCustomerClubMembers);
		this.searchOptions$ = this.store.select(customerClubSearchSelectors.searchOptions);
		this.searchLoading$ = this.store.select(customerClubSearchSelectors.searchLoading);

		this.searchTerm$.pipe(
			takeWhile(() => this.alive),
			debounceTime(200),
			distinctUntilChanged((prev, curr) => prev === curr && this.field === this.previousField),
			tap(() => this.previousField = this.field)
		).subscribe(currentTerm => {
			this.currentSkip = 0;
			this.noMoreResultsToLoad = false;
			this.previousMemberCount = 0;
			this.isLoadingMore = false;

			this.store.dispatch(customerClubSearchActions.setSearchCriteria({ term: currentTerm, field: this.field }));

			this.doSearch({
				searchString: currentTerm,
				first: 25,
				skip: 0,
				customerClubOrderByDirectionEnumDto: StockSearchOrderByDirectionEnumDto.ASC,
				customerClubSearchKeywordColumnDto: ClientSearchKeywordColumnDto[this.field],
				customerClubSearchOrderByColumnDto: ClientSearchOrderByColumnDto[this.field],
			});
		});

		this.searchLoading$.pipe(
			takeWhile(() => this.alive)
		).subscribe((loading) => {
			const wasLoadingMore = this.isLoadingMore;
			this.loading = loading;

			if (!loading) {
				if (wasLoadingMore) {
					if (this.customerClubMembers && this.customerClubMembers.length === this.previousMemberCount) {
						console.log("Load more returned no results. Preventing further loads.");
						this.noMoreResultsToLoad = true;
					}
					this.isLoadingMore = false;
				}
			}
		});
	}

	ngOnDestroy(): void {
		this.alive = false;
	}

	ngAfterViewInit() {
		setTimeout(() => {
			this.searchInput.nativeElement.focus();
		});
	}

	search() {
		localStorage.setItem('customerClubSearchField', this.field);
		this.currentSkip = 0;
		this.isLoadingMore = false;
		this.noMoreResultsToLoad = false;
		this.previousMemberCount = 0;
		this.searchTerm$.next(this.term);
	}

	goHome() {
		this.router.navigateByUrl('/home');
	}

	doSearch(options: ClientSearchRequestDto) {
		this.store.dispatch(customerClubSearchActions.search({ searchParams: options }));
	}

	selectMember(member: CustomerClubDto) {
		this.onSelectedMember.emit(member);
	}

	editMember(member: CustomerClubDto) {
		console.log("Editing: ", member);
		this.onEditedMember.emit(member);
	}

	addMember() {
		// Adding a member === editing an empty member
		this.onEditedMember.emit(
			{
				clientCode: "",
				title: "",
				firstname: "",
				surname: "",
				careof: "",
				street: "",
				suburb: "",
				state: "",
				postcode: "",
				email: "",
				telephone: "",
				clientPoints: 0,
				privDiscount: 0,
				expiryDate: "",
				issueDate: "",
				barcode: "",
				isVIP: 0,
			}
		);
	}

	searchByBarcode() {
		if (this.barcode) {
			this.store.dispatch(customerClubSearchActions.searchBarcode({ barcode: this.barcode }));
			this.barcode = ''; // Clear the input
			// Focus back on the input for the next scan
			setTimeout(() => {
				this.barcodeInput.nativeElement.focus();
			});
		}
	}

	onDoubleClick(member: CustomerClubDto) {
		this.selectMember(member);
		this.onDoubleClickMember.emit(member);
	}

	//called when user scrolls to the bottom of the list
	loadMore() {
		if (this.loading || this.isLoadingMore || this.noMoreResultsToLoad) return;

		this.previousMemberCount = this.customerClubMembers.length;
		this.isLoadingMore = true;
		this.currentSkip += 25;
		this.doSearch({
			searchString: this.term,
			first: 25,
			skip: this.currentSkip,
			customerClubOrderByDirectionEnumDto: StockSearchOrderByDirectionEnumDto.ASC,
			customerClubSearchKeywordColumnDto: ClientSearchKeywordColumnDto[this.field],
			customerClubSearchOrderByColumnDto: ClientSearchOrderByColumnDto[this.field],
		});
	}

	//keeps track of users scroll position and calls the load more function when the user is near the bottom of the list
	onScroll(event: any) {
		const target = event.target;
		const scrollPosition = target.scrollTop + target.offsetHeight;
		const scrollHeight = target.scrollHeight;

		// Define an amount of pixels from the bottom of the list that the user can scroll before the load more function is called
		const threshold = 100;

		if (scrollPosition >= scrollHeight - threshold && !this.isLoadingMore && !this.loading && !this.noMoreResultsToLoad) {
			this.loadMore();
		}
	}

	private scrollToSelectedMember(): void {
		// Use setTimeout to allow Angular to render the list before scrolling
		setTimeout(() => {
			if (this.selectedMember && this.memberItems && this.scrollContainer && this.scrollContainer.nativeElement) {
				const memberIndex = this.customerClubMembers.findIndex(
					m => m.clientCode === this.selectedMember.clientCode // Assuming clientCode is a unique and reliable identifier
				);

				if (memberIndex !== -1) {
					const memberElementRefArray = this.memberItems.toArray();
					if (memberElementRefArray[memberIndex]) {
						const memberElement = memberElementRefArray[memberIndex].nativeElement;
						memberElement.scrollIntoView({ behavior: 'auto', block: 'nearest' });
					} else {
						console.warn('ScrollToSelectedMember: Member element not found at index:', memberIndex);
					}
				}
			}
		}, 0);
	}

}
