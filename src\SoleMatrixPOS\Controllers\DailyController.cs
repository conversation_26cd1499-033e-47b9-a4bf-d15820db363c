using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Daily;
using SoleMatrixPOS.Application.Daily.Commands;
using SoleMatrixPOS.Application.Daily.Queries;
namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	// [RequireStaffCodeFilter]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class DailyController : ControllerBase
	{
		private readonly IMediator _mediator;
		public DailyController(IMediator mediator)
		{
			_mediator = mediator;
		}
		[HttpPut]
		public async Task<IActionResult> CreateDaily([FromBody] DailyDto dto)
		{
			await _mediator.Send(new CreateInitialDailyRecordCommand(dto));
			return Ok();
		}
		[HttpGet]
		public async Task<ActionResult<DailyDto>> GetDaily([FromQuery] DateTime date)
		{
			return await _mediator.Send(new GetTodayDailyRecordQuery(date));
		}
		[Route("range")]
		[HttpGet]
		public async Task<ActionResult<IEnumerable<DailyDto>>> GetDailyRange([FromQuery] DateTime startDate, [FromQuery] DateTime endDate)
		{
			var newStart = startDate.Date.AddDays(1);
			var newEnd = endDate.Date.AddDays(1);

			return Ok(await _mediator.Send(new GetDailyRecordRangeQuery(newStart, newEnd)));
		}
		[Route("range/totals")]
		[HttpGet]
		public async Task<ActionResult<DailyTotalsDto>> GetDailyRangeTotals([FromQuery] DateTime startDate, [FromQuery] DateTime endDate)
		{
			var newStart = startDate.Date.AddDays(1);
			var newEnd = endDate.Date.AddDays(1);

			return Ok(await _mediator.Send(new GetDailySalesTotalsQuery(newStart, newEnd)));
		}
		[Route("salesByStaff")]
		[HttpGet]
		public async Task<IEnumerable<SalesByStaffQueryDto>> SalesByStaff([FromQuery] DateTime date)
		{
			return await _mediator.Send(new SalesByStaffQuery(date));
		}
		[Route("refundsByStaff")]
		[HttpGet]
		public async Task<IEnumerable<SalesByStaffQueryDto>> RefundsByStaff([FromQuery] DateTime date)
		{
			return await _mediator.Send(new RefundsByStaffQuery(date));
		}
		[Route("salesByHour")]
		[HttpGet]
		public async Task<IEnumerable<SalesByHourQueryDto>> SalesByHour([FromQuery] DateTime date)
		{
			return await _mediator.Send(new SalesByHourQuery(date));
		}
		[Route("refundsByHour")]
		[HttpGet]
		public async Task<IEnumerable<SalesByHourQueryDto>> RefundsByHour([FromQuery] DateTime date)
		{
			return await _mediator.Send(new RefundsByHourQuery(date));
		}
		[Route("multiSales")]
		[HttpGet]
		public async Task<MultiSaleDto> MultiSales([FromQuery] DateTime date)
		{
			return await _mediator.Send(new MultiSalesQuery(date));
		}
		[Route("departmentSales")]
		[HttpGet]
		public async Task<IEnumerable<DepartmentSalesDto>> DepartmentSales([FromQuery] DateTime date)
		{
			return await _mediator.Send(new DepartmentSalesQuery(date));
		}
		[Route("departmentRefunds")]
		[HttpGet]
		public async Task<IEnumerable<DepartmentSalesDto>> DepartmentRefunds([FromQuery] DateTime date)
		{
			return await _mediator.Send(new DepartmentRefundsQuery(date));
		}
	}
}
