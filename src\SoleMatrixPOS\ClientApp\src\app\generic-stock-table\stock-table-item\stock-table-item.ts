import { StockItemDto, StockTableItemDto } from '../../pos-server.generated'

export function TableItemFromDto(dto: StockItemDto) : StockTableItemDto {
    return {
        stockItem: dto,
        quantity: 1
    }
}

export function checkEquivalence(ti1: StockTableItemDto, ti2: StockTableItemDto) : boolean {
    return (
        ti1.stockItem.styleCode == ti2.stockItem.styleCode &&
        ti1.stockItem.colourCode == ti2.stockItem.colourCode &&
        ti1.stockItem.size == ti2.stockItem.size
    );
}

export interface StockTableFieldsEnabled {
    style: boolean,
    description: boolean,
    makerCode: boolean,
    labelCode: boolean,
    colourCode: boolean,
    departmentCode: boolean,
    colourName: boolean,
    size: boolean,
    qty: boolean,
    qtySelectable: boolean,
    price: boolean,
    totalPrice: boolean
};

export const StockTableDefaultFields : StockTableFieldsEnabled = {
    style: true,
    description: true,
    makerCode: false,
    labelCode: false,
    colourCode: false,
    departmentCode: false,
    colourName: true,
    size: true,
    qty: true,
    qtySelectable: false,
    price: true,
    totalPrice: true
};