import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { map, mergeMap, catchError } from 'rxjs/operators';
import { of, from } from 'rxjs';
import { QuoteClient, FileResponse } from 'src/app/pos-server.generated';
import * as quoteActions from './quote.actions';

@Injectable()
export class QuoteEffects {
    constructor(
        private actions$: Actions,
        private client: QuoteClient
    ) {}

    submitQuoteWithTransaction$ = createEffect(() =>
        this.actions$.pipe(
            ofType(quoteActions.createQuote),
            mergeMap((action) => {
                console.log('Submitting quote:', action.payload);
                return this.client.addQuoteWithTransaction(action.payload).pipe(
                    map(() => quoteActions.createQuoteSuccess()),
                    catchError((error) => of(quoteActions.createQuoteFailure({ error })))
                );
            })
        )
    );

    cancelQuote$ = createEffect(() =>
        this.actions$.pipe(
            ofType(quoteActions.cancelQuote),
            mergeMap((action) =>
                this.client.cancelQuote(action.quoteNumber).pipe(
                    map(() => quoteActions.cancelQuoteSuccess()),
                    catchError((error) => of(quoteActions.cancelQuoteFailure({ error })))
                )
            )
        )
    );

    completeQuote$ = createEffect(() =>
        this.actions$.pipe(
            ofType(quoteActions.completeQuote),
            mergeMap((action) => {
                console.log('Submitting quote:', action.quoteCode);
                return this.client.completeQuote(action.quoteCode).pipe(
                    map(() => quoteActions.completeQuoteSuccess()),
                    catchError((error) => of(quoteActions.completeQuoteFailure({ error })))
                )
            })
        )
    );

    getQuoteNo$ = createEffect(() =>
        this.actions$.pipe(
            ofType(quoteActions.getQuoteNo),
            mergeMap(() =>
                this.client.getQuoteNo().pipe(
                    mergeMap((response: FileResponse) => {
                        if (response.status === 200 || response.status === 206) {
                            return from(this.extractQuoteNo(response.data)).pipe(
                                map((quoteNo) => quoteActions.getQuoteNoSuccess({ quoteNo })),
                                catchError((error) => {
                                    console.error('Extract Quote No Error:', error);
                                    return of(quoteActions.getQuoteNoFailure({ error: 'Failed to extract quote number.' }));
                                })
                            );
                        } else {
                            return of(quoteActions.getQuoteNoFailure({
                                error: `Unexpected status code: ${response.status}`
                            }));
                        }
                    }),
                    catchError((error) => {
                        console.error('Get Quote No Error:', error);
                        return of(quoteActions.getQuoteNoFailure({ error: error.message || 'Unknown error' }));
                    })
                )
            )
        )
    );

    // Helper method to extract quoteNo from Blob
    private extractQuoteNo(blob: Blob): Promise<string> {
        return new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const text = reader.result as string;
                if (text) {
                    resolve(text.trim());
                } else {
                    reject('Failed to extract quote number.');
                }
            };
            reader.onerror = () => {
                reject('Failed to read blob data.');
            };
            reader.readAsText(blob);
        });
    }
}
