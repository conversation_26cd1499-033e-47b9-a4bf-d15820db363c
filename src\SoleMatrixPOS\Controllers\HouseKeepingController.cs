using MediatR; 
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.HouseKeeping;
using SoleMatrixPOS.Application.HouseKeeping.Queries;
using SoleMatrixPOS.Application.Infrastructure;
using SoleMatrixPOS.Dal.Interface.Models.SMSys;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class HouseKeepingController : ControllerBase
	{
		private readonly IMediator _mediator;
		private readonly StaffCodeContext _staffCodeContext;

		public HouseKeepingController(IMediator mediator, StaffCodeContext staffCodeContext)
		{
			_mediator = mediator;
			_staffCodeContext = staffCodeContext;
		}

		[Route("MLogin")]
		public async Task<MloginResponseDto> Login(string password, CancellationToken ct)
		{
			return await _mediator.Send(new MLoginQuery(password), ct);
		}

		[Route("RecUpdate")]
		[HttpPut]
		public async Task<ReceiptUpdateDto> UpdateReceipt([FromBody] ReceiptUpdateDto receiptUpdateDto)
		{
			return await _mediator.Send(new ReceiptUpdateQuery(receiptUpdateDto));
		}

		[Route("SysUpdate")]
		[HttpPut]
		public async Task<SysControlUpdateDto> UpdateSysControl([FromBody] SysControlUpdateDto managerUpdate)
		{
			return await _mediator.Send(new SysUpdateQuery(managerUpdate));
		}

		[Route("SysConfig")]
		[HttpGet]
		public async Task<GetSystemStatusDto> GetSysControl()
		{
			return await _mediator.Send(new GetSysStatusQuery(_staffCodeContext.StoreDetailsDto.StoreId));
		}

		[Route("PINAuth/StoreId")]
		[HttpGet]
		public async Task<ActionResult<List<GetPINAuthDto>>> GetPINAuthByStoreId([FromRoute] string storeId, CancellationToken ct)
		{
			var result = await _mediator.Send(new GetPINAuthByUUIDQuery(null, storeId), ct);
			if (result == null)
			{
				return NotFound($"No PINAuth records found for Store: {storeId}");
			}
			return Ok(result);
		}

		[Route("PINAuth/PINPadId")]
		[HttpGet]
		public async Task<ActionResult<GetPINAuthDto>> GetPINAuthByPINPadId(string pinPadId, CancellationToken ct)
		{
			var result = await _mediator.Send(new GetPINAuthByUUIDQuery(pinPadId), ct);
			if (result == null)
			{
				return NotFound($"No PINAuth records found for PINPadId: {pinPadId}");
			}
			return Ok(result);
		}

		[Route("PINAuth")]
		[HttpPut]
		public async Task<ActionResult<GetPINAuthDto>> UpdatePINAuth([FromBody] GetPINAuthDto pinAuthDto, CancellationToken ct)
		{
			var result = await _mediator.Send(new UpdatePINAuthQuery(pinAuthDto), ct);
			if (result == null)
			{
				return BadRequest("Failed to update PINAuth record.");
			}
			return Ok(result);
		}

	}
}
