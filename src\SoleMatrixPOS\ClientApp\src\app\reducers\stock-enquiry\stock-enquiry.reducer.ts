import { createReducer, on, Action } from '@ngrx/store';
import { StockEnquiryResultDto, StockItemDto } from "src/app/pos-server.generated";
import * as StockEnquiryAction from './stock-enquiry.actions'

export class StockEnquiryState {
    isLoading: boolean;
    itemStocks: StockEnquiryResultDto;
    selected: StockItemDto;
}

export const initialState: StockEnquiryState = {
    isLoading: false,
    itemStocks: {
        stockEnquiryHeader: null,
        stockEnquiryLocation: null,
        stockEnquiryQty: null,
        stockEnquirySizes: null,
        stockEnquiryTotal: null,
        stockEnquiryTotalBySize: null,
        stockEnquiryGetColor: null
    
    } as StockEnquiryResultDto,
    selected: null
}


export const stockEnquiryReducer = createReducer(initialState,
    on(StockEnquiryAction.init, () => ({ ...initialState })),
    //search request
    on(StockEnquiryAction.enquiryQuery, (state, action) => ({ ...state, isLoading: true, options: action.stockItem.styleCode })),
    on(StockEnquiryAction.enquiryResponse, (state, action) => ({ ...state, isLoading: false, itemStocks: action.payload })),

    //color filter request
    on(StockEnquiryAction.enquiryColor, (state, action) => ({...state, isLoading: true, option: action.itemStock.colorCode})),
    on(StockEnquiryAction.enquiryFilterResponse, (state, action) => ({...state, isLoading: false, itemStocks: action.payload})),

    //on(StockEnquiryAction.enquiryFilter, (state, action) => ({...state,isLoading:false, filters: action.filter }))
    //on(StockEnquiryAction.enquiryQuery, (state, action) => ({...state,isLoading:true, option: action.stockItem.styleCode }))

);

export function reducer(state: StockEnquiryState | undefined, action: Action) {
    return stockEnquiryReducer(state, action);
}


