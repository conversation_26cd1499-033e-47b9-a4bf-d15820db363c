import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { HttpClientTestingModule } from "@angular/common/http/testing"
import { HttpClient, HttpHandler } from '@angular/common/http';

import { TransferComponent } from './transfer.component';
import { TransferTableComponent } from './transfer-table/transfer-table.component';
import { TransferFooterComponent } from './transfer-footer/transfer-footer.component';
import { TransferListComponent } from './transfer-list/transfer-list.component';
import { Store } from '@ngrx/store';
//import { AppState } from '../../../../redux/app.store';
import { AppState, state } from '../stock-sale-table/test-state';

describe('TransferComponent', () => {
  let component: TransferComponent;
  let fixture: ComponentFixture<TransferComponent>;
  let valueServiceSpy: jasmine.SpyObj<Store<AppState>>;

  beforeEach(async(() => {

    const spy = jasmine.createSpyObj('Store', ['select']);

    TestBed.configureTestingModule({
      declarations: [ 
        TransferComponent,
        TransferListComponent,
        TransferFooterComponent,
        TransferTableComponent
      ]
    })

    component = TestBed.get(TransferComponent);
    valueServiceSpy = TestBed.get(Store);
  }));

  // The cdkDragDrop stuff is refusing to cooperate
  xit('should create', () => {
    expect(component).toBeTruthy();
  });

});
