import { createSelector } from "@ngrx/store";
import { AppState } from "..";

export const select = (state: AppState) => state.giftVoucherPayment;

export const voucher = createSelector(select, (s) => s.giftVoucher);

// New selectors for tracking
export const getGiftCardTracking = createSelector(
  select, 
  (state) => state.giftCardTracking
);

export const errored = createSelector(
  select,
  (state) => state.giftCardErrored
)

// Get remaining amount for a specific gift card
export const getGiftCardRemainingAmount = (giftCardCode: string) => createSelector(
  select,
  (state) => {
    if (!state.giftCardTracking[giftCardCode]) {
      return null;
    }
    
    const tracking = state.giftCardTracking[giftCardCode];
    const usedAmount = tracking.payments.reduce((sum, payment) => sum + payment.amount, 0);
    
    return tracking.originalAmount - usedAmount;
  }
);

// Get all payments for a specific gift card
export const getGiftCardPayments = (giftCardCode: string) => createSelector(
  select,
  (state) => {
    if (!state.giftCardTracking[giftCardCode]) {
      return [];
    }
    
    return state.giftCardTracking[giftCardCode].payments;
  }
);

// Get used amount for a gift card
export const getGiftCardUsedAmount = (giftCardCode: string) => createSelector(
  select,
  (state) => {
    if (!state.giftCardTracking[giftCardCode]) {
      return 0;
    }
    
    return state.giftCardTracking[giftCardCode].payments.reduce(
      (sum, payment) => sum + payment.amount, 0
    );
  }
);

// Get all payments for a specific transaction
export const getTransactionGiftCardPayments = (transactionId: string) => createSelector(
  select,
  (state) => {
    const result = {};
    
    Object.keys(state.giftCardTracking).forEach(giftCardCode => {
      const tracking = state.giftCardTracking[giftCardCode];
      const transactionPayments = tracking.payments.filter(
        p => p.transactionId === transactionId
      );
      
      if (transactionPayments.length > 0) {
        result[giftCardCode] = transactionPayments;
      }
    });
    
    return result;
  }
);