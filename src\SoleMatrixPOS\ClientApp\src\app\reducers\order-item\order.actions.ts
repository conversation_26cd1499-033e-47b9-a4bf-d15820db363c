import { createAction, props } from '@ngrx/store';
import { CreateOrderRequest, CustOrderHeaderDTO, CustOrderLineDTO } from 'src/app/pos-server.generated';

// Initialize the order state
export const init = createAction("[Order] Init");

// Start a new order
export const startOrder = createAction("[Order] Start Order");

// Submit the combined order and transaction DTO
export const submitOrderWithTransaction = createAction(
    "[Order] Submit Order With Transaction",
    props<{ payload: CreateOrderRequest }>()
);

// Action dispatched when order and transaction are successfully created
export const orderWithTransactionCreated = createAction("[Order] Order With Transaction Created");

// Handle errors during the submission process
export const orderSubmissionFailed = createAction(
    "[Order] Order Submission Failed",
    props<{ error: any }>()
);

// Set order header to store
export const setOrderHeader = createAction("[Order] Set Order Header", props<{ payload: CustOrderHeaderDTO }>());

// Set order lines to store
export const setOrderLines = createAction("[Order] Set Order Lines", props<{ payload: CustOrderLineDTO[] }>());

// Reset the order state
export const resetOrderState = createAction("[Order] Reset Order State");

// Initiate order cancellation
export const cancelOrder = createAction(
    "[Order] Cancel Order",
    props<{ orderNumber: string }>()
);

// Action dispatched when an order is successfully canceled
export const orderCancelled = createAction("[Order] Order Cancelled");

// Handle errors during order cancellation
export const orderCancellationFailed = createAction(
    "[Order] Order Cancellation Failed",
    props<{ error: any }>()
);

// Submit a deposit amount
export const setDeposit = createAction(
    "[Order] Set Deposit",
    props<{ deposit: number }>()
);

export const uploadOrderCode = createAction(
    '[Order] Upload Order Code', 
    props<{ orderCode: string }>()
  );

  export const clearUploadedOrderCode = createAction(
    '[Order] Clear Uploaded Order Code'
  );
export const clearDeposit = createAction('[Order] Clear Deposit');

export const completeOrder = createAction(
    "[Order] Complete Order",
    props<{ orderNumber: string }>()
);

// Action dispatched when an order is successfully completed
export const orderCompleted = createAction("[Order] Order Completed");

// Handle errors during order completion
export const orderCompletionFailed = createAction(
    "[Order] Order Completion Failed",
    props<{ error: any }>()
);

export const getOrderNo = createAction(
    "[Order] Get Order Number"
);

export const getOrderNoSuccess = createAction(
    "[Order] Get Order Number Success",
    props<{ orderNo: string }>()
);

export const getOrderNoFailure = createAction(
    "[Order] Get Order Number Failure",
    props<{ error: any }>()
);