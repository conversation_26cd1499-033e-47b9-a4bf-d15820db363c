<nav class="navbar navbar-light navbar-expand-lg bg-light">
	<div class="container-fluid">
		<a class="navbar-brand" href="#">
			<img src="assets/logo.svg" alt="Solematrix Logo" />
		</a>
		<button class="navbar-toggler" type="button" (click)="collapsed = !collapsed"
				aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
			<span class="navbar-toggler-icon"></span>
		</button>
		<a id="logon" type="button" (click)="PINPadLogon()">PINPad Logon</a>
		<div class="navbar-collapse collapsed" [class.collapse]="collapsed" id="navbarSupportedContent">
			<ul class="navbar-nav ml-auto">
				<li class="nav-item d-flex align-items-center mr-3">
					<label for="printerDropdown" class="mr-2 mb-0" [translate]="'home-header.buttons.SelectPrinter'">Select Printer</label>
					<select id="printerDropdown"
							class="form-control"
							[(ngModel)]="selectedPrinter"
							(change)="onPrinterChange()">
						<option [ngValue]="null">No Printer</option>
						<option *ngFor="let printer of printers" [value]="printer">{{ printer }}</option>
					</select>
				</li>
				<li class="nav-item d-flex align-items-center mr-3">
					<label for="printerWidthDropdown" class="mr-2 mb-0">Print Width</label>
					<select id="printerWidthDropdown"
							class="form-control"
							[(ngModel)]="printerWidth"
							(change)="onPrinterWidthChange()">
						<option *ngFor="let width of printerWidthOptions" [value]="width">{{ width }}</option>
					</select>
				</li>
				<li class="nav-item active">
					<a class="nav-link" [routerLink]="['/support']" [translate]="'home-header.buttons.Support'">Support</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" (click)="openClockOutModal()" [translate]="'home-header.buttons.ClockOut'">Clock Out</a>
				</li>
				<li *ngIf="!showDownloadPrinterService" class="nav-item">
					<a class="nav-link" href="https://smstaticfiles.blob.core.windows.net/pos-print/pos-print-v1.1.0.exe" target="_blank" rel="noopener noreferrer">
						Download Printer Service
					</a>
				</li>
				<!-- Add this before the Logout dropdown -->
				<li class="nav-item">
					<a class="nav-link" [routerLink]="['/end-of-day-float']" [translate]="'home-header.buttons.EndOfDay'">End of Day</a>
				</li>
				<li class="nav-item" *ngIf="staff$ | async as staff">
					<span class="nav-link text-dark">Logged in as: {{ staff.name }}</span>
				</li>
				<li class="nav-item">
					<a class="nav-link" (click)="switchUser()" [translate]="'home-header.buttons.Logout'">Log out</a>
				</li>
			</ul>
		</div>
	</div>
</nav>
