import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SysControlUpdateDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import { Store } from '@ngrx/store';
import { EftposService } from '../../eftpos/eftpos.service';

@Component({
	selector: 'pos-pinpairing-modal',
	templateUrl: './linkly-pinpairing-modal.component.html',
	styleUrls: ['./linkly-pinpairing-modal.component.scss']
})
export class LinklyPINPairingModalComponent implements OnInit {
	pairingForm: FormGroup;

	constructor(
		public activeModal: NgbActiveModal,
		private fb: FormBuilder,
		private store: Store<AppState>,
		private eftposService: EftposService
	) { }

	ngOnInit() {
		this.pairingForm = this.fb.group({
			username: ['', Validators.required],
			password: ['', Validators.required],
			pairingCode: ['', Validators.required]
		});
	}

	submitPINPairCode() {
		if (this.pairingForm.valid) {
			const formValues = this.pairingForm.value;
			this.eftposService.pairPINPad(formValues.username, formValues.password, formValues.pairingCode);
			this.activeModal.close(formValues);
		}
	}

	close() {
		this.activeModal.close();
	}

	dismiss(reason: string) {
		this.activeModal.dismiss(reason);
	}
}
