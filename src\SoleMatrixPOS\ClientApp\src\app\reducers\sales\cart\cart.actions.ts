import { createAction, props } from '@ngrx/store';
import { CartItemDto, StockItemDto, SuspendTransToCartDto } from 'src/app/pos-server.generated';
import { CartItem } from './cart.reducer';

export const init = createAction('[Cart] Init');
export const addItem = createAction('[Cart] AddItem', props<{ stockItem: StockItemDto }>());
export const addItemResponse = createAction('[Cart] AddItemResponse', props<{ cartItem: CartItemDto }>());

export const addReturnItem = createAction('[Cart] AddReturnItem', props<{ stockItem: StockItemDto }>());
export const addReturnItemResponse = createAction('[Cart] AddReturnItemResponse', props<{ cartItem: CartItemDto }>());
export const removeItem = createAction('[Cart] RemoveItem', props<{ id?: string; stockItem?: StockItemDto }>());
export const setNumberOfItems = createAction('[Cart] SetNumItems', props<{ id?: string; stockItem?: StockItemDto; quantity: number }>());

// Reason-related
export const addReason = createAction("[Cart] Add Reason", props<{ barcode: string, reason: string }>());
export const removeReason = createAction("[Cart] Remove Reason", props<{ barcode: string, reasonId: number }>())
export const removeAllReasons = createAction("[Cart] Remove All Reasons", props<{ barcode: string }>());

// Exchange-related
export const negateCart = createAction("[Cart] Negate");

export const updateItemPrice = createAction(
    '[Cart] Update Item Price',
    props<{
        id?: string;
        stockItem?: StockItemDto;
        newPrice: number;
        reason: string;
        discountCode: string;
        discountPercent: number;
    }>()
);

export const removeDiscount = createAction(
    '[Cart] Remove Discount',
    props<{ id?: string; stockItem?: StockItemDto }>()
);

export const removeAllDiscounts = createAction(
    '[Cart] Remove All Discounts'
);

export const applyDiscountToAll = createAction(
    '[Cart] Apply Discount To All',
    props<{
        discountPercent: number;
        reason: string;
        discountCode: string;
    }>()
);

export const applyAmountToAll = createAction(
    '[Cart] Apply Amount To All',
    props<{
        discountAmount: number;
        reason: string;
        discountCode: string;
    }>()
);

export const addSuspendedSaleItem = createAction(
    '[Cart] Add Suspended Sale Item',
    props<{ 
        cartItem: SuspendTransToCartDto,
        reason?: string,
        reasonCode?: string 
    }>()
);

export const addSundryItem = createAction(
    '[Cart] Add Sundry Item',
    props<{ price: number }>()
);

export const addSundryItemWithReason = createAction(
    '[Cart] Add Sundry Item With Reason',
    props<{ price: number, description: string }>()
);

export const setExchangeMode = createAction(
    '[Cart] Set Exchange Mode',
    props<{ isExchangeMode: boolean }>()
);