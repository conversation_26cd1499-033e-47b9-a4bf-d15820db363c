import { Component, OnInit, Output, EventEmitter, Input, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CustomerClubModalComponent } from 'src/app/customer-club/customer-club-modal/customer-club-modal.component';
import { UrlHistoryService } from 'src/app/url-history.service';
import { Transaction } from '../payment.service';
import { CustomerClubDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';
import * as saleNoteSelectors from '../../reducers/sale-note/sale-note.selectors';
import * as SaleNoteActions from '../../reducers/sale-note/sale-note.actions';
import Swal from 'sweetalert2';

@Component({
  selector: 'pos-payment-footer',
  templateUrl: './payment-footer.component.html',
  styleUrls: ['./payment-footer.component.scss']
})
export class PaymentFooterComponent implements OnInit, OnDestroy {

  @Output() processPayment: EventEmitter<undefined> = new EventEmitter();

  @Input() transaction$: Observable<Transaction>;

  selectedCustomerClubMember$: any;
  selectedCustomerClubMember: CustomerClubDto = null;
  
  private destroy$ = new Subject<void>();
  saleComment: string = '';

  constructor(
    private router: Router, 
    private urlHistory: UrlHistoryService,
    private modalService: NgbModal,
    private store: Store<AppState>
  ) { }

  ngOnInit() {
    this.subscribeToState();
    
    this.store.select(saleNoteSelectors.selectSaleNote)
      .subscribe(note => {
        this.saleComment = note;
      });
  }
  
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  
  subscribeToState() {
    this.selectedCustomerClubMember$ = this.store.select(customerClubSearchSelectors.selectedCustomerClubMember);
    this.selectedCustomerClubMember$.pipe(
      takeUntil(this.destroy$)
    ).subscribe((s) => {
      this.selectedCustomerClubMember = s;
      console.log('Selected customer club member:', s);
    });
  }

  launchCustomerClubModal() {
    const modalRef = this.modalService.open(CustomerClubModalComponent, { size: 'xl', centered: true });
    modalRef.componentInstance.name = 'CustomerClubModal';
    modalRef.result.then((result) => {
      if (result) {
        console.log('result from modal:', result);
      }
    }).catch(error => {
      console.log("Error occurred: ", error);
    });
  }

  processBtnClick(){
    this.processPayment.emit();
  }

  backBtnClick(){
    console.log("Navigating backwards...");
    this.router.navigateByUrl(this.urlHistory.previousUrl);
  }

  async launchCommentModal() {
    const { value: comment } = await Swal.fire({
      title: 'Sale Comment',
      input: 'textarea',
      text: 'Enter your comment',
      inputValue: this.saleComment || '',
      showCancelButton: true,
      inputValidator: (value) => {
        if ((value && value.length > 49)) {
          return 'Comment must be less than 50 characters';
        }
        return null;
      }
    });

    if (comment !== undefined && comment.length > 0) {
      this.saleComment = comment;
      this.store.dispatch(SaleNoteActions.setSaleNote({ note: comment }));
    }
  }

  removeComment() {
    this.saleComment = '';
    this.store.dispatch(SaleNoteActions.clearSaleNote());
  }

}
