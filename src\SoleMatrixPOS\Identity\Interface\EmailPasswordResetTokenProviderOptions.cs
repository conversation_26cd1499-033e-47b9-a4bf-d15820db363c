using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using System;

namespace SoleMatrixPOS.Identity.Interface
{

	public class ConfigureEmailPasswordResetTokenProviderOptions : IConfigureOptions<EmailPasswordResetTokenProviderOptions>
	{
		private readonly IConfiguration _configuration;

		public ConfigureEmailPasswordResetTokenProviderOptions(IConfiguration configuration)
		{
			_configuration = configuration;
		}

		public void Configure(EmailPasswordResetTokenProviderOptions options)
		{
			options.Name = "EmailDataProtectorTokenProvider";
			options.TokenLifespan = TimeSpan.FromMinutes(
				_configuration.GetValue<int>("Identity:PasswordResetExpiryMinutes", 15)
			);
		}
	}
}
