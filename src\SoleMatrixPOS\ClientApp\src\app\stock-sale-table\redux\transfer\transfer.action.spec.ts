import * as TransferAction from './transfer.action';

describe('TransferAction', () => {

  describe('AddTransferAction', ()=>{

    it('should be of type "[TRANSFER] add"', ()=> {

      const action: TransferAction.AddTransferAction = 
        new TransferAction.AddTransferAction("Location 1", "Location 2", "BOUNCE", "1", 1);
    
      expect(action.type).toBe("[TRANSFER] add");
        
    });

    it('should create a unique id', ()=> {

      let ids: number[] = [0.5849752126537391];

      for(let i = 0; i < 10000; i++) {
        const action: TransferAction.AddTransferAction = 
          new TransferAction.AddTransferAction("Location 1", "Location 2", "BOUNCE", "1", 1);
      
        expect(ids.includes(action.id)).toBe(false);
        
        ids.push(action.id);
      }
 
    });

  });

  describe('RemoveTransferAction', ()=>{

    it('should be of type "[TRANSFER] remove"', ()=> {

      const action: TransferAction.RemoveTransferAction = 
        new TransferAction.RemoveTransferAction(1, "Location 1", "Location 2", "BOUNCE", "1", 1);
    
      expect(action.type).toBe("[TRANSFER] remove");
        
    });
  });

  describe('LoadTransfersAction', ()=>{

    it('should be of type "[TRANSFER] load"', ()=> {

      const action: TransferAction.LoadTransfersAction = 
        new TransferAction.LoadTransfersAction([]);
    
      expect(action.type).toBe("[TRANSFER] load");
        
    });

  });

  describe('HighlightTransferAction', ()=>{

    it('should be of type "[TRANSFER] highlight"', ()=> {

      const action: TransferAction.HighlightTransferAction = 
        new TransferAction.HighlightTransferAction(1, "Location 1", "Location 2", "BOUNCE", "1", 1);
    
      expect(action.type).toBe("[TRANSFER] highlight");
        
    });

  });

});


// export class AddTransferAction implements Action {
//   readonly type = ADD_TRANSFER;
//   public id: number;
//   public highlighted: string;

//   constructor(
//       public from: string,
//       public to: string,
//       public name: string,
//       public size: string,
//       public qty: number
//   ){
//       this.id = Math.random();
//       this.highlighted = "none";
//   }
// }

// /**
// * Removes a transfer from the transfer state slice by id
// */
// export class RemoveTransferAction implements Action {
//   readonly type = REMOVE_TRANSFER;

//   constructor(
//       public id: number
//   ){}
// }

// /**
// * Loads existing transfers
// */
// export class LoadTransfersAction implements Action {
//   readonly type = LOAD_TRANSFERS;

//   constructor(
//       public transfers: Transfer[]
//   ){}
// }

// /**
// * Switches the highlight state of a transfer
// */
// export class HighlightTransferAction implements Action {
//   readonly type = HIGHLIGHT_TRANSFER;

//   constructor(
//       public id: number,
//   ){}
// }