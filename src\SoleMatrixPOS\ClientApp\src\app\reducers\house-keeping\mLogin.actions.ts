import { createAction, props } from "@ngrx/store";
import { MloginResponseDto } from "src/app/pos-server.generated";


// init or clear
export const init = createAction('[HouseKeeping] init');
// dispatch to perform a login
export const mLogin = createAction('[HouseKeeping] ManagerLogin', props<{ password: string }>());
// process the result from a login response
export const mLoginResponse = createAction('[HouseKeeping] Login Response', props<{ payload: MloginResponseDto }>())
// raise when manager login process is complete and we can continue to main screen
export const mLoginComplete = createAction('[ManHouseKeepingager] ManagerLoginComplete');

export const mLogout = createAction('[HouseKeeping] Logout')