import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { AppState } from 'src/app/reducers';
import * as giftVoucherSelector from '../../reducers/gift-voucher/gift-voucher.selectors';
import { Payment, Transaction } from 'src/app/payment/payment.service';
import { ActivatedRoute, Router } from '@angular/router';
import { GiftVoucherCreationDto } from 'src/app/pos-server.generated';

@Component({
  selector: 'pos-gift-payment-table',
  templateUrl: './gift-payment-table.component.html',
  styleUrls: ['./gift-payment-table.component.scss']
})
export class GiftPaymentTableComponent implements OnInit {

  constructor(private store: Store<AppState>, private route: ActivatedRoute) { }

  payments$: Observable<Payment[]>;
  balances$: Observable<number[]>;
  voucher$: Observable<GiftVoucherCreationDto>;

  @Output() removePayment: EventEmitter<Payment> = new EventEmitter<Payment>();
  clickCount = 0; 


  ngOnInit() {
    this.payments$ = this.store.select(giftVoucherSelector.payments);
    this.balances$ = this.store.select(giftVoucherSelector.balances);
    this.balances$.subscribe(b => {
      console.log("Gift payment balances: ", b);
    })

    this.voucher$ = this.store.select(giftVoucherSelector.voucher);
   
  }

  paymentClick(payment: Payment){
    console.log("Payment ", payment, " was clicked.");
		++this.clickCount;
		setTimeout(()=>{
			if (this.clickCount >= 2) {
        console.log("Emitting!");
				this.removePayment.emit(payment);
			}
			this.clickCount = 0;
		}, 250);
  }

}
