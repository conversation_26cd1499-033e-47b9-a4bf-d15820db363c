import { Component, ElementRef, EventEmitter, OnInit, AfterViewInit, Output, ViewChild, HostListener } from '@angular/core';
import { FormControl } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CreateErrorModal } from '../error-modal/error-modal.component';
import { ItemBarcodeSearchRequestDto, StockItemDto, StockSearchClient } from '../pos-server.generated';
import { StockSearchModalComponent } from '../stock-search/stock-search-modal/stock-search-modal.component';
import { SundryModalComponent } from '../sales/sundry-modal/sundry-modal.component';

@Component({
  selector: 'pos-item-lookup',
  templateUrl: './item-lookup.component.html',
  styleUrls: ['./item-lookup.component.scss']
})
export class ItemLookupComponent implements OnInit, AfterViewInit {
  @Output() result: EventEmitter<StockItemDto> = new EventEmitter<StockItemDto>();

  @ViewChild('barcodeInput', { static: false }) barcodeInputChild: ElementRef;

  enterItemBarcode = new FormControl('');

  errorWithBarcode: boolean = false;

  constructor(private modalService: NgbModal, private stockSearchClient: StockSearchClient) { }

  ngOnInit() {
    this.subscribeToControls();
  }

  ngAfterViewInit() {
    // Focus the input after view initialization
    setTimeout(() => {
      this.barcodeInputChild.nativeElement.focus();
    });
  }

	// Listen for all keydown events on the document
	@HostListener('document:keydown', ['$event'])
	handleGlobalKeyDown(event: KeyboardEvent) {
		if (this.modalService.hasOpenModals()) {
			return;
		}
		if (this.barcodeInputChild && document.activeElement !== this.barcodeInputChild.nativeElement) {
			this.barcodeInputChild.nativeElement.focus();
		}
	}

  subscribeToControls() {
    // We no longer auto-send barcodes in item lookup
    // vvvvv

    // this.enterItemBarcode.valueChanges.subscribe(value => {
    //   // Handle full barcode length entries
    //   if (value.length == 13) this.submitBarcode(value);
    // });
  }

  submitBarcode(value: string) {
    // Check if the input contains any non-numeric characters
    if (/[^0-9]/.test(value)) {
      // Open search modal with the input value
      this.openSearch(value);
      return;
    }

    console.log("Submitting item: " + value);

    // Get item with barcode
    let query: Observable<StockItemDto> = this.performQuery(value);

    // Wait for query to complete
    query.subscribe((item: StockItemDto) => {
      // If the item is not null
      if (item) {
        console.log("Barcode scanner found item with barcode: " + value + " == " + item.barcode);
        this.result.emit(item);
      }
      // Otherwise, raise error
      else {
        console.log("Barcode was invalid.");
        this.errorWithBarcode = true;
        //CreateErrorModal(this.modalService, true, "Oops! "+ value+" Barcode was invalid.!")
      }

      // Clear the input
      this.enterItemBarcode.setValue("");
      // Refocus on input
      this.barcodeInputChild.nativeElement.focus();
    });
  }

  private performQuery(query: string): Observable<StockItemDto> {
    // Use HTTP client to send request
    return this.stockSearchClient.getItemByBarcode(
      { barcode: query } as ItemBarcodeSearchRequestDto
    );
  }

  openSearch(initialSearchValue?: string) {
    // If no value was passed, use the current input value
    if (!initialSearchValue) {
      const currentValue = this.enterItemBarcode.value;
      // Only use the current value if it's not empty and not a number
      if (currentValue && !/^\d+$/.test(currentValue)) {
        initialSearchValue = currentValue;
      }
    }

    // Open search modal
    const modalRef = this.modalService.open(StockSearchModalComponent, { size: 'xl', centered: true });
    modalRef.componentInstance.name = 'ProductSearch';

    // Pass the initial search value if provided
    if (initialSearchValue) {
      modalRef.componentInstance.initialSearchValue = initialSearchValue;
    }

    modalRef.result.then((result: StockItemDto) => {
      if (result) {
        this.result.emit(result);
      }
    }).catch((reason) => console.log(reason));

    // Clear the input
    this.enterItemBarcode.setValue("");
  }

  private openSundryModal() {
    const modalRef = this.modalService.open(SundryModalComponent, {
      centered: true
    });
    modalRef.componentInstance.name = 'SundryModal';
  }
}
