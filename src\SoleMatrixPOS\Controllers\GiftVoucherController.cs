using MediatR;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.GiftVoucher;
using SoleMatrixPOS.Application.GiftVoucher.Command;
using SoleMatrixPOS.Application.GiftVoucher.Queries;
using System.Threading.Tasks;
using System.Collections.Generic;
using SoleMatrixPOS.Application.Transaction;
using SoleMatrixPOS.Application.General;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class GiftVoucherController : ControllerBase
	{
		private readonly IMediator _mediator;

		public GiftVoucherController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[Route("SearchGiftVoucher")]
		[HttpPost]
		public async Task<GiftVoucherResultDto> SearchGiftVoucher([FromBody] string code, bool isCreditNote)
		{
			return await _mediator.Send(new GiftVoucherSearchQuery(code, isCreditNote));
		}

		[Route("ExistsGiftVoucher")]
		[HttpPost]
		public async Task<GiftVoucherResultDto> ExistsGiftVoucher([FromBody] string code, bool isCreditNote)
		{
			return await _mediator.Send(new GiftVoucherSearchQuery(code, isCreditNote, true));
		}

		//[Route("GetVoucherNo")]
		//[HttpPost]
		//public async Task<string> GetVoucherNo()
		//{
		//	return await _mediator.Send(new RequestGiftVoucherNoQuery());

		//}


		//[Route("addGiftCart")]
		//[HttpPost]
		//public async Task<IEnumerable<GiftCartItemDto>> AddGiftCart([FromBody] GiftVoucherDto giftVoucher)
		//{
		//	return await _mediator.Send(new AddGiftCartQuery(giftVoucher));
		//}


		[HttpPut]
		public async Task<ActionResult<GiftVoucherResultDto>> PurchaseGiftVoucher([FromBody] GiftVoucherPurchaseRequestDto request)
		{
			try
			{
				var voucher = await _mediator.Send(new PurchaseGiftVoucherCommand(request));
				return Ok(voucher);
			}

			catch (SolemateTransactionException e)
			{
				return BadRequest(new ErrorResponseDto(e.Message));
			}

		}

		//[HttpPut("giftPayment")] 
		//public async Task<IActionResult> SubmitGiftPayment([FromBody] GiftPaymentRequestDto makeGiftPayment)
		//{
		//	await _mediator.Send(new WriteGiftTranspayCommand(makeGiftPayment));
		//	return Ok();
		//}

		//work well with Visual studio 2022 version 17.53 and .net core 2.2.207

	}
}
