import { Component, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
	selector: 'pos-confirm-modal',
	templateUrl: './confirm-error-modal.component.html',
	styleUrls: ['./confirm-error-modal.component.scss']
})
export class ConfirmErrorModalComponent {
	@Input() message = 'Are you sure?';
	@Input() confirmText = 'Yes';
	@Input() cancelText = 'No';

	constructor(public activeModal: NgbActiveModal) { }

	onConfirm() {
		this.activeModal.close(true);
	}

	onCancel() {
		this.activeModal.dismiss(false);
	}
}
