<div class="container">
	<div class="container-fluid p-5">

		<div class="row align-items-center">
			<div class="col-1">
				<i class="fas fa-download fa-2x text-primary align-items-center"></i>
			</div>
			<div class="col-1">
				<h2 class="text-secondary align-items-center">Download</h2>
			</div>
			<div class="col-10">
				<button type="button" class="btn btn-circle float-right" (click)="close()">
					<i class="fas fa-times fa-2x"></i>
				</button>
			</div>
		</div>

		<hr style="height: 2px;">

		<div class="row p-4">
			<div class="col-12">
				<p class="lead">
					It seems that our local service is not installed on your machine. To enable direct integration and printing features, please download and install the service.
				</p>
				<p class="mt-4">
					<a href="https://smstaticfiles.blob.core.windows.net/pos-print/pos-print-v1.1.0.exe" target="_blank" class="btn btn-outline-primary btn-lg">
						<i class="fas fa-download mr-2"></i> Download Now
					</a>
				</p>
				<p class="text-muted mt-3">
					After installation, refresh this page or click the "Try Again" button below.
				</p>
			</div>
		</div>

		<div class="row justify-content-center p-4">
			<div class="col-12 text-center">
				<label class="form-check-inline">
					<input type="checkbox" class="form-check-input" [(ngModel)]="dontAskAgain" (change)="onDontAskAgainChange()">
					<span class="form-check-label">Don't ask again</span>
				</label>
			</div>
			<div class="col-12 text-center mt-3">
				<button class="btn btn-outline-success" type="button" (click)="tryAgain()">
					Try Again
				</button>
			</div>
		</div>

	</div>
</div>
