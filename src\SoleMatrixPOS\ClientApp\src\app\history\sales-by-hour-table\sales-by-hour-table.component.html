<div class="d-flex justify-content-between align-items-center">
    <h3 *ngIf="currentView === 'sales'">Sales By Hour</h3>
    <h3 *ngIf="currentView === 'refunds'">Refunds By Hour</h3>
    <button class="btn btn-link" (click)="toggleView()">
        <i class="fas fa-exchange-alt"></i>
    </button>
</div>
<div class="table-responsive">
    <div *ngIf="currentView === 'sales'">
        <table class="table table-striped ml-2 mr-2 table-hover">
            <thead>
                <tr>
                    <th scope="col-1">Hour</th>
                    <th scope="col-1">Sales</th>
                    <th scope="col-2">Items</th>
                    <th scope="col-1">Custs</th>
                    <th scope="col-1"> </th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let hour of salesByHour$|async; let i = index">
                    <td>
                        {{hour.hours}}
                    </td>
                    <td>
                        {{toFinancialString(hour.sales)}}
                    </td>
                    <td>
                        {{hour.items}}
                    </td>
                    <td>
                        {{hour.custs}}
                    </td>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div *ngIf="currentView === 'refunds'">
        <table class="table table-striped ml-2 mr-2 table-hover">
            <thead>
                <tr>
                    <th scope="col-1">Hour</th>
                    <th scope="col-1">Refunds</th>
                    <th scope="col-2">Items</th>
                    <th scope="col-1">Custs</th>
                    <th scope="col-1"> </th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let hour of refundsByHour$|async; let i = index">
                    <td>
                        {{hour.hours}}
                    </td>
                    <td>
                        {{toFinancialString(hour.sales)}}
                    </td>
                    <td>
                        {{hour.items}}
                    </td>
                    <td>
                        {{hour.custs}}
                    </td>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>