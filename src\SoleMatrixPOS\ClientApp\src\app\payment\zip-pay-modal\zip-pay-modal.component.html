<div class="container">
    <div class="container-fluid p-5">
        <div class="row align-items-center">
            <div class="col-1">
                <div class="col-2 align-items-center">
                    <img src="../../../assets/zip-logo.svg" style="width:25px;height:25px;">
                </div>
            </div>
            <div class="col-1">
                                <!-- TODO: name not defined... -->
                <!-- <h2 class="text-secondary align-items-center">{{this.name}}</h2> -->
            </div>
            <div class="col-10">
                <button type="button" class="btn btn-circle float-right" (click)="dismiss('Cross click')">
                    <i class="fas fa-times fa-2x"></i>
                </button>
            </div>
        </div>
        <hr style="height: 2px;">
        <form [formGroup]="form">
            <label for="amount" [translate]="'payment.modal.labels.TenderedAmount'">Tendered Amount $</label>
            <div class="form-group">
                <div class="input-group">
                    <input autofocus name="amount" id="amount" type="text"
                        class="form-control form-control-lg  ng-untouched ng-pristine ng-valid"
                        [ngClass]=" {'is-invalid': fieldValidate(form.get('Amount'))}" formControlName="Amount">

                    <div class="input-group-append">
                        <button id="remainder" type="submit" class="btn btn-outline-default" (click)="useRemainder()"
                            [translate]="'payment.modal.buttons.UseRemainder'">Use Remainder</button>
                    </div>
                    <div class="invalid-feedback" *ngIf="amount.invalid && (amount.dirty || amount.touched)">
                        <div *ngIf="amount.errors?.min" [translate]="'payment.modal.validation.Eftpos.AmountMin'">
                            Amount must be greater than or equal to 0.01
                        </div>
                        <div *ngIf="amount.errors?.required"
                            [translate]="'payment.modal.validation.Eftpos.AmountRequired'">
                            Amount required
                        </div>
                        <div *ngIf="amount.errors?.pattern"
                            [translate]="'payment.modal.validation.Eftpos.AmountPattern'">
                            Amount must be a number
                        </div>
                    </div>

                </div>
            </div>

            <div class="row justify-content-center p-4">
                <div><button class="btn btn-outline-default" type="button" (click)="apply()"
                        [translate]="'payment.modal.buttons.ApplyAmount'">
                        <i class="fas fa-lg fa-fw fa-check-circle text-success mr-2"></i>
                        Apply Amount</button>
                </div>
            </div>
        </form>

    </div>
</div>