
export class ReceiptBatch {
	private payload: any;
	private receiptActions: IReceiptAction[] = [];
	constructor(private printer: string = undefined){}

	static merge(...all: ReceiptBatch[]) {
		var collected: IReceiptAction[] = [];
		all.forEach(next => collected.push(...next.receiptActions), [])
		return new ReceiptBatch().add_all_actions(
			collected
		);
	}

    set_printer(printer: string) {
        this.printer = printer
    }

	add_action(action: IReceiptAction): ReceiptBatch {
		this.receiptActions.push(action);
		return this;
	}

	add_all_actions(actions: IReceiptAction[]): ReceiptBatch {
		this.receiptActions = actions;
		return this;
	}

	build(): object {
        if(!this.printer) throw new Error("No printer provided");
		let actions = this.receiptActions.map((v) => v.getPayload());
		return {
			printer: this.printer,
			actions
		}
	}
}

export class TextAction implements IReceiptAction {
	constructor(private text: string){}
	getPayload(): object {
		return {
			action: "text",
			text: this.text
		};
	}
}

export class BarcodeAction implements IReceiptAction {
	constructor(private barcode: string){}
	getPayload(): object {
		return {
			action: "barcode",
			barcode: this.barcode
		};
	}
}

export class ImageAction implements IReceiptAction {
	constructor(private png_b64: string){}
	getPayload(): object {
		return {
			action: "image",
			png_b64: this.png_b64
		};
	}
}

export class FeedAction implements IReceiptAction {
    constructor(private lines: number = 2){}
    getPayload(): object {
        return {
            action: "feed",
            lines: this.lines
        };
    }
}

export class CutAction implements IReceiptAction {
	constructor(private cut_type: CutType){}
	getPayload(): object {
		return {
			action: "cut",
			cut_type: this.cut_type
		};
	}
}

export enum CutType {
	Half = "half", Full = "full"
}

export class OpenCashDrawerAction implements IReceiptAction {
	constructor(){}
	getPayload(): object {
		return {
			action: "cash_drawer"
		};
	}
}

export interface IReceiptAction {
	getPayload(): object;
}
