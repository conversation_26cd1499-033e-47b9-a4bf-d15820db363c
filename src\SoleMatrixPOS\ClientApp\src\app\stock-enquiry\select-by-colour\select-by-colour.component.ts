import { Component, EventEmitter, Input, OnInit, Output, OnChanges, SimpleChanges } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { StockEnquiryGetAllColorDto, StockEnquiryRequestDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import * as StockEnquireSelector from '../../reducers/stock-enquiry/stock-enquiry.selectors';

@Component({
  selector: 'pos-select-by-colour',
  templateUrl: './select-by-colour.component.html',
  styleUrls: ['./select-by-colour.component.scss']
})
export class SelectByColourComponent implements OnInit, OnChanges {

  colors$: Observable<StockEnquiryGetAllColorDto[]>;
  currentColorIndex: number = 0;
  colors: StockEnquiryGetAllColorDto[] = [];
  isInitialized: boolean = false;
  lastSelectedColorCode: string = '';
  isAutoSelecting: boolean = false; // Flag to prevent auto-selection from triggering API calls

  @Output() selectedItemChanged = new EventEmitter<StockEnquiryRequestDto>();
  @Input() selectedColor: StockEnquiryGetAllColorDto;
  @Input() selectedColorCode: string; // Input for the color code to auto-select

  constructor(private store: Store<AppState>) { }

  ngOnInit() {
    this.colors$ = this.store.select(StockEnquireSelector.stockEnquiryColor);

    // Subscribe to colors to get the array for navigation
    this.colors$.subscribe(colors => {
      this.colors = colors || [];
      
      // Only auto-select on initial load or when colors change from empty to populated
      if (!this.isInitialized && this.colors.length > 0) {
        this.selectMatchingColor();
        this.isInitialized = true;
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    // Only handle selectedColorCode changes if it's a new value and we haven't processed it yet
    if (changes['selectedColorCode'] && 
        changes['selectedColorCode'].currentValue !== this.lastSelectedColorCode &&
        this.colors.length > 0) {
      
      this.lastSelectedColorCode = changes['selectedColorCode'].currentValue;
      this.selectMatchingColor();
    }
  }

  private selectMatchingColor() {
    if (this.colors.length > 0) {
      // If we have a selectedColorCode, try to find and select it
      if (this.selectedColorCode) {
        const matchingColorIndex = this.colors.findIndex(color => 
          color.colorCode === this.selectedColorCode
        );
        
        if (matchingColorIndex !== -1) {
          // Found the matching color, select it
          this.selectColorByIndex(matchingColorIndex, true); // true = auto-selection
          this.currentColorIndex = matchingColorIndex;
        } else {
          // No matching color found, default to first color
          this.selectColorByIndex(0, true); // true = auto-selection
        }
      } else {
        // No selectedColorCode provided, default to first color
        this.selectColorByIndex(0, true); // true = auto-selection
      }
    }
  }

  getCurrentColorName(): string {
    if (this.selectedColor) {
      return this.selectedColor.colorName;
    }
    return this.colors.length > 0 ? this.colors[0].colorName : 'No colors available';
  }

  nextColor() {
    if (this.colors.length > 1) {
      this.currentColorIndex = (this.currentColorIndex + 1) % this.colors.length;
      this.selectColorByIndex(this.currentColorIndex, false); // false = manual selection
    }
  }

  previousColor() {
    if (this.colors.length > 1) {
      this.currentColorIndex = this.currentColorIndex === 0
        ? this.colors.length - 1
        : this.currentColorIndex - 1;
      this.selectColorByIndex(this.currentColorIndex, false); // false = manual selection
    }
  }

  canGoNext(): boolean {
    return this.colors.length > 1;
  }

  canGoPrevious(): boolean {
    return this.colors.length > 1;
  }

  private selectColorByIndex(index: number, isAutoSelection: boolean = false) {
    if (index >= 0 && index < this.colors.length) {
      const color = this.colors[index];
      this.selectedColor = color;
      
      // Only emit event if it's not an auto-selection
      if (!isAutoSelection) {
        this.selectedItemChanged.emit(color);
      }
    }
  }

  selectItem(selectedValue: string) {
    // Parse the selectedValue to extract styleCode, colorCode, and colorName
    const [styleCode, colorCode, colorName] = selectedValue.split('|');

    // Create a new StockEnquiryRequestDto object with extracted properties
    const selectedItem: StockEnquiryGetAllColorDto = {
      styleCode,
      colorCode,
      colorName
    };

    // Update the current color index
    const selectedIndex = this.colors.findIndex(color => color.colorCode === colorCode);
    if (selectedIndex !== -1) {
      this.currentColorIndex = selectedIndex;
    }

    // Emit the selected item object to the parent component (this is always a manual selection)
    this.selectedItemChanged.emit(selectedItem);
  }

  getSelectedValue(): string {
    if (!this.selectedColor) return '';
    return `${this.selectedColor.styleCode}|${this.selectedColor.colorCode}|${this.selectedColor.colorName}`;
  }

  trackByColor(index: number, color: StockEnquiryGetAllColorDto): string {
    return color.colorCode; // Use a unique identifier for trackBy
  }
}
