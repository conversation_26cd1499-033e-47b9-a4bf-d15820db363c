using System;
using System.Globalization; // Important: Add this for CultureInfo
using MediatR;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using SoleMatrixPOS.Application.Infrastructure;
using SoleMatrixPOS.Application.Store.Queries;
using SoleMatrixPOS.Identity.Interface;

namespace SoleMatrixPOS.Filters
{
	/// <summary>
	/// look for a staff code specified in a request header and write to a context object if found
	/// </summary>
	public class CurrentTimeFilter : IAuthorizationFilter
	{
		public const string ClientTimeUTCHeaderKey = "ClientTime";
		public const string ClientTimezoneHeaderKey = "ClientTimezone";
		private readonly IMediator _mediator;
		public CurrentTimeFilter(IMediator mediator)
		{
			_mediator = mediator;
		}
		public void OnAuthorization(AuthorizationFilterContext context)
		{
			if (context.HttpContext.User.Identity.IsAuthenticated)
			{
				var timeUTCHeader = context.HttpContext.Request.Headers[ClientTimeUTCHeaderKey];
				var timezoneIdHeader = context.HttpContext.Request.Headers[ClientTimezoneHeaderKey];
				var storeTimeContext = context.HttpContext.RequestServices.GetService<StoreTimeContext>();

				if (!string.IsNullOrEmpty(timeUTCHeader) && !string.IsNullOrEmpty(timezoneIdHeader) && storeTimeContext != null)
				{
					string timeUTC = timeUTCHeader;
					string timezoneId = timezoneIdHeader;

					if (DateTimeOffset.TryParse(timeUTC, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTimeOffset parsedTimeOffset))
					{
						try
						{
							TimeZoneInfo clientTimeZone = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);
							DateTime localDateTime = TimeZoneInfo.ConvertTime(parsedTimeOffset.UtcDateTime, clientTimeZone);
							storeTimeContext.StoreLocalTime = localDateTime;
						}
						catch (TimeZoneNotFoundException)
						{
							Console.WriteLine($"Timezone '{timezoneId}' not found. Using server local time.");
							storeTimeContext.StoreLocalTime = DateTime.Now;
						}
						catch (Exception ex)
						{
							Console.WriteLine($"Error converting timezone: {ex.Message}. Using server local time.");
							storeTimeContext.StoreLocalTime = DateTime.Now;
						}
					}
					else
					{
						Console.WriteLine($"Invalid ClientTime format: {timeUTC}. Using server local time.");
						storeTimeContext.StoreLocalTime = DateTime.Now;
					}
				}
			}
		}
	}
}
