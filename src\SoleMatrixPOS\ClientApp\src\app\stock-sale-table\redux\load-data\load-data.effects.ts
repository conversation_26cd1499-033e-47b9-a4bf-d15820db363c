import { Injectable } from '@angular/core';
// Classes
import { LoadLocationsRequested, LoadLocationsAction } from '../location/location.action';
import { LoadImmutableLocationsRequested, LoadImmutableLocationsAction } from '../location/immutable-location.action';
import { LoadSizesRequested, LoadSizesAction } from '../size/size.action';
import { LoadDataService } from './load-data.service'

// Ngrx
import { map, mergeMap, catchError, tap } from 'rxjs/operators';
import { Actions, Effect, ofType } from '@ngrx/effects';
import { EMPTY } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LoadDataEffects {

  /**
   * This effect intercepts the Location load request 
   * and triggers the api call to get Location data
   */
  @Effect()
  loadLocationsRequested$ = this.actions$.pipe(
    ofType<LoadLocationsRequested>("[LOCATION] loadReq"),
    mergeMap(() => this.api.getLocations()
      .pipe(
        map(result => new LoadLocationsAction(result)),
        catchError(() => EMPTY)
      ))
  );

  /**
   * This effect intercepts the size load request 
   * and triggers the api call to get size data
   */
  @Effect()
  loadSizesRequested$ = this.actions$.pipe(
    ofType<LoadSizesRequested>("[SIZE] loadReq"),
    mergeMap(() => this.api.getSizes().pipe(
      map(result => new LoadSizesAction(result)),
      catchError(() => EMPTY)
    ))
  );

  /**
   * This effect intercepts the immutableLocation load request 
   * and triggers the api call to get immutableLocation data
   */
  @Effect()
  loadImmutableLocationsRequested$ = this.actions$.pipe(
    ofType<LoadImmutableLocationsRequested>("[IMMUTABLE_LOCATION] loadReq"),
    mergeMap(() => this.api.getImmutableLocations().pipe(
      map(result => new LoadImmutableLocationsAction(result)),
      catchError(() => EMPTY)
    ))
  );

  constructor(private actions$: Actions, private api: LoadDataService) {}
}