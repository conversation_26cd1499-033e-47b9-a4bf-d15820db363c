
import { createAction, props } from '@ngrx/store';
import { Transaction } from 'src/app/payment/payment.service';
import { CtransDto } from 'src/app/pos-server.generated';
import { CustomerMateDto } from 'src/app/pos-server.generated';
export const init = createAction('[Payment] Init');
export const commitTransaction = createAction('[Payment] Commit', props<{ payload: Transaction }>());
export const transactionResponse = createAction('[Payment] TransactionResponse');
export const updateTransaction = createAction('[Payment] Update Transaction', props<{ payload: Transaction }>());

export const selectCustomerAccount = createAction('[Payment] Add Customer Account', props<{ payload: CustomerMateDto }>());
export const addCtrans = createAction('[Payment] Add Ctrans', props<{ payload: CtransDto }>());
// update cTrans with transNo after transaction completed
export const updateCtrans = createAction('[Payment] Update Ctrans', props<{ payload: CtransDto[] }>());
export const commitCtrans = createAction('[Payment] Commit Ctrans', props<{ payload: CtransDto[] }>());
export const ctransResponse = createAction('[Payment] CtransReponse');