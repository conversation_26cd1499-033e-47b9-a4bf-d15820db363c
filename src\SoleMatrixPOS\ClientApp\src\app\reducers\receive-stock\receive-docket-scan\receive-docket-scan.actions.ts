import { createAction, props } from '@ngrx/store';
import { ValidateBarcodeRequestDto, ValidateBarcodeResponseDto, ValidateBarcodeResult } from '../../../pos-server.generated';
export const init = createAction("[Receive Docket Scan] Init")
export const validateBarcode = createAction("[Receive Docket Scan] Validate Barcode", props<{validation: ValidateBarcodeRequestDto}>());
export const retrieveBarcodeValidation = createAction("[Receive Docket Scan] Retrieve Barcode Validation", props<{response: ValidateBarcodeResponseDto}>());