import { createReducer, on } from '@ngrx/store';
import * as SaleNoteActions from './sale-note.actions';

export interface SaleNoteState {
    note: string;
}

export const initialState: SaleNoteState = {
    note: ''
};

export const saleNoteReducer = createReducer(
    initialState,
    on(SaleNoteActions.init, () => initialState),
    on(SaleNoteActions.setSaleNote, (state, { note }) => ({ ...state, note })),
    on(SaleNoteActions.clearSaleNote, (state) => ({ ...state, note: '' }))
); 