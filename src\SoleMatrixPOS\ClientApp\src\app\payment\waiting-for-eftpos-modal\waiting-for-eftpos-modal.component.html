<div class="modal-body text-center">
	<div class="spinner-border text-primary" role="status">
		<span class="sr-only">Processing...</span>
	</div>
	<h4>Waiting for EFTPOS Payment...</h4>
	<div *ngIf="integratedEftProvider !== 'Tyro'">
		<button id="Cancel" type="button" class="btn btn-outline-default mt-2" (click)="sendKey('0'); sendKey('2')">Cancel</button>
		<button id="Accept" type="button" class="btn btn-outline-default mt-2" (click)="sendKey('1')">Accept Signature</button>
	</div>
	<div *ngIf="integratedEftProvider == 'Tyro'">
		<div *ngIf="statusMessage" class="status-message">
			<p>{{ statusMessage }}</p>
		</div>
		<button id="Cancel" type="button" class="btn btn-outline-default mt-2" (click)="cancelTyroTransaction()">Cancel</button>
	</div>
	<div *ngIf="showQuestionButtons" class="question-panel">
		<p>{{ questionText }}</p>
		<div>
			<button *ngFor="let option of questionOptions" (click)="answerQuestion(option)" class="btn btn-outline-default mt-2">
				{{ option }}
			</button>
		</div>
	</div>
</div>
