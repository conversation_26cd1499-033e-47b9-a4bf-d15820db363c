<pos-nav-header pageName="Account - Payment"></pos-nav-header>
<div class="container-fluid min-vh-100 d-flex flex-column">
  <div class="row align-items-start">
    <!-- Left Column: Payment Details -->
    <div class="col-md-6">
      <!-- Account Info Card -->
      <div class="card mb-2 shadow-sm">
        <div class="card-body">
          <table class="table table-borderless mb-0">
            <thead>
              <tr>
                <th>Account</th>
                <th>Name</th>
                <th class="text-center">Payment Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>{{ (selectedCustomer$ | async).customer.customerName }}</td>
                <td>{{ (selectedCustomer$ | async).customer.contactName }}</td>
                <td class="text-center">{{ toFinancialString((selectedCustomer$ | async).amount) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Balances Card -->
      <div class="card mb-2 shadow-sm">
        <div class="card-body">
          <table class="table table-borderless mb-0">
            <thead>
              <tr>
                <th class="text-center">30 Day Balance</th>
                <th class="text-center">60 Day Balance</th>
                <th class="text-center">90 Day Balance</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="text-center">{{ toFinancialString((selectedCustomer$ | async).customer.thirtyDayBalance) }}
                </td>
                <td class="text-center">{{ toFinancialString((selectedCustomer$ | async).customer.sixtyDayBalance) }}
                </td>
                <td class="text-center">{{ toFinancialString((selectedCustomer$ | async).customer.ninetyDayBalance) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Totals Card -->
      <div class="card shadow-sm">
        <div class="card-body">
          <table class="table table-borderless mb-0">
            <tfoot>
              <tr>
                <td>Previous Amount Owed</td>
                <td class="text-right">
                  <h1 class="text-warning mb-0">{{ toFinancialString((sumBalances((selectedCustomer$ | async).customer))
                    )}}</h1>
                </td>
              </tr>
              <tr>
                <td>Amount Due</td>
                <td class="text-right">
                  <h1 class="text-danger mb-0">{{ toFinancialString((transaction$ | async).amountDue) }}</h1>
                </td>
              </tr>
              <tr>
                <td>Amount Tendered</td>
                <td class="text-right">
                  <h1 class="text-success mb-0">{{ toFinancialString((transaction$ | async).amountTendered) }}</h1>
                </td>
              </tr>
              <tr>
                <td>New Balance</td>
                <td class="text-right">
                  <h1 class="mb-0">{{ toFinancialString(updatedBalance((transaction$ | async).total, (selectedCustomer$
                    | async).customer)) }}</h1>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>

    <!-- Right Column: Payment Action -->
    <div class="col-md-6 d-flex flex-column justify-content-start align-items-center">
      <!-- Payment Modal Button Group -->
      <div class="mt-2">
        <pos-payment-modal-button-group [transType]="ACCOUNTPAYMENT_TRANSTYPE"
          [amountDue]="(transaction$ | async).amountDue" (paymentResult)="handlePayment($event)"
          [modalButtons]="modalButtons">
        </pos-payment-modal-button-group>
      </div>
      <!-- Process Payment Button Styled Like Sales Footer -->
      <div class="mt-2 next-button">
        <button type="button" class="btn-hidden" (click)="process()">
          <div class="btn btn-lg btn-circle btn-default">
            <i class="fas fa-caret-right fa-4x color-gradient-green ml-2"></i>
          </div>
          <h4 class="text-light">Process Payment</h4>
        </button>
      </div>
    </div>
  </div>
</div>