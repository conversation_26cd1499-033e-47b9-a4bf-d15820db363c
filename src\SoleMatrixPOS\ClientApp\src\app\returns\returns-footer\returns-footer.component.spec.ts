import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ReturnsFooterComponent } from './returns-footer.component';

describe('ReturnsFooterComponent', () => {
  let component: ReturnsFooterComponent;
  let fixture: ComponentFixture<ReturnsFooterComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ReturnsFooterComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ReturnsFooterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
