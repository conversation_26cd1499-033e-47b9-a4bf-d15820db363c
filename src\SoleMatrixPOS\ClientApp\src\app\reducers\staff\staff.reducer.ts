import { createReducer, on, Action} from '@ngrx/store';
import { StaffLoginDto, StaffLoginResult, TimecardDto, ClockInResult } from 'src/app/pos-server.generated';
import * as staffActions from './staff.actions';

export enum StaffLoginState {
	// staff member is logged out
	LoggedOut = 1,
	// staff member needs to clock in
	ClockIn = 2,
	// staff member needs to complete Daily Float
	DailyFloat = 3,
	// Staff memer is fully logged in and can proceed to the main screenstaffLoginState
	Complete = 4
}

export class StaffState {
	staffLoginState: StaffLoginState;
	staffDto: StaffLoginDto;
	isLoading: boolean;
	loginMessage: string;
	timecards: TimecardDto[]; // today's timecard history to display when clocking in
}

export const initialState: StaffState = {
	staffLoginState: StaffLoginState.LoggedOut,
	staffDto: null,
	isLoading: false,
	loginMessage: '',
	timecards: []

} as StaffState;

export const staffReducer = createReducer(initialState,
	on(staffActions.clearStaffLogin, state => initialState),
	on(staffActions.staffLogin, state => ({ ...initialState, isLoading: true })),

	// state changes from processing staffLogin response
	on(staffActions.staffLoginResponse, (state, action) => {
		// staff login is complete
		if (action.payload.resultCode === StaffLoginResult.Complete) {
			return {
				...state,
				isLoading: false,
				staffDto: action.payload.staffLogin,
				staffLoginState: StaffLoginState.Complete
			};
			// staff login failed
		} else if (action.payload.resultCode === StaffLoginResult.CodeNotFound) {
			return { ...state, isLoading: false, loginMessage: 'Login Failed' };
		} else if (action.payload.resultCode === StaffLoginResult.ClockInRequired) { // clock in required
			return {
				...state, isLoading: false,
				staffDto: action.payload.staffLogin,
				timecards: action.payload.staffClockedInState.todaysTimecards,
				staffLoginState: StaffLoginState.ClockIn
			};
		} else if (action.payload.resultCode === StaffLoginResult.EnterFloatRequired) {// float entering required
			return {
				...state, isLoading: false,
				staffDto: action.payload.staffLogin,
				timecards: action.payload.staffClockedInState.todaysTimecards,
				staffLoginState: StaffLoginState.DailyFloat
			};
		}
	}),

	// process clock-in response
	on(staffActions.clockInResponse, (state, action) => {
		console.log(action.payload.resultCode)
		let nextStaffState = StaffLoginState.LoggedOut;
		if (action.payload.resultCode === ClockInResult.Complete) {
			nextStaffState = StaffLoginState.Complete;
		} else if (action.payload.resultCode === ClockInResult.DailyFloatRequired) {
			nextStaffState = StaffLoginState.DailyFloat;
		}
		return {
			...state,
			staffLoginState: nextStaffState
		};
	}),

	// process daily float entered - mark as complete
	on(staffActions.dailyFloatEnterResponse, (state, action) => {
		const resultObj = {
			...state,
			staffLoginState: StaffLoginState.Complete
		};
		return resultObj;
	}),

);

export function reducer(state: StaffState | undefined, action: Action) {
	return staffReducer(state, action);
}
