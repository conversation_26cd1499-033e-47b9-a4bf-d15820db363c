import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {StockItemDto, StockSearchRequestDto} from '../../pos-server.generated';

@Component({
  selector: 'pos-stock-enquiry-search-table',
  templateUrl: './stock-enquiry-search-table.component.html',
  styleUrls: ['./stock-enquiry-search-table.component.scss']
})
export class StockEnquirySearchTableComponent implements OnInit {

	@Input() items: StockItemDto[];
	@Input() options: StockSearchRequestDto;
	@Input() isLoading: boolean;
	@Output() selectedItemChanged = new EventEmitter<StockItemDto>();
	@Output() scrolledDown = new EventEmitter<void>();

	throttle = 500;
	scrollDistance = 1;

	constructor() {
	}

	ngOnInit() {
	}

	selectItem(item: StockItemDto) {
		this.selectedItemChanged.emit(item);
	}

	onScrollDown() {
		console.log('scrolled down!!');
		this.scrolledDown.emit();
	}
}
