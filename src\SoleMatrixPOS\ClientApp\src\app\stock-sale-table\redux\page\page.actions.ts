import { Action } from "@ngrx/store"

export const NEXT_PAGE_REQUESTED: string = "[PAGE] nextReq"
export const PREVIOUS_PAGE_REQUESTED: string = "[PAGE] previousReq"

export const SET_PAGE: string = "[PAGE] set"

export const NEXT_PAGE: string = "[PAGE] next"
export const PREVIOUS_PAGE: string = "[PAGE] previous"

export class NextPageRequested implements Action {
    readonly type = NEXT_PAGE_REQUESTED;
    constructor() { }
}

export class PreviousPageRequested implements Action {
    readonly type = PREVIOUS_PAGE_REQUESTED;
    constructor() { }
}

export class NextPageAction implements Action {
    readonly type = NEXT_PAGE;
    constructor(public max: number) { }
}


export class PreviousPageAction implements Action {
    readonly type = PREVIOUS_PAGE;
    constructor(public max: number) { }
}

export class SetPageAction implements Action {
    readonly type = SET_PAGE;
    constructor(public page: number) { }
}

export type PageActionType =
    NextPageRequested |
    PreviousPageRequested |
    NextPageAction |
    PreviousPageAction |
    SetPageAction;