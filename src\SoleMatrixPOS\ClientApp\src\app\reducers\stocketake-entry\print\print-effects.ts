import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { map, mergeMap } from "rxjs/operators";
import { StocktakeEntryClient } from "src/app/pos-server.generated";
import * as PrintAction from '../print/print-actions'

@Injectable()
export class PrintInvalidEffects {
    constructor(
        private actions$: Actions,
        private stocktakeClient: StocktakeEntryClient
    ) { }

    printInvalid$ = createEffect(() => this.actions$.pipe(
        ofType(PrintAction.printInvalidBarcode),
        mergeMap(
            (action) => this.stocktakeClient.printInvalidCode(action.payload)
                .pipe(
                    map(
                        (response) => {
                            // successfully printed
                            return PrintAction.printInvalidConfirmed()
                        }
                    )
                )
        )
    ));

}

