import { createReducer, on, Action, State } from '@ngrx/store';
import * as paymentActions from './payment.actions';
import { Transaction } from 'src/app/payment/payment.service';
import { CtransDto, CustomerMateDto } from 'src/app/pos-server.generated';

export class PaymentState {
    transaction: Transaction; // need to make this some kind of map to allow transaction caching
    committing: boolean;
    ctrans:CtransDto[];
    selectedCustomerAccount:CustomerMateDto
}

export const initialState: PaymentState = {
    transaction: {},
    committing: false,
    ctrans:[],
    selectedCustomerAccount:null
} as PaymentState;

export const reducer = createReducer(initialState,
    on(paymentActions.init, state => initialState),
    on(paymentActions.commitTransaction,
        (state, action) => {
            return { ...initialState, transaction: action.payload }
        }
    ),
    on(paymentActions.selectCustomerAccount,
        (state, action) => {
            return { ...initialState, selectedCustomerAccount: action.payload }
        }
    ),
    on(paymentActions.addCtrans,
        (state, action) => {
            return {...state,ctrans:[...state.ctrans,action.payload]}
        }
    ),
    // update cTrans with transNo after transaction completed
    on(paymentActions.updateCtrans,
        (state, action) => {
            return  {...state,ctrans:action.payload  }
        }
    )
);

