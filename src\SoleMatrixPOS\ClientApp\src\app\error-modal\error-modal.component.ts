import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmErrorModalComponent } from './confirm-error-modal.component';

export function CreateErrorModal(
	modalService: NgbModal,
	isCritical = false,
	description: string,
	showConfirm = false,
	confirmButtonText = ''
) {
	const ref = modalService.open(ErrorModalComponent, { centered: true });
	ref.componentInstance.description = description;
	ref.componentInstance.isCritical = isCritical;
	ref.componentInstance.showConfirm = showConfirm;
	ref.componentInstance.confirmButtonText = confirmButtonText;
	return ref;
}

export function openConfirmModal(
	modalService: NgbModal,
	message = 'Are you sure?',
	confirmText = 'Yes',
	cancelText = 'No'
) {
	const ref = modalService.open(ConfirmErrorModalComponent, { centered: true });
	ref.componentInstance.message = message;
	ref.componentInstance.confirmText = confirmText;
	ref.componentInstance.cancelText = cancelText;
	return ref;
}

@Component({
  selector: 'pos-error-modal',
  templateUrl: './error-modal.component.html',
  styleUrls: ['./error-modal.component.scss']
})
export class ErrorModalComponent {
	@Input() description: string;
	@Input() isCritical = false;
	@Input() showConfirm = false;
	@Input() confirmButtonText = 'Confirm';

	constructor(
		public activeModal: NgbActiveModal,
		private modalService: NgbModal
	) { }

	onOk() {
		this.activeModal.close(false);
	}

	onConfirmClick() {
		const ref = this.modalService.open(ConfirmErrorModalComponent, { centered: true });
		ref.componentInstance.message = 'Are you sure the PINPad accepted the transaction?';
		ref.componentInstance.confirmText = 'Yes, accept';
		ref.componentInstance.cancelText = 'No, cancel';

		ref.result
			.then((confirmed: boolean) => {
				if (confirmed) {
					this.activeModal.close(true);
				}
			})
			.catch(() => {
				// dismissed → stay open
			});
	}
}
