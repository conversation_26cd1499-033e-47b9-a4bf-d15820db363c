.selectedMember {
    background-color: rgb(155, 255, 175) !important;
}

.table {
    table-layout: fixed; // Fixed table layout to ensure proper column sizing
    word-wrap: break-word; // Break words to prevent overflow

    th,
    td {
        overflow-wrap: break-word; // Modern property for word breaking
        word-wrap: break-word; // Legacy for IE support
        -ms-word-break: break-all; // For IE
        word-break: break-word; // Modern browsers
        overflow: visible; // Ensure overflow is visible
        white-space: normal; // Allow text to wrap
        vertical-align: middle; // Center content vertically
        padding: 8px 4px; // Reduce horizontal padding to fit more content
    }

    // Phone
    th:nth-child(1),
    td:nth-child(1) {
        width: 11%;
    }

    // Title
    th:nth-child(2),
    td:nth-child(2) {
        width: 7%;
    }

    // First name
    th:nth-child(3),
    td:nth-child(3) {
        width: 12%;
    }

    // Surname
    th:nth-child(4),
    td:nth-child(4) {
        width: 15%;
    }

    // Email
    th:nth-child(5),
    td:nth-child(5) {
        width: 29%;
    }

    // Suburb
    th:nth-child(6),
    td:nth-child(6) {
        width: 12%;
    }

    // State
    th:nth-child(7),
    td:nth-child(7) {
        width: 4%;
    }

    // PCode
    th:nth-child(8),
    td:nth-child(8) {
        width: 6%;
    }

    // Actions
    th:nth-child(9),
    td:nth-child(9) {
        width: 4%;
    }
}

// Highlight wrapped text properly
::ng-deep .search-highlight {
    background-color: yellow;
    display: inline;
}

// Better table responsiveness
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.small-text {
    font-size: 0.85rem;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.small-text:hover {
    overflow: visible;
    white-space: normal;
    word-break: break-word;
    position: relative;
}

th {
    white-space: nowrap;
    font-size: 0.7rem;
}

th[scope="col-1"],
th[scope="col-2"] {
    font-size: 0.8rem;
}