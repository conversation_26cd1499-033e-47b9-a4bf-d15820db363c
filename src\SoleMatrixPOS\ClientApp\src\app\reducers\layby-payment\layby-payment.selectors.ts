import { AppState } from '../index';
import { createSelector } from '@ngrx/store';
import { LaybyPaymentState } from './layby-payment.reducer';
import { MakeLaybyPaymentResultDto } from 'src/app/pos-server.generated';

export const select = (state: AppState) => state.laybyPayment;
export const isLoading = createSelector(select, (s) => s.isLoading);
export const selectedLayby = createSelector(select, (s) => s.layby);
export const items = createSelector(select, (s) => s.items);
export const completedPayments = createSelector(select, (s) => s.completedPayments);
export const pendingPayments = createSelector(select, (s) => s.pendingPayments);
export const balanceDue = createSelector(select, (s) => s.layby.balanceDue);

export const selectLastPaymentResult = createSelector(
    select,
    (s): MakeLaybyPaymentResultDto | null => s.lastPaymentResult
);

export const balances = createSelector(select, (s) => s.balances);
export const completed = createSelector(select, (s) => s.completed);
export const isLaybyOrderInProgress = createSelector(
    select,
    (state: LaybyPaymentState) => state.laybyOrderInProgress
);