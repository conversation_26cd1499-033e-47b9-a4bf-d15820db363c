import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { EftposService } from '../../../eftpos/eftpos.service';
import { AppState } from '../../../reducers';
import { TyroPINPairingModalComponent } from '../tyro-pinpairing-modal/tyro-pinpairing-modal.component';
import { GetSystemStatusDto, SysControlUpdateDto } from '../../../pos-server.generated';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import * as SysConfigActions from '../../../reducers/sys-config/sys-config.actions';
import * as SysConfigSelectors from '../../../reducers/sys-config/sys-config.selectors';

@Component({
	selector: 'pos-tyro-settings-modal',
	templateUrl: './tyro-settings-modal.component.html',
	styleUrls: ['./tyro-settings-modal.component.scss']
})
export class TyroSettingsModalComponent implements OnInit {

	constructor(
		public activeModal: NgbActiveModal,
		private fb: FormBuilder,
		private store: Store<AppState>,
		private eftposService: EftposService,
		private modalService: NgbModal
	) { }


	// Example config object
	tyroConfig$: Observable<GetSystemStatusDto>;


	logs: string | null = null;
	sysStatus: GetSystemStatusDto;
	integratedReceipt: string = 'F';
	integratedSurcharge: string = 'F';

	ngOnInit(): void {
		this.store.dispatch(SysConfigActions.readSysConfig());

		// 2. Select the config from your store
		this.tyroConfig$ = this.store.select(SysConfigSelectors.selectSysConfig).pipe(
			tap(config => {
				if (config) {
					// Map store this.sysStatus into local component properties
					this.sysStatus = config;
					this.integratedReceipt = config.integrated_Eft_Receipt;
					this.integratedSurcharge = config.integrated_Eft_Surcharge;
				}
			})
		);
	}

	/**
	 * Opens the separate Pairing Modal.
	 */
	openPairing(): void {
		// Open the TyroPINPairingModalComponent
		this.modalService.open(TyroPINPairingModalComponent, {
			size: 'lg',
			backdrop: 'static'
		});
	}

	/**
	 * Save the Tyro config toggles.
	 */
	saveTyroConfig(formValue: any): void {
		// Build your update DTO
		const sysControlUpdateDto: SysControlUpdateDto = {
			client_Database: this.sysStatus.clientDatabase,
			demo_Q1: this.sysStatus.demoQ1,
			demo_Q2: this.sysStatus.demoQ2,
			demo_Q3: this.sysStatus.demoQ3,
			demo_Q4: this.sysStatus.demoQ4,
			suspend_Sale: this.sysStatus.suspendSale,
			suspend_Rtn: this.sysStatus.suspendRTN,
			suspend_Tfr: this.sysStatus.suspendTFR,
			suspend_Laypay: this.sysStatus.suspendLayPay,
			tender_Centrev: this.sysStatus.tenderCentrev,
			points_Per_Dollar: this.sysStatus.pointPerDollar,
			non_Coloursize: this.sysStatus.nonColorSize,
			customer_Accounts: this.sysStatus.customerAccount,
			auto_Disc: this.sysStatus.automaticDiscount,
			integrated_Eft_Provider: this.sysStatus.integratedEFTProvider,
			order_Deposit: this.sysStatus.orderDeposit,
			onHold_Deposit: this.sysStatus.onHoldDeposit,
			layby_Deposit: this.sysStatus.laybyDeposit,
			always_Open_Cash_Drawer: this.sysStatus.alwaysOpenCashDrawer,
			customer_Order_No_Stock_Movement: this.sysStatus.customerOrderNoStockMovement,
			open_Layby: this.sysStatus.openLayby,
			point_On_All_Sales: this.sysStatus.pointOnAllSales,
			price_Prompt: this.sysStatus.pricePrompt,
			no_Rounding: this.sysStatus.noRounding,
			layby_Number_Of_Weeks: this.sysStatus.laybyNumberOfWeeks,
			block_Exchange_Button: this.sysStatus.blockExchangeButton,
			enter_Order_No_And_No_Deposit: this.sysStatus.enterOrderNoAndNoDeposit,
			header_Image: this.sysStatus.headerImage,
			footer_Image: this.sysStatus.footerImage,
			consolidate_C_Tran: this.sysStatus.consolidateCTran,
			customer_Name_On_Receipt: this.sysStatus.customerNameOnReceipt,
			customer_Account_Print_On_A4: this.sysStatus.customerAccountPrintOnA4,
			demographic_Question_Ask: this.sysStatus.demographicQuestionAsk,
			customer_Club_Mandatory: this.sysStatus.customerClubMandatory,
			customer_Club_Birthday: this.sysStatus.customerClubBirthday,
			basic_Cash_Up: this.sysStatus.basicCashUp,
			soft_Credit_Limit: this.sysStatus.softCreditLimit,
			dollar_Per_Points: this.sysStatus.dollarPerPoints,
			integrated_Eft_Receipt: formValue.integrated_Eft_Receipt,
			integrated_Eft_Surcharge: formValue.integrated_Eft_Surcharge
		};
		// Dispatch the action to save
		this.store.dispatch(SysConfigActions.updateSysConfig({ updateSys: sysControlUpdateDto }));
	}

	/**
	 * Request iClient logs from your backend or local service.
	 */
	requestIClientLogs(): void {
		// TODO
		var x = document.getElementById("iClientFrame") as HTMLIFrameElement
		x.src
			= "https://iclientsimulator.test.tyro.com/logs.html";

		if (x.style.display === "none") {
			x.style.display = "block";
		} else {
			x.style.display = "none";
		}
	}
}

