import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { StockSaleTableFooterComponent } from './stock-sale-table-footer/stock-sale-table-footer.component';
import { StockSaleTableContainerComponent } from './stock-sale-table-container/stock-sale-table-container.component';
import { StockSaleTableComponent } from './stock-sale-table/stock-sale-table.component';
import { PipesModule } from './pipes/pipes.module';
import { RouterModule } from '@angular/router';
import { TotalAmountComponent } from './total-amount/total-amount.component';
import { StockSaleTableModalComponent } from '../stock-sale-table/stock-sale-table-modal/stock-sale-table-modal.component';
import { TransferModule } from '../transfer-table/transfer.module';
import { TransferTableComponent } from '../transfer-table/transfer-table/transfer-table.component';
import { TransferComponent } from '../transfer-table/transfer.component';
import { Store, StoreModule } from '@ngrx/store';
import { ApiResponseFactory } from './factories/api-response-factory';
import { MysqlApiService } from './services/mysql-api.service';
import { LoadDataModule } from './redux/load-data/load-data.module';
import { EffectsModule } from '@ngrx/effects';
import { rootReducer } from './redux/app.store';
import { LocationEffects } from './redux/location/location.effects';
import { LoadDataEffects } from './redux/load-data/load-data.effects';

@NgModule({
  declarations: [
    StockSaleTableFooterComponent, 
    StockSaleTableModalComponent,
    StockSaleTableContainerComponent,
    StockSaleTableComponent,
    TotalAmountComponent    
  ],
  imports: [
    CommonModule,
    RouterModule,
    PipesModule,
    TransferModule,
    LoadDataModule,
    StoreModule.forFeature("stockSaleTable", rootReducer),
    EffectsModule.forFeature([LoadDataEffects, LocationEffects]),
  ],
  providers: [MysqlApiService, Store, ApiResponseFactory],
  entryComponents: [StockSaleTableModalComponent]

})
export class StockSaleTableModule { }
