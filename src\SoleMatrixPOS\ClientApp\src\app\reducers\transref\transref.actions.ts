import { createAction, props } from '@ngrx/store';
import { TransrefDto } from 'src/app/pos-server.generated';

export const init = createAction("[TransRef] Init");

export const submitTransref = createAction(
  "[TransRef] Submit ETransRef",
  props<{ payload: TransrefDto }>()
);

export const transRefConfirmation = createAction(
  "[TransRef] TransRef Confirmed",
  props<{ message: string }>()
);

export const transRefError = createAction(
  "[TransRef] TransRef Submission Failed",
  props<{ error: any }>()
);
