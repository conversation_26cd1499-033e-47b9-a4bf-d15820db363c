import { Action, createReducer, on } from '@ngrx/store';
import * as customerClubSearchActions from './customer-club.actions';

import { CustomerClubDto } from '../../../pos-server.generated';
import { ClientSearchRequestDto } from '../../../pos-server.generated';
import { CustomerSuburbDto } from '../../../pos-server.generated';


export class CustomerClubSearchState {
    isLoading: boolean;
    members: CustomerClubDto[];
    options: ClientSearchRequestDto;
    selected: CustomerClubDto;
    suburbs: CustomerSuburbDto[];
    searchTerm: string;
    searchField: string;
}

export const initialState: CustomerClubSearchState = {
    isLoading: false,
    members: [],
    options: { Term: '', Field: '' },
    selected: null,
    suburbs: [],
    searchTerm: '',
    searchField: 'Phone'
} as CustomerClubSearchState;

export const customerClubSearchReducer = createReducer(initialState,
    on(customerClubSearchActions.init, () => ({ ...initialState })),
    on(customerClubSearchActions.search, (state, action) => ({
        ...state,
        isLoading: true,
        options: action.searchParams,
        members: action.searchParams.skip === 0 ? [] : state.members
    })),
    on(customerClubSearchActions.searchResponse, (state, action) => ({
        ...state,
        isLoading: false,
        members: [...state.members, ...action.payload]
    })),
    on(customerClubSearchActions.selectCustomerClubMember, (state, action) => ({ ...state, selected: action.payload })),
    // Add handler for updating points
    on(customerClubSearchActions.updateCustomerPoints, (state, action) => {
        // Update selected customer if it's the one being modified
        const updatedSelected = state.selected && state.selected.clientCode === action.customerId
            ? { ...state.selected, clientPoints: action.points }
            : state.selected;
        // Update the customer in the members array if present
        const updatedMembers = state.members.map(member =>
            member.clientCode === action.customerId
                ? { ...member, clientPoints: action.points }
                : member
        );
        return {
            ...state,
            selected: updatedSelected,
            members: updatedMembers
        };
    }),
    on(customerClubSearchActions.searchSuburbs, (state) => ({
        ...state,
        isLoading: true
    })),
    on(customerClubSearchActions.searchSuburbsResponse, (state, action) => ({
        ...state,
        isLoading: false,
        suburbs: action.suburbs
    })),
    on(customerClubSearchActions.selectSuburb, (state) => ({
        ...state,
        suburbs: [] // Clear suburbs after selection
    })),
    on(customerClubSearchActions.clearSuburbSearch, (state) => ({ // Handle the clear action
        ...state,
        suburbs: []
      })),
    on(customerClubSearchActions.setSearchCriteria, (state, { term, field }) => ({
        ...state,
        searchTerm: term,
        searchField: field
    }))
);

export function reducer(state: CustomerClubSearchState | undefined, action: Action) {
    return customerClubSearchReducer(state, action);
}