import { createReducer, on } from '@ngrx/store';
import * as SysConfigActions from './sys-config.actions';
import { GetSystemStatusDto } from 'src/app/pos-server.generated';


export enum UpdateConfigStatus {
	Idle = 'idle',
	InProgress = 'in_progress',
	Success = 'success',
	Error = 'error',
}
export interface SysConfigState {
	sysConfig: GetSystemStatusDto | null;
	loading: boolean;
	error: any;
	updateStatus: UpdateConfigStatus;
}

export const initialState: SysConfigState = {
	sysConfig: null,
	loading: false,
	error: null,
	updateStatus: UpdateConfigStatus.Idle,
};

export const sysConfigReducer = createReducer(
	initialState,
	// Read Config Reducers
	on(SysConfigActions.readSysConfig, state => ({
		...state,
		loading: true,
		error: null,
	})),
	on(SysConfigActions.readSysConfigSuccess, (state, { sysConfig }) => ({
		...state,
		sysConfig,
		loading: false,
	})),
	on(SysConfigActions.readSysConfigFailure, (state, { error }) => ({
		...state,
		loading: false,
		error,
	})),
	// Update Config Reducers
	on(SysConfigActions.updateSysConfig, state => ({
		...state,
		updateStatus: UpdateConfigStatus.InProgress,
	})),
	on(SysConfigActions.updateSysConfigSuccess, state => ({
		...state,
		updateStatus: UpdateConfigStatus.Success,
	})),
	on(SysConfigActions.updateSysConfigFailure, (state, { error }) => ({
		...state,
		updateStatus: UpdateConfigStatus.Error,
		error,
	})),
	on(SysConfigActions.resetUpdateStatus, state => ({
		...state,
		updateStatus: UpdateConfigStatus.Idle,
	})),
);
