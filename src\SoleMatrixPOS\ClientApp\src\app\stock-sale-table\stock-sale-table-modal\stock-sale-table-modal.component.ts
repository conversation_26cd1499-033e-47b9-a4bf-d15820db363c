import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import {StyleItem} from '../../reducers/stock-search/styleItem';
import { Observable, of } from 'rxjs';

@Component({
  selector: 'stock-sale-table-modal',
  templateUrl: './stock-sale-table-modal.component.html',
  styleUrls: ['./stock-sale-table-modal.component.scss']
})
export class StockSaleTableModalComponent implements OnInit {

	@Input() type

	STOCK_SALE: number = 1;
	TRANSFER: number = 2;

	page: number;

	constructor(public activeModal: NgbActiveModal) {
		this.page = 1;
	}

	ngOnInit() {
	}

	switchPage(newPage: number) {
		this.page = newPage;
	}

	selectItem(item: StyleItem) {
		this.activeModal.close(item);
	}

	dismiss(reason: string) {
		this.activeModal.dismiss(reason);
	}
}
