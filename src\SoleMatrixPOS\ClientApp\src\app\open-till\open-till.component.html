<div class="modal-header">
    <h4 class="modal-title">Open Till</h4>
    <button type="button" class="close" aria-label="Close" (click)="dismiss()">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<div class="modal-body">
    <label for="predefined-reasons">Select a reason:</label>
    <select id="predefined-reasons" class="form-control" (change)="selectPredefinedReason($event.target.value)">
        <option value="">-- Select a reason --</option>
        <option *ngFor="let reason of predefinedReasons" [value]="reason">{{ reason }}</option>
    </select>

    <br />

    <label for="reason-input">Or enter your own (5-30 characters):</label>
    <input id="reason-input" type="text" class="form-control" [formControl]="txtReason"
        [ngClass]="{'is-invalid': inputInvalid || txtReason.errors?.maxLengthExceeded || txtReason.errors?.minLengthNotMet}"
        placeholder="Enter reason" maxlength="30" />
    <div *ngIf="inputInvalid && (!txtReason.value?.trim() || txtReason.value.trim().length < 5)"
        class="invalid-feedback">
        Reason must be at least 5 characters long.
    </div>
    <div *ngIf="txtReason.errors?.maxLengthExceeded" class="invalid-feedback">
        Reason must be 30 characters or less.
    </div>
    <small class="form-text text-muted">Characters: {{ (txtReason.value || '').length }}/{{ maxLength }}</small>

    <div *ngIf="pettyCash">
        <br />
        <label>Enter Cash Amount</label>
        <input class="form-control" [formControl]="openTillForm.get('amount')" type="number" step="0.01"
            [ngClass]="{'is-invalid': openTillForm.get('amount').invalid && openTillForm.get('amount').touched}" />
        <small *ngIf="openTillForm.get('amount').invalid && openTillForm.get('amount').touched" class="text-danger">
            Amount is required
        </small>
        <small *ngIf="openTillForm.get('amount').value < 0" class="text-success">
            This will add money back to cash totals
        </small>
    </div>

    <br />

</div>
<div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="dismiss()">Cancel</button>
    <button type="button" class="btn btn-primary" (click)="submitForm()" [disabled]="!canSubmit">
        Submit
    </button>
    <button type="button" class="btn btn-warning" (click)="initPettyCash()" *ngIf="!pettyCash">
        Add Petty Cash
    </button>
    <button type="button" class="btn btn-warning" (click)="removePettyCash()" *ngIf="pettyCash">
        Remove Petty Cash
    </button>
</div>