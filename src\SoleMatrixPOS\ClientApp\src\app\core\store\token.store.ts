import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable()
export class TokenStore {
  constructor() {
    addEventListener('storage', (event: StorageEvent) => {
      if (event.key === 'refresh_token')
        this.refreshToken$.next(event.newValue);
    });
  }

  private refreshToken$: BehaviorSubject<string | null> = new BehaviorSubject(
    localStorage.getItem('refresh_token'),
  );

  setAccessToken(accessToken: string) {
    localStorage.setItem('access_token', accessToken);
  }
  removeAccessToken() {
    localStorage.removeItem('access_token');
  }

  setRefreshToken(refreshToken: string) {
    window.localStorage.setItem('refresh_token', refreshToken);
    window.dispatchEvent(
      new StorageEvent('storage', {
        key: 'refresh_token',
        newValue: refreshToken,
      }),
    );
  }
  removeRefreshToken() {
    window.localStorage.removeItem('refresh_token');
    window.dispatchEvent(
      new StorageEvent('storage', { key: 'refresh_token', newValue: null }),
    );
  }

  getRefreshToken(): Observable<string | null> {
    return this.refreshToken$.asObservable();
  }
}
