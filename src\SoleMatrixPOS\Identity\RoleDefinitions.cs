using System.Collections.Generic;

namespace SoleMatrixPOS.Identity
{
	public class RoleDefinition
	{
		public string RoleName {  get; private set; }

		public RoleDefinition (string name)
		{
			RoleName = name;
		}
	}

	public static class RoleDefinitions
	{
		// These act as the .NET identity role names, seeded in startup
		public const string ROLE_ADMIN = "admin";
		public const string ROLE_TILL = "till";

		public static List<RoleDefinition> Definitions = new List<RoleDefinition>() {
			new RoleDefinition(ROLE_ADMIN),
			new RoleDefinition(ROLE_TILL)
		};
	}
}
