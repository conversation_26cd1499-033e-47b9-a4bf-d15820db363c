import { createReducer, on } from '@ngrx/store';
import * as laybyActions from './layby-refund.actions';

export class LaybyRefundState {
  laybyCancelled: boolean;
  error: string | null;
}

export const initialState: LaybyRefundState = {
  laybyCancelled: false,
  error: null,
};

export const reducer = createReducer(
  initialState,
  on(laybyActions.cancelLayby, (state) => ({
    ...state, 
    laybyCancelled: false,
    error: null 
  })),
  on(laybyActions.laybyCancelledSuccess, (state) => ({
    ...state, 
    laybyCancelled: true 
  })),
  on(laybyActions.laybyCancelledFailure, (state, { error }) => ({
    ...state, 
    error 
  }))
);
