import { createReducer, on } from '@ngrx/store';
import * as endOfDayActions from './end-of-day.actions';

export interface EndOfDayState {
  endOfDayCompleted: boolean;
  message: string | null;
}

export const initialState: EndOfDayState = {
  endOfDayCompleted: false,
  message: null
};

export const endOfDayReducer = createReducer(
  initialState,
  on(endOfDayActions.init, (state) => initialState),
  on(endOfDayActions.submitEndOfDay, (state, action) => {console.log(action); return {...state}}),

  on(endOfDayActions.endOfDayConfirmation, (state, action) => {
    return { ...state, endOfDayCompleted: true, message: action.message };
  }),

  on(endOfDayActions.endOfDayError, (state, action) => {
    // Assuming action.error is a string representing an error message
    return { ...state, endOfDayCompleted: false, message: action.error };
  })
);
