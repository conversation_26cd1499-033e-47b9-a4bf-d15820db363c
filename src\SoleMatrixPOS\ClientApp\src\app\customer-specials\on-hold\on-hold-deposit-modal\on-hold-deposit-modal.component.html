<div class="modal-header">
    <h4 class="modal-title">Deposit Payment Required</h4>
    <button type="button" class="close" aria-label="Close" (click)="dismissModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  
<div class="modal-body">
    <div class="form-group">
        <label for="depositAmount">Deposit Amount Tendered</label>
        <input type="text" id="depositAmount" class="form-control" [value]="amountPaid | currency" readonly>
    </div>

    <div class="form-group">
        <label for="despositAmountRemaining">Deposit Amount Remaining</label>
        <input type="text" id="despositAmountRemaining" class="form-control" [value]="depositRemaining$ | async | currency" readonly>
    </div>

    <div class="form-group">
        <label for="totalAmountRemaining">Total Order Amount Remaining</label>
        <input type="text" id="totalAmountRemaining" class="form-control" [value]="totalRemaining$ | async | currency" readonly>
    </div>

    <div class="form-group">
        <label for="paymentMethod">Select Payment Method</label>
        <div class="d-flex justify-content-around py-3">
            <!-- Use Payment Modal Button component for Cash and EFTPOS -->
            <pos-payment-modal-button *ngFor="let button of modalButtons"
                                      [buttonInfo]="button"
                                      (openModal)="launchPaymentModal($event)">
            </pos-payment-modal-button>
        </div>
    </div>
</div>
  
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="dismissModal()">Cancel</button>
    <button type="button" 
            class="btn btn-success" 
            [disabled]="softCreditLimit === 'F' && !isDepositFullyPaid()" 
            (click)="processTransaction()">
        Process Deposit
    </button>
  </div>
