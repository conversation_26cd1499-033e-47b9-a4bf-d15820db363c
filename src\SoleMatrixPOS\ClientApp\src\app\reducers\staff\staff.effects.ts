import { Injectable } from "@angular/core";
import { Actions, Effect, ofType, createEffect } from "@ngrx/effects";
import {
	DailyClient,
	StaffClient,
	StaffLoginResult,
	DailyFloatClient,
	ClockInClient,
} from "../../pos-server.generated";
import * as staffActions from "./staff.actions";
import { mergeMap, map, catchError, tap } from "rxjs/operators";
import { EMPTY } from "rxjs";
@Injectable()
export class StaffEffects {
	date: Date;

	constructor(
		private actions$: Actions,
		private staffClient: StaffClient,
		private dailyFloatClient: DailyFloatClient,
		private dailyClient: DailyClient,
		private clockInClient: ClockInClient
	) {
		this.date = new Date();
		console.log(`Clock in date ${this.date}`);
	}

	staffLogin$ = createEffect(() =>
		this.actions$.pipe(
			ofType(staffActions.staffLogin),
			mergeMap((action) =>
				this.staffClient.login(action.code).pipe(
					map(
						(loginResponse) =>
							staffActions.staffLoginResponse({
								payload: loginResponse,
							}),
						catchError(() => EMPTY)
					)
				)
			)
		)
	);

	clockIn$ = createEffect(() =>
		this.actions$.pipe(
			ofType(staffActions.clockIn),
			mergeMap((action) =>
				this.clockInClient
					.clockIn(this.date) // TODO - is this client-side current date ok to use?
					.pipe(
						map(
							(clockInResponse) =>
								staffActions.clockInResponse({
									payload: clockInResponse,
								}),
							catchError(() => EMPTY)
						)
					)
			)
		)
	);

	clockOut$ = createEffect(() =>
		this.actions$.pipe(
			ofType(staffActions.clockOut),
			mergeMap((action) =>
				this.clockInClient
					.clockOut(this.date) // TODO - is this client-side current date ok to use?
					.pipe(
						map(
							(r) => staffActions.clearStaffLogin(),
							catchError(() => EMPTY)
						)
					)
			)
		)
	);


	dailyFloatEnter$ = createEffect(() => this.actions$.pipe(
		ofType(staffActions.dailyFloatEnter),
		mergeMap((action) => this.dailyFloatClient.enterFloat(action.payload)
			.pipe(
				map(x => staffActions.dailyFloatEnterResponse(),
					catchError(() => EMPTY)
				)
			)
		)));

	endOfDayClockOutAll$ = createEffect(() =>
		this.actions$.pipe(
			ofType(staffActions.endOfDayClockOutAllStaff),
			mergeMap(() => {
					return this.clockInClient.clockOutAllForEndOfDay()
					.pipe(
						map(() => {
							return staffActions.endOfDayClockOutAllStaffSuccess();
						}),
						catchError(() => EMPTY)
					);
				}
			)
		)
	);
}