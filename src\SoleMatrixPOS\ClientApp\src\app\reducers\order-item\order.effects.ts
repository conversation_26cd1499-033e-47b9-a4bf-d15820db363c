import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { map, mergeMap, catchError } from 'rxjs/operators';
import { of, from } from 'rxjs';
import { OrderClient, FileResponse } from 'src/app/pos-server.generated';
import * as orderActions from './order.actions';

@Injectable()
export class OrderEffects {
    constructor(
        private actions$: Actions,
        private client: OrderClient
    ) {}

    submitOrderWithTransaction$ = createEffect(() =>
        this.actions$.pipe(
            ofType(orderActions.submitOrderWithTransaction),
            mergeMap((action) =>
                this.client.addOrderWithTransaction(action.payload).pipe(
                    map(() => orderActions.orderWithTransactionCreated()),
                    catchError((error) => of(orderActions.orderSubmissionFailed({ error })))
                )
            )
        )
    );

    cancelOrder$ = createEffect(() =>
        this.actions$.pipe(
            ofType(orderActions.cancelOrder),
            mergeMap((action) =>
                this.client.cancelOrder(action.orderNumber).pipe(
                    map(() => orderActions.orderCancelled()),
                    catchError((error) => of(orderActions.orderCancellationFailed({ error })))
                )
            )
        )
    );

    completeOrder$ = createEffect(() =>
        this.actions$.pipe(
            ofType(orderActions.completeOrder),
            mergeMap((action) =>
                this.client.completeOrder(action.orderNumber).pipe(
                    map(() => orderActions.orderCompleted()),
                    catchError((error) => of(orderActions.orderCompletionFailed({ error })))
                )
            )
        )
    );    

    getOrderNo$ = createEffect(() =>
        this.actions$.pipe(
            ofType(orderActions.getOrderNo),
            mergeMap(() =>
                this.client.getOrderNo().pipe(
                    mergeMap((response: FileResponse) => {
                        if (response.status === 200 || response.status === 206) {
                            return from(this.extractOrderNo(response.data)).pipe(
                                map((orderNo) => orderActions.getOrderNoSuccess({ orderNo })),
                                catchError((error) => {
                                    console.error('Extract Order No Error:', error);
                                    return of(orderActions.getOrderNoFailure({ error: 'Failed to extract order number.' }));
                                })
                            );
                        } else {
                            return of(orderActions.getOrderNoFailure({
                                error: `Unexpected status code: ${response.status}`
                            }));
                        }
                    }),
                    catchError((error) => {
                        console.error('Get Order No Error:', error);
                        return of(orderActions.getOrderNoFailure({ error: error.message || 'Unknown error' }));
                    })
                )
            )
        )
    );

    // Helper method to extract orderNo from Blob
    private extractOrderNo(blob: Blob): Promise<string> {
        return new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const text = reader.result as string;
                if (text) {
                    resolve(text.trim());
                } else {
                    reject('Failed to extract order number.');
                }
            };
            reader.onerror = () => {
                reject('Failed to read blob data.');
            };
            reader.readAsText(blob);
        });
    }
}
