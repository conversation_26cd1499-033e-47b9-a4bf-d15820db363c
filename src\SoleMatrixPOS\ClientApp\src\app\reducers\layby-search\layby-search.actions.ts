import {createAction, props} from '@ngrx/store';
import {LaybylineDto, LaybySearchQueryDto, LaybySearchResultDto, StockItemDto, StockSearchRequestDto} from '../../pos-server.generated';

export const init = createAction('[LaybySearch] Init');
export const search = createAction('[LaybySearch] Search', props<{searchParams: LaybySearchQueryDto}>());
export const searchResponse = createAction('[LaybySearch] Response', props<{payload: LaybySearchResultDto[] }>());
export const searchMore = createAction('[LaybySearch] SearchMore');
export const searchMoreResponse = createAction('[LaybySearch] SearchMore Response', props<{payload: LaybySearchResultDto[] }>());

export const selectLayby = createAction('[LaybySearch] Select Layby', props<{payload: LaybySearchResultDto }>());

// In layby-search.actions.ts
export const getLaybyLines = createAction(
    '[LaybySearch] GetLaybyLines',
    props<{ laybyCode: string }>()
  );  
export const getLaybyLinesResponse = createAction('[LaybySearch] GetLaybyLinesResponse', props<{laybyLines: LaybylineDto[]}>());