import { Location } from './location.model';
import * as LocationActions from './location.action';

const initialState: Location[] = []; 

export function LocationReducer(state: Location[] = initialState, action: any) {
    
    switch (action.type) {
        case LocationActions.SWAP_STOCK: {
            let stateCopy = [...state]

            stateCopy.forEach((location)=>{                     // For each location
                if(location.name === action.to) {         // If this location is the 'to' location in this action
                    location.stock.forEach((stock)=>{       // Search its stock
                        if(stock.size === action.size){ // For the 'size' in this action
                            stock.qty += action.qty;    // And add this action's qty to it
                        }
                    });
                }
            });          

            stateCopy.forEach((location)=>{                     // For each location
                if(location.name === action.from) {           // If this location is the 'from' location in this action
                    location.stock.forEach((stock)=>{       // Search its stock
                        if(stock.size === action.size){ // For the 'size' in this action
                            stock.qty -= action.qty;    // And subtract this action's qty from it
                        }
                    });
                }
            });

            return [...stateCopy];
        }
        case LocationActions.LOAD_LOCATIONS: {
            state = action.locations
            return state;
        }
        case LocationActions.HIGHLIGHT_TRANSFER: {

            let tempState = [...state]; 
            let from = tempState.filter(location => action.from === location.name)[0];      // Find the 'from' location
            let to = tempState.filter(location => action.to === location.name)[0];          // Find the 'to' location

            from.stock.forEach(stock=>{                                             // Search the 'from' locations
                if(stock.size === action.size){                                     // For the 'size' in this action
                    stock.highlight = stock.highlight == "off" ? "from" : "off";    // And set it to 'off' or 'from' 
                }
            });
            to.stock.forEach(stock=>{                                               // Search the 'to' location
                if(stock.size === action.size){                                     // For the 'size' in this action
                    stock.highlight = stock.highlight == "off" ? "to" : "off";      // And set it to 'off' or 'to'
                }
            });

            state = tempState;  // Replace the state
            return state;
        }
        case LocationActions.LOAD_LOCATIONS_REQUESTED: {
            return state;
        }
        default: {
            return state;
        }
    }
}
