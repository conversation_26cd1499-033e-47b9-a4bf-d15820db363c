<div class="content-wrapper flex-grow-1 pt-10">
	<div class="card-body">
		<div class="container">
			<div class="row text-align-center mt-2 mb-4">
				<div class="col">
					<div class="row">
						<i class="fas fa-lg fa-fw fa-crown text-danger mt-3 mr-2 ml-2 text-shadow"></i>
						<h3 class="mt-3 mb-0 mr-2 text-danger">
							Club Member: {{_member?.firstname}} {{_member?.surname}}</h3>
					</div>
				</div>
				<div class="col">
					<div class="container">
						<div class="row">
							<div class="col-6">
							</div>
							<div class="col-2"><button class="mr-2 btn btn-outline-default" type="button"
									[translate]="'customer-club.buttons.Details'" (click)="changePage(0)"
									[disabled]="getPage() == 0">Details</button>
							</div>
							<div class="col-2"><button class="mr-2 btn btn-outline-default" type="button"
									[translate]="'customer-club.buttons.History'" (click)="changePage(1)"
									[disabled]="getPage() == 1">History</button>
							</div>
							<div class="col-2"><button class="mr-2 btn btn-outline-default" type="button"
									[translate]="'customer-club.buttons.Notes'" (click)="changePage(2)"
									[disabled]="getPage() == 2">Notes</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<hr>
</div>

<div *ngIf="getPage() == 0">
	<div class="content-wrapper flex-grow-1 pt-10">
		<pos-customer-club-member-form [member]="_member" (selectedMemberChanged)="onCancel()">
		</pos-customer-club-member-form>
	</div>
</div>

<div *ngIf="getPage() == 1">
	<div class="content-wrapper flex-grow-1 pt-10">
		<div class="container">
			<pos-customer-club-history></pos-customer-club-history>
			<hr>
			<div class="row mt-5">
				<div class="col-6">
					<div><button class="btn btn-primary back-button" type="button" (click)="onCancel()"
							[translate]="'customer-club.buttons.Back'">
							<i class="fas fa-arrow-left mr-2"></i>Go Back to Member Search
						</button>
					</div>
				</div>
				<div class="col-6 text-right">
				</div>
			</div>
		</div>
	</div>
</div>

<div *ngIf="getPage() == 2">
	<div class="content-wrapper flex-grow-1 pt-10">
		<div class="container">
			<pos-customer-club-notes [member]="_member"></pos-customer-club-notes>
			<hr>
			<div class="row mt-5">
				<div class="col-6">
					<div><button class="btn btn-primary back-button" type="button" (click)="onCancel()"
							[translate]="'customer-club.buttons.Back'">
							<i class="fas fa-arrow-left mr-2"></i>Go Back to Member Search
						</button>
					</div>
				</div>
				<div class="col-6 text-right">
				</div>
			</div>
		</div>
	</div>
</div>
