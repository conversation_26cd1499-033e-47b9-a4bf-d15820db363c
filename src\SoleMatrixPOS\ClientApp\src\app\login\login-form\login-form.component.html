<form [formGroup]="loginForm" (ngSubmit)="onSubmit()" autocomplete="off">
    <div class="d-flex flex-column mb-3" style="gap: 0.75rem;">
        <div class="form-group position-relative">
            <label for="username">Username</label>
            <input 
                id="username" 
                type="text" 
                formControlName="id" 
                class="form-control form-control-lg"
                [class.is-invalid]="f.id.touched && f.id.invalid"
                autocomplete="off"
            />
            <div *ngIf="f.id.touched && f.id.invalid" class="invalid-feedback position-absolute">
                <span *ngIf="f.id.errors?.required"> Username is required</span>
            </div>
        </div>
        <div class="form-group position-relative">
            <label for="password">Password</label>
            <input 
                id="password" type="password" 
                formControlName="password"    
                class="form-control form-control-lg"
                [class.is-invalid]="f.password.touched && f.password.invalid" 
                autocomplete="new-password"
            />
            <div *ngIf="f.password.touched && f.password.invalid" class="invalid-feedback position-absolute">
                <span *ngIf="f.password.errors?.required">Password is required</span>
            </div>
        </div>
    </div>

    <div >
        <button class="btn btn-info btn-lg w-100">Login</button>
    </div>
    <div class="alert alert-warning my-2 " *ngIf="serverError" >
        {{ serverError }}
    </div>
</form>