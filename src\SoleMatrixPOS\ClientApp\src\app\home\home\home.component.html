<pos-home-header></pos-home-header>
<div class="container-fluid flex-fill">
	<div *ngIf="staffAccess$ | async as staffAccess" id="home-buttons" class="d-flex flex-column h-100">
		<div *ngIf="staffAccess.noAccess" class="row">
			<!-- TODO: Add a NoAccess page and redirect there instead -->
			<div class="alert alert-warning" [translate]="'home.StaffNoAccess'">You do not have access to anything.
			</div>
		</div>

		<div class="row top-row flex-grow-1">
			<div class="col-sm-4 pb-3">
				<a *ngIf="staffAccess.returns" (click)="onClickReturns()"
					class="btn btn-block btn-lg btn-default d-flex flex-column align-items-center justify-content-center">
					<span class="fa-stack fa-vw">
						<i class="fas fa-receipt fa-stack-2x fa-shadow"></i>
						<i class="fas fa-receipt fa-stack-2x text-shadow"></i>
						<i class="fas fa-receipt fa-stack-2x color-gradient-pink"></i>
					</span>
					<div class="mb-4">
						<h2 [translate]="'home.buttons.Returns'">Returns</h2>
					</div>
				</a>

			</div>
			<div class="col-sm-4 pb-3">
				<button *ngIf="staffAccess.sales" (click)="onClickSales()"
					class="btn btn-block btn-lg btn-default d-flex flex-column align-items-center justify-content-center">
					<span class="fa-stack fa-vw">
						<i class="fas fa-shopping-cart fa-stack-2x fa-shadow"></i>
						<i class="fas fa-shopping-cart fa-stack-2x text-shadow"></i>
						<i class="fas fa-shopping-cart fa-stack-2x color-gradient-green"></i>
					</span>
					<div class="mb-4">
						<h2 [translate]="'home.buttons.Sales'">Sales</h2>
					</div>
				</button>
		    </div>
			<div class="col-sm-4 pb-3">
				<button *ngIf="staffAccess.exchange" (click)="onClickExchange()"
					class="btn btn-block btn-lg btn-default d-flex flex-column align-items-center justify-content-center">
					<span class="fa-stack fa-vw">
						<i class="fas fa-exchange fa-stack-2x fa-shadow"></i>
						<i class="fas fa-exchange fa-stack-2x text-shadow"></i>
						<i class="fas fa-exchange fa-stack-2x color-gradient-orange"></i>
					</span>
					<div class="mb-4">
						<h2 [translate]="'home.buttons.Exchange'">Exchange</h2>
					</div>
				</button>
			</div>
		</div>

		<div class="row">
			<div class="col-md-4">
				<button *ngIf="staffAccess.accountPayment"
					class="btn btn-block btn-lg btn-default d-flex align-items-center mb-3"
					(click)="onClickAccountPayment()">
					<i class="fas fa-lg fa-fw fa-file-invoice-dollar text-success mr-2 text-shadow"></i>
					<h4 class="mb-0 mr-2" [translate]="'home.buttons.AccountPayment'">
						Account Payment
					</h4>
				</button>

				<button *ngIf="staffAccess.layby"
					class="btn btn-block btn-lg btn-default d-flex align-items-center mb-3"
					(click)="onClickLaybyPayment()">
					<i class="fas fa-lg fa-fw fa-watch text-success mr-2 text-shadow"></i>
					<h4 class="mb-0 mr-2" [translate]="'home.buttons.Layby'">Layby Payment</h4>
				</button>

				<button *ngIf="staffAccess.sendStock"
					class="btn btn-block btn-lg btn-default d-flex align-items-center mb-3"
					[routerLink]="'/send-stock'">
					<i class="fas fa-lg fa-fw fa-shipping-fast text-success mr-2 text-shadow"></i>
					<h4 class="mb-0 mr-2" [translate]="'home.buttons.SendStock'">Send Stock</h4>
				</button>

				<button *ngIf="staffAccess.receiveStock"
					class="btn btn-block btn-lg btn-default d-flex align-items-center mb-3"
					[routerLink]="'/receive-stock'">
					<i class="fas fa-lg fa-fw fa-hand-holding-box text-success mr-2 text-shadow"></i>
					<h4 class="mb-0 mr-2" [translate]="'home.buttons.ReceiveStock'">Receive Stock</h4>
				</button>
			</div>
			<div class="col-md-4">

				<button *ngIf="staffAccess.customerClub" [routerLink]="['/customer-club']"
						class="btn btn-block btn-lg btn-default d-flex align-items-center mb-3">
					<i class="fas fa-lg fa-fw fa-crown text-danger mr-2 text-shadow"></i>
					<h4 class="mb-0 mr-2" [translate]="'home.buttons.CustomerClub'">Customer Club</h4>
				</button>

				<button *ngIf="staffAccess.giftVoucher" (click)="onClickGiftVoucher()"
						class="btn btn-block btn-lg btn-default d-flex align-items-center mb-3">
					<i class="fas fa-lg fa-fw fa-gift text-danger mr-2 text-shadow"></i>
					<h4 class="mb-0 mr-2" [translate]="'home.buttons.GiftVoucher'">
						Gift Voucher
					</h4>
				</button>

				<button *ngIf="staffAccess.customerSpecials" (click)="onClickCustomerSpecials()"
					class="btn btn-block btn-lg btn-default d-flex align-items-center mb-3">
					<i class="fas fa-lg fa-fw fa-user-crown text-danger mr-2 text-shadow"></i>
					<h4 class="mb-0 mr-2" [translate]="'home.buttons.CustomerSpecials'">
						Customer Specials
					</h4>
				</button>

				<button *ngIf="staffAccess.history" [routerLink]="['/history']"
					class="btn btn-block btn-lg btn-default d-flex align-items-center mb-3">
					<i class="fas fa-lg fa-fw fa-info-circle text-danger mr-2 text-shadow"></i>
					<h4 class="mb-0 mr-2" [translate]="'home.buttons.History'">History</h4>
				</button>
			</div>

			<div class="col-md-4">
				<button *ngIf="staffAccess.stockEnquiry" [routerLink]="['/stock-enquiry']"
					class="btn btn-block btn-lg btn-default d-flex align-items-center mb-3">
					<i class="fas fa-lg fa-fw fa-clipboard-list text-warning mr-2 text-shadow"></i>
					<h4 class="mb-0 mr-2" [translate]="'home.buttons.StockEnquiry'">Stock Enquiry</h4>
				</button>

				<button *ngIf="staffAccess.houseKeeping" (click)="openPasswordEntryModal()"
					class="btn btn-block btn-lg btn-default d-flex align-items-center mb-3">
					<i class="fas fa-lg fa-fw fa-home text-warning mr-2 text-shadow"></i>
					<h4 class="mb-0 mr-2" [translate]="'home.buttons.Housekeeping'">Housekeeping</h4>
				</button>

				<button *ngIf="staffAccess.stocktakeEntry" [routerLink]="['/pos-stocktake-entry']"
					class="btn btn-block btn-lg btn-default d-flex align-items-center mb-3">
					<i class="fas fa-lg fa-fw fa-cash-register text-warning mr-2 text-shadow"></i>
					<h4 class="mb-0 mr-2" [translate]="'home.buttons.StocktakeEntry'">
						Stocktake Entry
					</h4>
				</button>

				<button *ngIf="staffAccess.openTill" 
					(click)="onClickOpenTill()"
					class="btn btn-block btn-lg btn-default d-flex align-items-center mb-3">
					<i class="fas fa-lg fa-fw fa-dot-circle text-info mr-2 text-shadow"></i>
					<h4 class="mb-0 mr-2" [translate]="'home.buttons.OpenTill'">
						Open Till
					</h4>
				</button>
			</div>
		</div>
	</div>

</div>
