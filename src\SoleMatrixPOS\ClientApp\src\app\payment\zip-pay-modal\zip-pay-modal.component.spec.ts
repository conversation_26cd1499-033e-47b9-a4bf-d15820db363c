import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ZipPayModalComponent } from './zip-pay-modal.component';

describe('ZipPayModalComponent', () => {
  let component: ZipPayModalComponent;
  let fixture: ComponentFixture<ZipPayModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ZipPayModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ZipPayModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
