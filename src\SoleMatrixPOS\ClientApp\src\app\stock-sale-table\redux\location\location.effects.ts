import { Injectable } from '@angular/core';

import { SwapStockAction, HighlightStockAction } from '../location/location.action';
import { AddTransferAction, RemoveTransferAction, HighlightTransferAction } from '../transfer/transfer.action'

// Ngrx
import { map, mergeMap, catchError, tap } from 'rxjs/operators';
import { Actions, Effect, ofType } from '@ngrx/effects';
import { EMPTY } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class LocationEffects {

    /**
     * This effect intercepts the [TRANSFER] add action 
     * and triggers the [LOCATION] swap action in the location reducer
     */
    @Effect()
    addTransfer$ = this.actions$.pipe(
        ofType<AddTransferAction>("[TRANSFER] add"),
        //tap(x => {console.log("Locationffects: addTransfer$")}),
        map(action => new SwapStockAction(
            action.id, 
            action.from,
            action.to,
            action.name, 
            action.size, 
            action.qty
        )),
        catchError(() => EMPTY)
    );

    /**
     * This effect intercepts the [TRANSFER] remove action 
     * and triggers the [LOCATION] swap action in the location reducer
     */
    @Effect()
    removeTransfer$ = this.actions$.pipe(
        ofType<RemoveTransferAction>("[TRANSFER] remove"),
        //tap(x => {console.log("Locationffects: removeTransfer$")}),
        map(action => new SwapStockAction(
            action.id, 
            action.from,      
            action.to,    
            action.name, 
            action.size, 
            action.qty
        )),
        catchError(() => EMPTY)
    );

    /**
     * This effect intercepts the [TRANSFER] highlight action 
     * and triggers the [LOCATION] highligh action in the location reducer
     */
    @Effect()
    highlightStock$ = this.actions$.pipe(
        ofType<HighlightTransferAction>("[TRANSFER] highlight"),
        //tap(x => {console.log("Locationffects: removeTransfer$")}),
        map(action => new HighlightStockAction(
            action.id, 
            action.from,      
            action.to,    
            action.name, 
            action.size, 
            action.qty
        )),
        catchError(() => EMPTY)
    );



    constructor(private actions$: Actions) {}
}