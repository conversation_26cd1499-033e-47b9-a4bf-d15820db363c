import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import { Observable, Subject } from 'rxjs';
import { CartItemDto, CreateLaybyDto, LaybySearchResultDto } from 'src/app/pos-server.generated';
import { takeWhile, debounceTime, distinctUntilChanged, map, filter, take } from 'rxjs/operators';
import * as laybySearchActions from 'src/app/reducers/layby-search/layby-search.actions';
import * as laybySearchSelectors from 'src/app/reducers/layby-search/layby-search.selectors';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CancelLaybyComponent } from '../cancel-layby/cancel-layby.component';
import { LaybyPaymentModalComponent } from '../layby-payment-modal/layby-payment-modal.component';

import {
  OrderSearchRequestDto,
  LaybySearchQueryDto,
  LaybySearchFields,
  SortDirection,
} from 'src/app/pos-server.generated';

@Component({
  selector: 'pos-layby-search',
  templateUrl: './layby-search.component.html',
  styleUrls: ['./layby-search.component.scss']
})
export class LaybySearchComponent implements OnInit, OnDestroy {
  field: string = 'LaybyCode';
  term: string = '';

  laybys$: Observable<LaybySearchResultDto[]>;
  searchOptions$: Observable<OrderSearchRequestDto>;
  searchLoading$: Observable<boolean>;
  loading: boolean = true;

  private searchTerm$ = new Subject<string>();
  private alive = true;

  constructor(
    private store: Store<AppState>, 
    private router: Router,
    private modalService: NgbModal
  ) {}

  ngOnInit() {
    const savedField = localStorage.getItem('laybySearchField');
    if (savedField) {
      this.field = savedField;
    }
    // Subscribe to the laybys$ observable
    this.laybys$ = this.store.select(laybySearchSelectors.searchedLaybys);

    this.laybys$.subscribe((laybys) => {
      console.log('Filtered laybys:', laybys);
    });

    this.searchLoading$ = this.store.select(laybySearchSelectors.isLoading);

    this.searchTerm$
    .pipe(
      takeWhile(() => this.alive),
      debounceTime(200),
      distinctUntilChanged(),
      map((): LaybySearchQueryDto => {
        const options: LaybySearchQueryDto = {
          searchBy: LaybySearchFields[this.field],
          arrangeBy: LaybySearchFields[this.field],
          sortDirection: SortDirection.DESC, 
          query: this.term ? this.term.toUpperCase() : "",
          first: 25,
          skip: 0
        };
        console.log('Emitting search options:', options);
        return options;
      })
    )
    .subscribe((options: LaybySearchQueryDto) => {
      console.log('Calling doSearch with options:', options);
      this.doSearch(options);
    });
  
    // Subscribe to search loading indicator
    this.searchLoading$.subscribe((s) => {
      this.loading = s;
    });

    // Initial search on page load
    this.search();
  }

  ngOnDestroy(): void {
    this.alive = false;
  }

  search() {
    localStorage.setItem('laybySearchField', this.field);
    this.searchTerm$.next(this.term);
  }

  goHome() {
    this.router.navigateByUrl('/home');
  }

  doSearch(options: LaybySearchQueryDto) {
    this.store.dispatch(laybySearchActions.search({ searchParams: options }));
    this.store.select(laybySearchSelectors.searchedLaybys).subscribe(results => {
      console.log('Layby search results:', results);
    });
  }

  selectLayby(layby: LaybySearchResultDto) {
    if (!layby) {
      console.error('Invalid layby provided for viewing.');
      return;
    }
  
    this.store.dispatch(laybySearchActions.getLaybyLines({ laybyCode: layby.laybyCode }));
    this.store.dispatch(laybySearchActions.selectLayby({ payload: layby }));
  
    // Wait for the layby lines to be available in state before opening the modal
    this.store
      .select(laybySearchSelectors.searchedLaybyLines)
      .pipe(
        filter((lines) => !!lines && lines.length > 0), // Ensure laybyLines are not null
        take(1) // Take only the first valid response
      )
      .subscribe((lines) => {
        console.log('Layby lines retrieved:', lines);
  
        // Open the layby payment modal
        const modalRef = this.modalService.open(LaybyPaymentModalComponent, {
          size: 'lg',
          backdrop: 'static',
          centered: true,
        });
  
        modalRef.componentInstance.clientCode = layby.clientCode;
        modalRef.componentInstance.clientName = layby.firstname + ' ' + layby.surname;
        modalRef.result
          .then((result) => {
            console.log('Modal closed with result:', result);
            if (result === 'laybyCancelled') {
              // Refresh the search results
              this.search();
            }
          })
          .catch((reason) => {
            console.log('Modal dismissed:', reason);
            if (reason === 'laybyCancelled') {
              // Refresh the search results
              this.search();
            }
          });
      });
  }  
    
  cancelLayby(layby: LaybySearchResultDto) {
    if (!layby) {
      console.error('Invalid layby provided for cancellation.');
      return;
    }
  
    this.store.dispatch(laybySearchActions.getLaybyLines({ laybyCode: layby.laybyCode }));
    this.store.dispatch(laybySearchActions.selectLayby({ payload: layby }));
  
    // Wait for the layby lines to be available in state before opening the modal
    this.store
      .select(laybySearchSelectors.searchedLaybyLines)
      .pipe(
        filter((lines) => !!lines && lines.length > 0), // Ensure laybyLines are not null
        take(1) // Take only the first valid response
      )
      .subscribe((lines) => {
        console.log('Layby lines retrieved:', lines);
  
        // Open the cancel layby modal
        const modalRef = this.modalService.open(CancelLaybyComponent, {
          size: 'lg',
          backdrop: 'static',
          centered: true,
        });
        modalRef.componentInstance.clientCode = layby.clientCode;
        modalRef.componentInstance.clientName = layby.firstname + ' ' + layby.surname;
  
        modalRef.result
          .then((result) => {
            console.log('Modal closed with result:', result);
            if (result === 'laybyCancelled') {
              // Refresh the search results
              this.search();
            }
          })
          .catch((reason) => {
            console.log('Modal dismissed:', reason);
            if (reason === 'laybyCancelled') {
              // Refresh the search results
              this.search();
            }
          });
      });
  }  
}