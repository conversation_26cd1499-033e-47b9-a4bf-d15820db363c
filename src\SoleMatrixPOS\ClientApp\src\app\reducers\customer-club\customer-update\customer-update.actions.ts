import { createAction, props } from '@ngrx/store';
import { CustomerClubDto } from '../../../pos-server.generated';

export const init = createAction('[CustomerClub] Init');

// Member update actions
export const updateMember = createAction('[CustomerClub] Update', props<{ payload: CustomerClubDto }>());
export const updateMemberSuccess = createAction('[CustomerClub] UpdateMemberSuccess', props<{ result: CustomerClubDto }>());
export const updateMemberError = createAction('[CustomerClub] UpdateMemberError', props<{ error: string }>());

// Points update actions
export interface PointsUpdatePayload {
  clientCode: string;
  pointsToAdjust: number;
}

export const updatePoints = createAction(
  '[CustomerClub] UpdatePoints', 
  props<{ payload: PointsUpdatePayload }>()
);
export const updatePointsSuccess = createAction(
  '[CustomerClub] UpdatePointsSuccess', 
  props<{ clientCode: string, newPointsTotal: number }>()
);
export const updatePointsError = createAction(
  '[CustomerClub] UpdatePointsError', 
  props<{ error: string }>()
);