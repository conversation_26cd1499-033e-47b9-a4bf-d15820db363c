.content-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-height: 100%;
    padding: 2rem;

    .container-fluid {
        max-width: 1400px;
        margin: 0 auto;
        width: 100%;
    }
}

.transfer-number-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;

    .transfer-number {
        text-align: center;

        .form-label {
            font-size: 1.25rem;
            margin-right: 0.5rem;
            color: #495057;
        }

        .number {
            font-size: 1.75rem;
            color: #0d6efd;
        }
    }
}

.item-lookup-section {
    margin: 2rem 0;

    .lookup-wrapper {
        max-width: 600px;
        margin: 0 auto;

        pos-item-lookup {
            width: 100%;
            display: block;
        }
    }
}

.select-boxes-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 2rem;
    margin: 2rem 0;

    .select-box-wrapper {
        flex: 1;
        max-width: 600px;
        padding-left: 20px;
        padding-right: 20px;

        .select-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #495057;
        }

        .form-control-special {
            width: 100%;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.5rem;
            background-color: #fff;
            min-height: 250px;

            &:focus {
                border-color: #86b7fe;
                box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            }

            option {
                padding: 0.75rem 1rem;
                font-size: 1.5rem;
                font-weight:600;

                &:hover {
                    background-color: #f8f9fa;
                }
            }
        }
    }

    .send-to-wrapper {
        display: flex;
        align-items: center;
        padding: 2rem 0;

        .send-to-label {
            font-weight: 500;
            color: #495057;
            font-size: 1.1rem;
            white-space: nowrap;
        }
    }
}

.reset-section {
    display: flex;
    justify-content: center;
    margin-top: 2rem;

    .btn-danger {
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.2s ease-in-out;

        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    }
}

@media (max-width: 992px) {
    .select-boxes-container {
        flex-direction: column;
        align-items: center;

        .select-box-wrapper {
            width: 100%;
            max-width: 600px;
        }

        .send-to-wrapper {
            padding: 1rem 0;
        }
    }

    .transfer-number-section {
        .transfer-number {
            .number {
                font-size: 1.75rem;
            }
        }
    }
}

@media (max-width: 576px) {
    .content-wrapper {
        padding: 1rem;
    }

    .select-boxes-container {
        .select-box-wrapper {
            min-width: 100%;
        }
    }

    .transfer-number-section {
        .transfer-number {
            .form-label {
                font-size: 1rem;
            }

            .number {
                font-size: 1.5rem;
            }
        }
    }
}
