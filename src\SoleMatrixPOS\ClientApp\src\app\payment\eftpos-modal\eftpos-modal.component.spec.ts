import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { EftposModalComponent } from './eftpos-modal.component';

describe('EftposModalComponent', () => {
  let component: EftposModalComponent;
  let fixture: ComponentFixture<EftposModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EftposModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EftposModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
