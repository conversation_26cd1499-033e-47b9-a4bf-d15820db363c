import { createReducer, on } from "@ngrx/store";
import { BarcodeResultDto, PrintInvalidDto } from "src/app/pos-server.generated";
import * as PrintAction from '../print/print-actions'
import { BarcodeCart } from "../read-dat-file/read-dat-file.reducers";


export class PrintInvalidState {
    printCompleted: boolean;
    printIvalid: PrintInvalidDto
}

export const initialState: PrintInvalidState = {
    printCompleted: false,
    printIvalid:null
}

export const printInvalidReducer = createReducer(initialState,
    on(PrintAction.init, (state) => initialState),
    on(PrintAction.printInvalidBarcode,(state) => ({...state, printCompleted:true}))
)
