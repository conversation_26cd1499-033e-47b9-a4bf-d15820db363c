import { AppState } from '../index';
import { createSelector } from '@ngrx/store';

export const select = (state: AppState) => state.daily;
export const isRangeMode = (state: AppState) => state.daily.isRangeMode;
export const storeTodaysDaily = createSelector(select, (s) => s.currDaily);
export const isLoading = createSelector(select, (s) => s.isLoading);
export const storeSalesByStaff = createSelector(select, (s) => s.salesByStaff);
export const storeRefundsByStaff = createSelector(select, (s) => s.refundsByStaff);
export const storeSalesByHour = createSelector(select, (s) => s.salesByHour);
export const storeRefundsByHour = createSelector(select, (s) => s.refundsByHour);
export const storeDepartmentRefunds = createSelector(select, (s) => s.departmentRefunds);
export const getGrossSales = createSelector(select, (s) => s.grossAmount);
export const getItems = createSelector(select, (s) => s.totalItems);
export const getCusts = createSelector(select, (s) => s.totalCustomers);
export const getMultiSales = createSelector(select, (s) => s.multiSales);
export const getDepartmentSales = createSelector(select, (s) => s.departmentSales);
export const getSelectedDate = createSelector(select, (s) => s.selectedDate);
export const storeDailyRange = createSelector(select, (s) => s.dailyRange);
export const storeDailyRangeTotals = createSelector(select, (s) => s.dailyRangeTotals);