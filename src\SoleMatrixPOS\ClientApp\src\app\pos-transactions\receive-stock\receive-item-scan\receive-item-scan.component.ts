import { Component, OnInit, Input, ViewChild, ElementRef } from '@angular/core';
import { FormControl } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { Subscription, Observable } from 'rxjs';
import { StockTableFieldsEnabled, StockTableDefaultFields, TableItemFromDto } from '../../../generic-stock-table/stock-table-item/stock-table-item';
import { StockItemDto, StockTableItemDto } from '../../../pos-server.generated';
import { AppState } from '../../../reducers';
import { ReceiveItemScanState } from '../../../reducers/receive-stock/receive-item-scan/receive-item-scan.reducer';
import { StockSearchModalComponent } from '../../../stock-search/stock-search-modal/stock-search-modal.component';
import * as receiveItemScanActions from '../../../reducers/receive-stock/receive-item-scan/receive-item-scan.actions'
import { QuantityUpdateEvent } from '../../../generic-stock-table/generic-stock-table.component';
import Swal from 'sweetalert2'
import { pluck, timeout } from 'rxjs/operators';
import { Router } from '@angular/router';
import { AppModule } from '../../../app.module';
@Component({
  selector: 'pos-receive-item-scan',
  templateUrl: './receive-item-scan.component.html',
  styleUrls: ['./receive-item-scan.component.scss']
})
export class ReceiveItemScanComponent implements OnInit {

  subscriptions: Subscription[] = [];

  stockTableItems: StockTableItemDto[] = [];

  stockItemCount = 0;

  itemBarcodeValidity: boolean = true;

  fieldsEnabled: StockTableFieldsEnabled = { ...StockTableDefaultFields, price: false, totalPrice: false };

  @Input() senderStoreName: string;
  @Input() docketBarcode: string;


  enterItemBarcode = new FormControl('');
  @ViewChild('barcodeInput', { static: false }) barcodeInputChild: ElementRef;

  receiveItemScanState$: Observable<ReceiveItemScanState>;

  constructor(private store: Store<AppState>, private modalService: NgbModal, private router: Router) { }

  openSearch() {
    // Open search modal
    const modalRef = this.modalService.open(StockSearchModalComponent, { size: 'xl', centered: true });
    modalRef.componentInstance.name = 'ProductSearch';
    modalRef.result.then((result: StockItemDto) => {
      if (result) {
        this.store.dispatch(receiveItemScanActions.addItem({ item: TableItemFromDto(result) }));
      }
    }).catch((reason) => console.log(reason));
  }

  onConfirmStockReceivalClick() {
    // Open confirm button with sweetalert2
    Swal.fire({
      title: "Confirm Receival?",
      text: `
        Are you sure you would like to submit this transaction of ${this.stockItemCount} items?\n\n
        Note that this action is irreversible.
      `,
      showCancelButton: true
    }).then((result) => {
      // Call submit routine (TODO)
      if (result.value) {
        // Send to server
        let dateValue: Date = new Date();
        //dateValue.setMonth(dateValue.getMonth() + 1);
        this.store.dispatch(receiveItemScanActions.submitReceival({
          payload: {
            stockTableItems: this.stockTableItems,
            docketBarcode: this.docketBarcode,
            receiveDatetime: dateValue
          }
        }));
        // Reset
        this.router.navigateByUrl("/home")
        this.store.dispatch(receiveItemScanActions.init());
      }

    });
  }

  showSuccessPopup() {
    Swal.fire({
      toast: true,
      width: "300px",
      position: "top-end",
      timer: 10000,
      text: "Stock receival submitted.",
      showConfirmButton: false,
    });
  }

  ngOnInit() {
    this.receiveItemScanState$ = this.store.select(s => s.receiveItemScan);
    this.subscribeToState();
    this.subscribeToControls();
    this.store.dispatch(receiveItemScanActions.init());
  }

  subscribeToState() {
    this.receiveItemScanState$.subscribe((state) => {
      this.stockTableItems = state.tableItems;
      this.itemBarcodeValidity = state.barcodeValidity;
      this.stockItemCount = this.calculateItemCount();
    });

    this.receiveItemScanState$.pipe(pluck("receivalSubmitSuccess")).subscribe((success) => {
      if (success) this.showSuccessPopup();
    })
  }
  calculateItemCount() {
    var newCount = 0;
    for (let item of this.stockTableItems) {
      newCount += item.quantity;
    }

    return newCount;

  }

  subscribeToControls() {
    this.enterItemBarcode.valueChanges.subscribe((value: string) => {
      if (value.length == 13) {
        this.submitBarcode(value);
      }
    });
  }
  submitBarcode(value: string) {
    this.store.dispatch(receiveItemScanActions.submitItemBarcode({
      payload: { barcode: value }
    }));
    // Clear the input
    console.log("CHanged!");
    this.enterItemBarcode.setValue("");
    // Refocus on input
    this.barcodeInputChild.nativeElement.focus();
  }

  deleteItem(index: number) {
    this.store.dispatch(receiveItemScanActions.removeItem({ index: index }));
  }

  changeItemQuantity(update: QuantityUpdateEvent) {
    console.log("Changed. Index: " + update.index + " and quantity: " + update.quantity);
    if (update.quantity <= 0) {
      this.store.dispatch(receiveItemScanActions.removeItem({ index: update.index }));
    }
    else {
      this.store.dispatch(receiveItemScanActions.updateItemQuantity({ index: update.index, quantity: update.quantity }));
    }
  }

}
