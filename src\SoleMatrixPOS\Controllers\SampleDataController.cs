using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Metadata.Ecma335;
using System.Threading;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Sample;
using SoleMatrixPOS.Application.Sample.Queries;

namespace SoleMatrixPOS.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SampleDataController : Controller
    {

        private readonly IMediator _mediator;

        public SampleDataController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpGet("[action]")]
        public async Task<IEnumerable<WeatherForecast>> WeatherForecasts(CancellationToken ct)
        {
            return await _mediator.Send(new GetWeatherForecasts(), ct);
        }

       
    }
}
