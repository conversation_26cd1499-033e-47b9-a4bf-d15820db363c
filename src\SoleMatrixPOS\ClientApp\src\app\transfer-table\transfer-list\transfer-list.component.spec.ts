import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TransferListComponent } from './transfer-list.component';
import { Store, MemoizedSelector } from '@ngrx/store';
//import { AppState } from '../../../../redux/app.store';
import { AppState, state } from '../../stock-sale-table/test-state';
import { MockStore, provideMockStore } from '@ngrx/store/testing';
import { Transfer } from '../../stock-sale-table/redux/transfer/transfer.model';
import { of } from 'rxjs';

type Highlight = "on" | "from" | "to" | "off";

describe('TransferContentComponent', () => {
  let component: TransferListComponent;
  let fixture: ComponentFixture<TransferListComponent>;

  let store: MockStore<{transfers: Transfer[]}>;
  let transfers: MemoizedSelector<AppState, Transfer[]>;

  let dispatchSpy;

  let initialState =  [
    {
      id: 1,
      from: "Location 1",
      to: "Location 2",
      name: "BOUNCE",
      size: "1",
      qty: 1,
      highlight: "off" as Highlight
    },
    {
      id: 2,
      from: "Location 3",
      to: "Location 4",
      name: "BOUNCE",
      size: "1",
      qty: 1,
      highlight: "off" as Highlight
    },
  ];

  beforeEach(() => {

    TestBed.configureTestingModule({
      providers: [ 
        TransferListComponent,
        provideMockStore()
      ],
      declarations: [ TransferListComponent ]
    });

    //valueServiceSpy = TestBed.get(Store);

    store = TestBed.get(Store);
    component = TestBed.get(TransferListComponent);
    component.transfers$ = of(initialState)

  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('readTransfersState', ()=>{
 
    it('should assign the store.transfers to transfers$', ()=>{

      let selectSpy = spyOn(store, 'select').and.returnValues(of(initialState));

      component.readTransfersState();

      component.transfers$.subscribe((result)=> {
        expect(result).toEqual(initialState);
      });

    });

  });

  describe('addTransfer', ()=>{

    it('should add a transfer in the transfers$ array', ()=> {

      let stateCopy = [...initialState];
      let newTransfer;
      
      let dispatchSpy = spyOn(store, 'dispatch').and.callFake((newValue)=>{

        //console.log("addTransfer: newValue", newValue);

        newTransfer = {
          id: newValue['id'],
          from: newValue['from'],
          to: newValue['to'],
          name: newValue['name'],
          size: newValue['size'],
          qty: newValue['qty'],
          highlight: newValue['highlight']
        };

        stateCopy.push(newTransfer);
      });

      component.addTransfer("Location 1", "Location 2", "BOUNCE", "1", 1);

      expect(stateCopy.length).toBe(3);
      expect(stateCopy).toEqual([...initialState, newTransfer]);

    });
    
  });

  describe('removeTransfer', ()=>{
    
    it('should remove a transfer in the transfers$ array', ()=> {

      let stateCopy = [...initialState];
      
      let dispatchSpy = spyOn(store, 'dispatch').and.callFake((newValue)=>{
        stateCopy = stateCopy.filter(x=> {
          return x.id != newValue['id'];
        });
      });

      // Remove a transfer
      component.removeTransfer(stateCopy[0].id);

      // Was 2, should now be 1
      expect(stateCopy.length).toBe(1);

      // The only entry should be what was the 2nd element in the initial state
      expect(stateCopy[0]).toBe(initialState[1]);

    });

  });

  describe('highlightTransfer', ()=>{
    
    it('should toggle the highlight state of a transfer', ()=> {

      let stateCopy = [...initialState];

      let dispatchSpy = spyOn(store, 'dispatch').and.callFake((searchValue)=>{

        stateCopy.filter(x=> {
          if(searchValue['type'] == '[TRANSFER] highlight') {
            if(x.id == searchValue['id']) {
              x.highlight = x.highlight == "off" ? "on" : "off"; 
            }
          }
        });

      });

      // Turn it on
      component.highlightTransfer(stateCopy[0].id);
      expect(stateCopy[0].highlight).toBe("on");

      // Turn it off
      component.highlightTransfer(stateCopy[0].id);
      expect(stateCopy[0].highlight).toBe("off");

    });
    
  });

});
