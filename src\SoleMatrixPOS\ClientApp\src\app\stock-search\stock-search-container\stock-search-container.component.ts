import { Component, EventEmitter, OnInit, Output, Input } from '@angular/core';
import { Observable } from 'rxjs';
import { createAction, Store } from '@ngrx/store';
import { AppState } from '../../reducers';
import * as stockSearchSelectors from '../../reducers/stock-search/stock-search.selectors';
import * as stockSearchActions from '../../reducers/stock-search/stock-search.actions';
import { StockItemDto, StockSearchRequestDto } from '../../pos-server.generated';

@Component({
	selector: 'pos-stock-search-container',
	templateUrl: './stock-search-container.component.html',
	styleUrls: ['./stock-search-container.component.scss']
})
export class StockSearchContainerComponent implements OnInit {
	@Input() initialSearchValue: string;
	styleItems$: Observable<StockItemDto[]>;
	searchOptions$: Observable<StockSearchRequestDto>;
	isLoading$: Observable<boolean>;

	@Output() selectedItemChanged = new EventEmitter<StockItemDto>();

	constructor(private store: Store<AppState>) {

	}

	ngOnInit() {
		this.styleItems$ = this.store.select(stockSearchSelectors.searchedItems);
		this.searchOptions$ = this.store.select(stockSearchSelectors.searchOptions);
		this.isLoading$ = this.store.select(stockSearchSelectors.isLoading);

		this.store.dispatch(stockSearchActions.clearSearchItems());

		if (this.initialSearchValue) {
			// Assuming you have a form control or variable for search
			// this.searchControl.setValue(this.initialSearchValue);
			// OR
			// this.searchTerm = this.initialSearchValue;
		}
	}

	onSearchChanged(searchRequest: StockSearchRequestDto) {
		console.log('Search request received:', searchRequest);
		// Dispatch the search action to NgRx
		this.store.dispatch(stockSearchActions.search({ searchParams: searchRequest }));
		// or however you're triggering the backend search
	}

	selectItem(item: StockItemDto) {
		this.selectedItemChanged.emit(item);
	}

	searchMore() {
		this.store.dispatch(stockSearchActions.searchMore());
	}
}
