import { Component, OnInit, Input, Output, EventEmitter, OnDestroy, HostListener, ElementRef, ViewChild } from '@angular/core';
import { CustomerClubDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import { Store } from '@ngrx/store';
import { FormBuilder, Validators, AbstractControl, FormControl, ValidationErrors } from '@angular/forms';
import { Observable, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { ofType, Actions } from '@ngrx/effects';
import Swal from 'sweetalert2';
import { take } from 'rxjs/operators';

import * as customerAddActions from '../../reducers/customer-club/customer-add/customer-add.actions';
import * as customerAddSelectors from '../../reducers/customer-club/customer-add/customer-add.selectors';
import { MemberAddStatus } from 'src/app/reducers/customer-club/customer-add/customer-add.reducer';
import { CreateErrorModal } from 'src/app/error-modal/error-modal.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import * as sysConfigSelectors from '../../reducers/sys-config/sys-config.selectors';
import * as customerClubSearchActions from '../../reducers/customer-club/club-search/customer-club.actions';
import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';
import { CustomerSuburbDto } from 'src/app/pos-server.generated';

@Component({
  selector: 'pos-customer-club-member-form',
  templateUrl: './customer-club-member-form.component.html',
  styleUrls: ['./customer-club-member-form.component.scss']
})
export class CustomerClubMemberFormComponent implements OnInit, OnDestroy {

  @ViewChild('suburbInput', { static: false }) suburbInputRef: ElementRef;
  @ViewChild('suburbSuggestions', { static: false }) suburbSuggestionsRef: ElementRef;

  public _member: CustomerClubDto;
  public vipHidden: boolean = true;
  public staffDetailsHidden: boolean = true;
  public otherDetailsHidden: boolean = true;
  subscriptions: Subscription[] = [];
  private customerClubBirthday: boolean = false;

  @Input()
  set member(member: CustomerClubDto) {
    this._member = member;

    this.customerClubNewMemberForm.get("ClubNumber").setValue(this._member.clientCode);
    this.customerClubNewMemberForm.get("ClubNumber").updateValueAndValidity();

    this.customerClubNewMemberForm.get("Points").setValue(this._member.clientPoints);
    this.customerClubNewMemberForm.get("Points").updateValueAndValidity();

    this.customerClubNewMemberForm.get("Title").setValue(this._member.title);
    this.customerClubNewMemberForm.get("Title").updateValueAndValidity();

    this.customerClubNewMemberForm.get("FirstName").setValue(this._member.firstname);
    this.customerClubNewMemberForm.get("FirstName").updateValueAndValidity();

    this.customerClubNewMemberForm.get("LastName").setValue(this._member.surname);
    this.customerClubNewMemberForm.get("LastName").updateValueAndValidity();

    this.customerClubNewMemberForm.get("CareOf").setValue(this._member.careof);
    this.customerClubNewMemberForm.get("CareOf").updateValueAndValidity();

    this.customerClubNewMemberForm.get("Street").setValue(this._member.street);
    this.customerClubNewMemberForm.get("Street").updateValueAndValidity();

    this.customerClubNewMemberForm.get("Suburb").setValue(this._member.suburb);
    this.customerClubNewMemberForm.get("Suburb").updateValueAndValidity();

    this.customerClubNewMemberForm.get("State").setValue(this._member.state);
    this.customerClubNewMemberForm.get("State").updateValueAndValidity();

    this.customerClubNewMemberForm.get("Country").setValue("AUSTRALIA"); // need to keep this somewhere
    this.customerClubNewMemberForm.get("Country").updateValueAndValidity();

    this.customerClubNewMemberForm.get("Postcode").setValue(this._member.postcode);
    this.customerClubNewMemberForm.get("Postcode").updateValueAndValidity();

    this.customerClubNewMemberForm.get("Email").setValue(this._member.email);
    this.customerClubNewMemberForm.get("Email").updateValueAndValidity();

    this.customerClubNewMemberForm.get("Mobile").setValue(this._member.mobileNo);
    this.customerClubNewMemberForm.get("Mobile").updateValueAndValidity();

    this.customerClubNewMemberForm.get("Phone").setValue(this._member.telephone);
    this.customerClubNewMemberForm.get("Phone").updateValueAndValidity();

    this.customerClubNewMemberForm.get("NoMail").setValue(this._member.noMail == "T" ? true : false);
    this.customerClubNewMemberForm.get("NoMail").updateValueAndValidity();

    // VIP fields
    if (this._member.isVIP === 1) {
      this.vipHidden = false; // Automatically show VIP section if they are VIP
      this.customerClubNewMemberForm.get("BarNo").setValue(this._member.barcode);
      this.customerClubNewMemberForm.get("BarNo").updateValueAndValidity();
      this.customerClubNewMemberForm.get("Discount").setValue(this._member.privDiscount);
      this.customerClubNewMemberForm.get("Discount").updateValueAndValidity();
      this.customerClubNewMemberForm.get("Issued").setValue(this._member.issueDate);
      this.customerClubNewMemberForm.get("Issued").updateValueAndValidity();
      this.customerClubNewMemberForm.get("Expires").setValue(this._member.expiryDate);
      this.customerClubNewMemberForm.get("Expires").updateValueAndValidity();

    }

    if (this._member.hasOther) {
      this.otherDetailsHidden = false;
      this.customerClubNewMemberForm.get("BusinessNumber").setValue(this._member.businessNumber);
      this.customerClubNewMemberForm.get("LicenceNo").setValue(this._member.licenceNo);
      this.customerClubNewMemberForm.get("RefType").setValue(this._member.refType);
      this.customerClubNewMemberForm.get("FamilyNo").setValue(this._member.familyNo);
      this.customerClubNewMemberForm.get("ReferrerNo").setValue(this._member.referrerNo);
      this.customerClubNewMemberForm.get("Relationship").setValue(this._member.relationship);
      this.customerClubNewMemberForm.get("Bday").setValue(this._member.bday);
      this.customerClubNewMemberForm.get("Bmth").setValue(this._member.bmth);
      this.customerClubNewMemberForm.get("Byr").setValue(this._member.byr);
    }

    // Update all form controls
    Object.keys(this.customerClubNewMemberForm.controls).forEach(key => {
      this.customerClubNewMemberForm.get(key).updateValueAndValidity();
    });
  }

  @Output() selectedMemberChanged = new EventEmitter<CustomerClubDto>();

  addStatus$: Observable<MemberAddStatus>;
  barcodeExists$: Observable<boolean>;

  private actions$: Actions;

  suburbs$: Observable<CustomerSuburbDto[]>;

  ngOnInit() {
    this.initState();
    this.subscribeToState();
    this.barcodeExists$ = this.store.select(customerAddSelectors.barcodeExists);

    this.subscriptions.push(
      this.actions$.pipe(
        ofType(customerAddActions.createMemberSuccess)
      ).subscribe(() => {
        Swal.fire({
          title: 'Success',
          text: 'Member added successfully',
          type: 'success',
          timer: 2000,
          showConfirmButton: false
        });

        this.customerClubNewMemberForm.reset();
        this.vipHidden = true;
        this.staffDetailsHidden = true;

        this.store.dispatch(customerAddActions.init());

        this.selectedMemberChanged.emit(null);
      })
    );

    this.subscriptions.push(
      this.customerClubNewMemberForm.get('BarNo').valueChanges.pipe(
        debounceTime(300),
        distinctUntilChanged()
      ).subscribe(value => {
        if (value) {
          this.store.dispatch(customerAddActions.checkBarcode({ barcode: value }));
        }
      })
    );

    this.subscriptions.push(
      this.barcodeExists$.subscribe(exists => {
        if (exists) {
          this.customerClubNewMemberForm.get('BarNo').setErrors({ barcodeExists: true });
        }
      })
    );

    this.subscriptions.push(
      this.store.select(sysConfigSelectors.selectCustomerClubBirthday)
        .subscribe(enabled => {
          this.customerClubBirthday = enabled == 'T';
        })
    );

    this.suburbs$ = this.store.select(customerClubSearchSelectors.suburbSearchResults);

    this.subscriptions.push(
      this.customerClubNewMemberForm.get('Suburb').valueChanges.pipe(
        debounceTime(100),
        distinctUntilChanged()
      ).subscribe(value => {
        if (value && value.length > 2 && value.length < 50) {
          this.store.dispatch(customerClubSearchActions.searchSuburbs({ search: value }));
        }
      })
    );
  }
  ngOnDestroy(): void {
    for (let s of this.subscriptions) s.unsubscribe();
  }

  initState() {
    this.store.dispatch(customerAddActions.init());
  }
  subscribeToState() {
    this.addStatus$ = this.store.select(customerAddSelectors.status);
    this.subscriptions.push(this.addStatus$.subscribe(
      status => {
        this.handleAddStatus(status);
      }
    ));
  }
  handleAddStatus(status: MemberAddStatus) {
    console.log("Status update! -> ", status);
    switch (status) {
      case MemberAddStatus.Success: {
        this.selectedMemberChanged.emit(null);
        return;
      }

      case MemberAddStatus.Error: {
        console.log("Must be!:", status == MemberAddStatus.Error);
        CreateErrorModal(this.modalService, true, "Failed to create member. Please try again later, or contact support.");
        this.initState();
        return;
      }

      default: {
        break;
      }

    }
  }


  constructor(
    private store: Store<AppState>,
    private formBuilder: FormBuilder,
    private modalService: NgbModal,
    private elementRef: ElementRef,
    actions$: Actions
  ) {
    this.actions$ = actions$;
  }

  public customerClubNewMemberForm = this.formBuilder.group({
    ClubNumber: [],
    Points: [{ value: 0, disabled: true },
    [Validators.min(0)]
    ],
    Title: ["",
      [Validators.pattern(/^[\w]{1}[\w\s',.-]{0,30}$/)]
    ],
    FirstName: ["",
      [Validators.pattern(/^[\w]{1}[\w\s',.-]{0,49}$/), Validators.required]
    ],
    LastName: ["",
      [Validators.pattern(/^[\w]{1}[\w\s',.-]{0,49}$/), Validators.required]
    ],
    CareOf: ["",
      [Validators.maxLength(50)]
    ],
    Street: ["",
      [Validators.maxLength(50)]
    ],
    Suburb: ["",
      [Validators.maxLength(50)]
    ],
    State: ["",
      [Validators.maxLength(50)]
    ],
    Country: ["",
      [Validators.maxLength(50)]
    ],
    Postcode: ["",
      [Validators.pattern(/^\d{4}$/)]
    ],
    Email: ["",
      [Validators.email, Validators.maxLength(50)]
    ],
    Mobile: ['',
      [Validators.pattern(/\+*[\d\s]{8,11}/)]
    ],
    Phone: ["",
      [Validators.pattern(/\+*[\d\s]{6,11}/)]
    ],
    NoMail: [],
    BarNo: ['', [
      Validators.maxLength(13)
    ]],
    Discount: ['', [
      Validators.min(0),
      Validators.max(100),
      Validators.pattern(/^\d*\.?\d*$/) // Allow decimal numbers
    ]],
    Issued: ['', [
      Validators.pattern(/^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/)
    ]],
    Expires: ['', [
      Validators.pattern(/^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/)
    ]],
    Created: [],
    Altered: [],
    LastSale: [],
    FirstSale: [],
    BusinessNumber: ['', [
      Validators.maxLength(25)
    ]],
    LicenceNo: ['', [
      Validators.maxLength(12)
    ]],
    RefType: ['', [
      Validators.maxLength(4)
    ]],
    FamilyNo: ['', [
      Validators.maxLength(12)
    ]],
    ReferrerNo: ['', [
      Validators.maxLength(12)
    ]],
    Relationship: ['', [
      Validators.maxLength(50)
    ]],
    Bday: ['', [
      Validators.min(0),
      Validators.max(31),
      Validators.pattern(/^\d*$/)
    ]],
    Bmth: ['', [
      Validators.min(0),
      Validators.max(12),
      Validators.pattern(/^\d*$/)
    ]],
    Byr: ['', [
      Validators.min(1900),
      Validators.max(new Date().getFullYear()),
      Validators.pattern(/^\d*$/)
    ]]
  }, {
    validators: [
      (formGroup: AbstractControl): ValidationErrors | null => {
        const email = formGroup.get('Email').value;
        const phone = formGroup.get('Phone').value;

        if (!email && !phone) {
          return { emailOrMobileRequired: true };
        }
        return null;
      }
    ]
  });

  public onCancel(): void {
    this.selectedMemberChanged.emit(null);
    //this.store.dispatch(staffActions.clearStaffLogin());
  }

  getDateForCSharp(): Date {
    return new Date();

    //date.setMonth(date.getMonth());
    //date.setMonth(date.getMonth() + 1); // Added 22/11 reverting back to +1
    //return date;//date.setMonth(date.getMonth());
    //date.setMonth(date.getMonth() + 1); // Added 22/11 reverting back to +1
    //return date;
    //date.setMonth(date.getMonth());
    //date.setMonth(date.getMonth() + 1); // Added 22/11 reverting back to +1

    //return date;
  }

  public onSubmit(): void {
    if (this.customerClubBirthday) {
      const bday = this.customerClubNewMemberForm.get('Bday').value;
      const bmth = this.customerClubNewMemberForm.get('Bmth').value;
      const byr = this.customerClubNewMemberForm.get('Byr').value;

      if (!bday && !bmth && !byr) {
        Swal.fire({
          title: 'Birthday Details Missing',
          showCancelButton: true,
          confirmButtonText: 'Submit without birthday',
          cancelButtonText: 'Cancel'
        }).then((result) => {
          if (result.value) {
            this.submitForm();
          }
        });
        return;
      }
    }

    this.submitForm();
  }

  public submitForm(): void {
    this.customerClubNewMemberForm.markAllAsTouched();

    if (!this.customerClubNewMemberForm.valid) {
      Object.keys(this.customerClubNewMemberForm.controls).forEach(key => {
        const control = this.customerClubNewMemberForm.get(key);
        control.markAsTouched();
      });

      if (this.hasEmailOrMobileError()) {
        Swal.fire({
          title: 'Email or Phone Required',
          text: 'You must enter a valid email or phone number to create a new member.',
          type: 'warning',
        })
        return;
      }
    }

    const customerClubDto: CustomerClubDto = {
      clientCode: this._member.clientCode,
      title: this.customerClubNewMemberForm.value.Title,
      firstname: this.customerClubNewMemberForm.value.FirstName,
      surname: this.customerClubNewMemberForm.value.LastName,
      careof: this.customerClubNewMemberForm.value.CareOf,
      street: this.customerClubNewMemberForm.value.Street,
      suburb: this.customerClubNewMemberForm.value.Suburb,
      state: this.customerClubNewMemberForm.value.State,
      email: this.customerClubNewMemberForm.value.Email,
      clientPoints: this.customerClubNewMemberForm.value.Points,
      mobileNo: this.customerClubNewMemberForm.value.Mobile,
      telephone: this.customerClubNewMemberForm.value.Phone,
      postcode: this.customerClubNewMemberForm.value.Postcode,
      noMail: this.customerClubNewMemberForm.value.NoMail,
      barcode: this.customerClubNewMemberForm.value.BarNo,
      privDiscount: this.customerClubNewMemberForm.value.Discount,
      issueDate: this.customerClubNewMemberForm.value.Issued,
      expiryDate: this.customerClubNewMemberForm.value.Expires,
      isVIP: this.customerClubNewMemberForm.value.VIP,
      businessNumber: this.customerClubNewMemberForm.value.BusinessNumber,
      licenceNo: this.customerClubNewMemberForm.value.LicenceNo,
      refType: this.customerClubNewMemberForm.value.RefType,
      familyNo: this.customerClubNewMemberForm.value.FamilyNo,
      referrerNo: this.customerClubNewMemberForm.value.ReferrerNo,
      relationship: this.customerClubNewMemberForm.value.Relationship,
      bday: this.customerClubNewMemberForm.value.Bday,
      bmth: this.customerClubNewMemberForm.value.Bmth,
      byr: this.customerClubNewMemberForm.value.Byr,
      hasOther: true
    };

    if (this.customerClubNewMemberForm.valid) {
      this.store.dispatch(customerAddActions.createMember({ payload: customerClubDto }));

      this.subscriptions.push(
        this.actions$.pipe(
          ofType(customerAddActions.createMemberSuccess),
          take(1)
        ).subscribe((action) => {
          this.store.dispatch(customerClubSearchActions.selectCustomerClubMember({
            payload: action.result
          }));

          Swal.fire({
            title: 'Success',
            text: 'Member added successfully',
            type: 'success',
            timer: 2000,
            showConfirmButton: false
          }).then(() => {
            this.customerClubNewMemberForm.reset();
            this.vipHidden = true;
            this.staffDetailsHidden = true;
            this.store.dispatch(customerAddActions.init());
            this.selectedMemberChanged.emit(null);
            this.modalService.dismissAll();
          });
        })
      );

      // Error handler
      this.subscriptions.push(
        this.actions$.pipe(
          ofType(customerAddActions.createMemberError),
          take(1)
        ).subscribe(({ error }) => {
          Swal.fire({
            title: 'Error',
            text: `Failed to create member: ${error}`,
            type: 'error',
            showConfirmButton: true
          });
        })
      );
    } else {
      this.customerClubNewMemberForm.markAsPristine();
    }
  }

  public fieldValidate(control: AbstractControl): boolean {
    // TODO handle errors
    return control.invalid;
  }

  public hasEmailOrMobileError(): boolean {
    const formErrors = this.customerClubNewMemberForm.errors;
    const emailControl = this.customerClubNewMemberForm.get('Email');
    const phoneControl = this.customerClubNewMemberForm.get('Phone');

    // Only show error if either field has been touched or form was submitted
    const shouldShowError = (emailControl.touched || phoneControl.touched || this.customerClubNewMemberForm.touched);

    return shouldShowError && (formErrors && 'emailOrMobileRequired' in formErrors);
  }

  onSuburbSelect(suburb: CustomerSuburbDto): void {
    this.store.dispatch(customerClubSearchActions.selectSuburb({ suburb }));

    this.customerClubNewMemberForm.patchValue({
      Suburb: suburb.suburb,
      State: suburb.state,
      Postcode: suburb.postcode.toString()
    }, { emitEvent: false });
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const clickedInsideInput = this.suburbInputRef && this.suburbInputRef.nativeElement && this.suburbInputRef.nativeElement.contains(event.target);
    const clickedInsideSuggestions = this.suburbSuggestionsRef && this.suburbSuggestionsRef.nativeElement && this.suburbSuggestionsRef.nativeElement.contains(event.target);

    if (!clickedInsideInput && !clickedInsideSuggestions) {
      this.suburbs$.pipe(take(1)).subscribe(suburbs => {
        if (suburbs && suburbs.length > 0) {
          this.store.dispatch(customerClubSearchActions.clearSuburbSearch());
        }
      });
    }
  }

}
