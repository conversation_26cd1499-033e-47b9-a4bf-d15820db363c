<div class="container">
    <div class="shadow-lg p-3 mb-5 bg-white round">
      <h5 class="justify-content-center">Receipt</h5>
      <div class="row mb-3">
        <table class="table table-striped table-hover table-scroll">
          <tbody class="body-half-screen">
            <!-- Title Section -->
            <tr>
              <th>Title</th>
              <td>
                <table>
                  <tr *ngFor="let recT of receiptTitles; let i = index" class="text">
                    <div (click)="editTitle(i)">
                      {{i+1}} {{recT.recLine}}
                    </div>
                    <div *ngIf="clickedIndexT === i" class="editor">
                      <input type="text" autofocus id="title{{i}}" name="title{{i}}" required
                        [(ngModel)]="recT.recLine"
                        (keydown.Enter)="processChangesTitle(recT.recLine)"
                        (keydown.Meta.Enter)="processChangesTitle(recT.recLine)"
                        (keydown.Escape)="onCancelT()" />
                      <div class="mt-2">
                        <button (click)="processChangesTitle(recT.recLine)" class="btn btn-light btn-sm">
                          <i class="fas fa-check-circle text-success"></i>
                        </button>
                        <button (click)="onCancelT()" class="btn btn-light btn-sm ml-2">
                          <i class="fa fa-times text-danger"></i>
                        </button>
                      </div>
                    </div>
                  </tr>
                </table>
              </td>
            </tr>
  
            <!-- Header Section -->
            <tr>
              <th>Header</th>
              <td>
                <table class="table table-striped table-hover table-scroll">
                  <tr *ngFor="let recH of receiptHeaders; let i = index" class="text">
                    <div (click)="editHeader(i)">
                      {{i+1}} {{recH.recLine}}
                    </div>
                    <div *ngIf="clickedIndexH === i" class="editor">
                      <input type="text" autofocus id="header{{i}}" name="header{{i}}" required
                        [(ngModel)]="recH.recLine"
                        (keydown.Enter)="processChangesHeader(recH.recLine)"
                        (keydown.Meta.Enter)="processChangesHeader(recH.recLine)"
                        (keydown.Escape)="onCancelH()" />
                      <div class="mt-2">
                        <button (click)="processChangesHeader(recH.recLine)" class="btn btn-light btn-sm">
                          <i class="fas fa-check-circle text-success"></i>
                        </button>
                        <button (click)="onCancelH()" class="btn btn-light btn-sm ml-2">
                          <i class="fa fa-times text-danger"></i>
                        </button>
                      </div>
                    </div>
                  </tr>
                </table>
              </td>
            </tr>
  
            <!-- System Section (Unchanged) -->
            <tr>
              <th>System</th>
              <td>
                <table class="table table-striped table-hover table-scroll">
                  <tr>
                    <th>Style</th>
                    <th>Colour</th>
                    <th>Size</th>
                    <th>Qty</th>
                    <th>Price</th>
                  </tr>
                  <tr>
                    <td>XXXXX</td>
                    <td>XXXXX</td>
                    <td>XXXXX</td>
                    <td>XXXXX</td>
                    <td>XXXXX</td>
                  </tr>
                  <tr>
                    <td></td>
                    <td></td>
                    <td colspan="3">
                      <p>Sales Total: $XXXX</p>
                      <p>Payment Total: $XXXX</p>
                      <p>Change: $XXXX</p>
                      <p>You have paid: $XXXX INCL GST</p>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
  
            <!-- Footer Section -->
            <tr>
              <th>Footer</th>
              <td>
                <table class="table table-striped table-hover table-scroll">
                  <tr *ngFor="let recF of receiptFooters; let i = index" class="text">
                    <div (click)="editFooter(i)">
                      {{i+1}} {{recF.recLine}}
                    </div>
                    <div *ngIf="clickedIndexF === i" class="editor">
                      <input type="text" autofocus id="footer{{i}}" name="footer{{i}}" required
                        [(ngModel)]="recF.recLine"
                        (keydown.Enter)="processChangesFooter(recF.recLine)"
                        (keydown.Meta.Enter)="processChangesFooter(recF.recLine)"
                        (keydown.Escape)="onCancelF()" />
                      <div class="mt-2">
                        <button (click)="processChangesFooter(recF.recLine)" class="btn btn-light btn-sm">
                          <i class="fas fa-check-circle text-success"></i>
                        </button>
                        <button (click)="onCancelF()" class="btn btn-light btn-sm ml-2">
                          <i class="fa fa-times text-danger"></i>
                        </button>
                      </div>
                    </div>
                  </tr>
                </table>
              </td>
            </tr>
  
            <!-- Layby Section -->
            <tr>
              <th>Layby</th>
              <td>
                <table class="table table-striped table-hover table-scroll">
                  <tr *ngFor="let recL of receiptLaybys; let i = index" class="text">
                    <div (click)="editLayby(i)">
                      {{i+1}} {{recL.recLine}}
                    </div>
                    <div *ngIf="clickedIndexL === i" class="editor">
                      <input type="text" autofocus id="layby{{i}}" name="layby{{i}}" required
                        [(ngModel)]="recL.recLine"
                        (keydown.Enter)="processChangesLayby(recL.recLine)"
                        (keydown.Meta.Enter)="processChangesLayby(recL.recLine)"
                        (keydown.Escape)="onCancelL()" />
                      <div class="mt-2">
                        <button (click)="processChangesLayby(recL.recLine)" class="btn btn-light btn-sm">
                          <i class="fas fa-check-circle text-success"></i>
                        </button>
                        <button (click)="onCancelL()" class="btn btn-light btn-sm ml-2">
                          <i class="fa fa-times text-danger"></i>
                        </button>
                      </div>
                    </div>
                  </tr>
                </table>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
