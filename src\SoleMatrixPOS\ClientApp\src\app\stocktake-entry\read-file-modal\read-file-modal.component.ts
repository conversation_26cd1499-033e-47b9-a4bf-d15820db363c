import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { BarcodeResultDto, PrintBarcodeDto, PrintInvalidDto, ReadDatFileDto, StockItemCartDto, StockItemDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import * as readDatSelector from "../../reducers/stocketake-entry/read-dat-file/read-dat-file.selectors";
import * as readDatAction from "../../reducers/stocketake-entry/read-dat-file/read-dat-file.actions";
import { Router } from '@angular/router';
import { BarcodeCart, CartToPrint, PrevNextItem} from 'src/app/reducers/stocketake-entry/read-dat-file/read-dat-file.reducers';
import * as PrintAction from '../../reducers/stocketake-entry/print/print-actions';
import * as itemCartAction from '../../reducers/stocketake-entry/itemCart/itemCart.actions'


@Component({
  selector: 'pos-read-file-modal',
  templateUrl: './read-file-modal.component.html',
  styleUrls: ['./read-file-modal.component.scss']
})
export class ReadFileModalComponent implements OnInit, OnDestroy {

  datFiles$: Observable<ReadDatFileDto[]>;
  datFiles: ReadDatFileDto[];

  searchLoading$: Observable<boolean>;
  checkingBarcode$: Observable<boolean>;

  public loading: boolean = true;
  public checking: boolean = true;

  barcodeItem$: Observable<BarcodeCart[]>;
  barcodeItem: BarcodeCart[];

  invalidUnit: number = 0;
  validUnit: number = 0;
  itemScanned: number = 0;

  Validated: boolean;
  initial: boolean;


  constructor(
    public activeModal: NgbActiveModal,
    private store: Store<AppState>,
    private router: Router,

  ) {

  }

  ngOnInit() {

    this.Validated = false;
    this.initial = true;

    //get the read dat file
    this.datFiles$ = this.store.select(readDatSelector.readDatFile);
    this.datFiles$.subscribe(value => {
      this.datFiles = value;

      //call for
      
      for (let i = 0; i < this.datFiles.length; i++) {
        let barcode = this.datFiles[i].barCode;
          this.itemScanned++; 

        //this.store.dispatch(readDatAction.validateBarcode({ barcode: barcode }));

      }
    },);

    this.searchLoading$ = this.store.select(readDatSelector.searchLoading);
    this.searchLoading$.subscribe((s) => { this.loading = s });

    this.checkingBarcode$ = this.store.select(readDatSelector.ischecking);
    this.checkingBarcode$.subscribe((s) => { this.checking = s });

    //get validated barcode for testing purposes
    this.barcodeItem$ = this.store.select(readDatSelector.getvalidateBarcode);
    this.barcodeItem$.subscribe(value => {
      this.barcodeItem = value;
    });

  }

  ngOnDestroy(): void {
    //Called once, before the instance is destroyed.
    //Add 'implements OnDestroy' to the class.

    this.invalidUnit = 0;
    this.validUnit = 0;


  }

  getClass(barcode: string) {

    for (let i = 0; i < this.barcodeItem.length; i++) {

      if (this.barcodeItem[i].validatedBarcode.barcode === barcode) {

        if (this.barcodeItem[i].validatedBarcode.valid === true) {
          return true;

        } else if (this.barcodeItem[i].validatedBarcode.valid === false) {
          return false;
        }
      }
    }

  }


  //get the item number and check true or false
  getCount() {

    this.Validated = true;

    for (let i = 0; i < this.datFiles.length; i++) {
      let barcode = this.datFiles[i].barCode;
      let quantity = parseInt(this.datFiles[i].quantity);

      for (let j = 0; j < this.barcodeItem.length; j++) {

        if (barcode == this.barcodeItem[j].validatedBarcode.barcode) {

          if (this.barcodeItem[j].validatedBarcode.valid === true) {

            this.validUnit += quantity;
          } else {

            this.invalidUnit += quantity;
          }
        }
      }

    }

  }

  printInvalid(){

    console.log("Printing invalid barcode!")

    let itemPrint: PrintInvalidDto = this.printBarcode();

    //print invalid item
    this.store.dispatch(PrintAction.printInvalidBarcode({ payload: itemPrint }));
  }


  // process() {
  //   console.log("Process add to ItemCart!")

  //   //Process item to the cart
  //   // for (let j = 0; j < this.datFiles.length; j++) {
  //   //   let barcode = this.datFiles[j].barCode;
  //   //   let quantity = Number(this.datFiles[j].quantity);

  //   //   for (let i = 0; i < this.barcodeItem.length; i++) {
  //   //     let barcodeCart = this.barcodeItem[i];

       
  //   //       //check the barcode is validated and get items
  //   //       if (barcodeCart.validatedBarcode.valid == true) {

  //   //       let itemProcess: StockItemCartDto = this.processBarcode();

  //   //         for (let k = 0; k < quantity; k++) {

  //   //           //submit based on the quantity
  //   //           this.submitItem(itemProcess);

  //   //         }
  //   //       }

  //   //   }


  //   // }


  //   let itemProcess: StockItemDto = this.processBarcode();

  //   this.store.dispatch(itemCartAction.submitBarcodeResponse({ addCartItem: itemProcess }));

    

  // }

  // // submitItem(itemProcess: StockItemDto) {

  // //   //let itemProcess: StockItemCartDto = this.processBarcode();

  // //   this.store.dispatch(itemCartAction.submitBarcode({ itembarcode: itemProcess }));
  // // }


  // processBarcode(): BarcodeResultDto {

  //   let itemBarcode: BarcodeResultDto[] = [];

  //  //read file
  //   let styleCode: string;
  //   let description: string;
  //   let color: string;
  //   let colorCode: string;
  //   let department: string;
  //   let makerCode: string;
  //   let size: string;

  //   //read validated item
  //   for (let i = 0; i < this.barcodeItem.length; i++) {
  //     let barcodeCart = this.barcodeItem[i];

  //     //check the barcode is validated and get items
  //     if (barcodeCart.validatedBarcode.valid == true) {
        
  //       itemBarcode.push(barcodeCart[i]);

  //         // styleCode = barcodeCart.validatedBarcode.styleCode,
  //         // description = barcodeCart.validatedBarcode.description,
  //         // color = barcodeCart.validatedBarcode.colorName,
  //         // colorCode = barcodeCart.validatedBarcode.colorCode,
  //         // department = barcodeCart.validatedBarcode.department,
  //         // makerCode = barcodeCart.validatedBarcode.makerCode,
  //         // size = barcodeCart.validatedBarcode.sizeCode

  //     }
  //   }

  //   return {
  //     // styleCode: styleCode,
  //     // styleDescription: description,
  //     // colourName: color,
  //     // colourCode: colorCode,
  //     // departmentCode: department,
  //     // makerCode: makerCode,
  //     // size: size,
  //   }

  // }




  //need to implement the correct request
  printBarcode(): PrintInvalidDto {

    let items: PrintBarcodeDto[] = [];

    for (let i = 0; i < this.barcodeItem.length; i++) {
      let prev = this.barcodeItem[i - 1];
      let barcodeCart = this.barcodeItem[i];
      let next = this.barcodeItem[i + 1]

      if (barcodeCart.validatedBarcode.valid == false) {

        items.push({
          invalidBarcode: CartToPrint(barcodeCart),
          //TODO
          previousItem: PrevNextItem(prev) ,
          nextItem: PrevNextItem(next) ,
        })

      }
    }

    return {
      printBarcodes: items
    } as PrintInvalidDto;

  }

  exit() {

    this.router.navigateByUrl('/');

  }

}

