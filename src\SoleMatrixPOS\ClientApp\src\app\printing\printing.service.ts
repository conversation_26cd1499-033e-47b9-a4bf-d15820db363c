import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, Subject, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import * as CustomerClubSearchSelectors from 'src/app/reducers/customer-club/club-search/customer-club.selectors'
declare const require: any;
const printJS = require('print-js');
import {
  BarcodeAction,
  CutAction,
  CutType,
  FeedAction,
  ImageAction,
  ReceiptBatch,
  TextAction
} from './printing-definitions';
import { CustomerClubDto, GetReceiptDto, ReceiptTemplateDto, ReceiptTransactionDto, StaffLoginDto, TranslogDto, TranspayDto, Transref } from '../pos-server.generated';
import { EmailRequest } from '../pos-server.generated';
import { Store } from '@ngrx/store';
import { AppState } from '../reducers';
import * as SysConfigSelectors from '../reducers/sys-config/sys-config.selectors';
import { PrintCacheService } from './print-cache.service';
import { selectStaffLoginDto } from '../reducers/staff/staff.selectors';
import { EndOfDayDTO } from '../pos-server.generated';
import { FloatDto } from '../pos-server.generated';
import { CustOrderLineDTO } from '../pos-server.generated';

export interface VoucherBalanceInfo {
  type: string;
  balance: number;
  number: string;
}

@Injectable({ providedIn: 'root' })
export class PrintingService {
  private baseUrl = 'http://localhost:4032';
  private refreshPrintersSubject = new Subject<void>();
  refreshPrinters$ = this.refreshPrintersSubject.asObservable();
  printWeeksOnLayby: string;
  selectedCustomer: CustomerClubDto;
  staff: StaffLoginDto = null;

  constructor(private http: HttpClient, private store: Store<AppState>, private printCache: PrintCacheService) {
    this.store.select(selectStaffLoginDto).subscribe(staffInfo => {
      this.staff = staffInfo;
    });
    // Initialize TABLE_WIDTH from localStorage
    this.updateTableWidth();
  }

  private updateTableWidth() {
    const savedWidth = localStorage.getItem('printerWidth');
    if (savedWidth) {
      TABLE_WIDTH = parseInt(savedWidth);
    }
  }

  public setPrinterWidth(width: number) {
    localStorage.setItem('printerWidth', width.toString());
    TABLE_WIDTH = width;
  }

  private getSelectedPrinter(): string {
    const printer = localStorage.getItem('selectedPrinter');
    return printer ? printer : '';
  }

  public checkService(): Observable<string> {
    return this.http.get(`${this.baseUrl}/test`, { responseType: 'text' })
      .pipe(catchError(this.handleError));
  }

  public getPrinters(): Observable<PrinterListResponse> {
    return this.http.get<PrinterListResponse>(`${this.baseUrl}/getprinters`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Core method: sends a single complete batch to the printer.
   */
  public executeBatch(batch: ReceiptBatch, printerNameOverride?: string | null): Observable<string> {
    const printer = printerNameOverride !== undefined ? printerNameOverride : this.getSelectedPrinter();
    if (printer === null || printer === '') { 
        console.warn("No printer selected or specified for batch execution.");
        return throwError(() => new Error("No printer selected. Cannot execute print batch."));
    }
    batch.set_printer(printer);
    console.log("Printing Batch: ", batch.build(), "to printer:", printer);
    return this.http.post(`${this.baseUrl}/batch`, batch.build(), { responseType: 'text' })
      .pipe(catchError(this.handleError));
  }

  /**
   * Wrapper for integrated receipts.
   */
  public printEftposReceipt(receipts: GetReceiptDto[], cut: boolean): void {
    console.log(receipts)

    for (let receipt of receipts) {
      if (receipt.merchReceipt != null) {
        const merchBatch = new ReceiptBatch();
        merchBatch.add_action(new TextAction(receipt.merchReceipt));
        if (cut) {
          merchBatch.add_action(new CutAction(CutType.Half));
        }
        this.executeBatch(merchBatch).subscribe(
          res => console.log('Merchant receipt print success:', res),
          err => console.error('Merchant receipt print error:', err)
        );
      }

      if (receipt.custReceipt != null) {
        const custBatch = new ReceiptBatch();
        custBatch.add_action(new TextAction(receipt.custReceipt));
        if (cut) {
          custBatch.add_action(new CutAction(CutType.Half));
        }
        this.executeBatch(custBatch).subscribe(
          res => console.log('Customer receipt print success:', res),
          err => console.error('Customer receipt print error:', err)
        );
      }
    }
  }

  // In your print service:
  async printPdfReceipt(receiptTrans: ReceiptTransactionDto) {
    const response = await this.http.post('api/email/get-receipt-pdf', receiptTrans, {
      responseType: 'blob'
    }).toPromise();
    const pdfUrl = URL.createObjectURL(response);
    printJS({ printable: pdfUrl, type: 'pdf' });
  }

  /**
   * Wrapper for printing vouchers.
   */
  public async printVoucher(voucherTitle: string,
    voucherNumber: string,
    voucherFunds: number,
    customerCode?: string,
    customerName?: string
  ): Promise<void> {
    const voucherBatch: ReceiptBatch = new ReceiptBatch();
    const template: ReceiptTemplateDto = await this.printCache.getTemplate();
    let receiptText = '';
    if (customerName && customerCode) {
      receiptText += `${GenerateCustomerInfo(customerCode, customerName)}\n\n`;
    }
    const paddedVoucherNumber = padBarcodeEAN13(voucherNumber);

    voucherBatch.add_action(new ImageAction(await this.printCache.getLogoPayload()));
    // Center the main text content
    voucherBatch.add_action(new TextAction(
      centerText("\n\n" + template.title + "\nIssuance Date: " + new Date().toLocaleString() + "\n" + receiptText + voucherTitle + "\n" + "VOUCHER CODE: " + voucherNumber + "\n" + "Value: " + financialFmt(voucherFunds), TABLE_WIDTH) + "\n"
    ));
    // Add barcode and the centered code below it
    voucherBatch.add_action(new BarcodeAction(paddedVoucherNumber));
    voucherBatch.add_action(new CutAction(CutType.Half));

    this.executeBatch(voucherBatch).subscribe(
      res => console.log('Voucher print success:', res),
      err => console.error('Voucher print error:', err)
    );
  }

  public async printTransferNumber(transferNumber: string, receiveLocation: string, translogItems: TranslogDto[], sendingStore: string) {
    var sendStockBatch: ReceiptBatch = new ReceiptBatch();
    let totalQty = 0;
    let receiptText = "\n\nStock Transfer for : " + receiveLocation +
      "\nSending Store: " + sendingStore +
      "\nTransfer Number: " + transferNumber +
      "\nDate: " + new Date().toLocaleString() + "\n\n";

    if (translogItems && translogItems.length > 0) {
      receiptText += "Items:\n";
      receiptText += "Style        Colour Size  Qty\n";
      receiptText += line(TABLE_WIDTH) + "\n";

      for (const item of translogItems) {
        receiptText += padTo(item.styleCode, 13) +
          padTo(item.colourCode, 7) +
          padTo(item.sizeCode, 6) +
          padTo(String(item.quantity || ''), 6) + "\n";
        totalQty += item.quantity || 0;
      }
      receiptText += line(TABLE_WIDTH) + "\n\n";
    }
    receiptText += "              Total Quantity: " + totalQty + "\n";
    sendStockBatch.add_action(new TextAction(receiptText));
    sendStockBatch.add_action(new BarcodeAction(padBarcodeEAN13(transferNumber)));
    sendStockBatch.add_action(new CutAction(CutType.Half));
    this.executeBatch(sendStockBatch).subscribe(
      res => console.log('Stock print success:', res),
      err => console.error('Stock print error:', err)
    );
  }

  /**
   * An example method that builds and prints a solemate receipt.
   */
  public async printSolemateReceipt(
    rType: string,
    logs: TranslogDto[],
    pays: TranspayDto[],
    transType: number,
    transNo: string,
    format: SolemateReceiptOptions = SolemateReceiptOptions.default(),
    receiptTrans: ReceiptTransactionDto,
    change?: number,
    transRefs?: Transref[],
    customerCode?: string,
    customerName?: string,
    voucherBalances?: VoucherBalanceInfo[],
    newCustomerPointsTotal?: number,
    pointsEarned?: number,
    customerAccountDetails?: { code: string, name: string, newBalance?: number } | null,
    orderNo?: string
  ): Promise<void> {
    // Check if printing service is available
    try {
      const response = await this.checkService().toPromise();
      console.log("Service check response:", response);
      console.log("Service check passed, proceeding with normal printing");
    } catch (error) {
      console.error('Printer service unavailable, falling back to PDF:', error);
      console.log("DOING PDF PRINTING");

      await this.printPdfReceipt(receiptTrans);
      return;
    }
    this.store.select(SysConfigSelectors.selectWeeksOnLayby).subscribe(limit => {
      this.printWeeksOnLayby = limit || 'F';
    });
    const template: ReceiptTemplateDto = await this.printCache.getTemplate();
    let mainBatch: ReceiptBatch = new ReceiptBatch();
    let receiptText = '';

    // Add logo if needed
    if (format.useLogo) {
      mainBatch.add_action(new ImageAction(await this.printCache.getLogoPayload()));
    }

    // Add reprint text if needed
    if (format.isReprint) {
      receiptText += "\n\n" + centerText("REPRINT", TABLE_WIDTH) + "\n\n";
    }

    // Add header content
    if (format.useTitle) {
      receiptText += centerText(template.title, TABLE_WIDTH) + "\n\n";
    }

    if (format.useHeader) {
      receiptText += centerText(template.header, TABLE_WIDTH) + "\n\n";
    }

    if (orderNo) {
      receiptText += centerText("ORDER", TABLE_WIDTH) + "\n\n";
      receiptText += centerText(`Order#: ${orderNo}`, TABLE_WIDTH) + "\n\n";
    }

    if (customerName && customerCode) {
      receiptText += `${GenerateCustomerInfo(customerCode, customerName, newCustomerPointsTotal, pointsEarned)}\n\n`;
    }
    else if (newCustomerPointsTotal) {
      receiptText += `${GenerateCustomerPointsInfo(newCustomerPointsTotal)}\n\n`;
    }
    else {
      receiptText += ' ';
    }

    // Find the layby code (first translog with no quantity)
    const laybyLog = logs.find(log => !log.quantity);
    let laybyCode = '';
    if (laybyLog && laybyLog.styleCode) {
      laybyCode = laybyLog.styleCode;
    }

    // Filter out "Reason" translogs for the cart table
    const nonReasonLogs = logs.filter(log => log.styleCode !== "Reason");

    if (transRefs && transRefs.length > 0) {
      const noteRef = transRefs.find(ref => ref.lineNo === 0);
      if (noteRef && noteRef.transReference) {
        receiptText += GenerateReceiptNote(noteRef.transReference.trim());
      }
    }

    // Add cart table
    if (nonReasonLogs.length > 0) {
      receiptText += `${GenerateCartTable(logs, transType, transRefs)}\n\n`;
    }

    // Add payment totals
    if (pays.length > 0) {
      let label = "";
      switch (transType) {
        case 2:
          label = "Refund";
          break;
        case 4:
          label = "Layby DepositTotal";
          break;
        case 5:
          label = "Layby Payment";
          break;
        case 11:
          label = "Order Total";
          break;
        default:
          label = `${rType} Total`;
      }
      receiptText += `${GeneratePaymentTotals(label, pays, change, undefined, false, customerAccountDetails)}\n\n`;
    }

    if (voucherBalances && voucherBalances.length > 0) {
      receiptText += `${GenerateVoucherBalanceInfo(voucherBalances)}\n\n`;
    }
    // Add transaction info
    if (format.useInfo) {
      receiptText += line(TABLE_WIDTH) + "\n";

      // Display different transaction info based on transaction type
      switch (transType) {
        case 1: // Sale
          receiptText += centerText(`SALE: ${transNo} - ${new Date().toLocaleString().toUpperCase()}`, TABLE_WIDTH) + "\n";
          break;
        case 2: // Return
          receiptText += centerText(`RETURN: ${transNo} - ${new Date().toLocaleString().toUpperCase()}`, TABLE_WIDTH) + "\n";
          break;
        case 3: // Exchange
          receiptText += centerText(`EXCHANGE: ${transNo} - ${new Date().toLocaleString().toUpperCase()}`, TABLE_WIDTH) + "\n";
          break;
        case 4: // Layby
          receiptText += centerText(`LAYBY: ${laybyCode} - ${new Date().toLocaleString().toUpperCase()}`, TABLE_WIDTH) + "\n";
          break;
        case 5: // Layby Payment
          receiptText += centerText(`LAYBY PAYMENT: ${laybyCode} - ${new Date().toLocaleString().toUpperCase()}`, TABLE_WIDTH) + "\n";
          break;
        case 11: // Customer Order
          receiptText += centerText(`ORDER: ${transNo} - ${new Date().toLocaleString().toUpperCase()}`, TABLE_WIDTH) + "\n";
          break;
        case 12: // Quote
          receiptText += centerText(`QUOTE: ${transNo} - ${new Date().toLocaleString().toUpperCase()}`, TABLE_WIDTH) + "\n";
          break;
        case 19:
          receiptText += centerText(`ACCOUNT PAYMENT: ${transNo} - ${new Date().toLocaleString().toUpperCase()}`, TABLE_WIDTH) + "\n";
          break;
        default:
          receiptText += centerText(`TRANSACTION: ${transNo} - ${receiptTrans.saleDateTime.toLocaleString().toUpperCase()}`, TABLE_WIDTH) + "\n";
      }

      if (this.staff) {
        receiptText += centerText(`You have been served by ${this.staff.name}`, TABLE_WIDTH) + "\n";
      }
      receiptText += line(TABLE_WIDTH) + "\n\n";
    }

    // Add footer content
    if (format.useFooter) {
      receiptText += centerText(`${template.footer}`, TABLE_WIDTH) + "\n\n";
    }

    // Add layby template if needed
    if ((transType === 4 || transType === 5) && this.printWeeksOnLayby === 'T') {
      receiptText += `${template.layby}\n`;
    }

    // Final actions: add text, feed, barcode, and cut.
    mainBatch.add_action(new TextAction(receiptText));
    mainBatch.add_action(new BarcodeAction(padBarcodeEAN13(transNo)));

    // Add customer signature section for layby receipts (transaction type 4)
    if (transType === 4) {
      let signatureText = "\n\n\n\n";
      signatureText += line(TABLE_WIDTH) + "\n";
      signatureText += centerText("Customer Signature", TABLE_WIDTH) + "\n";

      mainBatch.add_action(new TextAction(signatureText));
    }

    mainBatch.add_action(new CutAction(CutType.Half));

    this.executeBatch(mainBatch).subscribe(
      res => console.log('Solemate receipt printed successfully:', res),
      err => console.error('Error printing solemate receipt:', err)
    );
  }

  /**
   * Specialized method for printing layby receipts.
   */
  public async printLaybyReceipt(
    rType: string,
    logs: TranslogDto[],
    pays: TranspayDto[],
    transType: number,
    transNo: string,
    format: SolemateReceiptOptions,
    receiptTrans: ReceiptTransactionDto,
    amountDue: number,
    change: number,
    refs?: Transref[],
    customerCode?: string,
    customerName?: string,
    laybyNumber?: string,
    voucherBalances?: VoucherBalanceInfo[],
    newCustomerPointsTotal?: number,
    pointsEarned?: number
  ): Promise<void> {
    // Check if printing service is available (reuse existing check)
    try {
      const response = await this.checkService().toPromise();
      console.log("Service check passed, proceeding with normal printing");
    } catch (error) {
      console.error('Printer service unavailable, falling back to PDF:', error);
      await this.printPdfReceipt(receiptTrans);
      return;
    }
    console.log("Customer name", customerName);
    console.log("Customer code", customerCode);
    console.log("Points earned", pointsEarned);
    console.log("New customer points total", newCustomerPointsTotal);

    if (format == null) {
      format = SolemateReceiptOptions.default();
    }

    const template: ReceiptTemplateDto = await this.printCache.getTemplate();
    let mainBatch: ReceiptBatch = new ReceiptBatch();
    let receiptText = '';

    if (format.useLogo) {
      mainBatch.add_action(new ImageAction(await this.printCache.getLogoPayload()));
    }
    if (format.isReprint) {
      receiptText += "\n\n" + centerText("**REPRINT**", TABLE_WIDTH) + "\n\n";
    }

    let laybyCode = '';
    if (laybyNumber) {
      laybyCode = laybyNumber;
    } else {
      const laybyLog = logs.find(log => log.styleCode && !log.quantity);
      if (laybyLog) {
        laybyCode = laybyLog.styleCode;
      } else {
        laybyCode = transNo;
      }
    }

    // Add receipt type header
    switch (transType) {
      case 4:
        receiptText += "\n" + centerText("LAYBY RECEIPT", TABLE_WIDTH) + "\n";
        break;
      case 5:
        receiptText += "\n" + centerText("LAYBY PAYMENT RECEIPT", TABLE_WIDTH) + "\n";
        break;
      default:
        receiptText += "\n" + centerText(`${rType.toUpperCase()} RECEIPT`, TABLE_WIDTH) + "\n";
    }
    receiptText += "\n";

    // Add header content
    if (format.useTitle) {
      receiptText += centerText(template.title, TABLE_WIDTH) + "\n\n";
    }

    if (format.useHeader) {
      receiptText += centerText(template.header, TABLE_WIDTH) + "\n\n";
    }

    if (customerName && customerCode) {
      receiptText += `${GenerateCustomerInfo(customerCode, customerName, newCustomerPointsTotal, pointsEarned)}\n\n`;
    }
    else if (newCustomerPointsTotal) {
      receiptText += `${GenerateCustomerPointsInfo(newCustomerPointsTotal)}\n\n`;
    }
    else {
      receiptText += ' ';
    }

    if (refs && refs.length > 0) {
      const noteRef = refs.find(ref => ref.lineNo === 0);
      if (noteRef && noteRef.transReference) {
        receiptText += GenerateReceiptNote(noteRef.transReference.trim());
      }
    }

    // Filter valid items for cart table (has quantity and price > 0)
    const validItems = logs.filter(log =>
      log.quantity &&
      log.quantity > 0 &&
      log.sellingPrice !== undefined &&
      log.sellingPrice >= 0
    );
    if (validItems.length > 0) {
      receiptText += `${GenerateCartTable(validItems, transType)}\n\n`;
    }

    const total = validItems.reduce((acc, log) => acc + (log.sellingPrice || 0) * (log.quantity || 0), 0);
    const totalStr = financialFmt(total);
    const totalLine = padTo("Total", TABLE_WIDTH - totalStr.length) + totalStr;
    receiptText += [totalLine].join("\n\n");

    // Add prior payments section
    // Filter for payment entries (qty exists, not 0, and price is negative)
    const priorPayments = logs.filter(log =>
      log.quantity &&
      log.quantity !== 0 &&
      log.sellingPrice !== undefined &&
      log.sellingPrice < 0
    );

    if (priorPayments.length > 0) {
      receiptText += "\nPrior Payments:\n";
      receiptText += "Payment      Amount\n";
      receiptText += line(TABLE_WIDTH) + "\n";

      for (const payment of priorPayments) {
        const paymentAmount = Math.abs(payment.sellingPrice || 0);
        receiptText += padTo(payment.styleCode || '', 12) +
          padTo(financialFmt(paymentAmount), 6) + "\n";
      }
      receiptText += "\n";
    }
    else {
      receiptText += "\n\n*** NO PRIOR PAYMENTS ***\n\n";
    }

    // Add current payment
    let currentPaymentTotal = 0;
    if (pays.length > 0) {
      receiptText += "Current Payment:\n";
      receiptText += "Date: " + new Date().toLocaleDateString('en-AU', { day: '2-digit', month: '2-digit', year: 'numeric' }) + "\n";

      for (const pay of pays) {
        if (pay.payAmount !== undefined) {
          currentPaymentTotal += pay.payAmount;
          receiptText += padTo(pay.paymentType || '', 12) +
            padTo(financialFmt(pay.payAmount), 6) + "\n";
        }
      }
      receiptText += "\n";
    }
    console.log("Current payment total", currentPaymentTotal);
    console.log("Amount due", amountDue);
    console.log("Change", change);
    let remainingAmount = 0;
    if (transType == 5) {
      remainingAmount = Math.max(amountDue - currentPaymentTotal, 0);
    }
    else {
      remainingAmount = Math.max(amountDue + change, 0);
    }

    // Add "PAID IN FULL" if remaining amount is less than 0.01
    if (remainingAmount < 0.01) {
      receiptText += "\n" + padTo("*** PAID IN FULL ***", TABLE_WIDTH, false) + "\n";
    }

    receiptText += "\n";

    // Add payment totals
    if (pays.length > 0) {
      let label = "";
      console.log("TransType", transType);
      switch (transType) {
        case 4:
          label = "Layby Deposit Total";
          break;
        case 5:
          label = "Layby Payment";
          break;
        default:
          label = `${rType} Deposit Total`;
      }
      receiptText += `${GeneratePaymentTotals(label, pays, change, remainingAmount, true)}\n\n`;
    }

    if (voucherBalances && voucherBalances.length > 0) {
      receiptText += `${GenerateVoucherBalanceInfo(voucherBalances)}\n\n`;
    }

    if (format.useInfo) {
      receiptText += line(TABLE_WIDTH) + "\n";
      receiptText += centerText(`LAYBY: ${laybyCode} - ${receiptTrans.saleDateTime.toLocaleString().toUpperCase()}`, TABLE_WIDTH) + "\n";
      if (this.staff) {
        receiptText += centerText(`You have been served by ${this.staff.name}`, TABLE_WIDTH) + "\n";
      }
      receiptText += line(TABLE_WIDTH) + "\n\n";
    }

    // Add footer content
    if (format.useFooter) {
      receiptText += centerText(template.footer, TABLE_WIDTH) + "\n\n";
    }

    // Add layby template if needed
    if ((transType === 4 || transType === 5) && this.printWeeksOnLayby === 'T') {
      receiptText += `${template.layby}\n`;
    }

    // Final actions: add text, feed, barcode, and cut
    mainBatch.add_action(new TextAction(receiptText));
    mainBatch.add_action(new BarcodeAction(padBarcodeEAN13(transNo)));

    // Add customer signature section for layby receipts (transaction type 4)
    if (transType === 4) {
      let signatureText = "\n\n\n\n";
      signatureText += line(TABLE_WIDTH) + "\n";
      signatureText += centerText("Customer Signature", TABLE_WIDTH) + "\n";

      mainBatch.add_action(new TextAction(signatureText));
    }

    mainBatch.add_action(new CutAction(CutType.Half));

    this.executeBatch(mainBatch).subscribe(
      res => console.log('Layby receipt printed successfully:', res),
      err => console.error('Error printing layby receipt:', err)
    );
  }

  public async printLaybyReturnReceipt(
    rType: string,
    logs: TranslogDto[],
    pays: TranspayDto[],
    transType: number,
    transNo: string,
    format: SolemateReceiptOptions = SolemateReceiptOptions.default(),
    receiptTrans: ReceiptTransactionDto,
    amountDue: number,
    change: number,
    refs?: Transref[],
    customerCode?: string,
    customerName?: string,
    laybyNumber?: string,
    voucherBalances?: VoucherBalanceInfo[],
    newCustomerPointsTotal?: number,
    pointsEarned?: number
  ): Promise<void> {
    // Check if printing service is available (reuse existing check)
    try {
      const response = await this.checkService().toPromise();
      console.log("Service check passed, proceeding with normal printing");
    } catch (error) {
      console.error('Printer service unavailable, falling back to PDF:', error);
      await this.printPdfReceipt(receiptTrans);
      return;
    }

    console.log("New customer points total", newCustomerPointsTotal);
    console.log("Points earned", pointsEarned);

    const template: ReceiptTemplateDto = await this.printCache.getTemplate();
    let mainBatch: ReceiptBatch = new ReceiptBatch();
    let receiptText = '';

    if (format.useLogo) {
      mainBatch.add_action(new ImageAction(await this.printCache.getLogoPayload()));
    }
    if (format.isReprint) {
      receiptText += "\n\n" + centerText("REPRINT", TABLE_WIDTH) + "\n\n";
    }

    // Find the layby code (first translog with no quantity)
    const laybyLog = logs.find(log => !log.quantity);
    let laybyCode = laybyNumber;


    receiptText += "\n" + centerText("LAYBY RETURN RECEIPT", TABLE_WIDTH) + "\n";

    receiptText += "\n";

    // Add header content
    if (format.useTitle) {
      receiptText += centerText(template.title, TABLE_WIDTH) + "\n\n";
    }

    if (format.useHeader) {
      receiptText += centerText(template.header, TABLE_WIDTH) + "\n\n";
    }

    if (customerName && customerCode) {
      receiptText += `${GenerateCustomerInfo(customerCode, customerName, newCustomerPointsTotal, pointsEarned)}\n\n`;
    }

    if (refs && refs.length > 0) {
      const noteRef = refs.find(ref => ref.lineNo === 0);
      if (noteRef && noteRef.transReference) {
        receiptText += GenerateReceiptNote(noteRef.transReference.trim());
      }
    }

    // Filter valid items for cart table (has quantity and price > 0)
    const validItems = logs.filter(log =>
      log.quantity &&
      log.quantity > 0 &&
      log.sellingPrice !== undefined &&
      log.sellingPrice >= 0
    );
    if (validItems.length > 0) {
      receiptText += `${GenerateCartTable(validItems, transType)}\n\n`;
    }

    // Add prior payments section
    // Filter for payment entries (qty exists, not 0, and price is negative)
    const priorPayments = logs.filter(log =>
      log.quantity &&
      log.quantity !== 0 &&
      log.sellingPrice !== undefined &&
      log.sellingPrice < 0
    );

    if (priorPayments.length > 0) {
      receiptText += "Prior Payments:\n";
      receiptText += "Payment      Amount\n";
      receiptText += line(TABLE_WIDTH) + "\n";

      for (const payment of priorPayments) {
        const paymentAmount = Math.abs(payment.sellingPrice || 0);
        receiptText += padTo(payment.styleCode || '', 12) +
          padTo(financialFmt(paymentAmount), 6) + "\n";
      }
      receiptText += "\n";
    }
    else {
      receiptText += "No prior payments\n";
    }

    // Add current payment
    let currentPaymentTotal = 0;
    if (pays.length > 0) {
      receiptText += "Current Refund:\n";
      receiptText += "Date: " + new Date().toLocaleDateString('en-AU', { day: '2-digit', month: '2-digit', year: 'numeric' }) + "\n";

      for (const pay of pays) {
        if (pay.payAmount !== undefined) {
          currentPaymentTotal += pay.payAmount;
          receiptText += padTo(pay.paymentType || '', 12) +
            padTo(financialFmt(pay.payAmount), 6) + "\n";
        }
      }
      receiptText += "\n";
    }
    let amountPaid = priorPayments.reduce((acc, pay) => acc + (pay.sellingPrice || 0), 0) * -1;
    let amountRefunded = pays.reduce((acc, pay) => acc + (pay.payAmount || 0), 0);
    let refundDifference = amountPaid - amountRefunded;
    console.log("Amount paid", amountPaid);
    console.log("Amount refunded", amountRefunded);
    console.log("Current payment total", currentPaymentTotal);
    console.log("Amount due", amountDue);
    console.log("Change", change);

    receiptText += "\n";

    let label = "Layby Refund Total";

    receiptText += `${GeneratePaymentTotals(label, pays, change)}\n\n`;

    if (voucherBalances && voucherBalances.length > 0) {
      receiptText += `${GenerateVoucherBalanceInfo(voucherBalances)}\n\n`;
    }

    if (refundDifference === 0) {
      receiptText += padTo("*** REFUNDED IN FULL ***", TABLE_WIDTH, false) + "\n";
    }
    else if (refundDifference === amountPaid) {
      receiptText += padTo("*** NO REFUND GIVEN ***", TABLE_WIDTH, false) + "\n";
    }
    else if (refundDifference > 0) {
      receiptText += padTo("*** PARTIALLY REFUNDED ***", TABLE_WIDTH, false) + "\n";
    }

    if (format.useInfo) {
      receiptText += line(TABLE_WIDTH) + "\n";
      receiptText += centerText(`LAYBY: ${laybyCode} - ${receiptTrans.saleDateTime.toLocaleString().toUpperCase()}`, TABLE_WIDTH) + "\n";
      if (this.staff) {
        receiptText += centerText(`You have been served by ${this.staff.name}`, TABLE_WIDTH) + "\n";
      }
      receiptText += line(TABLE_WIDTH) + "\n\n";
    }

    // Add footer content
    if (format.useFooter) {
      receiptText += centerText(template.footer, TABLE_WIDTH) + "\n\n";
    }

    // Add layby template if needed
    if ((transType === 4 || transType === 5) && this.printWeeksOnLayby === 'T') {
      receiptText += `${template.layby}\n`;
    }

    // Final actions: add text, feed, barcode, and cut
    mainBatch.add_action(new TextAction(receiptText));
    mainBatch.add_action(new BarcodeAction(padBarcodeEAN13(transNo)));

    // Add customer signature section for layby receipts (transaction type 4)
    if (transType === 4) {
      let signatureText = "\n\n\n\n";
      signatureText += line(TABLE_WIDTH) + "\n";
      signatureText += centerText("Customer Signature", TABLE_WIDTH) + "\n";

      mainBatch.add_action(new TextAction(signatureText));
    }

    mainBatch.add_action(new CutAction(CutType.Half));

    this.executeBatch(mainBatch).subscribe(
      res => console.log('Layby receipt printed successfully:', res),
      err => console.error('Error printing layby receipt:', err)
    );
  }

  public async printEndOfDayReceipt(endOfDayDTO: EndOfDayDTO, floatData: FloatDto, varianceReason: string, systemTotals?: { systemCash: number, systemEFT: number }): Promise<void> {
    try {
      const response = await this.checkService().toPromise();
      console.log("Service check response:", response);
      console.log("Service check passed, proceeding with normal printing");
    } catch (error) {
      console.error('Printer service unavailable:', error);
      return;
    }

    let mainBatch: ReceiptBatch = new ReceiptBatch();
    let receiptText = '';

    receiptText += "END OF DAY SUMMARY\n";
    receiptText += line(TABLE_WIDTH) + "\n\n";

    receiptText += "FLOAT DETAILS:\n";
    receiptText += line(TABLE_WIDTH) + "\n";
    receiptText += padTo("$100: " + (floatData.hundreds || 0), 20) + padTo("$" + ((floatData.hundreds || 0) * 100).toFixed(2), 10) + "\n";
    receiptText += padTo("$50: " + (floatData.fifties || 0), 20) + padTo("$" + ((floatData.fifties || 0) * 50).toFixed(2), 10) + "\n";
    receiptText += padTo("$20: " + (floatData.twenties || 0), 20) + padTo("$" + ((floatData.twenties || 0) * 20).toFixed(2), 10) + "\n";
    receiptText += padTo("$10: " + (floatData.tens || 0), 20) + padTo("$" + ((floatData.tens || 0) * 10).toFixed(2), 10) + "\n";
    receiptText += padTo("$5: " + (floatData.fives || 0), 20) + padTo("$" + ((floatData.fives || 0) * 5).toFixed(2), 10) + "\n";
    receiptText += padTo("$2: " + (floatData.twos || 0), 20) + padTo("$" + ((floatData.twos || 0) * 2).toFixed(2), 10) + "\n";
    receiptText += padTo("$1: " + (floatData.ones || 0), 20) + padTo("$" + ((floatData.ones || 0) || 0).toFixed(2), 10) + "\n";
    receiptText += padTo("50c: " + (floatData.fiftyCents || 0), 20) + padTo("$" + ((floatData.fiftyCents || 0) * 0.5).toFixed(2), 10) + "\n";
    receiptText += padTo("20c: " + (floatData.twentyCents || 0), 20) + padTo("$" + ((floatData.twentyCents || 0) * 0.2).toFixed(2), 10) + "\n";
    receiptText += padTo("10c: " + (floatData.tenCents || 0), 20) + padTo("$" + ((floatData.tenCents || 0) * 0.1).toFixed(2), 10) + "\n";
    receiptText += padTo("5c: " + (floatData.fiveCents || 0), 20) + padTo("$" + ((floatData.fiveCents || 0) * 0.05).toFixed(2), 10) + "\n";

    const floatTotal = ((floatData.hundreds || 0) * 100) + ((floatData.fifties || 0) * 50) +
      ((floatData.twenties || 0) * 20) + ((floatData.tens || 0) * 10) +
      ((floatData.fives || 0) * 5) + ((floatData.twos || 0) * 2) +
      (floatData.ones || 0) + ((floatData.fiftyCents || 0) * 0.5) +
      ((floatData.twentyCents || 0) * 0.2) + ((floatData.tenCents || 0) * 0.1) +
      ((floatData.fiveCents || 0) * 0.05);

    receiptText += line(TABLE_WIDTH) + "\n";
    receiptText += padTo("FLOAT TOTAL:", 20) + padTo("$" + floatTotal.toFixed(2), 10) + "\n";
    receiptText += line(TABLE_WIDTH) + "\n\n";

    const systemCash = systemTotals && systemTotals.systemCash !== undefined ? systemTotals.systemCash : 0;
    const systemEFT = systemTotals && systemTotals.systemEFT !== undefined ? systemTotals.systemEFT : 0;

    receiptText += "System Totals:\n";
    receiptText += line(TABLE_WIDTH) + "\n";
    receiptText += padTo("System Cash:", 20) + padTo("$" + systemCash.toFixed(2), 10) + "\n";
    receiptText += padTo("System EFTPOS:", 20) + padTo("$" + systemEFT.toFixed(2), 10) + "\n";

    const systemTotal = systemCash + systemEFT;
    receiptText += line(TABLE_WIDTH) + "\n";
    receiptText += padTo("SYSTEM TOTAL:", 20) + padTo("$" + systemTotal.toFixed(2), 10) + "\n";
    receiptText += line(TABLE_WIDTH) + "\n\n";

    receiptText += "Actual Totals:\n";
    receiptText += line(TABLE_WIDTH) + "\n";
    receiptText += padTo("Actual Cash:", 20) + padTo("$" + (endOfDayDTO.actualCash || 0).toFixed(2), 10) + "\n";
    receiptText += padTo("Actual EFTPOS:", 20) + padTo("$" + (endOfDayDTO.actualEFT || 0).toFixed(2), 10) + "\n";

    const actualTotal = (endOfDayDTO.actualCash || 0) + (endOfDayDTO.actualEFT || 0);
    receiptText += line(TABLE_WIDTH) + "\n";
    receiptText += padTo("ACTUAL TOTAL:", 20) + padTo("$" + actualTotal.toFixed(2), 10) + "\n";
    receiptText += line(TABLE_WIDTH) + "\n\n";

    if (varianceReason && varianceReason !== "No Negative Variance") {
      receiptText += "VARIANCE DETAILS:\n";
      receiptText += line(TABLE_WIDTH) + "\n";
      const words = varianceReason.split(' ');
      let currentLine = '';

      for (const word of words) {
        if ((currentLine + word).length > TABLE_WIDTH) {
          receiptText += currentLine + "\n";
          currentLine = word + ' ';
        } else {
          currentLine += word + ' ';
        }
      }
      if (currentLine) {
        receiptText += currentLine + "\n";
      }
      receiptText += line(TABLE_WIDTH) + "\n\n";
    }

    receiptText += padTo("Submitted: " + new Date().toLocaleString(), TABLE_WIDTH, false) + "\n";

    mainBatch.add_action(new TextAction(receiptText));
    mainBatch.add_action(new CutAction(CutType.Half));

    this.executeBatch(mainBatch).subscribe(
      res => console.log('End of day receipt printed successfully:', res),
      err => console.error('Error printing end of day receipt:', err)
    );
  }

  public async printOrderRefundReceipt(
    title: string,
    orderLines: CustOrderLineDTO[],
    orderCode: string,
    customerName: string | null,
    transNo: string,
    pays: TranspayDto[],
    format: SolemateReceiptOptions = SolemateReceiptOptions.default(),
    change?: number,
    clientCode?: string,
    pointsEarned?: number,
    newCustomerPointsTotal?: number
  ): Promise<void> {
    try {
      await this.checkService().toPromise();
      console.log("Service check passed, proceeding with order refund receipt");
    } catch (error) {
      console.error('Printer service unavailable:', error);
      return;
    }

    const template: ReceiptTemplateDto = await this.printCache.getTemplate();
    const mainBatch: ReceiptBatch = new ReceiptBatch();
    let receiptText = '';

    if (format.useLogo) {
      mainBatch.add_action(new ImageAction(await this.printCache.getLogoPayload()));
    }

    if (format.isReprint) {
      receiptText += "\n\n" + centerText("REPRINT", TABLE_WIDTH) + "\n\n";
    }

    if (format.useTitle) {
      receiptText += centerText(template.title, TABLE_WIDTH) + "\n\n";
    }
    if (format.useHeader) {
      receiptText += centerText(template.header, TABLE_WIDTH) + "\n\n";
    }

    receiptText += "\n" + centerText(title.toUpperCase(), TABLE_WIDTH) + "\n\n";

    receiptText += line(TABLE_WIDTH);
    receiptText += centerText(`Order #: ${orderCode}`, TABLE_WIDTH) + "\n";

    if (customerName && clientCode) {
      receiptText += `${GenerateCustomerInfo(clientCode, customerName, newCustomerPointsTotal, pointsEarned)}\n`;
    }

    if (orderLines && orderLines.length > 0) {
      receiptText += "Style        Colour Size  Qty\n";
      receiptText += line(TABLE_WIDTH) + "\n";

      for (const item of orderLines) {
        receiptText += padTo(item.styleCode || '', 13) +
          padTo(item.colourCode || '', 7) +
          padTo(item.sizeCode || '', 6) +
          padTo(String(item.quantity || ''), 6) + "\n";
      }
      receiptText += line(TABLE_WIDTH) + "\n\n";
    }

    if (pays && pays.length > 0) {

      receiptText += `${GeneratePaymentTotals("Total Deposit Refunded", pays, change)}\n\n`;
    }

    if (format.useInfo) {
      receiptText += line(TABLE_WIDTH) + "\n";
      receiptText += centerText(`REFUND: ${transNo} - ${new Date().toLocaleString().toUpperCase()}`, TABLE_WIDTH) + "\n";

      if (this.staff) {
        receiptText += centerText(`You have been served by ${this.staff.name}`, TABLE_WIDTH) + "\n";
      }
      receiptText += line(TABLE_WIDTH) + "\n\n";
    }

    if (format.useFooter) {
      receiptText += centerText(template.footer, TABLE_WIDTH) + "\n\n";
    }

    mainBatch.add_action(new TextAction(receiptText));
    mainBatch.add_action(new BarcodeAction(padBarcodeEAN13(transNo)));
    mainBatch.add_action(new CutAction(CutType.Half));

    this.executeBatch(mainBatch).subscribe(
      res => console.log('Order refund receipt printed successfully:', res),
      err => console.error('Error printing order refund receipt:', err)
    );
  }

  public async printQuoteReceipt(
    logs: TranslogDto[],
    transType: number,
    transNo: string,
    format: SolemateReceiptOptions = SolemateReceiptOptions.default(),
    receiptTrans: ReceiptTransactionDto,
    clientName?: string,
    clientCode?: string,
    quoteNumber?: string
  ): Promise<void> {
    // Check if printing service is available
    try {
      const response = await this.checkService().toPromise();
      console.log("Service check passed, proceeding with normal printing");
    } catch (error) {
      console.error('Printer service unavailable, falling back to PDF:', error);
      console.log("DOING PDF PRINTING");
      await this.printPdfReceipt(receiptTrans);
      return;
    }

    const template: ReceiptTemplateDto = await this.printCache.getTemplate();
    let mainBatch: ReceiptBatch = new ReceiptBatch();
    let receiptText = '';

    // Add logo if needed
    if (format.useLogo) {
      mainBatch.add_action(new ImageAction(await this.printCache.getLogoPayload()));
    }

    // Add reprint text if needed
    if (format.isReprint) {
      receiptText += "\n\n" + centerText("REPRINT", TABLE_WIDTH);
    }

    // Add header content
    if (format.useTitle) {
      receiptText += "\n\n" + centerText(template.title, TABLE_WIDTH) + "\n\n";
    }

    if (format.useHeader) {
      receiptText += centerText(template.header, TABLE_WIDTH) + "\n\n";
    }

    // Add QUOTE header
    receiptText += centerText("QUOTE", TABLE_WIDTH) + "\n\n";

    // Add quote number if provided
    if (quoteNumber) {
      receiptText += centerText(`Quote #: ${quoteNumber}`, TABLE_WIDTH) + "\n\n";
    }

    // Add customer info if provided
    if (clientName && clientCode) {
      receiptText += `${GenerateCustomerInfo(clientCode, clientName)}\n\n`;
    }

    // Filter out "Reason" translogs for the cart table
    const nonReasonLogs = logs.filter(log => log.styleCode !== "Reason");

    // Add cart table
    if (nonReasonLogs.length > 0) {
      receiptText += `${GenerateCartTable(logs, transType)}\n\n`;
    }

    // Calculate and display total
    const total = logs.reduce((acc, log) => acc + (log.sellingPrice || 0), 0);
    const totalStr = centerText("Quote Total: " + financialFmt(total), TABLE_WIDTH) + "\n\n";
    receiptText += totalStr;

    // Add transaction info
    if (format.useInfo) {
      receiptText += line(TABLE_WIDTH) + "\n";
      receiptText += centerText(`QUOTE: ${transNo} - ${new Date().toLocaleString().toUpperCase()}`, TABLE_WIDTH) + "\n";
      if (this.staff) {
        receiptText += centerText(`You have been served by ${this.staff.name}`, TABLE_WIDTH) + "\n";
      }
      receiptText += line(TABLE_WIDTH) + "\n\n";
    }

    // Add footer content
    if (format.useFooter) {
      receiptText += centerText(template.footer, TABLE_WIDTH) + "\n\n";
    }

    // Final actions: add text, barcode, and cut
    mainBatch.add_action(new TextAction(receiptText));
    mainBatch.add_action(new BarcodeAction(padBarcodeEAN13(transNo)));
    mainBatch.add_action(new CutAction(CutType.Half));

    this.executeBatch(mainBatch).subscribe(
      res => console.log('Quote receipt printed successfully:', res),
      err => console.error('Error printing quote receipt:', err)
    );
  }

  /**
   * Prints a petty cash receipt with basic transaction details
   */
  public async printPettyCashReceipt(amount: number, reason: string, staffName?: string): Promise<void> {
    try {
      await this.checkService().toPromise();
      let mainBatch: ReceiptBatch = new ReceiptBatch();
      let receiptText = '';

      // Header

      receiptText += "\n" + centerText("STORE RECEIPT", TABLE_WIDTH) + "\n";

      receiptText += "\n" + centerText("PETTY CASH", TABLE_WIDTH) + "\n\n";

      // Date and Time
      receiptText += `Date: ${new Date().toLocaleDateString()}\n`;
      receiptText += `Time: ${new Date().toLocaleTimeString()}\n\n`;

      // Amount
      receiptText += `Amount: $${amount.toFixed(2)}\n\n`;

      // Reason
      receiptText += "Reason:\n";
      receiptText += `${reason}\n\n`;

      if (staffName && staffName.length > 0) {
        receiptText += `Staff: ${staffName}\n\n\n`;
      }

      // Add line
      receiptText += line(TABLE_WIDTH) + "\n";

      // Add actions to batch
      mainBatch.add_action(new TextAction(receiptText));
      mainBatch.add_action(new CutAction(CutType.Half));

      // Execute batch
      await this.executeBatch(mainBatch).toPromise();
      console.log('Petty cash receipt printed successfully');
    } catch (error) {
      console.error('Error printing petty cash receipt:', error);
    }
  }

  public async printTestReceipt(printerToTest: string | null, printerWidth: number): Promise<void> {
    if (!printerToTest) {
      console.warn('Test print requested but no printer specified. Aborting test print.');
      // Or, throw an error to be caught by the UI to inform the user
      throw new Error('No printer selected in the modal to test.');
    }

    try {
      await this.checkService().toPromise();
      console.log("Print service check passed for test print.");
    } catch (error) {
      console.error('Printer service unavailable for test print:', error);
      throw error; // Re-throw to be caught by the caller to inform the user
    }

    const template: ReceiptTemplateDto = await this.printCache.getTemplate();
    const testBatch: ReceiptBatch = new ReceiptBatch();
    let receiptText = '';

    try {
        const logoPayload = await this.printCache.getLogoPayload();
        if (logoPayload) {
            testBatch.add_action(new ImageAction(logoPayload));
        }
    } catch (e) {
        console.warn("Could not load logo for test print:", e);
    }

    if (template && template.title) {
        receiptText += centerText("\n\n" + template.title, printerWidth) + "\n";
    }
    if (template && template.header) {
        receiptText += centerText(template.header, printerWidth) + "\n\n";
    }

    // Add a line to test the width
    receiptText += line(printerWidth) + "\n";

    receiptText += centerText("--- TEST PRINT ---", printerWidth) + "\n\n";
    receiptText += centerText(`Date: ${new Date().toLocaleDateString()}`, printerWidth) + "\n";
    receiptText += centerText(`Time: ${new Date().toLocaleTimeString()}`, printerWidth) + "\n\n";
    receiptText += centerText(`Printer: ${printerToTest}`, printerWidth) + "\n\n";

    if (this.staff && this.staff.name) {
        receiptText += centerText(`Test initiated by: ${this.staff.name}`, printerWidth) + "\n";
    }
    receiptText += "\n";

    if (template && template.footer) {
        receiptText += centerText(template.footer, printerWidth) + "\n";
    }

    testBatch.add_action(new TextAction(receiptText));
    testBatch.add_action(new FeedAction(1));
    testBatch.add_action(new CutAction(CutType.Half));

    try {
        const result = await this.executeBatch(testBatch, printerToTest).toPromise();
        console.log('Test print success:', result);
    } catch (error) {
        console.error('Error executing test print batch:', error);
        throw error; // Re-throw to be caught by the caller
    }
  }

  private handleError(error: HttpErrorResponse) {
    console.error("Error in PrintingService:", error);
    let errorMsg = 'An error occurred';
    if (error.error instanceof ErrorEvent) {
      errorMsg = `Client-side error: ${error.error.message}`;
    } else {
      errorMsg = `Server-side error: ${error.status} - ${error.message}`;
    }
    return throwError(() => new Error(errorMsg));
  }

  triggerPrinterRefresh() {
    this.refreshPrintersSubject.next();
  }
}

export interface PrinterListResponse {
  printers: string[];
}

export function padBarcodeEAN13(bar: string) {
  return "0".repeat(Math.max(12 - bar.length, 0)) + bar;
}

export class SolemateReceiptOptions {
  public useLogo: boolean;
  public useTitle: boolean;
  public useHeader: boolean;
  public useFooter: boolean;
  public useInfo: boolean;
  public isReprint: boolean;

  public static default(): SolemateReceiptOptions {
    return {
      useLogo: true,
      useFooter: true,
      useHeader: true,
      useTitle: true,
      useInfo: true,
      isReprint: false,
    };
  }
}

let TABLE_WIDTH: number = 48;
let fmt: Intl.NumberFormat = new Intl.NumberFormat('en-us', { minimumFractionDigits: 2 });
let financialFmt = (value: number) => {
  return value === null || value === undefined || isNaN(value) ? '' : `$${fmt.format(value)}`;
};

function GeneratePaymentTotals(type: string, payments: TranspayDto[], change?: number, remainingAmount?: number, isLayby?: boolean, customerAccountDetails?: { code: string, name: string, newBalance?: number } | null) {
  let totalAmount = payments.reduce((sum, next) => sum + next.payAmount, 0);
  let total = `${toTitleCase(type)}: ${financialFmt(totalAmount)}`;

  // Create a copy of payments to modify if needed
  let adjustedPayments = [...payments];

  // Adjust cash payment by subtracting change if both exist
  if (change !== undefined && change > 0) {
    const cashIndex = adjustedPayments.findIndex(pay =>
      pay.paymentType.toLowerCase() === 'cash');

    if (cashIndex !== -1) {
      const cashPayment = { ...adjustedPayments[cashIndex] };
      cashPayment.payAmount = cashPayment.payAmount + change;
      adjustedPayments[cashIndex] = cashPayment;
    }
  }

  let remainBalance = "";
  if (isLayby) {
    remainBalance = `Remaining Balance: ${financialFmt(remainingAmount)}`;
  }

  let paymentLines: string[] = adjustedPayments.map(pay => {
    let paymentLabel = '';
    if (pay.paymentType === 'Customer Account' && customerAccountDetails) {
      paymentLabel = `Customer Account ${customerAccountDetails.name} - ${customerAccountDetails.code}`;
    } else {
      paymentLabel = toTitleCase(pay.paymentType);
    }
    return `${paymentLabel}: ${financialFmt(pay.payAmount)}`;
  });

  let changeText = change !== undefined && cashRound(change) > 0.01 ? `Change: ${financialFmt(cashRound(change))}` : "";

  let result = [total, ...paymentLines];
  if (changeText) {
    result.push(changeText);
  }
  if (remainBalance) {
    result.push(remainBalance);
  }

  if (customerAccountDetails) {
    if (customerAccountDetails.newBalance) {
      let newBalanceText = `New Account Balance: ${financialFmt(customerAccountDetails.newBalance)}`;
      result.push(newBalanceText);
    }
  }

  return result.map(v => padTo(v, TABLE_WIDTH, false)).join("\n");
}

function cashRound(value: number): number {
  const roundedValue = Math.round(value * 100) / 100;

  const dollars = Math.floor(roundedValue);
  const cents = Math.round((roundedValue - dollars) * 100);

  if (cents % 5 === 0) return roundedValue;

  const nearestLower = Math.floor(cents / 5) * 5;
  const nearestUpper = Math.ceil(cents / 5) * 5;

  const newCents = (cents % 5 < 3) ? nearestLower : nearestUpper;
  return dollars + (newCents / 100);
}

function GenerateCustomerInfo(customerCode: string, customerName: string, newCustomerPointsTotal?: number, pointsEarned?: number) {
  const customerLine = `Customer: ${customerName}`;
  const codeLine = customerCode;

  return `${line(TABLE_WIDTH)}\n${centerText(customerLine, TABLE_WIDTH)}\n${centerText(codeLine, TABLE_WIDTH)}${centerText(newCustomerPointsTotal !== undefined ? `\nTotal Points: ${newCustomerPointsTotal}` : "", TABLE_WIDTH)}${centerText(pointsEarned !== undefined ? `\nPoints Earned: ${pointsEarned}` : "", TABLE_WIDTH)}\n${line(TABLE_WIDTH)}\n`;
}

function centerText(text: string, width: number): string {
  // If text is empty, return empty string
  if (!text) return '';

  // If text contains newlines already, center each line individually
  if (text.includes('\n')) {
    return text.split('\n').map(line => centerText(line, width)).join('\n');
  }

  // If text is shorter than width, center it
  if (text.length <= width) {
    const paddingTotal = width - text.length;
    const paddingLeft = Math.floor(paddingTotal / 2);
    return " ".repeat(paddingLeft) + text + " ".repeat(paddingTotal - paddingLeft);
  }

  // If text is longer than width, we need to wrap it
  // For simplicity in receipts, we'll just return the original text
  // You could implement line wrapping here if needed
  return text;
}

function GenerateCartTable(
  logs: TranslogDto[],
  transType: number,
  transRefs?: Transref[]
): string {
  if (logs.length > 0 && transType === 19) {
    return "Account Number: " + logs[0].styleCode + "\n"; // todo: add account name here.
  }

  var discounted = logs.some(function (l) {
    return l.nettSelling && l.sellingPrice < l.nettSelling;
  });

  var head = "Style        Colour Size  Qty   " + (discounted ? "Was   Now" : "Price");

  var reasonsByLine: { [lineNo: number]: string[] } = {};

  if (transRefs && transRefs.length) {
    var reasonLogs = logs.filter(function (l) {
      return l.styleCode === "Reason";
    });

    for (var i = 0; i < reasonLogs.length; i++) {
      var rlog = reasonLogs[i];
      if (rlog.lineNo != null) {
        var ref = transRefs.filter(function (r) {
          return r.lineNo === rlog.lineNo;
        })[0];
        if (ref && ref.transReference) {
          var itemLine = rlog.lineNo - 1;
          if (!reasonsByLine[itemLine]) {
            reasonsByLine[itemLine] = [];
          }
          reasonsByLine[itemLine].push(ref.transReference);
        }
      }
    }
  }

  var logTotal = 0;
  var lines: string[] = [];

  for (var j = 0; j < logs.length; j++) {
    var log = logs[j];
    logTotal += log.sellingPrice * log.quantity;

    if (log.styleCode === "Reason") continue;

    var qtyStr = log.quantity != null ? log.quantity.toString() : "";

    var nettStr = "";
    if (discounted && log.nettSelling != null && log.nettSelling > log.sellingPrice) {
      nettStr = financialFmt(log.nettSelling);
    }

    lines.push(
      padTo(log.styleCode, 13) +
      padTo(log.colourCode, 7) +
      padTo(log.sizeCode, 6) +
      padTo(qtyStr, 6) +
      (discounted ? padTo(nettStr, 6) : "") +
      padTo(financialFmt(log.sellingPrice), 6)
    );
    
    
    // create a new var which if the stock description exists and the colour descriptions exists add them together
    let stockAndColourDescription = "";
    if (log.stockDescription != null && log.stockDescription.length > 0) {
      stockAndColourDescription = log.stockDescription;
    }
    if (log.colourDescription != null && log.colourDescription.length > 0) {
      stockAndColourDescription += " " + log.colourDescription;
    }

    if (stockAndColourDescription != null && stockAndColourDescription.length > 0) {
      // check the length of the description, if its greater than the table width, then we need to wrap it.
      if (stockAndColourDescription.length > TABLE_WIDTH) {
        let wrappedDescription = stockAndColourDescription.match(
          new RegExp(`(.{1,${TABLE_WIDTH - 1}})(\\s+|$)`, 'g')
        ).map(line => padTo(' ' + line.trim(), TABLE_WIDTH)).join('\n');

        lines.push(padTo(wrappedDescription, TABLE_WIDTH)); //pad to might be unnecessary here.
      } else {
        lines.push(padTo(" " +stockAndColourDescription, TABLE_WIDTH)); //pad to might be unnecessary here.
        lines.push('');
      }
    }

    if (log.lineNo != null && reasonsByLine[log.lineNo]) {
      lines.push(" Details: " + reasonsByLine[log.lineNo].join(", "));
    }
  }

  var separator = line(TABLE_WIDTH);

  return [head, separator].concat(lines).concat([separator]).join("\n");
}

function padTo(val: string, to: number, after = true) {
  if (val === null || val === undefined) val = "";
  let padding = Math.max(to - val.length, 0);
  return after ? val + " ".repeat(padding) : " ".repeat(padding) + val;
}

function toTitleCase(str: string): string {
  return str.replace(/\w\S*/g, text => text.charAt(0).toUpperCase() + text.substring(1).toLowerCase());
}

function line(length: number) {
  return "-".repeat(length);
}

function GenerateCustomerPointsInfo(newCustomerPointsTotal: number): string {
  if (newCustomerPointsTotal !== -1) {
    return `New Points Total: ${newCustomerPointsTotal}`;
  }
  return "";
}

function GenerateVoucherBalanceInfo(voucherBalances: VoucherBalanceInfo[]): string {
  let lines: string[] = [];
  for (const voucher of voucherBalances) {
    let typeLabel = voucher.type === 'giftcard' ? 'Gift card' : 'Credit Note';
    let maskedVoucherNumber = voucher.number; // Default to full number
    if (voucher.number && voucher.number.length > 7) {
      const numHidden = voucher.number.length - 7; // First 3 + Last 4 = 7
      const asterisks = '*'.repeat(numHidden);
      maskedVoucherNumber = `${voucher.number.slice(0, 3)}${asterisks}${voucher.number.slice(-4)}`;
    }
    lines.push(`${typeLabel} ${maskedVoucherNumber} - New amount ${financialFmt(voucher.balance)}`);
  }
  return lines.map(line => padTo(line, TABLE_WIDTH, true)).join("\n");
}

function GenerateReceiptNote(note: string): string {
  const noteText = `Note: ${note}`;
  return `${line(TABLE_WIDTH)}\n${centerText(noteText, TABLE_WIDTH)}\n${line(TABLE_WIDTH)}\n`;
}
