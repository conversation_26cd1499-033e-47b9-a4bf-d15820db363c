using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application;
using SoleMatrixPOS.Application.Identity;

namespace SoleMatrixPOS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = "Administrator")]
    public class ManageIdentitiesController : ControllerBase
    {
        private readonly IMediator _mediator;

        public ManageIdentitiesController(IMediator mediator)
        {
            _mediator = mediator;
        }
    }
}
