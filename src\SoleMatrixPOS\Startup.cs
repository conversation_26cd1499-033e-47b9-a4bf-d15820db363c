using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json.Serialization;

using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Localization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SoleMatrixPOS.Application;
using SoleMatrixPOS.Application.Float.Commands;
using SoleMatrixPOS.Application.Float.Validations;
using SoleMatrixPOS.Application.HouseKeeping.Queries;
using SoleMatrixPOS.Application.Infrastructure;
using SoleMatrixPOS.Application.MediatrPipelines;
using SoleMatrixPOS.Application.Sample.Queries;
using SoleMatrixPOS.Auth;
using SoleMatrixPOS.Dal;
using SoleMatrixPOS.Domain;
using SoleMatrixPOS.Filters;
using SoleMatrixPOS.Infrastructure;
using SoleMatrixPOS.Middleware;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Microsoft.EntityFrameworkCore.Infrastructure;
using SoleMatrixPOS.Domain.Identity.Database;
using Microsoft.EntityFrameworkCore;
using SoleMatrixPOS.Domain.Identity.Service;
using SoleMatrixPOS.Domain.Identity.Models;
using SoleMatrixPOS.Identity.Interface;
using SoleMatrixPOS.Identity.Models;
using SoleMatrixPOS.Domain.Service;
using SoleMatrixPOS.Domain.Interfaces;
using SoleMatrixPOS.Email;
using System.Linq;
using SoleMatrixPOS.Dal.Repositories.DapperRepositories;
using AutoMapper;
using System.Reflection;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection.Extensions;
using System.Threading.Tasks;
using SoleMatrixPOS.Identity;
using SoleMatrixPOS.Application.Transaction;
using SoleMatrixPOS.Application.GiftVoucher;
using SoleMatrixPOS.Application.Service;
using Microsoft.AspNetCore.HttpOverrides;

namespace SoleMatrixPOS
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
			// TODO Why the hell are we using an example class here? Just get the assembly through other means.
			services.AddMediatR(cfg => cfg.RegisterServicesFromAssemblyContaining<GetSysStatusQueryHandler>());
			services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestValidationBehavior<,>));
            services.AddValidationDependencies();
			services.AddControllersWithViews().AddNewtonsoftJson(
				options =>
				{
					// Optional: Configure Newtonsoft.Json options here
					options.SerializerSettings.DateFormatHandling = DateFormatHandling.IsoDateFormat;
					options.SerializerSettings.NullValueHandling = NullValueHandling.Include;
				}
			);

			AddMapperDependencies(services);

			AddDapperDependencies(services);

			services.AddHttpClient();


			// TODO: remove when JWT handling is done on the frontend
			//services.AddAuthentication(
			//	options => {
			//		options.DefaultAuthenticateScheme = "FallThrough";
			//		options.DefaultChallengeScheme = "FallThrough";
			//	})
			//	.AddScheme<AuthenticationSchemeOptions, FallThroughAuthenticationHandler>("FallThrough", null);


			// TODO: uncomment when JWT handling is done on the frontend

			// If connection strings section is empty, make it clear and return from this function
			// When build + nswag takes place, it runs startup.cs
			// At this point, appsettings will be non-existent
			

			string jwtPrivKey = Configuration.GetValue<string>("Jwt:Key");
			string jwtIssuer = Configuration.GetValue<string>("Jwt:Issuer");

			services.AddAuthentication(options =>
			{
				options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
				options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
				options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
			})
			.AddJwtBearer(options =>
			{
				options.TokenValidationParameters = new TokenValidationParameters
				{
					ValidateIssuer = true,
					ValidateAudience = true,
					ValidateLifetime = true,
					ValidateIssuerSigningKey = true,
					ValidIssuer = Configuration["Jwt:Issuer"],
					ValidAudience = Configuration["Jwt:Issuer"],
					IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(Configuration["Jwt:Key"]))
				};
			});

			//services.AddAuthentication(options =>
			//{
			//	options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
			//	options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
			//	options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
			//}).AddJwtBearer(configureOptions =>
			//{
			//	configureOptions.Events = new JwtBearerEvents()
			//	{
			//		OnForbidden = msg =>
			//		{
			//			Console.WriteLine($"Error on auth: {msg}");
			//			return Task.CompletedTask;
			//		}
			//	};
			//	configureOptions.ClaimsIssuer = jwtOptions["Issuer"];
			//	configureOptions.TokenValidationParameters = new TokenValidationParameters
			//	{
			//		ValidateIssuer = true,
			//		ValidateAudience = true,
			//		ValidIssuer = jwtOptions["Issuer"],
			//		ValidAudience = jwtOptions["Audience"],
			//		ValidateIssuerSigningKey = true,
			//		IssuerSigningKey = accessKey,
			//		RequireExpirationTime = true,
			//		ValidateLifetime = true,
			//		ClockSkew = TimeSpan.Zero
			//	};
			//});


			// Add identity database context (SQLITE)
			string identityDb = Configuration.GetConnectionString("IdentityConnectionString");
			services.AddDbContext<TillIdentityDbContext>(
				options =>
				{
					options.UseSqlite(identityDb);
				}
			);


			services.Configure<RequestLocalizationOptions>(options =>
            {
                options.DefaultRequestCulture = new RequestCulture("en-AU");
            });

			services.AddMvc(options =>
				{
					options.Filters.Add<CurrentStaffCodeFilter>();
					options.Filters.Add<CurrentTimeFilter>();
				})
				.AddJsonOptions(o => o.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter()));


            // In production, the Angular files will be served from this directory
            services.AddSpaStaticFiles(configuration => { configuration.RootPath = "wwwroot"; });

            services.AddOpenApiDocument(doc =>
            {
                doc.Title = "SoleMate POS";
                doc.Description = "API for SoleMate's POS Software";
            });

			// TODO: review seeding for identity framework 
			//services.AddSingleton<SeedUsers>();

			//// if config contains some PosRegistrations to seed, register them
			//var seedRegistrations =
			//    Configuration.GetSection("SeedUserRegistrations")?.Get<IEnumerable<SeedIdentityPosRegistration>>()
			//    ?? new List<SeedIdentityPosRegistration>();
			//services.AddSingleton<IEnumerable<SeedIdentityPosRegistration>>(seedRegistrations);

			//services.AddSingleton<SeedRegistrations>();
			// services.AddScoped<PosRegistrationContext>();

			if (!Configuration.GetSection("ConnectionStrings").Exists())
			{
				Console.WriteLine("WARNING: NO CONNECTION STRINGS SUPPLIED. THIS IS NOW RUNNING IN BUILD ONLY MODE. IF RUNTIME IS EXPECTED, ENSURE APPSETTINGS IS CONFIGURED CORRECTLY");
				return;
			}


			// ----- IDENTITY -----
			services.AddHttpContextAccessor();

			services.ConfigureOptions<ConfigureEmailPasswordResetTokenProviderOptions>();

			services.AddIdentity<RegisteredTill, TillRole>(
				options =>
				{
					options.Tokens.PasswordResetTokenProvider = TokenOptions.DefaultEmailProvider;
				}
			)
			.AddDefaultTokenProviders()
			// Removed in favour of default email token provider
			// .AddTokenProvider<EmailPasswordResetTokenProvider<RegisteredTill>>("PasswordResetProvider")
			.AddEntityFrameworkStores<TillIdentityDbContext>();

			services.AddTransient<ClientCertMiddleware>();
            services.AddTransient<AuthMiddleware>();
            services.AddTransient<ValidationExceptionMiddleware>();
			services.AddScoped<TokenService>();
			services.AddTransient<RegisteredTillService>();

			services.AddSingleton<TokenValidationParameters>(
				new TokenValidationParameters
				{
					ValidateIssuer = true,
					ValidateAudience = true,
					ValidIssuer = Configuration.GetValue<string>("JWT:Issuer"),
					ValidAudience = Configuration.GetValue<string>("JWT:Issuer"),
					ValidateIssuerSigningKey = true,
					IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(Configuration.GetValue<string>("JWT:Key"))),
					RequireExpirationTime = true,
					ValidateLifetime = true,
					ClockSkew = TimeSpan.Zero
				}
			);

			// per-request context objects
			// TODO: refactor


			services.AddScoped<StaffCodeContext>();
			services.AddScoped<StoreTimeContext>();
			services.AddScoped<IBarcodeService, BarcodeService>();

			services.AddScoped<ILinklyService, LinklyService>();
			services.AddScoped<ITyroService, TyroService>();


			// Postmark email as an abstracted service
			services.AddScoped<IEmailService, PostmarkEmailService>();

			// Admin auth service
			services.AddScoped<IAdminAuthService, AdminAuthService>();

			services.AddScoped<IGiftVoucherService, GiftVoucherService>();
			services.AddScoped<ITransactionService, TransactionService>();

			

		}

		public void AddMapperDependencies(IServiceCollection services)
		{
			var assemblyNames = Assembly.GetExecutingAssembly().GetReferencedAssemblies().ToList();

			// Add the main executed assembly as well
			assemblyNames.Add(Assembly.GetExecutingAssembly().GetName());

			// Load types from referenced assemblies that are AutoMapper profiles
			var assembliesTypes = assemblyNames
				.SelectMany(an => Assembly.Load(an).GetTypes())
				.Where(p => typeof(Profile).IsAssignableFrom(p) && p.IsPublic && !p.IsAbstract)
				.Distinct()
				.ToList();

			var autoMapperProfiles = assembliesTypes
				.Select(p => (Profile)Activator.CreateInstance(p))
				.ToList();

			try
			{
				// Register MapperConfiguration as a singleton
				services.AddSingleton(provider =>
				{
					var config = new MapperConfiguration(cfg =>
					{
						foreach (var profile in autoMapperProfiles)
						{
							cfg.AddProfile(profile);
						}
					});
					return config;
				});

				// Register IMapper to use the singleton MapperConfiguration
				services.AddScoped<IMapper>(provider =>
				{
					var config = provider.GetRequiredService<MapperConfiguration>();
					return config.CreateMapper();
				});
			}
			catch
			{
				// Handle any error here
				// Typically, you could log the error for later handling.
			}
		}

		public void AddDapperDependencies(IServiceCollection services)
		{
			// Database contexts etc
			services.AddScoped<SmPosConnectionFactory>();
			services.AddScoped<SmSysConnectionFactory>();
			services.AddScoped<SmateConnectionFactory>();

			// register connections
			services.AddScoped<SmPosContext>();
			services.AddScoped<SmSysContext>();
			services.AddScoped<SmateContext>();

			var assemblies = Assembly.GetExecutingAssembly().GetReferencedAssemblies().Where(name => name.Name.StartsWith("SoleMatrixPOS")).Select(name => Assembly.Load(name));

			foreach (var assembly in assemblies)
			{
				var types = assembly.GetTypes()
					.Where(t => t.Name.EndsWith("Repository"));

				foreach (var type in types)
				{
					var interfaces = type.GetInterfaces();
					foreach (var iface in interfaces)
					{
						services.AddTransient(iface, type);  // Or AddScoped/AddSingleton depending on the lifetime
					}
				}
			}
		}


		/// <summary>
		/// additional autofac DI configuration - as per https://autofaccn.readthedocs.io/en/latest/integration/aspnetcore.html
		/// </summary>
		/// <param name="builder"></param>


		// This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
		public async void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILogger<Startup> log)
		{
			log.LogInformation("Starting Environment: {Environment}", env.EnvironmentName);
			if (env.IsDevelopment())
			{
				app.UseDeveloperExceptionPage();
				app.UseOpenApi();
				app.UseSwaggerUi();
			}
			else
			{
				app.UseExceptionHandler("/Error");
				// The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
				app.UseHsts();
			}

			app.UseForwardedHeaders(new ForwardedHeadersOptions
			{
				ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto | ForwardedHeaders.XForwardedHost
			});

			app.UseHttpsRedirection();
			app.UseRequestLocalization();

			app.UseMiddleware<ValidationExceptionMiddleware>();
			app.UseMiddleware<ClientCertMiddleware>();
			app.UseStaticFiles();

			app.UseRouting();

			app.UseAuthentication();
			app.UseAuthorization();

			app.UseEndpoints(
				endpoints =>
				{
					endpoints.MapControllerRoute(
						name: "default",
						pattern: "{controller}/{action=Index}/{id?}"
					);
				}
			);

			// this middleware requires user to be logged in before we will deliver any SPA files
			// position of this line is important - after MVC so we can use mvc routes to login, but before UseSpa and SpaStaticFiles
			// mvc controllers should be configured with appropriate [Authorized] attributes

			// TODO: this was deprecated after 2.2 -> 6 upgrade
			// app.UseMiddleware<AuthMiddleware>();

            app.UseSpaStaticFiles();
            app.UseSpa(spa =>
            {
                // To learn more about options for serving an Angular SPA from ASP.NET Core,
                // see https://go.microsoft.com/fwlink/?linkid=864501

                spa.Options.SourcePath = "ClientApp";

                if (env.IsDevelopment())
                {
                    spa.UseProxyToSpaDevelopmentServer("http://localhost:4200");
                }
            });

			// Seed initial admin user
			// Grab from configuration
			using (var seedScope = app.ApplicationServices.CreateScope())
			{
				var provider = seedScope.ServiceProvider;
				await SeedRoles(provider, log);
				await SeedAdminUser(provider, log);
			}
				
			
			// TODO: review changes to identity framework setup
			// ensure db is migrated
			// db migration should be moved out of the webapp before we do any scaling out to multiple web instances
			// idContext.Database.Migrate();

			// seed initial users
			// SeedUsers.Seed(userManager, roleManager).GetAwaiter().GetResult();

			// seed user registrations
			// seedRegistrations.SeedPosRegistrations().GetAwaiter().GetResult();



		}

		private static async Task SeedRoles(IServiceProvider provider, ILogger logger)
		{
			// Start by getting each role name
			RoleManager<TillRole> roleManager = provider.GetService<RoleManager<TillRole>>();

			foreach(RoleDefinition def in RoleDefinitions.Definitions)
			{
				if (await roleManager.FindByNameAsync(def.RoleName) == null)
				{
					var result = await roleManager.CreateAsync(new TillRole(def.RoleName));
					if (!result.Succeeded)
					{
						// Log error
						logger.LogError($"Failed to seed role <{def.RoleName}>");
					}
				}
			}
		}

		private static async Task SeedAdminUser(IServiceProvider provider, ILogger logger)
		{
			var authService = provider.GetService<IAdminAuthService>();

			var config = provider.GetService<IConfiguration>();

			var admin = config.GetSection("AdminSeed");

			if (admin.Exists())
			{
				// Attempt to create the user
				var res = await authService.RegisterUser(new RegisteredTill()
				{
					Email = admin["Email"],
					Id = admin["Id"],
					UserName = admin["Id"]
				}, RoleDefinitions.ROLE_ADMIN);

				if (!res.Succeeded)
				{
					if (res.Reason == AdminAuthResponseReason.TillExists)
					{
						if (!admin.GetValue<bool>("ResendResetEmail"))
						{
							// User was already seeded
							// ... and no additional reset required
							return;
						}
					}

					else
					{
						// Some potentially unexpected issue occurred
						logger.LogError($"Error when seeding initial user: {res.Reason} | {res.Message}"); 
					}
						
				}

				// POST condition: either creation was successful, or
				// ... additional reset required

				res = await authService.ResetPasswordViaEmail(admin["Id"]);

				if (!res.Succeeded)
				{
					logger.LogError($"Error when sending initial user password reset email: {res.Reason} | {res.Message}");
				}

			}
			
		}


    }
}
