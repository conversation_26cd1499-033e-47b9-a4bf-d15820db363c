import { Component, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { EndOfDayCashUpComponent } from '../end-of-day-cash-up/end-of-day-cash-up.component';

@Component({
  selector: 'app-end-of-day-variance',
  templateUrl: './end-of-day-variance.component.html',
  styleUrls: ['./end-of-day-variance.component.scss']
})
export class EndOfDayVarianceComponent implements OnInit {
  VarianceReason: string = '';
  parent: EndOfDayCashUpComponent; // Reference to the parent component

  constructor(public activeModal: NgbActiveModal) {}

  ngOnInit(): void {}

  submitReason(): void {
    console.log('Submitted reason:', this.VarianceReason);
    // Pass the reason back to the parent component and close the modal
    this.activeModal.close(this.VarianceReason);
  }

  onCloseModal(): void {
    this.activeModal.dismiss();
  }
}
