<h3>Main Table</h3>
<div class="table">
    <table class="table table-striped">
        <!-- Header Table component-->
        <thead>
            <tr class="table-info">
                <th scope="col" class="title-border-square">
                    Location
                </th>
                <!-- Display the sizes *ngFor="let size of selectItem$ | async"-->
                <ng-container *ngFor="let size of size$ |async;">
                    <ng-container>
                        <th scope="col" class="title-border-square">
                            {{size.sizeCode}}
                        </th>
                    </ng-container>
                </ng-container>
            </tr>
        </thead>
        <!-- Body Table -->
        <tbody>
            <!-- ng-container is a very useful tag for implementing logic without affecting the heirarchy -->
            <ng-container *ngFor="let loc of location$ | async;">
                <tr>
                    <th scope="row" class="border-square">
                        {{loc.locName}}
                    </th>
                    <ng-container *ngFor="let size of size$ |async;">
                        <ng-container *ngFor="let qty of qty$|async;">
                            <ng-container *ngIf="loc.locationCode == qty.locationCode && size.sizeCode == qty.sizeCode">
                                <ng-container>
                                    <td class="stock-border-square" #elm [id]="qty.sizeCode">
                                        <b>{{qty.stkOnHandQty|removeZeros}}</b>
                                    </td>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                    </ng-container>
                </tr>
                <tr class="table-danger">
                    <th scope="row" class="border-square">
                        SALES
                    </th>
                    <ng-container *ngFor="let size of size$ |async;">
                        <ng-container *ngFor="let qty of qty$|async;">
                            <!-- *ngIf="size.sizeCode == qty.sizeCode" -->
                            <ng-container *ngIf="loc.locationCode == qty.locationCode && size.sizeCode == qty.sizeCode">
                                <ng-container>
                                    <td class="stock-border-square sale-qty-color-square">
                                        {{qty.saleQty |removeZeros}}
                                    </td>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                    </ng-container>
                </tr>
            </ng-container>
                <tr>
                    <th scope="row" class="title-border-square">
                       TOTAL STOCK
                    </th>
                    <ng-container *ngFor="let size of size$ |async;">
                        <ng-container *ngFor="let total of totalBySize$|async;">
                            <!-- *ngIf="size.sizeCode == total.sizeCode" -->
                            <ng-container *ngIf="size.sizeCode == total.sizeCode">
                                <ng-container>
                                    <td class="stock-border-square">
                                        {{total.totalStock |removeZeros}}
                                    </td>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                    </ng-container>
                </tr>
                <tr class="table-danger">
                    <th scope="row" class="border-square-total">
                        TOTAL SALES
                    </th>
                    <!-- *ngFor="let size of size$ |async;" -->
                    <ng-container *ngFor="let size of size$ |async;">
                        <ng-container *ngFor="let total of totalBySize$|async;">
                            <!-- *ngIf="size.sizeCode == qty.sizeCode" -->
                            <ng-container *ngIf="size.sizeCode == total.sizeCode">
                                <ng-container>
                                    <td class="stock-border-square sale-qty-color-square">
                                        {{total.totalSales |removeZeros}}
                                    </td>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                    </ng-container>
                </tr>
        </tbody>
    </table>
</div>