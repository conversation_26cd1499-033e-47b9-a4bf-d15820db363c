import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from './reducers';
import * as appActions from './reducers/app/app.actions';
import { TranslateService } from '@ngx-translate/core';
import { HouseKeepingStateNavigationService } from './reducers/house-keeping/mLogin-navigation.service';

@Component({
	selector: 'pos-root',
	templateUrl: './app.component.html',
	styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {

	title = 'SoleMatrixPOS';

	constructor(
		private store: Store<AppState>,
		private translateService: TranslateService,
		private houseKeepingNavigationServices: HouseKeepingStateNavigationService
	) {
		this.translateService.setDefaultLang('en-au');
		this.translateService.addLangs(['en-au', 'en-US']);
		//this.translateService.setDefaultLang('cn');
	}

	ngOnInit(): void {
		this.store.dispatch(appActions.appInit());
	}

}
