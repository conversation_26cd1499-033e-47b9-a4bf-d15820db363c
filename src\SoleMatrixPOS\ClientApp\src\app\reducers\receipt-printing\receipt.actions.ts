import { createAction, props } from '@ngrx/store';
import { TransactionDto,TransNoDto,ReceiptTransactionDto, ReadDatFileDto, ReceiptGiftVoucherDto } from 'src/app/pos-server.generated';
import { ReceiptBatch } from '../../printing/printing-definitions';

export const init = createAction("[Receipt] Init");
export const printSale = createAction("[Receipt] printSale",props<{payload:ReceiptTransactionDto}>())
export const printReturn = createAction("[Receipt] printReturn",props<{payload:ReceiptTransactionDto}>())
export const printLayby = createAction("[Receipt] printReturn",props<{payload:TransactionDto}>())
export const printTransactionConfirmed = createAction("[Receipt] printTransactionConfirmed")
export const reprintReceipt = createAction("[Receipt] rePrintReceipt",props<{payload:TransNoDto}>())
export const reprintConfirmed = createAction("[Receipt] reprintConfirmed ")

export const executeBatch = createAction("[Receipt] Batch Print", props<{ payload: ReceiptBatch }>());
export const executeBatchComplete = createAction("[Receipt] Batch Print Complete");


export const printGiftVoucher = createAction("[Receipt] print GiftVoucher", props<{payload: ReceiptGiftVoucherDto}>())
