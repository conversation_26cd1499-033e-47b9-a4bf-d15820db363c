import { createAction, props } from '@ngrx/store';
import { ItemBarcodeSearchRequestDto, StockTableItemDto, SubmitReceivalRequestDto } from '../../../pos-server.generated';
export const init = createAction("[Receive Item Scan] Init")
export const submitItemBarcode = createAction("[Receive Item Scan] Submit Item Barcode", props<{payload: ItemBarcodeSearchRequestDto}>());
export const invalidItemBarcode = createAction("[Receive Item Scan] Invalid Item Barcode");
export const addItem = createAction("[Receive Item Scan] Add Item", props<{item: StockTableItemDto}>());
export const removeItem = createAction("[Receive Item Scan] Remove Item", props<{index: number}>());
export const updateItemQuantity = createAction("[Receive Item Scan] Update Item Quantity", props<{index: number, quantity: number}>());
export const submitReceival = createAction("[Receive Item Scan] Submit Receival", props<{payload: SubmitReceivalRequestDto}>());
export const receivalSubmissionCompleted = createAction("[Receive Item Scan] Receival Submission Completed");