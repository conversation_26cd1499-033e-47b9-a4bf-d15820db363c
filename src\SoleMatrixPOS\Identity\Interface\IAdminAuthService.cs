using Microsoft.AspNetCore.Identity;
using SoleMatrixPOS.Domain.Identity.Models;
using SoleMatrixPOS.Identity.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SoleMatrixPOS.Identity.Interface
{
	public interface IAdminAuthService
	{
		public Task<AdminAuthResponse> RegisterUser(RegisteredTill till, string roleName);
		public Task<AdminAuthResponse> ResetPasswordViaEmail(string tillId);
	}

	public enum AdminAuthResponseReason
	{
		Ok,
		TillExists,
		TillNotFound,
		RoleAssignmentError,
		TillCreationError,
		EmailSendFailure,
		ResetTokenIssue,
		InvalidEmail
	}

	// TODO: delete this and use ReasonedResponse (didn't have it in branch at time)
	public struct AdminAuthResponse
	{
		public bool Succeeded { get; set; }
		public AdminAuthResponseReason Reason { get; set; }

		public string Message { get; set; }
		public object? Inner { get; set; }
	}
}
