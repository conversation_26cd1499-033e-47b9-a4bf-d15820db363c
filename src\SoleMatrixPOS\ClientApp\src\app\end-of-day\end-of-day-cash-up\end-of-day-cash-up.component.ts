import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { Form<PERSON>uilder, FormGroup } from '@angular/forms';
import { Observable, Subject, Subscription } from 'rxjs';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import * as dailyActions from 'src/app/reducers/daily/daily.actions';
import * as dailySelectors from 'src/app/reducers/daily/daily.selectors';
import { DailyDto, EndOfDayDTO, TransrefDto, FloatDto } from 'src/app/pos-server.generated';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { EndOfDayVarianceComponent } from '../end-of-day-variance/end-of-day-variance.component';
import * as endOfDayActions from '../../reducers/end-of-day/end-of-day-cash-up/end-of-day.actions';
// import * as transRefActions from '../../reducers/transref/transref.actions'; // Keep if used elsewhere, otherwise remove
// import * as endOfDayFloatSelectors from '../../reducers/end-of-day/end-of-day-float/end-of-day-float.selectors'; // Keep if floatData$ used in template
import * as endOfDayCashUpSelectors from '../../reducers/end-of-day/end-of-day-cash-drawer/end-of-day-cash-up.selectors';
import { storeEndFloatData } from '../../reducers/end-of-day/end-of-day-float/end-of-day-float.actions'; // Keep if dispatched elsewhere
import { Actions, ofType } from '@ngrx/effects';
import { takeUntil } from 'rxjs/operators';
import * as staffActions from '../../reducers/staff/staff.actions';
import { StaffLoginState, StaffState } from 'src/app/reducers/staff/staff.reducer'; // Keep StaffLoginState if used for checks?
import Swal from 'sweetalert2';
import { PrintingService } from 'src/app/printing/printing.service';
import { PrintReturnModalComponent } from 'src/app/shared/components/print-return-modal/print-return-modal.component';
import * as endOfDayFloatSelectors from '../../reducers/end-of-day/end-of-day-float/end-of-day-float.selectors'; // Ensure this is imported
import { SweetAlertResult } from 'sweetalert2';

@Component({
  selector: 'pos-end-of-day-cash-up',
  templateUrl: './end-of-day-cash-up.component.html',
  styleUrls: ['./end-of-day-cash-up.component.scss']
})
export class EndOfDayCashUpComponent implements OnInit, OnDestroy {

  cashUpForm: FormGroup;
  dailyTotal$: Observable<DailyDto>;
  // combinedTotal: number; // Seems unused
  varianceReason: string = "Variance Not Applicable"; // Default if variance is 0
  floatData$: Observable<FloatDto>;
  cashDrawerTotal$: Observable<number>;
  endFloatPayload: FloatDto;
  // ActualCash: number; // Seems unused directly
  // ActualEFT: number; // Seems unused directly
  // ActualCheque: number; // Seems unused directly
  // ActualVisa: number; // Seems unused directly
  // ActualMCard: number; // Seems unused directly
  // ActualAmex: number; // Seems unused directly
  // staff$: Observable<StaffState>; // Seems unused directly
  // staffLoginStateSub: Subscription; // REMOVED - Navigation handled directly
  private destroyed$ = new Subject<void>();
  private floatData: FloatDto; // Used for printing payload

  systemFields = [
    { label: 'Cash', controlName: 'systemCash' },
    { label: 'EFTPOS', controlName: 'systemEFT' }
    // Add other system fields if needed (Cheque, Visa, etc.) matching backend DTO
  ];

  actualFields = [
    { label: 'Cash', controlName: 'actualCash' },
    { label: 'EFTPOS', controlName: 'actualEFT' }
    // Add other actual fields if needed (Cheque, Visa, etc.) matching backend DTO
  ];

  constructor(
    private fb: FormBuilder,
    private store: Store<AppState>,
    // private route: ActivatedRoute, // Seems unused
    private router: Router,
    private modalService: NgbModal,
    private actions$: Actions, // Keep if listening to other actions
    private printService: PrintingService
  ) { }

  ngOnInit(): void {
    this.initializeForm();

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    // Dispatch action to get today's data if needed for system totals
    this.store.dispatch(dailyActions.getTodaysDaily({ date: today }));

    this.fetchDailyTotals(); // Populate form with system totals

    // Subscribe to float data needed for payload & printing
    // Renamed selector for clarity
    this.store.select(endOfDayFloatSelectors.selectEndFloatData)
      .pipe(takeUntil(this.destroyed$))
      .subscribe(floatData => {
        if (floatData) {
          console.log('Received Float Data for payload:', floatData);
          this.floatData = floatData; // Store for later use
          // Prepare payload immediately (optional, can be done in finalizeSubmission)
          this.endFloatPayload = { ...floatData };
        } else {
          console.warn('Float data not available. End of Day float entry might be incomplete.');
          // Consider disabling submit button or showing a warning if floatData is essential
        }
      });

    // Renamed selector for clarity
    this.cashDrawerTotal$ = this.store.select(endOfDayCashUpSelectors.selectCashDrawerTotal);
    this.cashDrawerTotal$
      .pipe(takeUntil(this.destroyed$))
      .subscribe(total => {
        // Check if total is a valid number (could be null/undefined initially)
        if (typeof total === 'number' && !isNaN(total)) {
          console.log('Received cash drawer total:', total);
          // Only update if the value is different to avoid infinite loops if not careful
          if (this.cashUpForm.get('actualCash').value !== total) {
            this.cashUpForm.get('actualCash').setValue(total);
            // calculateTotals will be triggered by valueChanges subscription
          }
        }
      });

    // Recalculate actualTotal whenever actual fields change
    this.cashUpForm.valueChanges
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => this.calculateTotals());

    // Listening to endOfDayConfirmation - keep if needed for other UI updates
    this.actions$.pipe(
      ofType(endOfDayActions.endOfDayConfirmation),
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      console.log('End of Day Confirmation Action Received (if needed).');
      // Add UI updates if required upon backend confirmation
    });
  }

  initializeForm(): void {
    this.cashUpForm = this.fb.group({
      systemCash: [{ value: 0, disabled: true }],
      systemEFT: [{ value: 0, disabled: true }],
      systemTotal: [{ value: 0, disabled: true }],
      actualCash: [0], // Will be updated by cashDrawerTotal$
      actualEFT: [0],
      // Add other actual fields if needed
      actualTotal: [{ value: 0, disabled: true }]
    });
  }

  fetchDailyTotals(): void {
    // Renamed selector for clarity
    this.dailyTotal$ = this.store.select(dailySelectors.storeTodaysDaily);
    this.dailyTotal$
      .pipe(takeUntil(this.destroyed$))
      .subscribe(daily => {
        if (daily) {
          console.log('Received Daily Totals:', daily);
          this.populateSystemFields(daily);
        } else {
          console.warn('Daily totals not available yet.');
          // Keep form fields at 0 or show loading state?
        }
      });
  }

  populateSystemFields(daily: DailyDto): void {
    this.cashUpForm.patchValue({
      systemCash: daily.cashTotal || 0, // Use || 0 as fallback
      systemEFT: daily.eftTotal || 0,
      // Add other system fields if needed
    }, { emitEvent: false }); // Prevent triggering valueChanges unnecessarily
    this.calculateTotals(); // Recalculate totals after patching
  }

  calculateTotals(): void {
    const systemTotal = this.systemFields.reduce((total, field) => total + (this.cashUpForm.get(field.controlName) ? this.cashUpForm.get(field.controlName).value || 0 : 0), 0);
    // Only update if value changed
    const currentSystemTotal = this.cashUpForm.get('systemTotal');
    if (currentSystemTotal && currentSystemTotal.value !== systemTotal) {
      currentSystemTotal.setValue(systemTotal, { emitEvent: false });
    }

    const actualTotal = this.actualFields.reduce((total, field) => total + (this.cashUpForm.get(field.controlName) ? this.cashUpForm.get(field.controlName).value || 0 : 0), 0);
    // Only update if value changed
    const currentActualTotal = this.cashUpForm.get('actualTotal');
    if (currentActualTotal && currentActualTotal.value !== actualTotal) {
      currentActualTotal.setValue(actualTotal, { emitEvent: false });
    }
  }

  onDoubleClick(controlName: string): void {
    // Construct system field name correctly
    const systemControlName = 'system' + controlName.charAt(6).toUpperCase() + controlName.slice(7);
    const systemControl = this.cashUpForm.get(systemControlName);
    const systemValue = systemControl ? systemControl.value : null;
    // Check if systemValue is valid before setting
    if (typeof systemValue === 'number') {
      const targetControl = this.cashUpForm.get(controlName);
      if (targetControl) {
        targetControl.setValue(systemValue);
      }
      // calculateTotals will be triggered by valueChanges subscription
    }
  }

  get variance(): number {
    const actualControl = this.cashUpForm.get('actualTotal');
    const systemControl = this.cashUpForm.get('systemTotal');
    const actual = actualControl ? actualControl.value || 0 : 0;
    const system = systemControl ? systemControl.value || 0 : 0;
    // Round variance to avoid floating point issues if necessary
    return parseFloat((actual - system).toFixed(2));
  }

  submit(): void {
    // Form validation check (though button might be disabled)
    if (this.cashUpForm.invalid) {
      Swal.fire('Invalid Form', 'Please ensure all fields are filled correctly.', 'warning');
      return;
    }
    // Check if float data is available, prevent submission if critical
    if (!this.floatData) {
      Swal.fire('Missing Data', 'End of Day Float data is not available. Please complete the float entry first.', 'error');
      return;
    }


    const currentVariance = this.variance; // Calculate once

    if (currentVariance !== 0) {
      // Always open modal if there's a variance (positive or negative)
      this.openVarianceReasonModal(currentVariance);
    } else {
      // No variance, proceed directly
      this.varianceReason = "No Variance"; // Set default reason
      this.finalizeSubmission();
    }
  }

  openVarianceReasonModal(varianceAmount: number): void {
    const modalRef = this.modalService.open(EndOfDayVarianceComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false // Prevent closing with Esc
    });
    // Pass data to the modal if needed (e.g., the variance amount)
    modalRef.componentInstance.title = 'End of Day Variance Reason';
    modalRef.componentInstance.varianceAmount = varianceAmount; // Pass the amount
    // modalRef.componentInstance.parent = this; // Avoid passing parent directly if possible

    modalRef.result.then(reason => {
      // This block executes when modal is CLOSED (resolve)
      console.log('Variance reason received:', reason);
      if (reason && reason.trim()) { // Ensure a reason was provided
        const varianceFormatted = varianceAmount.toFixed(2);
        this.varianceReason = `Variance: $${varianceFormatted} Reason: ${reason}`;
        this.finalizeSubmission(); // Proceed with the reason
      } else {
        // User closed modal without providing a reason (or clicked OK with empty reason)
        Swal.fire('Reason Required', 'A reason must be provided for the variance.', 'warning');
        // Optionally re-open the modal or just stop submission
      }
    }, dismissReason => {
      // This block executes when modal is DISMISSED (reject)
      console.log('Variance modal dismissed:', dismissReason);
      // User clicked cancel or closed the modal otherwise, stop the submission process
    });
  }

  private clearEndOfDayLocalStorage(): void {
    localStorage.removeItem('endOfDayFloatFormValues');
    localStorage.removeItem('endOfDayCashDrawerFormValues');
    console.log('Cleared end of day form values from local storage');
  }

  finalizeSubmission(): void {
    console.log('Finalizing EOD Submission...');

    // Ensure float payload is ready
    if (!this.endFloatPayload) {
      Swal.fire('Error', 'Float data payload is missing. Cannot proceed.', 'error');
      return;
    }
    this.store.dispatch(staffActions.dailyFloatEnter({ payload: this.endFloatPayload }));

    const endOfDayDTO: EndOfDayDTO = {
      actualCash: this.cashUpForm.get('actualCash').value || 0,
      actualEFT: this.cashUpForm.get('actualEFT').value || 0,
      actualCheque: 0, // Assuming 0 for these based on form
      actualVisa: 0,
      actualMCard: 0,
      actualAmex: 0,
      actualDiners: 0
      // Add variance reason to DTO if backend expects it here
      // varianceReason: this.varianceReason
    };
    this.store.dispatch(endOfDayActions.submitEndOfDay({ payload: endOfDayDTO }));

    // Handle TransrefDto submission if required
    const transRefDTO: TransrefDto = {
      transReference: this.varianceReason,
      lineNo: 0 // Or appropriate line number if applicable
    };
    // Dispatch action if needed:
    // this.store.dispatch(transRefActions.submitVarianceReason({ payload: transRefDTO }));

    // Clear local storage values after successful submission
    this.clearEndOfDayLocalStorage();

    // --- Show Print Modal ---
    const printModalRef = this.modalService.open(PrintReturnModalComponent, {
      backdrop: 'static',
      keyboard: false
    });

    printModalRef.result.then(async (printResult) => {
      // --- Optional Printing ---
      if (printResult === 'print' && this.floatData) {
        const systemTotals = {
          systemCash: this.cashUpForm.get('systemCash').value || 0,
          systemEFT: this.cashUpForm.get('systemEFT').value || 0
        };
        try {
          console.log('Printing EOD receipt...');
          await this.printService.printEndOfDayReceipt(
            endOfDayDTO,
            this.floatData,
            this.varianceReason, // Consider security if printing sensitive reasons
            systemTotals
          );
        } catch (printError) {
          console.error("Printing failed:", printError);
          Swal.fire('Printing Error', 'Failed to print End of Day receipt. Please check printer.', 'warning');
          // Decide if EOD should still proceed even if printing fails
        }
      }

      Swal.fire({
        title: "Clock Out All?",
        type: "question",
        text: "Do You Want To Clock Out All Staff?",
        showCancelButton: true,
        cancelButtonText: "No",
        confirmButtonText: "Yes, Clock Out All",
      }).then(async (result) => {
        if (result.value) {
          this.store.dispatch(staffActions.endOfDayClockOutAllStaff());
          this.store.dispatch(staffActions.clearStaffLogin());
        } else if (result.dismiss === Swal.DismissReason.cancel) {
          this.store.dispatch(staffActions.clearStaffLogin());
        }
        else {
          this.store.dispatch(staffActions.clearStaffLogin());
        }
      });
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
    // No need to unsubscribe staffLoginStateSub as it was removed
  }

  cancel() {
    // Confirmation before cancelling EOD
    this.router.navigateByUrl('/home'); // Navigate back
  }
}