using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using SoleMatrixPOS.Application.StockEnquiry;
using System.Collections.Generic;
using SoleMatrixPOS.Application.StockEnquiry.Queries;
using SoleMatrixPOS.Application.Stock;
using SoleMatrixPOS.Application.Stock.Dtos;
using System.Threading;
using SoleMatrixPOS.Dal.Interface.Models.SMate;
using SoleMatrixPOS.Application.Discount;
using SoleMatrixPOS.Application.Discount.Queries;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class DiscountsController : ControllerBase
	{
		private readonly IMediator _mediator;

		public DiscountsController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[HttpGet("GetDiscountTypes")]
		public async Task<IEnumerable<DiscountsDto>> GetDiscountsTypes()
		{
			return await _mediator.Send(new DiscountTypeQuery());
		}


	}
}
