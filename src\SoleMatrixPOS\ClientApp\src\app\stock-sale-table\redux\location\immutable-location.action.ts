import { Action } from '@ngrx/store';
import { Location } from './location.model';

export const LOAD_IMMUTABLE_LOCATIONS_REQUESTED: string = "[IMMUTABLE_LOCATION] loadReq";
export const LOAD_IMMUTABLE_LOCATIONS: string = "[IMMUTABLE_LOCATION] load";

/**
 * Request action, this is the preferred action for loading data
 * as it is caught by an effect that ensures successive actions
 * are triggered in the correct order
 */
export class LoadImmutableLocationsRequested implements Action {
    readonly type = LOAD_IMMUTABLE_LOCATIONS_REQUESTED;
}

/**
 * An array of locations can be given to this action and the store
 * will replace the immutablelocations state slice with it
 */
export class LoadImmutableLocationsAction implements Action {
    readonly type = LOAD_IMMUTABLE_LOCATIONS;

    constructor(
        public locations: Location[]
    ){}
}

export type ImmutableLocationsActionType = 
LoadImmutableLocationsRequested | 
LoadImmutableLocationsAction;