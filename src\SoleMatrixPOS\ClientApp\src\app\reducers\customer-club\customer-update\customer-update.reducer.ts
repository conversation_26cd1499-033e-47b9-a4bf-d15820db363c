import { Action, createReducer, on } from '@ngrx/store';
import * as customerUpdateActions from './customer-update.actions';
import { CustomerClubDto } from 'src/app/pos-server.generated';

export enum MemberUpdateStatus {
  None,
  Loading,
  Success,
  Error
}

export enum PointsUpdateStatus {
  None,
  Loading,
  Success,
  Error
}

export class CustomerClubUpdateMemberState {
  member: CustomerClubDto;
  memberUpdateStatus: MemberUpdateStatus;
  pointsUpdateStatus: PointsUpdateStatus;
  lastUpdatedClientCode: string;
  newPointsTotal: number;
  error: string;
}

export const updateMemberState: CustomerClubUpdateMemberState = {
  member: null,
  memberUpdateStatus: MemberUpdateStatus.None,
  pointsUpdateStatus: PointsUpdateStatus.None,
  lastUpdatedClientCode: null,
  newPointsTotal: null,
  error: null
} as CustomerClubUpdateMemberState;

export const customerClubUpdateMemberReducer = createReducer(updateMemberState,
  // Initialize state
  on(customerUpdateActions.init, (state) => ({ ...updateMemberState })),
  
  // Member update actions
  on(customerUpdateActions.updateMember, (state, action) => ({ 
    ...state, 
    memberUpdateStatus: MemberUpdateStatus.Loading 
  })),
  on(customerUpdateActions.updateMemberSuccess, (state, action) => ({ 
    ...state, 
    member: action.result, 
    memberUpdateStatus: MemberUpdateStatus.Success 
  })),
  on(customerUpdateActions.updateMemberError, (state, action) => ({ 
    ...state, 
    memberUpdateStatus: MemberUpdateStatus.Error,
    error: action.error
  })),
  
  // Points update actions
  on(customerUpdateActions.updatePoints, (state, action) => ({ 
    ...state, 
    pointsUpdateStatus: PointsUpdateStatus.Loading,
    lastUpdatedClientCode: action.payload.clientCode 
  })),
  on(customerUpdateActions.updatePointsSuccess, (state, action) => ({ 
    ...state, 
    pointsUpdateStatus: PointsUpdateStatus.Success,
    lastUpdatedClientCode: action.clientCode,
    newPointsTotal: action.newPointsTotal
  })),
  on(customerUpdateActions.updatePointsError, (state, action) => ({ 
    ...state, 
    pointsUpdateStatus: PointsUpdateStatus.Error,
    error: action.error
  }))
);

export function reducer(state: CustomerClubUpdateMemberState | undefined, action: Action) {
  return customerClubUpdateMemberReducer(state, action);
}