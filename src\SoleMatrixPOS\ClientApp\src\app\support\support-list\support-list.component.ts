import { Component, Input } from "@angular/core";


export interface ISupportListData {
    title: String,
    description: String,
    internalUrl: string,
    url: string,
    list: ISupportListData[]
}

@Component({
    selector: 'app-support-list',
    templateUrl: 'support-list.component.html',
    styleUrls: ['support-list.component.scss']

})
export class SupportListComponent {
    constructor(){}

    @Input()
    data: ISupportListData

}

