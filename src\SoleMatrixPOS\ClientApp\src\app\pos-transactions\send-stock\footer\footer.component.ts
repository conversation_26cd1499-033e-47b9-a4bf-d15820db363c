import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { AppState } from 'src/app/reducers';
import * as itemCartSelector from '../../../reducers/stocketake-entry/itemCart/itemCart.selectors'
import * as sendStockSelector from 'src/app/reducers/send-stock/send-stock.selectors';
import * as sendStockActions from '../../../reducers/send-stock/send-stock.actions';

@Component({
  selector: 'pos-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss']
})
export class FooterComponent implements OnInit {

  totalUnits$: Observable<number>
  enableProcessButton: boolean;

  @Output() onNextClick: EventEmitter<void> = new EventEmitter();

  constructor(private router: Router, private store: Store<AppState>) { }

  getTransferNumber$: Observable<string>;
  transferNumber: string;

  ngOnInit() {

    this.totalUnits$ = this.store.select(itemCartSelector.UnitTotal);

    this.getTransferNumber$ = this.store.select(sendStockSelector.getTransferNo);
    this.getTransferNumber$.subscribe(transNo => {
      this.transferNumber = transNo;
      console.log("Transfer number footer: " + this.transferNumber);
      if (transNo) {
        this.enableProcessButton = true;
      }
    })

    if (sendStockActions.setTransferNumber) {

    }


  }


  backButtonClick() {
    this.router.navigateByUrl("/home");
  }

  okToProcess() {
    this.onNextClick.emit();
  }

}
