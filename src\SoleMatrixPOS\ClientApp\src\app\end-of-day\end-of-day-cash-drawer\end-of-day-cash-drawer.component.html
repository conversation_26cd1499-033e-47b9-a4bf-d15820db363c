<div class="container-fluid">
    <form [formGroup]="floatForm">
        <div class="row">
            <div class="col-md-4"></div>
            <div class="col-md-4">
                <div class="card mx-auto float-card" style="margin-top: 1rem; margin-bottom: 1rem;">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span [translate]="'end-of-day-cash-drawer.CashDrawer'">Cash Drawer</span>
                        <button class="btn btn-secondary btn-sm" type="button" (click)="openTill()">
                            <i class="fas fa-cash-register"></i> Open Till
                        </button>
                    </div>
                    <div class="card-body py-2">
                        <div class="container">
                            <div class="row compact-row">
                                <label class="col-4 text-right col-form-label-sm text-nowrap">
                                    $100:
                                </label>
                                <div class="col-4">
                                    <input type="number" class="form-control form-control-sm"
                                           [ngClass]="{'is-invalid': floatForm.get('Denomination100').invalid}"
                                           formControlName="Denomination100">
                                    <div class="invalid-feedback"
                                         [translate]="'end-of-day-cash-drawer.EndFloatValidationMessage'"
                                         [translateParams]="{ denomination: '$100 note' }"
                                         *ngIf="floatForm.get('Denomination100').invalid">
                                        invalid
                                    </div>
                                </div>
                            </div>
                            <div class="row compact-row">
                                <label class="col-4 text-right col-form-label-sm text-nowrap">
                                    $50:
                                </label>
                                <div class="col-4">
                                    <input type="number" class="form-control form-control-sm"
                                           [ngClass]="{'is-invalid': floatForm.get('Denomination50').invalid}"
                                           formControlName="Denomination50">
                                    <div class="invalid-feedback"
                                         [translate]="'end-of-day-cash-drawer.EndFloatValidationMessage'"
                                         [translateParams]="{ denomination: '$50 note' }"
                                         *ngIf="fieldValidate(floatForm.get('Denomination50'))">
                                        invalid
                                    </div>
                                </div>
                            </div>
                            <div class="row compact-row">
                                <label class="col-4 text-right col-form-label-sm text-nowrap">
                                    $20:
                                </label>
                                <div class="col-4">
                                    <input type="number" class="form-control form-control-sm"
                                           [ngClass]="{'is-invalid': floatForm.get('Denomination20').invalid}"
                                           formControlName="Denomination20">
                                    <div class="invalid-feedback"
                                         [translate]="'end-of-day-cash-drawer.EndFloatValidationMessage'"
                                         [translateParams]="{ denomination: '$20 note' }"
                                         *ngIf="fieldValidate(floatForm.get('Denomination20'))">
                                        invalid
                                    </div>
                                </div>
                            </div>
                            <div class="row compact-row">
                                <label class="col-4 text-right col-form-label-sm text-nowrap">
                                    $10:
                                </label>
                                <div class="col-4">
                                    <input type="number" class="form-control form-control-sm"
                                           [ngClass]="{'is-invalid': floatForm.get('Denomination10').invalid}"
                                           formControlName="Denomination10">
                                    <div class="invalid-feedback"
                                         [translate]="'end-of-day-cash-drawer.EndFloatValidationMessage'"
                                         [translateParams]="{ denomination: '$10 note' }"
                                         *ngIf="fieldValidate(floatForm.get('Denomination10'))">
                                        invalid
                                    </div>
                                </div>
                            </div>
                            <div class="row compact-row">
                                <label class="col-4 text-right col-form-label-sm text-nowrap">
                                    $5:
                                </label>
                                <div class="col-4">
                                    <input type="number" class="form-control form-control-sm"
                                           [ngClass]="{'is-invalid': floatForm.get('Denomination5').invalid}"
                                           formControlName="Denomination5">
                                    <div class="invalid-feedback"
                                         [translate]="'end-of-day-cash-drawer.EndFloatValidationMessage'"
                                         [translateParams]="{ denomination: '$5 note' }"
                                         *ngIf="fieldValidate(floatForm.get('Denomination5'))">
                                        invalid
                                    </div>
                                </div>
                            </div>
                            <div class="row compact-row">
                                <label class="col-4 text-right col-form-label-sm text-nowrap">
                                    $2:
                                </label>
                                <div class="col-4">
                                    <input type="number" class="form-control form-control-sm"
                                           [ngClass]="{'is-invalid': floatForm.get('Denomination2').invalid}"
                                           formControlName="Denomination2">
                                    <div class="invalid-feedback"
                                         [translate]="'end-of-day-cash-drawer.EndFloatValidationMessage'"
                                         [translateParams]="{ denomination: '$2 coin' }"
                                         *ngIf="fieldValidate(floatForm.get('Denomination2'))">
                                        invalid
                                    </div>
                                </div>
                            </div>
                            <div class="row compact-row">
                                <label class="col-4 text-right col-form-label-sm text-nowrap">
                                    $1:
                                </label>
                                <div class="col-4">
                                    <input type="number" class="form-control form-control-sm"
                                           [ngClass]="{'is-invalid': floatForm.get('Denomination1').invalid}"
                                           formControlName="Denomination1">
                                    <div class="invalid-feedback"
                                         [translate]="'end-of-day-cash-drawer.EndFloatValidationMessage'"
                                         [translateParams]="{ denomination: '$1 coin' }"
                                         *ngIf="fieldValidate(floatForm.get('Denomination1'))">
                                        invalid
                                    </div>
                                </div>
                            </div>
                            <div class="row compact-row">
                                <label class="col-4 text-right col-form-label-sm text-nowrap">
                                    50c:
                                </label>
                                <div class="col-4">
                                    <input type="number" class="form-control form-control-sm"
                                           [ngClass]="{'is-invalid': floatForm.get('Denomination50c').invalid}"
                                           formControlName="Denomination50c">
                                    <div class="invalid-feedback"
                                         [translate]="'end-of-day-cash-drawer.EndFloatValidationMessage'"
                                         [translateParams]="{ denomination: '50c coin' }"
                                         *ngIf="fieldValidate(floatForm.get('Denomination50c'))">
                                        invalid
                                    </div>
                                </div>
                            </div>
                            <div class="row compact-row">
                                <label class="col-4 text-right col-form-label-sm text-nowrap">
                                    20c:
                                </label>
                                <div class="col-4">
                                    <input type="number" class="form-control form-control-sm"
                                           [ngClass]="{'is-invalid': floatForm.get('Denomination20c').invalid}"
                                           formControlName="Denomination20c">
                                    <div class="invalid-feedback"
                                         [translate]="'end-of-day-cash-drawer.EndFloatValidationMessage'"
                                         [translateParams]="{ denomination: '20c coin' }"
                                         *ngIf="fieldValidate(floatForm.get('Denomination20c'))">
                                        invalid
                                    </div>
                                </div>
                            </div>
                            <div class="row compact-row">
                                <label class="col-4 text-right col-form-label-sm text-nowrap">
                                    10c:
                                </label>
                                <div class="col-4">
                                    <input type="number" class="form-control form-control-sm"
                                           [ngClass]="{'is-invalid': floatForm.get('Denomination10c').invalid}"
                                           formControlName="Denomination10c">
                                    <div class="invalid-feedback"
                                         [translate]="'end-of-day-cash-drawer.EndFloatValidationMessage'"
                                         [translateParams]="{ denomination: '10c coin' }"
                                         *ngIf="fieldValidate(floatForm.get('Denomination10c'))">
                                        invalid
                                    </div>
                                </div>
                            </div>
                            <div class="row compact-row">
                                <label class="col-4 text-right col-form-label-sm text-nowrap">
                                    5c:
                                </label>
                                <div class="col-4">
                                    <input type="number" class="form-control form-control-sm"
                                           [ngClass]="{'is-invalid': floatForm.get('Denomination5c').invalid}"
                                           formControlName="Denomination5c">
                                    <div class="invalid-feedback"
                                         [translate]="'end-of-day-cash-drawer.EndFloatValidationMessage'"
                                         [translateParams]="{ denomination: '5c coin' }"
                                         *ngIf="fieldValidate(floatForm.get('Denomination5c'))">
                                        invalid
                                    </div>
                                </div>
                            </div>
                            <div class="row mx-auto">
                                <label class="col-4 text-right col-form-label-sm text-nowrap"
                                       [translate]="'end-of-day-cash-drawer.Total'">
                                    Total:
                                </label>
                                <label class="col-4 col-form-label-sm text-nowrap">
                                    {{total$ | async | currency}}
                                </label>
                            </div>
                        </div>
                        <div class="container">
                            <div class="row">
                                <div class="col-6">
                                    <div>
                                        <button class="btn btn-warning" type="button" [routerLink]="['/home']"
                                                (click)="Cancel()" [translate]="'end-of-day-cash-drawer.buttons.Cancel'">
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                                <div class="col-6 text-right">
                                    <div>
                                        <button class="btn btn-info" type="button" [disabled]="!floatForm.valid"
                                                (click)="onSubmit()"
                                                [translate]="'end-of-day-cash-drawer.buttons.Submit'">
                                            Next
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4"></div>
        </div>
    </form>
</div>
