import { createFeatureSelector, createSelector } from "@ngrx/store";
import { SuspendSaleState } from "./suspend-sale.reducers";
import { AppState } from "..";
import { CartItemDto } from "src/app/pos-server.generated";

export const selectSuspendSaleState =
	createFeatureSelector<SuspendSaleState>("suspendSale");

export const selectSuspendSaleHeaders = createSelector(
	selectSuspendSaleState,
	(state: SuspendSaleState) => state.headers
);

export const selectSuspendSaleLoading = createSelector(
	selectSuspendSaleState,
	(state: SuspendSaleState) => state.isLoading
);

export const selectSuspendSaleError = createSelector(
	selectSuspendSaleState,
	(state: SuspendSaleState) => state.error
);

export const selectSelectedSaleLines = createSelector(
	selectSuspendSaleState,
	(state: SuspendSaleState) => state.selectedSaleLines
);

export const selectCurrentSuspendSaleNo = createSelector(
	selectSuspendSaleState,
	(state) => state.currentSuspendSaleNo
);
