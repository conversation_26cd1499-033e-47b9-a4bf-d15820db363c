import {Action, createReducer, on} from '@ngrx/store';
import * as stockSearchActions from '../stock-search/stock-search.actions';
import {SortDirection, StockItemDto, StockSearchFields, StockSearchRequestDto} from '../../pos-server.generated';

export class StockSearchState {
	isLoading: boolean;
	items: StockItemDto[];
	options: StockSearchRequestDto;
}

export const initialState: StockSearchState = {
	isLoading: false,
	items: [],
	options: {}
} as StockSearchState;

export const stockSearchReducer = createReducer(initialState,
	on(stockSearchActions.clearSearchItems, (state, action) => ({...initialState})),

	on(stockSearchActions.search, (state, action) => ({...state, isLoading: true, options: {...action.searchParams}})),
	on(stockSearchActions.searchResponse, (state, action) => ({...state, isLoading: false, items: action.payload || []})),

	on(stockSearchActions.searchMore, (state, action) => ({...state, isLoading: true})),
	on(stockSearchActions.searchMoreResponse, (state, action) => ({...state, isLoading: false, items: [...state.items, ...action.payload]})),

	on(stockSearchActions.searchStockEnquiry, (state, action) => ({
		...state, 
		isLoading: true, 
		options: {...action.searchParams}
	})),
	on(stockSearchActions.searchStockEnquirySuccess, (state, action) => ({
		...state, 
		isLoading: false, 
		items: action.items || []
	})),

	on(stockSearchActions.searchStockEnquiryMore, (state, action) => ({
		...state, 
		isLoading: true
	})),
	on(stockSearchActions.searchStockEnquiryMoreSuccess, (state, action) => ({
		...state, 
		isLoading: false, 
		items: [...state.items, ...action.items]
	}))
);

export function reducer(state: StockSearchState | undefined, action: Action) {
	return stockSearchReducer(state, action);
}
