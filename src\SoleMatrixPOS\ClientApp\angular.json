{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"SoleMatrixPOS": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "pos", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/SoleMatrixPOS", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": false, "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/assets/css/styles.scss", "src/styles.css", "node_modules/sweetalert2/dist/sweetalert2.min.css"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "SoleMatrixPOS:build"}, "configurations": {"production": {"browserTarget": "SoleMatrixPOS:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "SoleMatrixPOS:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/assets/css/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "SoleMatrixPOS:serve"}, "configurations": {"production": {"devServerTarget": "SoleMatrixPOS:serve:production"}}}}}}, "defaultProject": "SoleMatrixPOS", "schematics": {"@schematics/angular:component": {"styleext": "scss"}}}