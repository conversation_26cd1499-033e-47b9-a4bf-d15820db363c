<div class="container-fluid">
  <form [formGroup]="cashUpForm">
    <div class="row">
      <div class="col-md-8 offset-md-2">
        <div class="card mx-auto" style="margin-top: 1rem; margin-bottom: 1rem;">
          <div class="card-header">End of Day Cash Up</div>
          <div class="card-body">
            <div class="container">
              <div class="row justify-content-center mb-4">
                <div class="col-md-5 text-center">
                  <h5>System Totals</h5>
                </div>
                <div class="col-md-5 text-center">
                  <h5>Actual Totals</h5>
                </div>
              </div>
              
              <div class="row justify-content-center">
                <div class="col-md-5">
                  <div class="form-group row" *ngFor="let field of systemFields">
                    <label class="col-6 col-form-label text-right">{{field.label}}:</label>
                    <div class="col-6">
                      <div class="input-group">
                        <div class="input-group-prepend">
                          <span class="input-group-text">$</span>
                        </div>
                        <input type="number" class="form-control" [formControlName]="field.controlName" readonly />
                      </div>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="col-6 col-form-label text-right">Total:</label>
                    <div class="col-6">
                      <div class="input-group">
                        <div class="input-group-prepend">
                          <span class="input-group-text">$</span>
                        </div>
                        <input type="number" class="form-control" formControlName="systemTotal" readonly />
                      </div>
                    </div>
                  </div>
                </div>

                <div class="col-md-5">
                  <div class="form-group row" *ngFor="let field of actualFields">
                    <label class="col-6 col-form-label text-right">{{field.label}}:</label>
                    <div class="col-6">
                      <div class="input-group">
                        <div class="input-group-prepend">
                          <span class="input-group-text">$</span>
                        </div>
                        <input type="number" 
                               class="form-control" 
                               [formControlName]="field.controlName"
                               (dblclick)="onDoubleClick(field.controlName)" />
                      </div>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="col-6 col-form-label text-right">Total:</label>
                    <div class="col-6">
                      <div class="input-group">
                        <div class="input-group-prepend">
                          <span class="input-group-text">$</span>
                        </div>
                        <input type="number" class="form-control" formControlName="actualTotal" readonly />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="form-group row">
                <label class="col-6 col-form-label text-right">Variance:</label>
                <div class="col-6">
                  <label class="form-control-plaintext">{{ variance | currency }}</label>
                </div>
              </div>
            </div>
            <div class="container">
              <div class="row">
                <div class="col-6">
                  <button class="btn btn-warning" type="button" [routerLink]="['/home']" (click)="cancel()">Cancel</button>
                </div>
                <div class="col-6 text-right">
                  <button class="btn btn-info" type="button" [disabled]="!cashUpForm.valid" (click)="submit()">Submit</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
