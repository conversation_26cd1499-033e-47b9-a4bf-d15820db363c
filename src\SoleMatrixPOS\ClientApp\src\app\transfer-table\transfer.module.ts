import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TransferFooterComponent } from './transfer-footer/transfer-footer.component';
import { TransferTableComponent } from './transfer-table/transfer-table.component';
import { TransferListComponent } from './transfer-list/transfer-list.component';
import { TransferComponent } from './transfer.component';
import { RouterModule } from '@angular/router';
import { PipesModule } from '../stock-sale-table/pipes/pipes.module';

import { DragDropModule } from '@angular/cdk/drag-drop';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2'; 
import { MatTableModule } from '@angular/material/table';

/** 
 * To install dependencies for this module
 * 'npm i @angular/cdk'
 * 'npm i @angular/material'
 * 'npm i sweetalert2'
 * 'npm i @sweetalert2/ngx-sweetalert2'
 * 
 * Will likely replace sweetalert2
 */ 

@NgModule({
  declarations: [
    TransferComponent,
    TransferFooterComponent,
    TransferTableComponent,
    TransferListComponent,
  ],
  imports: [
    CommonModule,
    DragDropModule,
    RouterModule,
    PipesModule,
    SweetAlert2Module.forRoot({
      buttonsStyling: false,
      customClass: 'modal-content',
      confirmButtonClass: 'btn btn-primary',
      cancelButtonClass: 'btn'
    }),  
    MatTableModule
  ],
  exports: [
    TransferComponent,
    TransferFooterComponent,
    TransferTableComponent,
    TransferListComponent
  ]
})
export class TransferModule { }
