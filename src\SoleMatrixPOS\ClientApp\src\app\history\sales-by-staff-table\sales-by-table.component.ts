import { Component, OnInit,Input } from '@angular/core';
import { SalesByStaffQueryDto } from 'src/app/pos-server.generated';
import { Observable, from, Subject } from 'rxjs';
import { Store } from '@ngrx/store';
import * as historySelectors from '../../reducers/customer-club/history/history.selector';
import * as historyActions from '../../reducers/customer-club/history/history.actions';
import * as dailyActions from '../../reducers/daily/daily.actions'
import * as dailySelectors from '../../reducers/daily/daily.selectors'
import { AppState } from 'src/app/reducers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { toFinancialString } from 'src/app/utility/math-helpers';

@Component({
  selector: 'pos-sales-by-table',
  templateUrl: './sales-by-table.component.html',
  styleUrls: ['./sales-by-table.component.scss']
})

export class SalesByTableComponent implements OnInit {
  public salesByStaff$: Observable<SalesByStaffQueryDto[]>;
  public refundsByStaff$: Observable<SalesByStaffQueryDto[]>;
  public currentView: 'sales' | 'refunds' = 'sales';

  constructor(private store: Store<AppState>){}

  ngOnInit(): void {
    this.salesByStaff$ = this.store.select(dailySelectors.storeSalesByStaff);
    this.refundsByStaff$ = this.store.select(dailySelectors.storeRefundsByStaff);
    this.store.dispatch(dailyActions.getSalesByStaff({}));
    this.store.dispatch(dailyActions.getRefundsByStaff({}));
  }

  toggleView(): void {
    this.currentView = this.currentView === 'sales' ? 'refunds' : 'sales';
  }
  
  toFinancialString(val) {
    if (val < 0) {
      return `-${toFinancialString(-1*val)}`
    }else {
      return toFinancialString(val)
    }
  }
}
