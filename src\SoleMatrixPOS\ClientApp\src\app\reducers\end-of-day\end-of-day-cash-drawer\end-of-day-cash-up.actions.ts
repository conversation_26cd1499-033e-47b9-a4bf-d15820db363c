import { createAction, props } from '@ngrx/store';
import { FloatDto } from 'src/app/pos-server.generated';

export const init = createAction("[EndOfDay] Init");

export const saveEndOfDayFloat = createAction(
  '[End Of Day] Save End Of Day Float',
  props<{ payload: FloatDto }>()
);

export const saveCashDrawerTotal = createAction(
  '[End Of Day] Save Cash Drawer Total',
  props<{ total: number }>()
);
