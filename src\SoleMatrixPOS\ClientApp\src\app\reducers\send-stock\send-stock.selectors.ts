import { createSelector } from "@ngrx/store";
import { AppState } from "..";

export const select = (state: AppState) => state.sendStock;

export const getReason = createSelector(select, s => s.lookups.reasons);
export const selectedReason = createSelector(select, s => s.reason);

export const setReason = createSelector(select, s => s.reason);
export const getDestination = createSelector(select, s => s.lookups.destinations.destinationStoreList);
export const getLockStore = createSelector(select, s => s.lookups.destinations.lockedDestinationStore);

export const setDestination = createSelector(select, s => s.destination)
export const getTransferNo = createSelector(select, s => s.transferNumber);

export const transCompleted = createSelector(select, s => s.completed);


