import { Component } from '@angular/core';


import { TransferTableService } from './transfer-table.service';

import { AppState } from '../../stock-sale-table/redux/app.store';
//import { AppState, state } from '../../stock-sale-table/test-state';

import { Store } from '@ngrx/store';
import * as TransferActions from '../../stock-sale-table/redux/transfer/transfer.action';
import { CdkColumnDef } from '@angular/cdk/table';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-transfer-table',
  templateUrl: './transfer-table.component.html',
  styleUrls: ['./transfer-table.component.scss'],
  providers:[CdkColumnDef]
})
export class TransferTableComponent {

  previousLocation: string = "";  // Last dragged 'from' location name
  previousSize: string = "";      // Last dragge size

  constructor(public transferTableService: TransferTableService, private store: Store<AppState>) { }

  /**
   * Brings up a popup when triggered. The user may enter a value
   * in the popup which is then used to create a transfer action
   * which is then dispatched to the store
   * 
   * @param {CdkDragDrop<any>}  event 
   * @param {string}            from 
   * @param {string}            to 
   * @param {string}            name 
   * @param {string}            size 
   */
  public popUpDrop(event: CdkDragDrop<any>, from: string, to: string, name: string, size: string) {

    // Get the dragged Value in previousContainer
    let draggedValue = +event.item.element.nativeElement.innerHTML; 

    // Create popup
    const popup = Swal.mixin({
      customClass: {
        confirmButton: 'btn btn-info',
        cancelButton: 'btn btn-danger'
      },
      buttonsStyling: false,
    })

    // POPUP- Input a value
    popup.fire({

      title: 'Transfer Quantity',
      input: 'text',
      inputValue: draggedValue.toString(),
      type: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Confirm',
      cancelButtonText: 'Cancel',
      reverseButtons: true,      

    }).then((result) => {
      if (result.value) {
        
        let inputQtyPrompt = this.removeDecimals(result.value.toString());
        let response = this.validateResponse((+inputQtyPrompt), 1, (+draggedValue));
           
        if (response !== "") {
          
          // POPUP- Invalid Response
          popup.fire(
            'Cancelled',
            response,
            'error'
          )

        } else {

          let sizes;

          this.transferTableService.sizes$.subscribe(
            x => {sizes = x}
          );
          
          for (let i = 0; i < sizes.length; i++) {  // Find the size
            
            if (size === sizes[i].size)  {          // Size found
         
              this.addTransfer(from, to, name, size, +inputQtyPrompt);
            
            }

          }
        
          // POPUP- Confirmed
          popup.fire(
            'Confirmed!',
            `${inputQtyPrompt} pair${(+inputQtyPrompt === 1 ? "" : "s")} of ${name} ${(+inputQtyPrompt === 1 ? "has" : "have")} been transferred from ${from} to ${to}.`,
            'success'
          )

        }

      } else if (result.dismiss === Swal.DismissReason.cancel) {
        
        // POPUP- Cancelled
        popup.fire(
          'Cancelled',
          '',
          'error'
        )

      }

    })

  }

  /**
   * Ensures that a numeric response is within the range of
   * minimum and maximum values (inclusive) provided
   * 
   * @param  {number} value
   * @param  {number} min   
   * @param  {number} max 
   * @return {string}       An error message
   */
  private validateResponse(value: number, min: number, max: number): string {
    let errorMessage = "";
    let re = /^[0-9]+$/;

    // console.log("Validate response:", value);
    if(value < min) {
      errorMessage += `Invalid number, must be between ${min} and ${max}.`;
    } else if(value > max) {
      errorMessage += `Invalid number, must be between ${min} and ${max}.`;
    } else if(!re.test(value.toString())) {
      errorMessage += `Invalid number, must be a number`;
    }

    return errorMessage;
  }

  /**
   * Removes any decimals from the inputed value
   * 
   * @param  {number | string} value 
   * @return {string}
   */
  private removeDecimals(value: number | string): string {
    if (typeof value === "string") {
      value = +(parseFloat(value)).toFixed()
    } else if (typeof value === "number") {
      value = +(parseFloat(value.toString())).toFixed()
    }
    return value.toString();
  }

  /**
   * Adds a row to the transfer table and ngrx store
   * 
   * @param {string}   from
   * @param {string}   to
   * @param {string}   name
   * @param {string}   size
   * @param {number}   qty
   */
  addTransfer(from: string, to: string, name: string, size: string, qty: number) {

    // NOTE: Poor separation of concerns here, consider creating a transfer request in locationActions
    // and using an effect to call TransferActions.AddTransferAction(from, to, name, size, qty);


    // console.log(`TransferTableComponent.addTransfer(from: ${from}, to: ${to}, name: ${name}, size: ${size}, qty: ${qty})`);
    const action = new TransferActions
      .AddTransferAction(from, to, name, size, qty);

    this.store.dispatch(action);

  }

  /**
   * Saves the name of the location the drag event originated from
   * 
   * @param {string}  previousLocation
   * @param {string}  previousSize
   */
  pickup(previousLocation: string, previousSize: string) {

    this.previousLocation = previousLocation;
    this.previousSize = previousSize;

  }

}