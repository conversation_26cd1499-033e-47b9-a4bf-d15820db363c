import { Injectable } from '@angular/core';
import { Actions, Effect, ofType, createEffect } from '@ngrx/effects';
import { map, mergeMap } from 'rxjs/operators';
import { TransactionClient,ReceiptPrintingClient } from 'src/app/pos-server.generated';
import * as receiptActions from './receipt.actions'
import { PrintingService } from 'src/app/printing/printing.service';

@Injectable()
export class ReceiptPrintingEffects {
	constructor(
		private actions$: Actions,
		private client: ReceiptPrintingClient,
		private printService: PrintingService
	) {}

	batch$ = createEffect(() => this.actions$.pipe(
		ofType(receiptActions.executeBatch),
		mergeMap(
			(action)=>this.printService.executeBatch(action.payload)
			.pipe(
				map(
					(response) => {
						// successfully printed
						return receiptActions.executeBatchComplete()
					}
				)
			)
		)
	))

	printSale$ = createEffect(() => this.actions$.pipe(
		ofType(receiptActions.printSale),
		mergeMap(
			(action)=>this.client.printSales(action.payload)
			.pipe(
				map(
					(response) => {
						// successfully printed
						return receiptActions.printTransactionConfirmed()
					}
				)
			)
		)
	));

	reprintReceipt$ = createEffect(() => this.actions$.pipe(
		ofType(receiptActions.reprintReceipt),
		mergeMap(
			(action)=>this.client.reprintReceipt(action.payload)
			.pipe(
				map(
					(response) => {
						// successfully printed
						return receiptActions.reprintConfirmed()
					}
				)
			)
		)
	));

	printReturn$ = createEffect(() => this.actions$.pipe(
		ofType(receiptActions.printReturn),
		mergeMap(
			(action)=>this.client.printReturn(action.payload)
			.pipe(
				map(
					(response) => {
						// successfully printed
						return receiptActions.printTransactionConfirmed()
					}
				)
			)
		)
	));


	printGift$ = createEffect(() => this.actions$.pipe(
		ofType(receiptActions.printGiftVoucher),
		mergeMap(
			(action)=>this.client.printGiftVoucher(action.payload)
			.pipe(
				map(
					(response) => {
						// successfully printed
						return receiptActions.printTransactionConfirmed()
					}
				)
			)
		)
	));

	
}
