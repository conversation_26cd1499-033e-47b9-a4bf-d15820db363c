import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PlaceOnHoldComponent } from './place-on-hold.component';

describe('PlaceOnHoldComponent', () => {
  let component: PlaceOnHoldComponent;
  let fixture: ComponentFixture<PlaceOnHoldComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PlaceOnHoldComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PlaceOnHoldComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
