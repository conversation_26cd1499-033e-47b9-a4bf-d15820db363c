<div class="footer-wrapper">
	<div class="container-fluid">
		<div class="row align-items-center">
			<div class="col-auto back-button">
				<button type="button" class="btn-hidden">
					<div
						class="btn btn-lg btn-circle btn-default"
						(click)="backButtonClick()"
					>
						<i
							class="fas fa-caret-left fa-4x color-gradient-grey mr-2"
						></i>
					</div>
				</button>
			</div>

			<div class="col-auto">
				<ng-content></ng-content>
			</div>

			<div *ngIf="showSundries" class="col-auto">
				<button class="btn btn-lg btn-default d-flex align-items-center" (click)="openSundryModal()">
					<i class="fas fa-plus-circle fa-lg"></i>
					<h4 class="ml-3">
						Add<br/>
						Sundry
					</h4>
				</button>
			</div>

			<div *ngIf="showCustomerClub" class="col-auto">
				<div class="btn-group">
					<button
						*ngIf="selectedCustomerClubMember != null"
						type="button"
						(click)="launchCustomerClubModal()"
						class="btn-lg btn-default btn d-flex align-items-center"
					>
						<i class="fas fa-crown fa-lg text-danger"></i>
						<div class="ml-3">
							<h4 class="mb-0">{{selectedCustomerClubMember.firstname}}<br />
								{{selectedCustomerClubMember.surname}}</h4>
							<small class="text-light">Points: {{selectedCustomerClubMember.clientPoints || 0}}</small>
						</div>
					</button>
					<button
						*ngIf="selectedCustomerClubMember == null"
						type="button"
						(click)="launchCustomerClubModal()"
						class="btn-lg btn-default btn d-flex align-items-center"
					>
						<i class="fas fa-crown fa-lg"></i>
						<h4 class="ml-3">
							Customer<br />
							Club
						</h4>
					</button>
					
					<button
						*ngIf="selectedCustomerClubMember != null"
						type="button"
						(click)="deselectCustomerClubMember()"
						class="btn-lg btn-default btn d-flex align-items-center"
					>
						<i class="fas fa-times-circle fa-lg"></i>
					</button>
				</div>
			</div>

			<div class="col-auto d-flex align-items-stretch">
				<button type="button" class="btn-lg btn-default btn d-flex align-items-center"
					[ngClass]="{'rounded-right-0': saleComment}"
					(click)="launchCommentModal()">
					<i [ngClass]="{'text-danger': saleComment}" class="fas fa-comment fa-lg"></i>
					<h4 class="ml-3">Note</h4>
				</button>
				<button *ngIf="saleComment" type="button" 
					(click)="removeComment()"
					class="btn-lg btn-default btn d-flex align-items-center rounded-left-0"
					ngbTooltip="Remove sale note">
					<i class="fas fa-times-circle fa-lg"></i>
				</button>
			</div>

			<div class="col-auto ml-auto">
				<div class="total-container">
					<div class="total-items">
						<h6 class="items-label">
							{{ noItems$ | async }} items
						</h6>
						<h6 class="total-label">Total</h6>
					</div>
					<h1 class="total">
						<span>
							{{ totalValue$ | async | currency }}
						</span>
					</h1>
				</div>
			</div>
			<div class="col-auto next-button">
				<button type="button" class="btn-hidden" (click)="nextBtnClick()" [disabled]="disableNext">
					<div class="btn btn-lg btn-circle btn-default">
						<i class="fas fa-caret-right fa-4x color-gradient-green ml-2"></i>
					</div>
					<h4 class="text-light">{{ nextButtonText }}</h4>
				</button>
			</div>
		</div>
	</div>
</div>
