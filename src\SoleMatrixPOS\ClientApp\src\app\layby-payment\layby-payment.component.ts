import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import * as laybyPaymentActions from '../reducers/layby-payment/layby-payment.actions';
import { CustomerClubDto } from 'src/app/pos-server.generated';
import { ClientSearchClient } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import { LaybySearchComponent } from './layby-search/layby-search.component';
import * as customerClubSearchSelectors from '../reducers/customer-club/club-search/customer-club.selectors';
import * as customerClubSearchActions from '../reducers/customer-club/club-search/customer-club.actions';
import * as customerAddActions from '../reducers/customer-club/customer-add/customer-add.actions';
import * as customerAddSelectors from '../reducers/customer-club/customer-add/customer-add.selectors';
import * as customerClubUpdateActions from '../reducers/customer-club/customer-update/customer-update.actions';
import * as customerClubUpdateSelectors from '../reducers/customer-club/customer-update/customer-update.selectors';
import { Observable } from 'rxjs';
import { filter, switchMap, take } from 'rxjs/operators';
import { TemplateRef, ViewChild } from '@angular/core';
import { ClientSearchRequestDto, ClientSearchKeywordColumnDto, ClientSearchOrderByColumnDto, StockSearchOrderByDirectionEnumDto } from 'src/app/pos-server.generated';
import * as orderActions from '../reducers/order-item/order.actions';
import * as cartActions from '../reducers/sales/cart/cart.actions';

@Component({
  selector: 'pos-layby-payment',
  templateUrl: './layby-payment.component.html',
  styleUrls: ['./layby-payment.component.scss']
})
export class LaybyPaymentComponent implements OnInit {
  customerOrderForm: FormGroup;
  selectedClubMember: CustomerClubDto | null = null;
  handlingCustClub: boolean = false;
  mode: 'order' | 'quote' | 'hold' = 'order';

  @ViewChild('confirmExistingMemberModal', { static: true }) confirmExistingMemberModal: TemplateRef<any>;

  constructor(
    private formBuilder: FormBuilder,
    private store: Store<AppState>,
    private router: Router,
    private modalService: NgbModal,
    private clientSearchClient: ClientSearchClient
  ) {}

  ngOnInit() {
    this.customerOrderForm = this.formBuilder.group({
      clubNumber: [''],
      title: [''],
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      street: [''],
      suburb: [''],
      state: [''],
      postcode: [''],
      phone: ['', [Validators.required, Validators.pattern('^\\d{8,11}$')]],
      email: ['', [Validators.required, Validators.email]]
    });

    this.store.select(customerClubSearchSelectors.selectedCustomerClubMember).subscribe(
      (selected) => {
        this.selectedClubMember = selected;
        if (selected != null) this.fillFormWithCustClubInfo(selected);
      }
    );
  }

  // Existing methods
  searchCustomer(content) {
    this.modalService.open(content, { centered: true, size: 'xl' });
  }

  onBack() {
    this.handlingCustClub = false;
  }

  onUseSelected() {
    this.store.dispatch(customerClubSearchActions.selectCustomerClubMember({ payload: this.selectedClubMember }));
    this.modalService.dismissAll();
  }

  fillFormWithCustClubInfo(clubInfo: CustomerClubDto) {
    this.customerOrderForm.setValue({
      clubNumber: clubInfo.clientCode,
      title: clubInfo.title,
      firstName: clubInfo.firstname,
      lastName: clubInfo.surname,
      street: clubInfo.street,
      suburb: clubInfo.suburb,
      state: clubInfo.state,
      postcode: clubInfo.postcode,
      phone: clubInfo.telephone,
      email: clubInfo.email
    });
  }

  searchMemberByPhone(phoneNumber: string): Observable<CustomerClubDto[]> {
    const searchParams: ClientSearchRequestDto = {
      searchString: phoneNumber,
      first: 25,
      skip: 0,
      customerClubOrderByDirectionEnumDto: StockSearchOrderByDirectionEnumDto.ASC,
      customerClubSearchKeywordColumnDto: ClientSearchKeywordColumnDto.Phone,
      customerClubSearchOrderByColumnDto: ClientSearchOrderByColumnDto.Phone,
    };
    this.clientSearchClient.get(searchParams)
    // Dispatch the search action
    this.store.dispatch(customerClubSearchActions.search({ searchParams }));
  
    // Return an observable that waits until loading is false
    return this.store.select(customerClubSearchSelectors.searchLoading).pipe(
      filter(loading => !loading),
      switchMap(() => this.store.select(customerClubSearchSelectors.searchedCustomerClubMembers)),
      take(1)
    );
  }

  confirmUseExistingMember(existingMember: CustomerClubDto) {
    const modalRef = this.modalService.open(this.confirmExistingMemberModal, { centered: true });

    modalRef.result.then(
      (result) => {
        if (result === 'Yes') {
          // User wants to fill in the details
          this.fillFormWithCustClubInfo(existingMember);
          this.store.dispatch(
            customerClubSearchActions.selectCustomerClubMember({ payload: existingMember }));
        } else {
          // User does not want to use existing member, proceed to create new customer member
          this.createNewCustomerMember();
        }
      }
    );
  }

  createNewCustomerMember() {
    const customerClubDto: CustomerClubDto = {
      clientCode: null, // Will be assigned by the backend
      title: this.customerOrderForm.value.title,
      firstname: this.customerOrderForm.value.firstName,
      surname: this.customerOrderForm.value.lastName,
      careof: 'none',
      street: this.customerOrderForm.value.street,
      suburb: this.customerOrderForm.value.suburb,
      state: this.customerOrderForm.value.state,
      email: this.customerOrderForm.value.email,
      clientPoints: 0,
      telephone: this.customerOrderForm.value.phone,
      postcode: this.customerOrderForm.value.postcode,
      noMail: 'true'
    };

    // Dispatch the action to create a new customer member
    this.store.dispatch(customerAddActions.createMember({ payload: customerClubDto }));

    // Wait for the createMemberSuccess action
    this.store
      .select(customerAddSelectors.customer) // Use the `customer` selector to observe the created member
      .pipe(
        filter(member => !!member), // Ensure the member exists
        take(1) // Complete after one successful creation
      )
      .subscribe((createdMember) => {
        // Dispatch the selection action once the member is created
        this.store.dispatch(customerClubSearchActions.selectCustomerClubMember({ payload: createdMember }));
        console.log('Successfully created and selected member:', createdMember);
      });    

    this.navigateToEntryItem();
  }

  openSearchModal() {
    const modalRef = this.modalService.open(LaybySearchComponent, { centered: true, size: 'xl' });
    modalRef.result.then((result) => {
    }).catch((reason) => {
      console.log('Modal dismissed:', reason);
    });
  }

  updateMember() {
    if (!this.selectedClubMember) {
      console.error("No selected club member to update.");
      return;
    }
  
    const updatedCustomer: CustomerClubDto = {
      clientCode: this.selectedClubMember.clientCode, // Ensure clientCode is included
      title: this.customerOrderForm.value.title,
      firstname: this.customerOrderForm.value.firstName,
      surname: this.customerOrderForm.value.lastName,
      street: this.customerOrderForm.value.street,
      suburb: this.customerOrderForm.value.suburb,
      state: this.customerOrderForm.value.state,
      postcode: this.customerOrderForm.value.postcode,
      telephone: this.customerOrderForm.value.phone,
      email: this.customerOrderForm.value.email,
      careof: this.selectedClubMember.careof, 
      clientPoints: this.selectedClubMember.clientPoints, 
      noMail: this.selectedClubMember.noMail 
    };
    console.log('Updating customer:', updatedCustomer);
    // Dispatch action to update customer
    this.store.dispatch(customerClubUpdateActions.updateMember({ payload: updatedCustomer }));
  
    // Wait for update confirmation before proceeding
    this.store.select(customerClubUpdateSelectors.customer)
      .pipe(
        filter(member => !!member), // Ensure the member is updated
        take(1)
      )
      .subscribe((updatedMember) => {
        console.log('Customer updated successfully:', updatedMember);
        this.navigateToEntryItem(); // Proceed once update is successful
      });
  }

  createEntry() {
    if (this.customerOrderForm.valid) {
      const clubNumber = this.customerOrderForm.get('clubNumber').value;
      const phoneNumber = this.customerOrderForm.get('phone').value;

      if (!clubNumber) {
        this.searchMemberByPhone(phoneNumber).subscribe(
          (members) => {
            if (members && members.length > 0) {
              this.confirmUseExistingMember(members[0]);
            } else {
              this.createNewCustomerMember();
            }
          },
          (error) => {
            console.error('Error searching member by phone:', error);
            this.createNewCustomerMember();
          }
        );
      } else {
        this.updateMember();
      }
    } 
  }

  navigateToEntryItem() {
    this.store.dispatch(laybyPaymentActions.setLaybyOrderInProgress({ inProgress: true }));
    this.router.navigate(['/sales']);
  }

  onMemberSelected(result: CustomerClubDto) {
    this.selectedClubMember = result;
  }
}
