<!--Solemate Header logo etc..-->
<pos-nav-header pageName="Stock Enquiry"></pos-nav-header>

<!--Item look and search bar-->
<div class="justify-content-center">
	<div class="content-wrapper flex-grow-1">
		<div class="container-fluid">
			
			<!--Stock enquiry search-->
			<div class="row mb-5 justify-content-center">
				<pos-item-lookup (result)="stockItemLookup($event)"></pos-item-lookup>
			</div>

			<!--First Main Header-->
			<div class="row mb-3">
				<div class="table-responsive bordered">
					<table class="table table-striped" style="margin: 0">
						<thead>
							<tr>
								<!-- Todo: reorder these to fit -->
								<th scope="col">Style Description</th>
								<th scope="col">Colour Code</th>
								<th scope="col">Maker Code</th>
								<th scope="col">Label</th>
								<th scope="col">Department Code</th>
								<th scope="col">Retail Price</th>
							</tr>
						</thead>
						<!-- *ngIf="selectedItemChanged | async; let item" -->
						<tbody *ngIf="header$ | async; let item">
							<tr>
								<td>{{ item.styleDescription }}</td>
								<td><b>{{ item.colorCode }}</b></td>
								<td>{{ item.makerCode }}</td>
								<td>{{ item.lableCode }}</td>
								<td>{{ item.departmentCode }}</td>
								<td>{{ item.retailPrice }}</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<!--Filter by Color-->
			<div class="row mb-3">
				<div class="form-group col-sm-3 mt-5 mb-3">
					<h5>SELECT COLOUR</h5>
					<pos-select-by-colour (selectedItemChanged)="selectedByColor($event)"></pos-select-by-colour>
				</div>
			</div>

			<!-- Table -->
			<div class="row">
				<div class="col-sm-8 table-responsive">
					<!-- Main Table -->
					<pos-main-table></pos-main-table>
				</div>
				<!-- Total Amount Table -->
				<div class="col-sm-4 table-responsive">
					<pos-total-amount></pos-total-amount>
				</div>
			</div>
		</div>
	</div>
</div>