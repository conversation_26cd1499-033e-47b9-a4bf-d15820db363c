import { Component, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { CustomerClubDto, GiftVoucherClient, GiftVoucherCreationDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import * as customerClubSearchSelectors from '../../../reducers/customer-club/club-search/customer-club.selectors';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import * as giftVoucherSelector from '../../../reducers/gift-voucher/gift-voucher.selectors';
import * as giftVoucherAction from '../../../reducers/gift-voucher/gift-voucher.actions';
import { Observable } from 'rxjs';
import { Router } from '@angular/router';
import { CustomerClubModalComponent } from 'src/app/customer-club/customer-club-modal/customer-club-modal.component';
import { CreateErrorModal } from 'src/app/error-modal/error-modal.component';


@Component({
    selector: 'pos-form-modal',
    templateUrl: './form-modal.component.html',
    styleUrls: ['./form-modal.component.scss']
})
export class FormModalComponent implements OnInit {

    giftFormC: FormGroup;
    get f() { return this.giftFormC.controls; }
    get voucherNumberCtrl() { return this.f.giftVoucherNumber; }
    giftVoucherNo: Observable<string>;

    selectedCustomerClubMember$: any;
    selectedCustomerClubMember: CustomerClubDto = null;

    customVoucherNumber: boolean = false;

    @Input() showCustomerClub: boolean;

    GENERATED_NUMBER_PLACEHOLDER: string = "<Generated>"


    constructor(private formbuilder: FormBuilder, private modalService: NgbModal, private store: Store<AppState>, private router: Router, private giftVoucherClient: GiftVoucherClient) { }

    ngOnInit() {

        this.initState();
        this.subscribeToState();
        this.voucherNumberCtrl.setValue(this.GENERATED_NUMBER_PLACEHOLDER);

        this.launchCustomerClubModal();

    }

    initState() {
        this.giftFormC = this.formbuilder.group({
            giftVoucherNumber: ['', [Validators.required, this.voucherNoValidator(this.GENERATED_NUMBER_PLACEHOLDER, 5, 13)]],
            giftValue: ['', [Validators.required, Validators.pattern(/^(?:\d+(?:\.\d{0,2})?|\.\d{1,2})$/)]],
        });

    }

    toggleCustomVoucherNumber(){
        this.customVoucherNumber = !this.customVoucherNumber;
        this.voucherNumberCtrl.setValue(this.customVoucherNumber ? "" : this.GENERATED_NUMBER_PLACEHOLDER);
    }

    subscribeToState() {
        //get the club member
        this.selectedCustomerClubMember$ = this.store.select(customerClubSearchSelectors.selectedCustomerClubMember);
        this.selectedCustomerClubMember$.subscribe((s) => {
            this.selectedCustomerClubMember = s;
        });
    }

    //launch customer club modal
    launchCustomerClubModal() {
        const modalRef = this.modalService.open(CustomerClubModalComponent, { size: 'xl', centered: true });
        modalRef.componentInstance.name = 'CustomerClubModal';
        modalRef.result.then((result) => {
            if (!result)
                this.router.navigateByUrl("/home");
        }).catch(error => {
            this.router.navigateByUrl("/home");
        });
    }

    voucherNoValidator(excludedString: string, minLength: number, maxLength: number): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (control.value === excludedString) {
                return null; // Skip validation if the value is the excluded string
            }

            if (control.value.length < minLength || control.value.length > maxLength) {
                return { "lengthError": `Vouchers must be between ${minLength} and ${maxLength} characters long` };
            }

            return null; // Valid case
        };
    }

    //populate the gift-voucher number into form
    populateGiftNo(value) {
        this.giftFormC.controls.giftVoucherNumber.setValue(value);
    }

    setTwoNumberDecimal(el) {
        el.value = parseFloat(el.value).toFixed(2);
    }

    backBtnClick() {
        this.router.navigateByUrl('/home');
    }

    //next button if the form is not valid cannot procceed
    async nextBtnClick() {
		if (this.giftFormC.valid) {
			var voucherNoValue = (this.giftFormC.get('giftVoucherNumber').value as string).trim();
			this.giftVoucherClient.existsGiftVoucher(false, voucherNoValue)
				.subscribe({
					next: voucher => {
						if (voucher) {
							CreateErrorModal(this.modalService, false, "Gift Voucher number already in use");
							return;
						}
						else {
							//dispatch the form vavlue into the gift cart
							let giftVoucher: GiftVoucherCreationDto = this.createGiftVoucher();
							this.store.dispatch(giftVoucherAction.addGiftVoucher({ giftCard: giftVoucher }));

							//pass reactive form value to the router to get the form value from any component
							//this.router.navigate(['/gift/payment', this.giftFormC.value]);
							this.router.navigateByUrl('/gift/payment');
						}
					},
					error: err => {
						console.error("Error during voucher search:", err);
						return;
					}
				});
        } else {

            console.log(this.giftFormC);
            CreateErrorModal(this.modalService, false, "You will need to fill the form to proceed!.");
        }

    }

    createGiftVoucher(): GiftVoucherCreationDto {
        var voucherNoValue = (this.giftFormC.get('giftVoucherNumber').value as string).trim();
        return {
            clientCode: this.selectedCustomerClubMember.clientCode,
            voucherFunds: parseFloat(this.giftFormC.get('giftValue').value),
            // Pass along null if needed
            voucherNo: (voucherNoValue == this.GENERATED_NUMBER_PLACEHOLDER) ? null : voucherNoValue
        } as GiftVoucherCreationDto;
    }
}
