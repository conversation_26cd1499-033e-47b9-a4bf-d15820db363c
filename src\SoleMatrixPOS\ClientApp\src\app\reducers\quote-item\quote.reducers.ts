import { createReducer, on } from '@ngrx/store';
import * as QuoteActions from './quote.actions';

export interface QuoteState {
  selectedQuote: any;
  uploadedQuoteCode: string | null;
  loading: boolean;
  error: any;
  quoteNo: string | null;
}

export const initialState: QuoteState = {
  selectedQuote: null,
  uploadedQuoteCode: null,
  loading: false,
  error: null,
  quoteNo: null
};

export const quoteReducer = createReducer(
  initialState,
  
  on(QuoteActions.createQuote, (state) => ({
    ...state,
    loading: true,
    error: null
  })),
  
  on(QuoteActions.createQuoteSuccess, (state) => ({
    ...state,
    loading: false
  })),
  
  on(QuoteActions.createQuoteFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  on(QuoteActions.cancelQuote, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(QuoteActions.cancelQuoteSuccess, (state) => ({
    ...state,
    loading: false
  })),

  on(QuoteActions.cancelQuoteFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  on(QuoteActions.completeQuote, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(QuoteActions.completeQuoteSuccess, (state) => ({
    ...state,
    loading: false
  })),

  on(QuoteActions.completeQuoteFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),
  
  on(QuoteActions.clearUploadedQuoteCode, (state) => ({
    ...state,
    uploadedQuoteCode: null
  })),

  on(QuoteActions.uploadQuoteCode, (state, { quoteCode }) => ({
    ...state,
    uploadedQuoteCode: quoteCode
  })),

  on(QuoteActions.getQuoteNo, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(QuoteActions.getQuoteNoSuccess, (state, { quoteNo }) => ({
    ...state,
    quoteNo,
    loading: false,
    error: null
  })),

  on(QuoteActions.getQuoteNoFailure, (state, { error }) => ({
    ...state,
    quoteNo: null,
    loading: false,
    error
  })),
);
