import { createReducer, on, Action } from '@ngrx/store';
import { checkEquivalence } from '../../../generic-stock-table/stock-table-item/stock-table-item';
import { StockTableItemDto } from '../../../pos-server.generated';
import * as receiveItemScanActions from "./receive-item-scan.actions"

export class ReceiveItemScanState {
    itemBarcodeSearchInProgress: boolean;
    tableItems: StockTableItemDto[];
    barcodeValidity: boolean;
    receivalSubmitSuccess: boolean;
}

export const initialState: ReceiveItemScanState = {
    itemBarcodeSearchInProgress: false,
    tableItems: [],
    barcodeValidity: true,
    receivalSubmitSuccess: false
}

export const receiveItemScanReducer = createReducer(initialState,
    on(receiveItemScanActions.init, (state) => initialState),
    on(
        receiveItemScanActions.submitItemBarcode,
        (state) => {
            return { ...state, itemBarcodeSearchInProgress: true };
        }
    ),
    on(
        receiveItemScanActions.invalidItemBarcode,
        (state, action) => {
            return {
                ...state,
                barcodeValidity: false,
                itemBarcodeSearchInProgress: false
            }
        }
    ),
    on(
        receiveItemScanActions.addItem,
        (state, action) => {
            // Check all other items
            for(var i = 0; i < state.tableItems.length; i ++){
                if(checkEquivalence(state.tableItems[i], action.item)){
                    // Get the list intermediately
                    var iTableItems = [...state.tableItems];
                    iTableItems[i] = {stockItem: iTableItems[i].stockItem, quantity: iTableItems[i].quantity + 1};
                    return {
                        ...state,
                        tableItems: iTableItems
                    };
                }
            }

            // If haven't returned here, then we have a new item that hasn't been seen before
            return {
                ...state,
                itemBarcodeSearchInProgress: false,
                tableItems: [...state.tableItems, action.item],
                barcodeValidity: true
            };
        }
    ),
    on(
        receiveItemScanActions.updateItemQuantity,
        (state, action) => {
            // Get the list intermediately
            var iTableItems = [...state.tableItems];
            iTableItems[action.index] = {stockItem: iTableItems[action.index].stockItem, quantity: iTableItems[action.index].quantity + 1};
            return {
                ...state,
                tableItems: iTableItems
            };
        }
    ),
    on(
        receiveItemScanActions.removeItem,
        (state, action) => {
            // Get the list intermediately
            var iTableItems = [...state.tableItems];
            // Splice at index
            iTableItems.splice(action.index, 1);
            return {
                ...state,
                tableItems: iTableItems
            };
        }
    ),
    on(
        receiveItemScanActions.receivalSubmissionCompleted,
        (state, action) => {
            return {...state, receivalSubmitSuccess: true}
        }
    )

)