<button *ngIf="buttonInfo != null" type="button"
    class="btn btn-block btn-lg btn-outline-default d-flex flex-column align-items-center justify-content-center" (click)="onClick()">
    <div class="row align-items-center">
        <div class="col-2 align-items-center">
            <i *ngIf="!buttonInfo.useImage" [ngClass]="buttonInfo.imgOrIconClass" class="fas fa-lg align-items-center"></i>
            <img *ngIf="buttonInfo.useImage" src={{buttonInfo.imgOrIconClass}} style="width:25px;height:25px;">
        </div>
        <div class="col-2 align-items-center">
            <h4 class="ml-4 text-secondary align-items-center">{{buttonInfo.text}}</h4>
        </div>
    </div>
</button>