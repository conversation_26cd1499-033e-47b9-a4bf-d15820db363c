import { createReducer, on} from '@ngrx/store';
import {TransferReasonDto, TransferDestinationResponseDto, LocationDto, SendStockItemDto} from '../../pos-server.generated';
import * as sendStockActions from './send-stock.actions';
import { StockCartItem } from '../stocketake-entry/itemCart/itemCart.reducers';

export function ItemToProcess(item: StockCartItem, lineNumber: number): SendStockItemDto{
    return {
        styleCode: item.stockItem.styleCode,
		colourCode: item.stockItem.colourCode,
        size: item.stockItem.size,
        transferOutQuantity: item.quantity,
        lineNo: lineNumber,
    } as SendStockItemDto;
}

export class SendStockState {
	lookups: {
		reasons: TransferReasonDto[],
		destinations: TransferDestinationResponseDto,
	};
	reason: TransferReasonDto;
	destination: LocationDto;
	transferNumber: string;
	isloading: boolean;
	completed: boolean;
}

export const initialState = {
	lookups: {
		reasons: [],
		destinations: []
	},
	reason: null,
	destination: null,
	transferNumber: '',
	isloading: false,
	completed: false
} as SendStockState;

export const sendStockReducer = createReducer(initialState,
	on(sendStockActions.init, state => initialState),
	on(sendStockActions.setReasonsLookup, (state, action) => {
		return { ...state, lookups: {...state.lookups, reasons: action.reasons.filter(r => r)}};
	}),


	on(sendStockActions.setReason, (state, action) => {
		return {...state, reason: action.reason };
	}),
	on(sendStockActions.setDestinationsLookup, (state, action) => {
		return { ...state, lookups: {...state.lookups, destinations: action.destinations}};
	}),
	

	on(sendStockActions.setDestination, (state, action) => {
		return {...state, destination: action.destination };
	}),
	on(sendStockActions.setTransferNumber, (state, action) => {
		return {...state, transferNumber: action.transferNumber };
	}),

	on(sendStockActions.submit, (state, action) => {
		return {...state, isloading: true }
	}),
	on(sendStockActions.submitResponse, (state, action) => {
		return {...state, isloading: false, completed: true}
	}),
	on(sendStockActions.resetTransfer, (state) => ({
		...state,
		reason: null,
		destination: null,
		transferNumber: '',
		completed: false,
		// Preserve the lookups and other state
		lookups: {
			...state.lookups,
		},
		isloading: false
	}))
);


