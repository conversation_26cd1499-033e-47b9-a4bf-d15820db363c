<pos-nav-header [pageName]="'Layby Search'"></pos-nav-header>
<div class="content-wrapper flex-grow-1 pt-10">
  <div class="container-fluid">
    <div class="row text-align-center mt-2 mb-4">
      <i class="fas fa-lg fa-fw fa-shopping-cart text-danger mt-3 mr-2 ml-4 text-shadow"></i>
      <h3 class="mt-3 mb-0 mr-2 text-danger">Search Laybys</h3>

      <!-- Search field dropdown with all 6 allowed fields -->
      <div class="col-2 pr-0">
        <select class="form-control form-control-special" [(ngModel)]="field" (change)="search()">
          <option value="LaybyCode">Layby Code</option>
          <option value="Firstname">First Name</option>
          <option value="Surname">Surname</option>
          <option value="Street">Street</option>
          <option value="PhoneNumber">Phone Number</option>
          <option value="Email">Email</option>
        </select>
      </div>

      <!-- Search input -->
      <div class="col-3 pl-0">
        <input
          type="text"
          class="form-control"
          [(ngModel)]="term"
          (keyup)="search()"
          autofocus
        />
      </div>
    </div>

    <!-- Loading Spinner -->
    <div *ngIf="loading">
      <mat-spinner style="margin:0 auto;" mode="indeterminate"></mat-spinner>
    </div>

    <!-- Layby Results Table -->
    <div class="row m-2" *ngIf="!loading">
      <div class="table-responsive">
        <table class="table table-striped table-hover table-fit">
          <thead>
            <tr>
              <th>Layby Code</th>
              <th>Client Code</th>
              <th>First Name</th>
              <th>Last Name</th>
              <th>Last Payment Date</th>
              <th>Balance Due</th>
              <th>Email</th>
              <th>Cancel Layby</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let layby of laybys$ | async">
              <tr
                (click)="selectLayby(layby)"
                [ngClass]="{'selectedOrder': selectedLayby && layby.laybyCode === selectedLayby.laybyCode}"
              >
                <td>
                  <ngb-highlight [result]="layby.laybyCode || 'N/A'" [term]="term" [highlightClass]="'search-highlight'">
                  </ngb-highlight>
                </td>
                <td>
                  <ngb-highlight [result]="layby.clientCode || 'N/A'" [term]="term" [highlightClass]="'search-highlight'">
                  </ngb-highlight>
                </td>
                <td>
                  <ngb-highlight [result]="layby.firstname || 'N/A'" [term]="term" [highlightClass]="'search-highlight'">
                  </ngb-highlight>
                </td>
                <td>
                  <ngb-highlight [result]="layby.surname || 'N/A'" [term]="term" [highlightClass]="'search-highlight'">
                  </ngb-highlight>
                </td>
                <td>{{ layby.laybyLastPaymentDate | date: 'dd/MM/yy' }}</td>
                <td>{{ layby.balanceDue | currency }}</td>
                <td>
                  <ngb-highlight [result]="layby.email || 'N/A'" [term]="term" [highlightClass]="'search-highlight'">
                  </ngb-highlight>
                </td>
                <td>
                  <button class="btn btn-danger"
                          (click)="cancelLayby(layby); $event.stopPropagation()"
                          [disabled]="loading">
                    Cancel Layby
                  </button>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
