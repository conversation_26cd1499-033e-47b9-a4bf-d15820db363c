import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { CustomerClubDto } from '../../pos-server.generated'; // Ensure correct import path
import { AppState } from 'src/app/reducers';
import { Store } from '@ngrx/store';
import * as customerClubSearchActions from '../../reducers/customer-club/club-search/customer-club.actions';
import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';

@Component({
  selector: 'pos-customer-club-container',
  templateUrl: './customer-club-container.component.html',
  styleUrls: ['./customer-club-container.component.scss']
})
export class CustomerClubContainerComponent implements OnInit {

  @Output() onSelectedMember = new EventEmitter<CustomerClubDto>();
  @Output() isEditing = new EventEmitter<boolean>();
  @Output() onMemberDoubleClick = new EventEmitter<CustomerClubDto>();

  member: CustomerClubDto = null;
  _isViewing: boolean = false;

  get isViewing(): boolean {
    return this._isViewing;
  }

  set isViewing(value: boolean) {
    this._isViewing = value;
    this.isEditing.emit(value);
  }

  constructor(private store: Store<AppState>) {}

  ngOnInit() {
    // Subscribe to store for the selected customer member and handle the change
    this.store.select(customerClubSearchSelectors.selectedCustomerClubMember)
      .subscribe(value => {
        this.member = value;
        this.onSelectedMember.emit(value); // Emit selected member to the parent component
      });
  }

  onMemberSelected(member: CustomerClubDto) {
    this.member = member;
    this.onSelectedMember.emit(member);
  }

  // Add new method for confirming selection
  confirmMemberSelection(member: CustomerClubDto) {
    if (member) {
      this.store.dispatch(customerClubSearchActions.selectCustomerClubMember({ payload: member }));
    }
  }

  // Handle member editing logic
  onMemberEdited(member: CustomerClubDto) {
    this.member = member;
    this.isViewing = true;
  }

  // Handle back action for viewing state
  onBack() {
    this.isViewing = false;
  }

  onDoubleClickMember(member: CustomerClubDto) {
    this.confirmMemberSelection(member);
    this.onMemberDoubleClick.emit(member);
  }
}
