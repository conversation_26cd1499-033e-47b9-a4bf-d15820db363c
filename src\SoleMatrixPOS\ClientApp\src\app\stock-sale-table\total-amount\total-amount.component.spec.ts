import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TotalAmountComponent } from './total-amount.component';
import { TotalAmountService } from './total-amount.service';
import { RemoveZerosPipe } from '../pipes/remove-zeros.pipe';
import { Observable, of } from 'rxjs';
import { Location } from '../classes/location.model';
import { Stock } from '../classes/stock';
import { Sale } from '../classes/sale';
import { By } from '@angular/platform-browser';


type Trading = "T" | "F";
type Toggle = "on" | "off";

class MockTotalAmountService {
  public locations$: Observable<Location[]>;

  constructor() {
    this.locations$ = of(this.locations);
  }

  private locations = [
    {
      name: "Location 1",
      trading: "T" as Trading,
      rank: 1,
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 2",
      trading: "T" as Trading,
      rank: 1,
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 3",
      trading: "T" as Trading,
      rank: 1,
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 4",
      trading: "T" as Trading,
      rank: 1,
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
  ]

}

describe('TotalAmountComponent', () => {
  let component: TotalAmountComponent;
  let fixture: ComponentFixture<TotalAmountComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [TotalAmountComponent, RemoveZerosPipe],
      providers: [
        TotalAmountComponent,
        {
          provide: TotalAmountService,
          useClass: MockTotalAmountService
        }
      ]
    });

    component = TestBed.get(TotalAmountComponent)
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display table data', () => {
    fixture = TestBed.createComponent(TotalAmountComponent);
    fixture.detectChanges();

    const tableElements = fixture.debugElement.queryAll(By.css('table'));
    expect(tableElements.length).toBe(1);
  });

  describe('Table Header', () => {

    it('should contain "Total Amount"', () => {

      fixture = TestBed.createComponent(TotalAmountComponent);
      fixture.detectChanges();
      const tableElements = fixture.debugElement.queryAll(By.css('.table-info'));

      expect(tableElements[0].nativeElement.textContent.trim()).toContain("Total Amount")

    });

  });

});
