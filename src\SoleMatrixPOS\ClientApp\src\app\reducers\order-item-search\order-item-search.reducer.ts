// src/app/reducers/order-item-search/order-item-search.reducer.ts

import { Action, createReducer, on } from '@ngrx/store';
import * as orderSearchActions from './order-item-search.actions';
import { CreateOrderDto, OrderSearchRequestDto } from '../../pos-server.generated';

export interface OrderSearchState {
  isLoading: boolean;
  orders: CreateOrderDto[];
  options: OrderSearchRequestDto;
  selected: CreateOrderDto | null;
  error: any; // To handle errors
}

export const initialState: OrderSearchState = {
  isLoading: false,
  orders: [],
  options: {
    searchString: '',
    first: 0,
    skip: 0,
    orderSearchKeywordColumnDto: null,
    orderSearchOrderByColumnDto: null,
    orderSearchOrderByDirectionEnumDto: null,
  } as OrderSearchRequestDto,
  selected: null,
  error: null,
};

export const orderSearchReducer = createReducer(
  initialState,
  on(orderSearchActions.init, () => ({
    ...initialState,
  })),
  on(orderSearchActions.search, (state, action) => ({
    ...state,
    orders: [],
    isLoading: true,
    options: action.searchParams,
  })),
  on(orderSearchActions.searchResponse, (state, action) => ({
    ...state,
    isLoading: false,
    orders: action.payload,
  })),
  on(orderSearchActions.selectOrder, (state, action) => ({
    ...state,
    selected: action.payload,
  })),
  // Handle Cancel Order Actions
  on(orderSearchActions.cancelOrder, (state) => ({
    ...state,
    isLoading: true,
    error: null,
  })),
  on(orderSearchActions.cancelOrderSuccess, (state, action) => ({
    ...state,
    isLoading: false,
    orders: state.orders.filter(order => order.orderHeader.orderCode !== action.orderCode),
    selected: null,
  })),
  on(orderSearchActions.cancelOrderFailure, (state, action) => ({
    ...state,
    isLoading: false,
    error: action.error,
  }))
);

export function reducer(state: OrderSearchState | undefined, action: Action) {
  return orderSearchReducer(state, action);
}
