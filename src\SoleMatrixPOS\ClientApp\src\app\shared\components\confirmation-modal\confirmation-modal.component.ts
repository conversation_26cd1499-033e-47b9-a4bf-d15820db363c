import { Component, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-confirmation-modal',
  template: `
    <div class="modal-header">
      <h4 class="modal-title">{{title}}</h4>
    </div>
    <div class="modal-body">
      <p>{{message}}</p>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-primary" (click)="activeModal.close('confirm')">
        {{confirmButtonText}}
      </button>
      <button type="button" class="btn btn-secondary" (click)="activeModal.dismiss('cancel')">
        {{cancelButtonText}}
      </button>
    </div>
  `
})
export class ConfirmationModalComponent {
  @Input() title: string;
  @Input() message: string;
  @Input() confirmButtonText: string = 'Confirm';
  @Input() cancelButtonText: string = 'Cancel';

  constructor(public activeModal: NgbActiveModal) {}
} 