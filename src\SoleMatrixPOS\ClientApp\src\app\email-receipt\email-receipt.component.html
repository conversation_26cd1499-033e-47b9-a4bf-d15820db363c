<div class="modal-content position-relative">
  <div class="modal-header">
    <h5 class="modal-title">Email Receipt</h5>
    <button type="button" class="close" aria-label="Close" (click)="onCancel()" [disabled]="isLoading">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div class="modal-body">
    <div class="email-receipt">
      <form>
        <div class="form-group">
          <label for="email">Send Receipt To Email</label>
          <input
            type="email"
            id="email"
            name="email"
            [(ngModel)]="email"
            class="form-control"
            placeholder="Enter email"
            [disabled]="isLoading"
          />
        </div>
      </form>
    </div>
  </div>

  <div class="modal-footer">
    <button (click)="onSendReceipt()" class="btn btn-primary" [disabled]="isLoading">
      Send Receipt
    </button>
    <button (click)="onCancel()" class="btn btn-secondary ml-2" [disabled]="isLoading">
      Close
    </button>
  </div>

  <!-- Spinner overlay with cancel button -->
  <div *ngIf="isLoading" class="spinner-overlay d-flex flex-column justify-content-center align-items-center">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
</div>
