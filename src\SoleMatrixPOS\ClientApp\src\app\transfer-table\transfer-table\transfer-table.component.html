<!-- Main Table -->
<div class="table">
    <table class="table table-striped">
        <!-- Header Table component-->
        <thead>
            <tr class="table-info">
                <th mat-header-cell scope="col" class="title-border-square">
                    Location
                </th>
                <!-- Display the sizes -->
                <ng-container *ngFor="let size of transferTableService.sizes$ | async">
                    <th mat-header-cell scope="col" class="title-border-square">
                        {{size.size}}
                    </th>
                </ng-container>
            </tr>
        </thead>
        <!-- Body Table -->
        <tbody cdkDropListGroup>
            <!-- ng-container is a very useful tag for implementing logic without affecting the heirarchy -->
            <ng-container *ngFor="let location of transferTableService.locations$ | async">
                <ng-container *ngIf="location.trading == 'T'">
                    <!-- locations's Quantity Rows-->
                    <tr>
                        <!-- Location Title -->
                        <th mat-header-cell scope="row" class="border-square">
                            {{location.name}}
                        </th>
                        <!-- location's Quantity -->
                        <ng-container *ngFor="let size of transferTableService.sizes$ | async">
                            <ng-container *ngFor="let stock of location.stock">
                                <ng-container *ngIf="stock.size == size.size">
                                    <ng-container class="example-list">

                                        <!-- Should proabably move to a new component. A bit fiddley -->
                                        <ng-container *ngIf="stock.highlight == 'from'">
                                            <!-- Drag and Drop Execution
                                                [cdkDropListSortingDisabled]="true"
                                            -->
                                            <td mat-cell
                                                class="highlight-from"
                                                #elem [id]="stock.size" 
                                                cdkDropList 
                                                [cdkDropListLockAxis]="'y'"
                                                [cdkDropListData]="location.stock"
                                                (cdkDragStarted)="pickup(location.name, stock.size)"
                                                (cdkDropListDropped)="popUpDrop($event, previousLocation, location.name, 'BOUNCE', previousSize)"
                                                cdkDrag
                                            >
                                                {{stock.qty | removeZeros}}
                                        </td>
                                        </ng-container>
                                        <ng-container *ngIf="stock.highlight == 'to'">
                                            <!-- Drag and Drop Execution
                                                [cdkDropListSortingDisabled]="true"
                                            -->
                                            <td mat-cell
                                                class="highlight-to"
                                                #elem [id]="stock.size" 
                                                cdkDropList 
                                                [cdkDropListLockAxis]="'y'"
                                                [cdkDropListData]="location.stock"
                                                (cdkDragStarted)="pickup(location.name, stock.size)"
                                                (cdkDropListDropped)="popUpDrop($event, previousLocation, location.name, 'BOUNCE', previousSize)"
                                                cdkDrag
                                            >
                                                {{stock.qty | removeZeros}}
                                        </td>
                                        </ng-container>
                                        <ng-container *ngIf="stock.highlight == 'off'">
                                            <!-- Drag and Drop Execution
                                                [cdkDropListSortingDisabled]="true"
                                            -->
                                            <td mat-cell
                                                class="example-box"
                                                #elem [id]="stock.size" 
                                                cdkDropList 
                                                [cdkDropListLockAxis]="'y'"
                                                [cdkDropListData]="location.stock"
                                                (cdkDragStarted)="pickup(location.name, stock.size)"
                                                (cdkDropListDropped)="popUpDrop($event, previousLocation, location.name, 'BOUNCE', previousSize)"
                                                cdkDrag
                                            >
                                                {{stock.qty | removeZeros}}
                                        </td>
                                        </ng-container>
                                    </ng-container>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                    </tr>
                    <!-- locations's Sales Rows-->
                    <tr class="table-danger hideSales">
                        <!-- Sales Title -->
                        <ng-container *ngIf="location.toggle != 'on' || location.toggle == null ">
                            <th mat-header-cell scope="row" class="border-square">
                                SALES
                            </th>
                            <!-- location's  Sales -->
                            <ng-container *ngFor="let size of transferTableService.sizes$ | async">
                                <ng-container *ngFor="let sale of location.sales">
                                    <ng-container *ngIf="sale.size == size.size">
                                        <ng-container>
                                            <td class="stock-border-square sale-qty-color-square">
                                                {{sale.qty | removeZeros}}
                                            </td>
                                        </ng-container>
                                    </ng-container>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                    </tr>
                </ng-container>
            </ng-container>
        </tbody>
    </table>
</div>