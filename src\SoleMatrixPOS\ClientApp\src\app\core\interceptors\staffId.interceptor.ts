import { Injectable } from '@angular/core';
import {
  <PERSON>ttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor
} from '@angular/common/http';
import { Observable } from 'rxjs';
import * as staffSelectors from '../../reducers/staff/staff.selectors';
import { AppState } from '../../reducers';
import { Store } from '@ngrx/store';
import { first, flatMap } from 'rxjs/operators';

@Injectable()
export class StaffIdInterceptor implements HttpInterceptor {

	constructor(public store: Store<AppState>) {}

	intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {

		return this.store.select(staffSelectors.selectStaffLoginDto).pipe(
			first(),
			flatMap(staffDto => {
				if (staffDto && staffDto.code) {
					request = request.clone({
						setHeaders: { SolemateStaffCode: staffDto.code }
					});
				}
				return next.handle(request);
			}),
		);
	}

}
