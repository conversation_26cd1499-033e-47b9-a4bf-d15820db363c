import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { WaitingForEftposModalComponent } from './waiting-for-eftpos-modal.component';

describe('WaitingForEftposModalComponent', () => {
  let component: WaitingForEftposModalComponent;
  let fixture: ComponentFixture<WaitingForEftposModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ WaitingForEftposModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(WaitingForEftposModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
