import { Component, OnInit, Input } from '@angular/core';
import { Store } from '@ngrx/store';
import { Router } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { AppState } from 'src/app/reducers';
import { TranslogDto, TranspayDto, TransactionClient, StockItemDto } from 'src/app/pos-server.generated';
import { switchMap } from 'rxjs/operators';
import * as cartActions from '../../reducers/sales/cart/cart.actions';
import { CartItem } from '../../reducers/sales/cart/cart.reducer';

@Component({
  selector: 'pos-history-refund',
  template: `
    <div class="modal-header">
      <h4 class="modal-title">Processing Return</h4>
    </div>
    <div class="modal-body text-center">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
      <p class="mt-2">Loading transaction details...</p>
    </div>
  `
})
export class HistoryRefundComponent implements OnInit {
  @Input() transNo: number;
  @Input() transType: number;
  @Input() tillNo: string;
  @Input() storeId: string;

  constructor(
    private store: Store<AppState>,
    private router: Router,
    public activeModal: NgbActiveModal,
    private transactionClient: TransactionClient
  ) { }

  ngOnInit() {
    // First, clear the cart
    this.store.dispatch(cartActions.init());
    
    // Fetch transaction details
    this.transactionClient.getTransLogByTransNo(this.transNo)
      .pipe(
        switchMap((logs: TranslogDto[]) => {
          // Process each valid translog entry
          logs.forEach((log, index) => {
            if (log.styleCode !== "Reason" && log.quantity > 0) {
              // Create a cart item directly from the translog
              const cartItem = {
                stockItem: {
                  barcode: `${log.styleCode}${log.colourCode}${log.sizeCode}`,
                  styleCode: log.styleCode,
                  colourCode: log.colourCode,
                  size: log.sizeCode,
                  price: log.sellingPrice,
                  rrp: log.sellingPrice,
                  styleDescription: log.styleCode, // Use styleCode as description if needed
                  makerCode: '',
                  labelCode: '',
                  departmentCode: '',
                  colourName: log.colourCode // Use colourCode as name if needed
                },
                quantity: log.quantity,
                bestValue: log.sellingPrice,
                originalPrice: log.sellingPrice,
                lineNo: log.lineNo || index + 1
              };
              
              // Add the item to the cart using addSuspendedSaleItem which preserves prices
              this.store.dispatch(cartActions.addSuspendedSaleItem({ 
                cartItem: cartItem,
                reason: log.discReasonCode,
                reasonCode: log.discReasonCode
              }));
            }
          });
          
          // Proceed to get transaction payment details
          return this.transactionClient.getTransPayByTransNo(this.transNo);
        })
      )
      .subscribe({
        next: (transpay: TranspayDto[]) => {
          console.log('Transpay:', transpay);
          this.activeModal.close();
          this.router.navigate(['returns']);
        },
        error: (err) => {
          console.error('Error fetching transaction details:', err);
          this.activeModal.dismiss('Error loading transaction details');
        }
      });
  }
} 