import { createAction, props } from "@ngrx/store";
import { ReceiptUpdateDto } from "src/app/pos-server.generated";

export const init = createAction('[Receipt] Init');

export const updateRec = createAction('[Receipt] Update', props<{updateRec: ReceiptUpdateDto}>());
export const updateRecSuccess = createAction('[Receipt] Update Success', props<{result: ReceiptUpdateDto}>());
export const updateError = createAction('[Receipt] Receipt Update Error', props<{error: string}>());

export const loggedout = createAction('[Receipt] logout');