.modal-body {
    background-color: whitesmoke;
    border-radius: 11px;
}

.modal {
    background-color: rgba(58, 51, 51, 0.4);
    padding-top: 5px;
}

.form-group {
    margin-bottom: 1rem;
}

.form-label {
    font-weight: bold;
}

.form-control:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
}

.quantity-btn {
    min-width: 40px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:active {
        transform: scale(0.95);
    }
}

.input-group-text {
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    color: #495057;
}

.quantity-input-wrapper {
    position: relative;
    flex: 1;
    
    input {
        border-radius: 4px;
        width: 100%;
    }
}

// If the input is in an input-group, adjust the border radius
.input-group .quantity-input-wrapper {
    input {
        border-radius: 0;
    }
}