<div class="content-wrapper pt-10">
	<div class="m-2 d-flex justify-content-between">
		<form [formGroup]="pairingForm" (ngSubmit)="submitPINPairCode()" class="d-flex flex-column justify-content-center align-items-center w-100">

			<div class="form-group w-75">
				<label for="username">Linkly Username</label>
				<input id="username"
					   formControlName="username"
					   type="text"
					   class="form-control"
					   [class.is-invalid]="pairingForm.get('username').invalid && pairingForm.get('username').touched" />
				<small *ngIf="pairingForm.get('username').invalid && pairingForm.get('username').touched" class="text-danger">
					Username is required.
				</small>
			</div>

			<div class="form-group w-75">
				<label for="password">Linkly Password</label>
				<input id="password"
					   formControlName="password"
					   type="password"
					   class="form-control"
					   [class.is-invalid]="pairingForm.get('password').invalid && pairingForm.get('password').touched" />
				<small *ngIf="pairingForm.get('password').invalid && pairingForm.get('password').touched" class="text-danger">
					Password is required.
				</small>
			</div>

			<div class="form-group w-75">
				<label for="pairingCode">PIN Pad Pairing Code</label>
				<input id="pairingCode"
					   formControlName="pairingCode"
					   type="text"
					   class="form-control"
					   [class.is-invalid]="pairingForm.get('pairingCode').invalid && pairingForm.get('pairingCode').touched" />
				<small *ngIf="pairingForm.get('pairingCode').invalid && pairingForm.get('pairingCode').touched" class="text-danger">
					Pairing Code is required.
				</small>
			</div>

			<button type="submit" class="btn btn-success mt-3" [disabled]="pairingForm.invalid">
				Pair PIN Pad
			</button>

			<button class="text-secondary float-right mt-2" type="button" (click)="dismiss('Cross Click')">X</button>

		</form>
	</div>
</div>
