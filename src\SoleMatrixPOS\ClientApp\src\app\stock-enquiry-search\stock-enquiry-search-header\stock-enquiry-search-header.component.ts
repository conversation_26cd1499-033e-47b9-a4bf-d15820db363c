import {
	<PERSON>mpo<PERSON>,
	ElementRef,
	<PERSON><PERSON><PERSON>ter,
	OnDestroy,
	OnInit,
	Output,
	ViewChild,
	AfterViewInit,
	Input
} from '@angular/core';
import { Subject } from 'rxjs';
import { debounceTime, filter, takeWhile } from 'rxjs/operators';
import {
	SortDirection,
	StockSearchFields,
	StockSearchRequestDto
} from '../../pos-server.generated';

class StockSearchOptions {
	constructor() {
		this.searchString = '';
		this.first = 20;
		this.skip = 0;
		this.searchByField = StockSearchFields.StyleCode;
		this.orderByField = StockSearchFields.StyleCode;
		this.sortDirection = SortDirection.ASC;
		this.collapseColours = false;
	}

	searchString: string;
	first: number;
	skip: number;
	searchByField: StockSearchFields;
	orderByField: StockSearchFields;
	sortDirection: SortDirection;
	collapseColours: boolean;
}

@Component({
	selector: 'pos-stock-enquiry-search-header',
	templateUrl: './stock-enquiry-search-header.component.html',
	styleUrls: ['./stock-enquiry-search-header.component.scss']
})
export class StockEnquirySearchHeaderComponent
	implements OnInit, OnDestroy, AfterViewInit {
	@Input() initialSearchValue: string;
	@Output() searchChanged = new EventEmitter<StockSearchRequestDto>();

	// Add a columns map for display names
	columnDisplayNames = new Map<StockSearchFields, string>([
		[StockSearchFields.StyleCode, 'Style Code'],
		[StockSearchFields.StyleDescription, 'Style Description'],
		[StockSearchFields.MakerCode, 'Maker Code'],
		[StockSearchFields.DepartmentCode, 'Department Code'],
		[StockSearchFields.Barcode, 'Barcode'],
		[StockSearchFields.Size, 'Size Code']
	]);

	// Keep the existing columns array
	columns = [
		StockSearchFields.StyleCode,
		StockSearchFields.StyleDescription,
		StockSearchFields.MakerCode,
		StockSearchFields.DepartmentCode,
		StockSearchFields.Barcode,
		StockSearchFields.Size
	];

	public searchOptions = new StockSearchOptions();

	private searchRequest$ = new Subject<StockSearchRequestDto>();
	private alive = true;

	@ViewChild('searchInput', { static: false }) searchInputElement: ElementRef;

	constructor() { }

	ngOnInit() {
		this.searchRequest$
			.pipe(
				takeWhile(() => this.alive),
				debounceTime(150),
				filter((x) => x.searchString && x.searchString.length > 0)
			)
			.subscribe((val) => this.searchChanged.emit(val));

		// Set initial search value if provided
		if (this.initialSearchValue) {
			this.searchOptions.searchString = this.initialSearchValue.toUpperCase();
			// Trigger search with the initial value - use a longer timeout to ensure components are ready
			setTimeout(() => this.search(), 300);
		} else {
			// Only search if we're not using an initial value
			// or add a small delay to ensure components are initialized
			setTimeout(() => this.search(), 100);
		}
	}

	ngAfterViewInit() {
		setTimeout(() => {
			this.searchInputElement.nativeElement.focus();
		});
	}

	ngOnDestroy(): void {
		this.alive = false;
	}

	// Getter to determine the max length based on the selected search field
	get maxLength(): number | null {
		switch (this.searchOptions.searchByField) {
			case StockSearchFields.StyleCode:
				return 14;
			case StockSearchFields.StyleDescription:
				return 50;
			case StockSearchFields.MakerCode:
				return 8;
			case StockSearchFields.DepartmentCode:
				return 5;
			case StockSearchFields.Barcode:
				return 15;
			default:
				return null; // No limit
		}
	}

	// Handle changes in the search string input
	onSearchStringChange(value: string) {
		// Enforce max length if defined
		const maxLength = this.maxLength;
		if (maxLength && value.length > maxLength) {
			value = value.substring(0, maxLength);
		}

		this.searchOptions.searchString = value.toUpperCase();
		this.search();
	}

	// Handle changes in the search field selection
	onSearchFieldChange() {
		// Reset the search string when the search field changes
		this.searchOptions.searchString = '';
		// Refocus the input field
		setTimeout(() => {
			this.searchInputElement.nativeElement.focus();
		});

		// Optionally, trigger a search if needed
		// this.search();
	}

	// Handle collapse colours checkbox change
	onCollapseColoursChange() {
		this.search();
	}

	search() {
		console.log('Searching with:', this.searchOptions);
		this.searchRequest$.next({ ...this.searchOptions });
	}

	searchByBarcode() {
		this.searchOptions.searchByField = StockSearchFields.Barcode;
		this.search();
	}
}
