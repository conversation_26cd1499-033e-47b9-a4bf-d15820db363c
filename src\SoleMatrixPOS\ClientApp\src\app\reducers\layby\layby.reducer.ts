import { createReducer, on, Action } from '@ngrx/store';
import * as laybyActions from "./layby.actions";

export class LaybyState {
    active: boolean;
    laybySubmitting: boolean;
    laybyCreated: boolean;
    laybyNumber: string | null;
    laybyCancelled: boolean;
    minDeposit: number;
    depositPending: number;
    laybyOrderInProgress: boolean; // New property
}

export const initialState: LaybyState = {
    active: false,
    laybySubmitting: false,
    laybyCreated: false,
    laybyCancelled: false,
    laybyNumber: null,
    minDeposit: null,
    depositPending: null,
    laybyOrderInProgress: false // Initialize as false
}

function clampZero(value: number) {
    return value > 0 ? value : 0;
}

export const laybyReducer = createReducer(
    initialState,
    on(laybyActions.init, (state) => initialState),

    on(laybyActions.startLayby, (state, action) => {
        return { ...state, active: true, minDeposit: action.minDeposit, depositPending: action.minDeposit }
    }),

    on(laybyActions.addToLaybyDeposit, (state, action) => {
        return { ...state, active: true, depositPending: clampZero(state.depositPending - action.amount) }
    }),

    on(laybyActions.submitLayby, (state) => {
        return { ...state, laybySubmitting: true }
    }),

    on(laybyActions.laybyCreated, (state) => {
        return { ...state, laybySubmitting: false, laybyCreated: true }
    }),
    on(laybyActions.init, (state) => initialState),
    on(laybyActions.startLayby, (state, action) => {
        return { ...state, active: true, minDeposit: action.minDeposit, depositPending: action.minDeposit }
    }),
    // ... your other reducers ...
    on(laybyActions.setLaybyOrderInProgress, (state, action) => {
        return { ...state, laybyOrderInProgress: action.inProgress }
    }),
    on(laybyActions.getLaybyNumber, (state) => ({
        ...state,
        loading: true,
        error: null,
      })),
    on(laybyActions.getLaybyNumberSuccess, (state, action) => ({
    ...state,
    laybyNumber: action.payload.toString(),
    loading: false,
    error: null,
    })),
    // Handle getTransactionNoFailure
    on(laybyActions.getLaybyNumberFailure, (state, action) => ({
    ...state,
    loading: false,
    error: action.error,
    }))
);


