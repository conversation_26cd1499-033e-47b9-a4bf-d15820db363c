import { Component, OnInit, OnDestroy } from '@angular/core';
import { CustomerClubModalComponent } from '../../customer-club/customer-club-modal/customer-club-modal.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { Observable, of, Subject } from 'rxjs';
import { CustomerClubDto, ReceiptTransactionDto, StockItemDto, TransactionDto, TranslogDto, TransrefDto, GetReceiptDto, EftposClient, SuspendSaleClient, GiftVoucherResultDto } from '../../pos-server.generated';
import { Store } from '@ngrx/store';
import { AppState } from '../../reducers';
import * as staffActions from '../../reducers/staff/staff.actions';
import { OpenCashDrawerAction } from "src/app/printing/printing-definitions";
import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';
import * as cartSelectors from '../../reducers/sales/cart/cart.selectors';
import * as transSelectors from '../../reducers/transaction/transaction.selectors';
import * as paymentActions from '../../reducers/sales/payment/payment.actions';
import * as transActions from '../../reducers/transaction/transaction.actions'
import * as receiptActions from '../../reducers/receipt-printing/receipt.actions'
import { UrlHistoryService } from 'src/app/url-history.service';

import { CartItem, cartItemToTranslog } from '../../reducers/sales/cart/cart.reducer';
import * as suspendSaleActions from '../../reducers/suspend-sale/suspend-sale.actions';
import { Payment, Transaction } from '../../payment/payment.service';
import { ProcessPaymentModalComponent } from '../../payment/process-payment-modal/process-payment-modal.component';
import { PaymentModalButton } from '../../payment/payment-modal-button/payment-modal-button.component';
import { PaymentType } from 'src/app/payment/payment.service';
import Swal from 'sweetalert2';
import { EmailReceiptComponent } from 'src/app/email-receipt/email-receipt.component';
import * as sysSelectors from '../../reducers/sys-config/sys-config.selectors';
import { EftposService, mapCartToLinklyBasket } from '../../eftpos/eftpos.service';
import { WaitingForEftposModalComponent } from '../../payment/waiting-for-eftpos-modal/waiting-for-eftpos-modal.component';
import { receipt } from '../../reducers/house-keeping/updateReceipt/receipt.selector';
import { ReceiptBatch, TextAction, CutAction, CutType, BarcodeAction, FeedAction } from '../../printing/printing-definitions';
import { PrintingService, SolemateReceiptOptions } from 'src/app/printing/printing.service';
import { takeUntil, tap, take } from 'rxjs/operators';
import * as cartActions from '../../reducers/sales/cart/cart.actions';
import { GiftVoucherIssueComponent } from 'src/app/gift-voucher/gift-voucher-issue/gift-voucher-issue.component';

import * as voucherActions from '../../reducers/gift-voucher/gift-voucher.actions';
import * as saleNoteSelectors from '../../reducers/sale-note/sale-note.selectors';
import * as paymentSelector from '../../reducers/sales/payment/payment.selector';
import { CtransDto } from '../../pos-server.generated';
import * as customerClubUpdateActions from 'src/app/reducers/customer-club/customer-update/customer-update.actions';
import { PointsUpdatePayload } from 'src/app/reducers/customer-club/customer-update/customer-update.actions';

const RETURN_TRANSTYPE = 2;
const EXCHANGE_TRANSTYPE = 3;

@Component({
    selector: 'pos-returns-process-refund',
    templateUrl: './returns-process-refund.component.html',
    styleUrls: ['./returns-process-refund.component.scss']
})
export class ReturnsProcessRefundComponent implements OnInit, OnDestroy {

    private selectedCustomerClubMember$: Observable<CustomerClubDto>;
    public selectedCustomerClubMember: CustomerClubDto = null;

    // Replace boolean with value from store
    private isExchangeMode$: Observable<boolean>;
    private isExchangeMode: boolean = false;

    public cart$: Observable<CartItem[]>;
    public total$: Observable<number>;
    alwaysOpenCashTill: string = 'F';

    public reasons$: Observable<Map<string, string[]>>;

    modalButtons: PaymentModalButton[] = [
        new PaymentModalButton("Cash", "fa-money-bill-wave text-success", PaymentType.Cash),
        new PaymentModalButton("Eftpos", "fa-credit-card text-success", PaymentType.Eftpos),
        new PaymentModalButton("Customer Account", "fa-user-crown text-success", PaymentType.CustomerAccount),
        new PaymentModalButton("Credit Note", "fa-sticky-note text-info", PaymentType.CreditNote)
        //new PaymentModalButton("Credit Note", "fa-store-alt text-success", PaymentType.CreditNote)
    ];

    public transaction: Transaction;
    public transaction$: Observable<Transaction>;
    private transactionDto: TransactionDto
    cart: CartItem[];
    cartTotal: number;

    transNo$: Observable<number>
    transNo: number

    creditNote: GiftVoucherResultDto = null;

    intEftReceipts: GetReceiptDto[];

    sysStatus: any;
    public sysStatus$: Observable<any>;
    reasons: Map<string, string[]>;
    alreadyAutoRequestedConfirmation: boolean = false;

    customerNameOnReceipt: string = 'F';

    private destroy$ = new Subject<void>();
    private isProcessingTransaction = false;

    cTrans$: Observable<CtransDto[]>;
    cTrans: CtransDto[];

    // Added properties for points processing
    PointsPerDollar: number = 0;
    newCustomerPointsTotal: number = null;
    pointsAdjusted: number = 0;

    constructor(
        private modalService: NgbModal,
        private router: Router,
        private store: Store<AppState>,
        private eftposService: EftposService,
        private printService: PrintingService,
        private urlHistory: UrlHistoryService
    ) { }

    ngOnInit() {
        // if (this.urlHistory.previousUrl.includes('exchange/out')) {
        //     this.store.dispatch(cartActions.setExchangeMode({ isExchangeMode: true }));
        // }   exchange-out causes the state to be set to exchange mode

        // Subscribe to exchange mode from store
        this.isExchangeMode$ = this.store.select(cartSelectors.isExchangeMode);
        this.isExchangeMode$.subscribe(mode => {
            this.isExchangeMode = mode;
        });

        this.store.dispatch(transActions.getTransactionNo());
        this.store.select(transSelectors.transNo)
            .pipe(
                tap(transNo => {
                    this.transNo = transNo;
                    console.log('Transaction number updated:', transNo);
                })
            )
            .subscribe();

        console.log(this.modalButtons);
        this.selectedCustomerClubMember$ = this.store.select(customerClubSearchSelectors.selectedCustomerClubMember);
        this.selectedCustomerClubMember$.pipe(
            takeUntil(this.destroy$)
        ).subscribe(member => {
            this.selectedCustomerClubMember = member;
            console.log('Selected customer club member in returns:', member);
        });

        this.cart$ = this.store.select(cartSelectors.cart);
        this.cart$.subscribe(
            (s) => this.cart = s
        );

        this.reasons$ = this.store.select(cartSelectors.reasons);
        this.reasons$.subscribe((s) => this.reasons = s);

        this.sysStatus$ = this.store.select(sysSelectors.selectSysConfig);
        this.sysStatus$.subscribe((sysconfig) => {
            this.sysStatus = sysconfig
        }
        );

        this.total$ = this.store.select(cartSelectors.total);
        this.total$.subscribe((t) => {
            this.cartTotal = t;
            // Use absolute value for transaction amount while preserving negative cart total
            const transactionAmount = Math.abs(t as number);
            this.transaction = new Transaction(transactionAmount);
            console.log("new transaction", this.transaction);
        });

        this.transaction$ = of(this.transaction);

        this.transaction$.subscribe(t => {
            console.log("Transaction has changed: ", t);
            if (t.amountDue <= 0) {
                console.log("Let's check...");
                // If we haven't asked before
                if (!this.alreadyAutoRequestedConfirmation) {
                    this.process();
                    this.alreadyAutoRequestedConfirmation = true;
                }
            }
        });
        this.store.select(sysSelectors.OpenCashTill)
            .pipe()
            .subscribe(limit => {
                this.alwaysOpenCashTill = limit || 'F';
            });

        this.store.select(sysSelectors.CustomerNameOnReceipt)
            .pipe()
            .subscribe(limit => {
                this.customerNameOnReceipt = limit || 'F';
            });

        //this.store.select(transSelectors.completed).subscribe(
        //    value => {
        //        if (value) this.checkIntegratedEftpos()
        //    }
        //)

        this.store.select(transSelectors.creditNote).subscribe(
            value => { this.creditNote = value; }
        )

        this.cTrans$ = this.store.select(paymentSelector.ctrans);
        this.cTrans$.subscribe(
            (s) => this.cTrans = s
        );

        // Subscribe to PointsPerDollar config
        this.store.select(sysSelectors.PointsPerDollar)
            .pipe(takeUntil(this.destroy$))
            .subscribe(limit => {
                this.PointsPerDollar = limit || 0;
            });
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }

	async checkIntegratedEftpos(): Promise<boolean> {
		// Return true if all successful

		// const transNo = this.transNo.toString();
        // update the ctrans
        // const updatedCtrans = this.cTrans
        // Check if any payment has the description "Integrated Eftpos"
        let intEftPayments = new Array;
        let intEft = false;
        let intAmount = 0;
        for (const payment of this.transaction.payments) {
            if (payment.desc === "Integrated Eftpos") {
                intEftPayments.push(payment);
                intEft = true;
                intAmount += payment.amount;
            }
        }

        if (intEft) {
            let allSuccessful = true;
            let eftRefunded = 0;
            for (const eftPayment of intEftPayments) {
                const modalRef = this.modalService.open(WaitingForEftposModalComponent, {
                    size: 'md',
                    centered: true,
                    backdrop: 'static',
                    keyboard: false
                });
                switch (this.sysStatus.integratedEFTProvider) {
                    case "Linkly":
                        modalRef.componentInstance.tenderAmount = intAmount;
                        modalRef.componentInstance.totalAmount = this.cartTotal;
                        modalRef.componentInstance.store = this.store;
                        modalRef.componentInstance.discountAmt = 0; // TODO: calculate discount if needed
                        modalRef.componentInstance.surchargeAmt = intAmount * 0; // TODO: adjust surcharge calculation if required
                        modalRef.componentInstance.taxAmt = intAmount * 0; // TODO: adjust tax calculation based on config
                        modalRef.componentInstance.transNo = this.transNo; // TODO: generate or retrieve a transaction number

                        // Map the current cart to the format required by Linkly
                        const mappedItems = mapCartToLinklyBasket(this.cart);
                        modalRef.componentInstance.items = mappedItems;
                        modalRef.componentInstance.transType = "Refund";
                        break;

                    case "Tyro":
                        modalRef.componentInstance.tenderAmount = intAmount;
                        modalRef.componentInstance.transNo = this.transNo;
                        modalRef.componentInstance.transType = "Refund";
                        break;

                    default:
                        console.log("Integrated EFTPOS not configured");
                        return false;
                }

                try {
                    const result: any = await modalRef.result;
                    if (!result) {
                        this.store.dispatch(transActions.getTransactionNo());
                        this.transaction.removePayment(eftPayment);
                        allSuccessful = false;

                        console.log("EFTPOS payment failed or was cancelled");
                    } else {
                        console.log("EFTPOS payment result:", result);
                        eftRefunded += eftPayment.amount;
                        this.transaction.acceptedEft(eftPayment.amount);



                    }
                } catch (error) {
                    console.error("Error in waiting-for-EFTPOS modal:", error);
                    allSuccessful = false;
					break;
                }
            }
            if (allSuccessful) {
                this.eftposService.getReceipts(this.transNo, false)
                    .subscribe((receipts: GetReceiptDto[]) => {
                        this.intEftReceipts = receipts;
                        console.log("going to returnTransactionCompleted here 2");
                        this.returnTransactionCompleted();
                        this.printService.printEftposReceipt(receipts, true);
                    });
				return true
			}

        }
        else {
            console.log("going to returnTransactionCompleted here 3");
			this.returnTransactionCompleted()
			return true;
        }

    }

    returnTransactionCompleted(): void {
        if (this.isProcessingTransaction) {
            return;
        }
        this.isProcessingTransaction = true;

        try {
            console.log("Now printing.. " + JSON.stringify(this.transactionDto));

            // Store the transaction number locally before any potential state changes
            const currentTransNo = this.transNo;
            console.log("Current transaction number:", currentTransNo);

            // Update and commit cTrans if it exists
            if (this.cTrans) {
                const updatedCtrans = this.cTrans.map(c => ({
                    ...c,
                    amount: c.amount * -1,
                    outstandingAmount: c.outstandingAmount * -1,
                    realdocNo: currentTransNo
                }));

                this.store.dispatch(paymentActions.updateCtrans({ payload: updatedCtrans }));
                console.log(`C trans - ${JSON.stringify(updatedCtrans)}`);
                this.store.dispatch(paymentActions.commitCtrans({ payload: updatedCtrans }));
            }

            if (!this.transactionDto) {
                console.log("No transaction data available, waiting for data...");
                return;
            }

            // Calculate and subtract points BEFORE showing the receipt prompt
            this.calculateAndSubtractPoints(this.cartTotal);

            // Init the transaction reducer 
            const saleDateTime = new Date();

            if (this.alwaysOpenCashTill === 'T') {
                this.store.dispatch(receiptActions.executeBatch({
                    payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
                }));
            }
            else {
                if (this.transactionDto.payments.some(payment => payment.paymentType === 'Cash')) {
                    this.store.dispatch(receiptActions.executeBatch({
                        payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
                    }));
                }
            }

            // Create a local copy of the transaction data for this specific transaction
            const transactionCopy = {
                translogs: [...this.transactionDto.translogs],
                payments: [...this.transactionDto.payments],
                transReferences: this.transactionDto.transReferences || [],
                transType: this.isExchangeMode ? EXCHANGE_TRANSTYPE : RETURN_TRANSTYPE
            };

            let trans: ReceiptTransactionDto = {
                logs: transactionCopy.translogs,
                pays: transactionCopy.payments,
                saleDateTime: saleDateTime,
                transType: transactionCopy.transType,
                refs: transactionCopy.transReferences
            };

            Swal.fire({
                title: "Return Successful",
                text: "The Item Was Successfully Returned.",
                showCancelButton: true,
                cancelButtonText: "Email Receipt",
                confirmButtonText: "Print Receipt",
            }).then(async (result) => {
                if (result.value) {
                    const transtype = trans.transType;
                    const pays = trans.pays;
                    const logs = trans.logs;
                    const refs = trans.refs;
                    const change = this.transaction.change;
                    const customerCode = this.selectedCustomerClubMember && this.customerNameOnReceipt == 'T' ? this.selectedCustomerClubMember.clientCode : undefined;
                    const customerName = this.selectedCustomerClubMember && this.customerNameOnReceipt == 'T' ? `${this.selectedCustomerClubMember.firstname} ${this.selectedCustomerClubMember.surname}` : undefined;


                    // If the "Print Receipt" button is clicked
                    await this.printService.printSolemateReceipt(
                        this.isExchangeMode ? "Exchange" : "Refund", // Use correct title based on mode
                        logs,
                        pays,
                        transtype,
                        currentTransNo.toString(), // Use the stored transaction number
                        SolemateReceiptOptions.default(),
                        trans,
                        change,
                        refs,
                        customerCode,
                        customerName,
                        null, // Placeholder for voucher balances if needed later
                        this.newCustomerPointsTotal, // Pass new points total
                        this.pointsAdjusted        // Pass points adjusted
                    );

                    // Clear transaction data after printing
                    this.transactionDto = null;
                    this.transaction = new Transaction(0);

                    this.finalizeTransaction();
                } else if (result.dismiss === Swal.DismissReason.cancel) {
                    await this.openEmailModal(trans);

                    // Clear transaction data after emailing
                    this.transactionDto = null;
                    this.transaction = new Transaction(0);

                    this.finalizeTransaction();
                } else {
                    // Still clear transaction data if neither option is chosen
                    this.transactionDto = null;
                    this.transaction = new Transaction(0);

                    this.finalizeTransaction();
                }
            });
        } finally {
            this.isProcessingTransaction = false;
        }
    }

    // Add a new method to handle all cleanup and initialization
    private finalizeTransaction(): void {
        // Handle credit note
        if (this.creditNote) {
            // Set GV state
            this.store.dispatch(voucherActions.creditNoteReceived({
                payload: this.creditNote
            }));

            // Open modal as is in gift voucher
            const modalRef = this.modalService.open(GiftVoucherIssueComponent, {
                windowClass: 'daily-modal-window',
                size: 'l',
                centered: true,
                backdrop: 'static',  // Prevent closing on backdrop click
                keyboard: false      // Prevent closing with escape key
            });

        } else {
            // Clear staff login state
            this.store.dispatch(staffActions.clearStaffLogin());
        }

        // Initialize all necessary reducers and clear state AFTER printing is complete
        this.store.dispatch(cartActions.init());
        this.store.dispatch(paymentActions.init());
        this.store.dispatch(transActions.init()); // Initialize transaction state last

        // Reset component state
        this.alreadyAutoRequestedConfirmation = false;
        this.transactionDto = null;
        this.transaction = new Transaction(0); // Reset transaction amount
        this.creditNote = null; // Clear credit note DTO
        this.newCustomerPointsTotal = null; // Reset points total
        this.pointsAdjusted = 0; // Reset points adjustment

        // Reset exchange mode if it was set
        if (this.isExchangeMode) {
            this.store.dispatch(cartActions.setExchangeMode({ isExchangeMode: false }));
        }

        // Clear all related state in the store
        this.store.dispatch({ type: '[SuspendSale] Clear Suspend Sale' });
        this.store.dispatch({ type: '[Receipt] Clear Receipt State' });
        this.store.dispatch({ type: '[Sales] Reset All State' });
    }

    openEmailModal(receiptTrans: ReceiptTransactionDto): Promise<void> {
        return new Promise((resolve) => {
            const modalRef = this.modalService.open(EmailReceiptComponent, {
                size: 'lg',
                backdrop: 'static'
            });

            // Pass receiptTrans to the EmailReceiptComponent
            modalRef.componentInstance.receiptTrans = receiptTrans;
            modalRef.componentInstance.customerSelected = this.selectedCustomerClubMember; // Pass selected customer
            modalRef.componentInstance.pointsEarned = this.pointsAdjusted; // Pass adjusted points (will be negative)
            modalRef.componentInstance.newCustomerPointsTotal = this.newCustomerPointsTotal; // Pass new total

            // Check if a customer club member is selected and pass the email
            if (this.selectedCustomerClubMember && this.selectedCustomerClubMember.email) {
                modalRef.componentInstance.customerEmail = this.selectedCustomerClubMember.email;
            }

            modalRef.result.then(() => {
                console.log('Email receipt sent.');
                resolve();  // Resolve the promise once the modal is closed
            }).catch(() => {
                resolve();  // Resolve the promise if the modal is dismissed
            });
        });
    }

    handlePayment(payment: Payment) {
        console.log("Attempting to add payment: ", payment);

        let success = this.transaction.addPayment(payment);

        if (success) {
            // Payment completed, let's check if we've gone below 0
            if (this.transaction.amountDue <= 0) {
                if (!this.alreadyAutoRequestedConfirmation) {
                    this.process();
                    this.alreadyAutoRequestedConfirmation = true;
                }
            }
        }

        else {
            // Show warning / error popup
            Swal.fire({
                title: "Error",
                text: "You have attempted a payment which exceeds the due amount."
            });
        }
    }

    goHome() {
        this.router.navigateByUrl('/home');
    }

    launchCustomerClubModal() {
        const modalRef = this.modalService.open(CustomerClubModalComponent, { size: 'xl', centered: true });
        modalRef.componentInstance.name = 'CustomerClubModal';
        modalRef.result.then((result) => {
            if (result) {
                console.log('result from modal:', result);
            }
        });
    }

    async process() {
        console.log("This: ", this.cart);
        let modalRef = this.modalService.open(ProcessPaymentModalComponent, { size: 'xl', centered: true });
        modalRef.componentInstance.name = "Process Payment";
        modalRef.result.then(async (result) => {
            if (result === 'Process') {
                // Build out the entire transaction first
                let trans = this.createTransaction();
                console.log("Transaction is submitting...");
                this.transactionDto = trans; // Set the transactionDto before proceeding

                // Now check integrated eftpos and submit transaction
				const ok = await this.checkIntegratedEftpos();
				if (ok) {
					this.store.dispatch(
						transActions.submitTransaction({ payload: this.transactionDto })
					);
				}
            }
        }).catch(res => console.log("Error occurred: ", res));
    }

    createTransaction() {
        let lineNo: number = 1;
        let resTransrefs: TransrefDto[] = [];
        let resTranslogs: TranslogDto[] = [];

        let clientCode = (this.selectedCustomerClubMember && this.selectedCustomerClubMember.clientCode)
            ? this.selectedCustomerClubMember.clientCode
            : undefined;

        this.store.select(saleNoteSelectors.selectSaleNote)
            .pipe(take(1))
            .subscribe(note => {
                if (note) {
                    resTransrefs.push({
                        lineNo: 0,
                        transReference: note,
                        transNo: this.transNo
                    } as TransrefDto);
                }
            });

        for (let i = 0; i < this.cart.length; i++) {
            let cartItem = this.cart[i];

            let clientCode = (this.selectedCustomerClubMember && this.selectedCustomerClubMember.clientCode)
                ? this.selectedCustomerClubMember.clientCode
                : undefined;

            // Create log for item
            resTranslogs.push(cartItemToTranslog(cartItem, lineNo, this.transNo, clientCode));
            lineNo++;

            // To use as key
            let stockItem: StockItemDto = cartItem.stockItem;

            // TODO: Change this to barcode
            let reasons = this.reasons.get(stockItem.barcode);
            if (reasons) {
                for (let reason of reasons) {
                    //  Create translog for the reason
                    // (this is a dodgy but necessary workaround for legacy)
                    resTranslogs.push({
                        styleCode: "Reason",
                        lineNo: lineNo,
                        clientCode
                    } as TranslogDto);

                    // Create transref for the reason
                    resTransrefs.push({
                        lineNo: lineNo,
                        transReference: reason
                    } as TransrefDto);
                    lineNo++;
                }
            }
        }
        const isRefund = true;
        const payments = this.transaction.toTranspayDtos(isRefund);

        // Create the transaction DTO using the local isExchangeMode variable
        return {
            payments: payments,
            translogs: resTranslogs,
            transReferences: resTransrefs,
            transType: this.isExchangeMode ? EXCHANGE_TRANSTYPE : RETURN_TRANSTYPE
        } as TransactionDto;
    }

    clickCount: number = 0;

    removePaymentOnDoubleClick(payment: Payment) {
        console.log("Received..!");
        this.transaction.removePayment(payment);
    }

    // Added method to calculate and subtract points
    calculateAndSubtractPoints(refundAmount: number): void {
        // Only proceed if there's a selected customer club member and points system is active
        if (this.selectedCustomerClubMember && this.PointsPerDollar > 0) {
            const clientCode = this.selectedCustomerClubMember.clientCode;

            // Calculate points to subtract on a per-item basis
            let totalPointsToSubtract = 0;

            // Iterate through each cart item
            this.cart.forEach(item => {
                // Skip points calculation if item has discount
                if (item.discountPercent > 0) {
                    return; // Skip this item
                }

                // Calculate points for this item based on its total value
                const itemTotal = Math.abs((item.bestValue || item.stockItem.price) * item.quantity);
                const itemPoints = Math.floor(itemTotal / this.PointsPerDollar);
                totalPointsToSubtract += itemPoints;
            });

            if (totalPointsToSubtract > 0) {
                this.pointsAdjusted = -totalPointsToSubtract; // Negative value for subtraction
                this.newCustomerPointsTotal = (this.selectedCustomerClubMember.clientPoints || 0) - totalPointsToSubtract;

                // Ensure points don't go negative
                if (this.newCustomerPointsTotal < 0) {
                    this.newCustomerPointsTotal = 0;
                    // Adjust pointsAdjusted if total became 0
                    this.pointsAdjusted = -this.selectedCustomerClubMember.clientPoints;
                }

                // Create payload for points update
                const pointsUpdatePayload: PointsUpdatePayload = {
                    clientCode: clientCode,
                    pointsToAdjust: this.pointsAdjusted // Send negative value to subtract
                };

                // Dispatch the action to update points in the backend/store
                this.store.dispatch(customerClubUpdateActions.updatePoints({ payload: pointsUpdatePayload }));
                console.log(`Subtracted ${totalPointsToSubtract} points from customer ${clientCode}. New total: ${this.newCustomerPointsTotal}`);
            } else {
                this.pointsAdjusted = 0;
                this.newCustomerPointsTotal = this.selectedCustomerClubMember.clientPoints;
                console.log(`No points to subtract - all items were discounted or refund amounts too small.`);
            }
        } else {
            this.pointsAdjusted = 0;
            this.newCustomerPointsTotal = this.selectedCustomerClubMember ? this.selectedCustomerClubMember.clientPoints : null;
        }
    }
}
