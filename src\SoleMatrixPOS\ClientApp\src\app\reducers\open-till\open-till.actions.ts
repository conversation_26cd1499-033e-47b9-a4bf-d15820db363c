import { createAction, props } from "@ngrx/store";
import { OpenTillTransactionDto, PettyCashTransactionDto } from "src/app/pos-server.generated";

export const init = createAction("[OpenTill] Init");
export const submitTransaction = createAction("[OpenTill] Submit OpenTillTransaction", props<{payload: OpenTillTransactionDto}>());
export const transactionConfirmation = createAction("[OpenTill] OpenTillTransaction Submitted");
export const submitPettyCash = createAction("[OpenTill] Submit PettyCash", props<{payload: PettyCashTransactionDto}>());
export const updateDaily = createAction("[OpenTill] Update Daily", props<{payload: PettyCashTransactionDto}>());