import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { HomeClient } from "src/app/pos-server.generated";
import * as StoreAction from './store-info.actions';
import { catchError, map, mergeMap } from "rxjs/operators";
import { EMPTY } from "rxjs";



@Injectable()
export class StoreEffect {

    constructor(private action$: Actions, private storeClient: HomeClient) { }

    getStoreInfo$ = createEffect(() => this.action$.pipe(
        ofType(StoreAction.getStoreInfo),
        mergeMap((action) => this.storeClient.getStoreInfo()
            .pipe(
                map(
                    response => StoreAction.getStoreInfoResponse({ payload: response }),
                    catchError(() => EMPTY)
                )
            )
        )
    ))


}
