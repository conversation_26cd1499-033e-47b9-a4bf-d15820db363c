import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { StockItemDto } from '../../pos-server.generated';

@Component({
	selector: 'pos-stock-enquiry-search-modal',
	templateUrl: './stock-enquiry-search-modal.component.html',
	styleUrls: ['./stock-enquiry-search-modal.component.scss']
})
export class StockEnquirySearchModalComponent implements OnInit {

	@Input() name: string;
	@Input() initialSearchValue: string;
	constructor(public activeModal: NgbActiveModal) { }

	ngOnInit() {
	}

	selectItem(item: StockItemDto) {
		this.activeModal.close(item);
	}

	dismiss(reason: string) {
		this.activeModal.dismiss(reason);
	}
}
