import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { CustomerClubDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import { UrlHistoryService } from 'src/app/url-history.service';
import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';
import { Observable } from 'rxjs';
import { Transaction } from 'src/app/payment/payment.service';
import { CustomerClubModalComponent } from 'src/app/customer-club/customer-club-modal/customer-club-modal.component';

@Component({
  selector: 'pos-gift-footer',
  templateUrl: './gift-footer.component.html',
  styleUrls: ['./gift-footer.component.scss']
})
export class GiftFooterComponent implements OnInit {

  selectedCustomerClubMember$: any;
  selectedCustomerClubMember: CustomerClubDto = null;

  @Input() showCustomerClub: boolean;
  @Output() process = new EventEmitter<void>();


  constructor(private store: Store<AppState>, private router: Router, private modalService: NgbModal, private urlHistory: UrlHistoryService) { }

  ngOnInit() {
    this.subscribeToState();

  }

  subscribeToState() {
    this.selectedCustomerClubMember$ = this.store.select(customerClubSearchSelectors.selectedCustomerClubMember);
    this.selectedCustomerClubMember$.subscribe((s) => {
      this.selectedCustomerClubMember = s;
    });
  }


  backBtnClick() {
    console.log("Navigating backwards...");
    this.router.navigateByUrl(this.urlHistory.previousUrl);
  }

  launchCustomerClubModal(){
    const modalRef = this.modalService.open(CustomerClubModalComponent, { size: 'xl', centered: true });
  	modalRef.componentInstance.name = 'CustomerClubModal';
  	modalRef.result.then((result) => {
  		if (result) {
  			console.log('result from modal:', result);
  		}
  	}).catch(error => {
      console.log("Error occurred: ", error);
    });
  }


  processBtnClick() {
    this.process.emit();
  }


}
