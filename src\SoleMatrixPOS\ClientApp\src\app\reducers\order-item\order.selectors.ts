// order.selectors.ts
import { AppState } from '../index';
import { createSelector } from '@ngrx/store';
import { OrderState } from './order.reducers';

export const selectOrderState = (state: AppState) => state.customerOrder;

export const selectOrderActive = createSelector(
    selectOrderState,
    (state: OrderState) => state.active
);

export const selectOrderSubmitting = createSelector(
    selectOrderState,
    (state: OrderState) => state.orderSubmitting
);

export const selectOrderCreated = createSelector(
    selectOrderState,
    (state: OrderState) => state.orderCreated
);

export const selectOrderCancelled = createSelector(
    selectOrderState,
    (state: OrderState) => state.orderCancelled
);

// Select the order header
export const selectOrderHeader = createSelector(
    selectOrderState,
    (state: OrderState) => state.orderHeader
);

// Select the order lines
export const selectOrderLines = createSelector(
    selectOrderState,
    (state: OrderState) => state.orderLines
);

export const selectOrderDeposit = createSelector(
    selectOrderState,
    (state: OrderState) => state.deposit
);

export const selectUploadedOrderCode = createSelector(
    selectOrderState,
    (state: OrderState) => state.uploadedOrderCode
  );

export const selectOrderCompleting = createSelector(
    selectOrderState,
    (state: OrderState) => state.orderSubmitting
);

export const selectOrderNo = createSelector(
    selectOrderState,
    (state: OrderState) => state.orderNo
);
