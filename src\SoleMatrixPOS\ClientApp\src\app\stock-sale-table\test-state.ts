import { Stock } from './classes/stock';
import { Sale } from './classes/sale';
import { Size } from './classes/size';
import { Transfer } from './classes/transfer.model';
import { Location } from './classes/location.model';
import { ActionReducer } from '@ngrx/store';
import { TransfersReducer } from './redux/transfer/transfer.reducer';
import { LocationReducer } from './redux/location/location.reducer';
import { SizeReducer } from './redux/size/size.reducer';
import { ImmutableLocationReducer } from './redux/location/immutable-location.reducer';

type Trading = "T" | "F";
type Toggle = "on" | "off";
type Highlight = "on" | "from" | "to" | "off";

export interface AppState {
  transfers: Transfer[];
  locations: Location[];
  sizes: Size[];
  immutableLocations: Location[];
}

export const state: AppState = {
    transfers: [
      {
        id: 1,
        from: "Location 1",
        to: "Location 2",
        name: "BOUNCE",
        size: "1",
        qty: 1,
        highlight: "off" as Highlight
      },
      {
        id: 2,
        from: "Location 3",
        to: "Location 4",
        name: "BOUNCE",
        size: "1",
        qty: 1,
        highlight: "off" as Highlight
      }
    ],
    locations: [
      {
        name: "Location 1",
        trading: "T" as  Trading,
        rank: 1, 
        stock: [
          new Stock("1", 1),
          new Stock("2", 2),
          new Stock("3", 3),
          new Stock("4", 4)
        ],
        sales: [
          new Sale("1", 1),
          new Sale("2", 2),
          new Sale("3", 3),
          new Sale("4", 4)
        ],
        toggle: "off" as Toggle
      },
      {
        name: "Location 2",
        trading: "T" as  Trading,
        rank: 1, 
        stock: [
          new Stock("1", 1),
          new Stock("2", 2),
          new Stock("3", 3),
          new Stock("4", 4)
        ],
        sales: [
          new Sale("1", 1),
          new Sale("2", 2),
          new Sale("3", 3),
          new Sale("4", 4)
        ],
        toggle: "off" as Toggle
      },
      {
        name: "Location 3",
        trading: "T" as  Trading,
        rank: 1, 
        stock: [
          new Stock("1", 1),
          new Stock("2", 2),
          new Stock("3", 3),
          new Stock("4", 4)
        ],
        sales: [
          new Sale("1", 1),
          new Sale("2", 2),
          new Sale("3", 3),
          new Sale("4", 4)
        ],
        toggle: "off" as Toggle
      },
      {
        name: "Location 4",
        trading: "T" as  Trading,
        rank: 1, 
        stock: [
          new Stock("1", 1),
          new Stock("2", 2),
          new Stock("3", 3),
          new Stock("4", 4)
        ],
        sales: [
          new Sale("1", 1),
          new Sale("2", 2),
          new Sale("3", 3),
          new Sale("4", 4)
        ],
        toggle: "off" as Toggle
      },
    ],
    sizes: [
      new Size("1"),
      new Size("2"),
      new Size("3"),
      new Size("4")
    ],
    immutableLocations: [
      {
        name: "Location 1",
        trading: "T" as  Trading,
        rank: 1, 
        stock: [
          new Stock("1", 1),
          new Stock("2", 2),
          new Stock("3", 3),
          new Stock("4", 4)
        ],
        sales: [
          new Sale("1", 1),
          new Sale("2", 2),
          new Sale("3", 3),
          new Sale("4", 4)
        ],
        toggle: "off" as Toggle
      },
      {
        name: "Location 2",
        trading: "T" as  Trading,
        rank: 1, 
        stock: [
          new Stock("1", 1),
          new Stock("2", 2),
          new Stock("3", 3),
          new Stock("4", 4)
        ],
        sales: [
          new Sale("1", 1),
          new Sale("2", 2),
          new Sale("3", 3),
          new Sale("4", 4)
        ],
        toggle: "off" as Toggle
      },
      {
        name: "Location 3",
        trading: "T" as  Trading,
        rank: 1, 
        stock: [
          new Stock("1", 1),
          new Stock("2", 2),
          new Stock("3", 3),
          new Stock("4", 4)
        ],
        sales: [
          new Sale("1", 1),
          new Sale("2", 2),
          new Sale("3", 3),
          new Sale("4", 4)
        ],
        toggle: "off" as Toggle
      },
      {
        name: "Location 4",
        trading: "T" as  Trading,
        rank: 1, 
        stock: [
          new Stock("1", 1),
          new Stock("2", 2),
          new Stock("3", 3),
          new Stock("4", 4)
        ],
        sales: [
          new Sale("1", 1),
          new Sale("2", 2),
          new Sale("3", 3),
          new Sale("4", 4)
        ],
        toggle: "off" as Toggle
      }
    ],
}