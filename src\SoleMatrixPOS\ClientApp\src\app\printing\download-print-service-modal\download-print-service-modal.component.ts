import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
	selector: 'pos-download-print-service-modal',
	templateUrl: './download-print-service-modal.component.html',
	styleUrls: ['./download-print-service-modal.component.scss']
})
export class DownloadPrintServiceModalComponent implements OnInit {
	@Input() retryCallback: () => Promise<boolean>;
	dontAskAgain = false;

	constructor(private activeModal: NgbActiveModal) { }

	ngOnInit() {
		// Only dismiss if the value is explicitly 'true'
		const savedPreference = localStorage.getItem('dontAskDownloadPrinterService');
		if (savedPreference === 'true') {
			this.activeModal.dismiss('dont-ask-again');
		}
	}

	close() {
		this.activeModal.dismiss('closed');
	}

	async tryAgain() {
		if (this.retryCallback) {
			const isRunning = await this.retryCallback();
			if (isRunning) {
				this.activeModal.dismiss('service-running');
			}
		}
	}

	onDontAskAgainChange() {
		localStorage.setItem('dontAskDownloadPrinterService', String(this.dontAskAgain));
	}
}
