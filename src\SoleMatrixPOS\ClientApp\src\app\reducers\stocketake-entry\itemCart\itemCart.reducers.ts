import { createReducer, on } from "@ngrx/store";
import { observable } from "rxjs";
import { LogSTAuditDto, LogTranslogDto, StockItemCartDto, StockItemDto, TranslogDto } from "src/app/pos-server.generated"
import * as stockCartAction from './itemCart.actions';

// all stock items in the cart will be save in STAudit
export function stockCartItemToSTAudit(item: StockCartItem, lineNumber: number): LogSTAuditDto {
	return {
		styleCode: item.stockItem.styleCode,
		colourCode: item.stockItem.colourCode,
		sizeCode: item.stockItem.size,
		stakeCount: item.quantity,
		lineNo: lineNumber,
	} as LogSTAuditDto;
}

export class StockCartItem {
	quantity: number;
	stockItem: StockItemDto;
	value: number = 0;

	public clone(): StockCartItem {
		return {
			quantity: this.quantity,
			stockItem: Object.assign({}, this.stockItem),
			value: this.value
		} as StockCartItem;
	}
}

export class StockCartItemState {
	tableLoading: boolean;
	items: StockCartItem[];
	unitTotal: number;
	reasons: Map<string, string[]>;
}

export const initialState: StockCartItemState = {
	tableLoading: false,
	items: [],
	unitTotal: 0,
	reasons: new Map<string, string[]>()
} as StockCartItemState;


function compareStockItemDtos(first: StockItemDto | StockItemCartDto, second: StockItemDto | StockItemCartDto) {
	return first.barcode === second.barcode;
}

function recalculateTotal(items: StockCartItem[]): number {
	let total = 0;
	items.forEach(element => {
		total += element.quantity;
	});
	return total;
}

export const StocktakeReducer = createReducer(initialState,
	on(stockCartAction.init, state => initialState),

	on(stockCartAction.addStockItem, (state, action) => ({ ...state, tableLoading: true, options: action.stockItem.styleCode })),

	on(stockCartAction.addStockItemResponse, (state, action) => {
		let tmpItems = Object.assign([], state.items);

		let isDuplicate = false;
		console.log("Before: ", tmpItems);

		tmpItems.forEach((el: StockCartItem, i) => {
			if (el.stockItem.barcode == action.stockCartItem.barcode && Math.sign(el.quantity) == 1) {
				// Update the quantity
				isDuplicate = true;
				tmpItems[i] = { ...tmpItems[i], quantity: tmpItems[i].quantity + 1 } as StockCartItem;
			}
		});
		console.log("After: ", tmpItems);

		if (!isDuplicate) {
			tmpItems.push({
				stockItem: {
					barcode: action.stockCartItem.barcode,
					styleCode: action.stockCartItem.styleCode,
					styleDescription: action.stockCartItem.styleDescription,
					makerCode: action.stockCartItem.makerCode,
					labelCode: action.stockCartItem.labelCode,
					colourCode: action.stockCartItem.colourCode,
					departmentCode: action.stockCartItem.departmentCode,
					colourName: action.stockCartItem.colourName,
					size: action.stockCartItem.size,
					price: action.stockCartItem.price
				},
				quantity: 1,
			} as StockCartItem);
		}

		return { ...state, tableLoading: false, items: tmpItems, unitTotal: recalculateTotal(tmpItems) };
	}),

	on(stockCartAction.submitBarcode, (state, action) => ({ ...state, tableLoading: true, options: action.itembarcode.barcode })),

	on(stockCartAction.submitBarcodeResponse, (state, action) => {
		let tmpItems = Object.assign([], state.items);

		let isDuplicate = false;
		console.log("Before: ", tmpItems);

		tmpItems.forEach((el: StockCartItem, i) => {
			if (el.stockItem.barcode == action.addCartItem.barcode && Math.sign(el.quantity) == 1) {
				// Update the quantity
				isDuplicate = true;
				tmpItems[i] = { ...tmpItems[i], quantity: tmpItems[i].quantity + 1 } as StockCartItem;
			}
		});
		console.log("After: ", tmpItems);

		if (!isDuplicate) {
			tmpItems.push({
				stockItem: {
					barcode: action.addCartItem.barcode,
					styleCode: action.addCartItem.styleCode,
					styleDescription: action.addCartItem.styleDescription,
					makerCode: action.addCartItem.makerCode,
					labelCode: action.addCartItem.labelCode,
					colourCode: action.addCartItem.colourCode,
					departmentCode: action.addCartItem.departmentCode,
					colourName: action.addCartItem.colourName,
					size: action.addCartItem.size,
					price: action.addCartItem.price
				},
				quantity: 1,
			} as StockCartItem);
		}

		return { ...state, tableLoading: false, items: tmpItems, unitTotal: recalculateTotal(tmpItems) };
	}),



	on(stockCartAction.removeItem, (state, action) => {
		let itemTemp = Object.assign([], state.items);

		console.log("Remove", action);

		itemTemp.forEach((el: StockCartItem, i) => {
			if (compareStockItemDtos(el.stockItem, action.stockItem)) {
				itemTemp.splice(i, 1);
			}
		});
		return { ...state, items: itemTemp, unitTotal: recalculateTotal(itemTemp) };
	}),

	on(stockCartAction.setNumberOfItems, (state, action) => {
		let tempItem = Object.assign([], state.items);

		console.log("set num of item", action);

		tempItem.forEach((el: StockCartItem, i) => {
			console.log("cartActions.setNumberOfItems");
			if (compareStockItemDtos(el.stockItem, action.stockItem)) {
				if (action.quantity > 0) {
					let tempValue = tempItem[i].value;
					tempItem.splice(i, 1, { stockItem: action.stockItem, quantity: action.quantity, value: tempValue } as StockCartItem);
				}
				else {
					//remove item
					tempItem.splice(i, 1);
				}
			}
		});
		return { ...state, items: tempItem, total: recalculateTotal(tempItem) };
	}),
	on(stockCartAction.addReason, (state, action) => {
		// Copy map (to conform to immutable state)
		let copyReasons = deepCopyReasonMap(state.reasons);
		// If reasons already exists, append to list
		// Otherwise, create a new list
		if (copyReasons.get(action.barcode)) {
			copyReasons.get(action.barcode).push(action.reason);
		}
		else {
			copyReasons.set(action.barcode, [action.reason]);
		}

		// Return the modified state
		console.log("Added: ", copyReasons);
		return { ...state, reasons: copyReasons };
	}
	),
	on(stockCartAction.removeReason, (state, action) => {

		let mapCopy = deepCopyReasonMap(state.reasons);
		mapCopy.get(action.barcode).splice(action.reasonId, 1);

		if (mapCopy.get(action.barcode).length == 0)
			mapCopy.delete(action.barcode);

		return { ...state, reasons: mapCopy };
	}
	),
	on(stockCartAction.removeAllReasons, (state, action) => {
		let mapCopy = deepCopyReasonMap(state.reasons);
		mapCopy.delete(action.barcode);
		return { ...state, reasons: mapCopy };
	}
	),

	on(stockCartAction.negateCart, (state, action) => {
		let negatedItems: StockCartItem[] = [];
		for (var item of state.items) {
			let cloned = (Object.assign(new StockCartItem(), item)).clone();
			cloned.quantity = -cloned.quantity;
			negatedItems.push(cloned);
		}
		return { ...state, items: negatedItems, unitTotal: recalculateTotal(negatedItems) };
	}
	)

);

function deepCopyReasonMap(reasonMap: Map<string, string[]>): Map<string, string[]> {
	let mapCopy = new Map<string, string[]>();
	for (let key of reasonMap.keys()) {
		mapCopy.set(key, [...reasonMap.get(key)]);
	}
	return mapCopy;
}
