import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { IntegratedEftposModalComponent } from './integrated-eftpos-modal.component';

describe('IntegratedEftposModalComponent', () => {
  let component: IntegratedEftposModalComponent;
  let fixture: ComponentFixture<IntegratedEftposModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ IntegratedEftposModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(IntegratedEftposModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
