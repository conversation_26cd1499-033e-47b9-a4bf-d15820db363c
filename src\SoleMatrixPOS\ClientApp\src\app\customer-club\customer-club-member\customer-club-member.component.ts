import { Component, EventEmitter, OnInit, Input, Output } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import { ClientSearchKeywordColumnDto, ClientSearchOrderByColumnDto, CustomerClubDto, StockSearchOrderByDirectionEnumDto } from 'src/app/pos-server.generated';
import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';
import * as customerClubSearchActions from '../../reducers/customer-club/club-search/customer-club.actions';

export enum Page {
  DETAILS = 0, HISTORY, NOTES
}

@Component({
  selector: 'pos-customer-club-member',
  templateUrl: './customer-club-member.component.html',
  styleUrls: ['./customer-club-member.component.scss']
})
export class CustomerClubMemberComponent implements OnInit {

  public _member: CustomerClubDto;
  public page: Page = Page.DETAILS;

  @Input()
  set member(member: CustomerClubDto) {

    this._member = member;
  }

  @Output() onBack = new EventEmitter<void>(true);

  constructor(
    private store: Store<AppState>,
    private formBuilder: FormBuilder
  ) { }

  ngOnInit() {

  }

  public changePage(page: Page) {
    this.page = page;
    console.log("new page: ", this.page);
  }

  public getPage(): Page {
    return this.page;
  }

  public onCancel(): void {
    this.onBack.emit();
    //this.store.dispatch(staffActions.clearStaffLogin());
  }

  getDateForCSharp(): Date {
    return new Date();
    //date.setMonth(date.getMonth() + 1);
    //return date;
  }

  loadMemberWithId(custId: string) {

    this.store.dispatch(customerClubSearchActions.search({
      searchParams: {
        searchString: custId,
        first: 1,
        skip: 0,
        customerClubOrderByDirectionEnumDto: StockSearchOrderByDirectionEnumDto.ASC,
        customerClubSearchKeywordColumnDto: ClientSearchKeywordColumnDto['Id'],
        customerClubSearchOrderByColumnDto: ClientSearchOrderByColumnDto['Id'],
      }
    }));

    this.store.select(customerClubSearchSelectors.searchedCustomerClubMembers).subscribe((s) => {
      console.log("CustomerClubMemberComponent.loadMemberWithId", s);
      this.member = s[0];
    })
  }

}