import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { OnHoldSearchComponent } from './on-hold-search.component';

describe('OnHoldSearchComponent', () => {
  let component: OnHoldSearchComponent;
  let fixture: ComponentFixture<OnHoldSearchComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ OnHoldSearchComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(OnHoldSearchComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
