import { createReducer, on } from "@ngrx/store";
import { BarcodeResultDto, PrintInvalidDto, ReadDatFileDto } from "src/app/pos-server.generated";
import * as readDatAction from "../read-dat-file/read-dat-file.actions";

export function CartToPrint(item: BarcodeCart): BarcodeResultDto {
    return {  
        barcode: item.validatedBarcode.barcode,
    
    } as BarcodeResultDto;
}

export function PrevNextItem(item: BarcodeCart): BarcodeResultDto {
    return {
        styleCode: item.validatedBarcode.styleCode,
        barcode: item.validatedBarcode.barcode,
        valid: item.validatedBarcode.valid,
        colorCode: item.validatedBarcode.colorCode,
        sizeCode: item.validatedBarcode.sizeCode

    } as BarcodeResultDto;
}

export class BarcodeCart {
    validatedBarcode: BarcodeResultDto;

    public clone(): BarcodeCart {
        return {
            validatedBarcode: Object.assign({}, this.validatedBarcode),
        } as BarcodeCart;
    }
}


// export function CartToProcess(item: BarcodeCart): BarcodeResultDto {
//     return {
//         barcode: item.validatedBarcode.barcode,
//         valid: item.validatedBarcode.valid,
//         styleCode: item.validatedBarcode.styleCode,
//         colorCode: item.validatedBarcode.colorCode,
//         sizeCode: item.validatedBarcode.sizeCode,
//         colorName: item.validatedBarcode.colorName,
//         description: item.validatedBarcode.description,
//         department: item.validatedBarcode.department,
//         makerCode: item.validatedBarcode.makerCode
//     } as BarcodeResultDto;
// }



export class DatFileCartItemState {
    itemBar: BarcodeCart[];
    ischeckingBarcode: boolean;
    barcode: string;
    isLoading: boolean;
    fileName: string;
    //items: FileCart[];
    items: ReadDatFileDto[];

}

export const initialState: DatFileCartItemState = {
    itemBar: [],
    ischeckingBarcode: false,
    barcode: null,
    isLoading: false,
    fileName: '',
    items: [],

} as DatFileCartItemState;

export const DatFileItemReducer = createReducer(initialState,
    on(readDatAction.init, state => initialState),

    on(readDatAction.readDatFile, (state, action) => ({ ...state, isLoading: true, fileName: action.fileName })),

    on(readDatAction.readDatFileResponse, (state, action) => ({ ...state, isLoading: false, items: action.payload })),

    on(readDatAction.validateBarcode, (state, action) => ({ ...state, ischeckingBarcode: true, barcode: action.barcode })),

    //on(readDatAction.validateBarcodeResponse, (state, action) => ({ ...state, ischeckingBarcode: false, validatedBarcode: action.payload })),

    on(readDatAction.validateBarcodeResponse, (state, action) => {

        let itemBarcode = Object.assign([], state.itemBar);

        itemBarcode.push({
            validatedBarcode: {
                barcode: action.payload.barcode,
                valid: action.payload.valid,
                styleCode: action.payload.styleCode,
                colorCode: action.payload.colorCode,
                sizeCode: action.payload.sizeCode,
                colorName: action.payload.colorName,
                description: action.payload.description,
                department: action.payload.department,
                makerCode: action.payload.makerCode

            }
        } as BarcodeCart);

        return { ...state, ischeckingBarcode: false, itemBar: itemBarcode };
    }),

)