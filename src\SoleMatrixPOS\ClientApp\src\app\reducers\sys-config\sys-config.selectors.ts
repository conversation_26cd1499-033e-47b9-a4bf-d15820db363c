import { createFeatureSelector, createSelector } from '@ngrx/store';
import { SysConfigState } from './sys-config.reducers';

export const selectSysConfigState = createFeatureSelector<SysConfigState>('sysConfig');

export const selectSysConfig = createSelector(
	selectSysConfigState,
	state => state.sysConfig
);

export const selectSysConfigLoading = createSelector(
	selectSysConfigState,
	state => state.loading
);

export const selectSysConfigError = createSelector(
	selectSysConfigState,
	state => state.error
);

export const selectUpdateStatus = createSelector(
	selectSysConfigState,
	state => state.updateStatus
);

export const selectOrderDeposit = createSelector(
	selectSysConfigState,
	(state: SysConfigState) => state.sysConfig.orderDeposit || 0
);

export const selectLaybyDeposit = createSelector(
	selectSysConfigState,
	(state: SysConfigState) => state.sysConfig.laybyDeposit || 0
);

export const selectOnHoldDeposit = createSelector(
	selectSysConfigState,
	(state: SysConfigState) => state.sysConfig.onHoldDeposit || 0
);

export const selectSoftCreditLimit = createSelector(
	selectSysConfigState,
	(state: SysConfigState) => state.sysConfig.softCreditLimit
);

export const selectWeeksOnLayby = createSelector(
	selectSysConfigState,
	(state: SysConfigState) => state.sysConfig.laybyNumberOfWeeks
);

export const selectPointsOnAllSales = createSelector(
	selectSysConfigState,
	(state: SysConfigState) => state.sysConfig.pointOnAllSales
);

export const PointsPerDollar = createSelector(
	selectSysConfigState,
	(state: SysConfigState) => state.sysConfig.pointPerDollar
);

export const DollarPerPoints = createSelector(
	selectSysConfigState,
	(state: SysConfigState) => state.sysConfig.dollarPerPoints
);

export const OpenCashTill = createSelector(
	selectSysConfigState,
	(state: SysConfigState) => state.sysConfig.alwaysOpenCashDrawer
);

export const CustomerAccount = createSelector(
	selectSysConfigState,
	(state: SysConfigState) => state.sysConfig.customerAccount
);

export const selectCustomerClubBirthday = createSelector(
	selectSysConfigState,
	(state: SysConfigState) => state.sysConfig.customerClubBirthday
);

export const CustomerNameOnReceipt = createSelector(
	selectSysConfigState,
	(state: SysConfigState) => state.sysConfig.customerNameOnReceipt
);
