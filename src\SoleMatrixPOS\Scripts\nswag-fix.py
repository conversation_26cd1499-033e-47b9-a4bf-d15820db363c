"""
    Tool to take an NSWAG generated TS file and convert any newer syntax incompatible with Angular 8
    to compatible counterparts.
    This script should be ran AFTER the nswag generation step (during build).

    Written by: <PERSON>
"""

import argparse as ap

import os
import re

def iter_nullish(text: str) -> list[tuple[int, int]]:
    matches = re.finditer(r"\S*\s*\?\?\s*.*", text)
    return ((match.start(0), match.end(0)) for match in matches)


def get_nullish_alternative(code: str) -> str:
    term1, term2 = map(str.strip, code.split("??"))
    return f"({term1} != undefined && {term1} != null) ? {term1} : {term2}"


def repair_code(text: str) -> tuple[str, int]:
    parts = []
    idx = 0
    count = 0
    for nullish_start, nullish_end in iter_nullish(text):
        parts.append(text[idx : nullish_start])
        parts.append(get_nullish_alternative(text[nullish_start: nullish_end]))
        idx = nullish_end
        count += 1

    # Append last bit
    parts.append(text[idx : ])
    
    # Join and return
    return "".join(parts), count


def main():
    argparser = ap.ArgumentParser()
    argparser.add_argument('-t', '--target', help="The TS file to target and fix", required=True)
    argns = argparser.parse_args()

    with open(argns.target, 'r') as f:
        fixed, count = repair_code(f.read())
        
    with open(argns.target, 'w') as f:
        f.write(fixed)
        
    if count: print(f"Successfully modified file: '{os.path.basename(argns.target)}' with {count} change(s)!!!")
    else: print(f"File: '{os.path.basename(argns.target)}' had no issues to remediate!!!")

if __name__ == "__main__": main()
