import { Component, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import { Payment, Transaction } from '../payment.service';

@Component({
  selector: 'pos-eftpos-modal',
  templateUrl: './eftpos-modal.component.html',
  styleUrls: ['./eftpos-modal.component.scss']
})
export class EftposModalComponent implements OnInit {

  //public eftposTypes = ['Direct Debit', 'Mastercard', 'Visa', 'Amex', 'Zip Pay', 'After Pay'];
  //public amount: number;

	@Input() amountDue: number;
	@Input() intEft: boolean;

  get amount() { return this.form.get('Amount'); }

  constructor(
    public activeModal: NgbActiveModal,
		private store: Store<AppState>,
    private formBuilder: FormBuilder
    ) { }

	public form;

	ngOnInit() {
	  this.form = this.formBuilder.group({
		  Amount: [this.amountDue, [
			  Validators.required,
			  Validators.min(0.01),
			  Validators.max(this.amountDue),
			  Validators.pattern(/^\d*[.]{0,1}\d{0,2}$/)
		  ]]
	  });
  }
 

  dismiss(reason: string) {
		this.activeModal.dismiss(reason);
	}

	apply() {
		let desc = "Eftpos";
		if (this.intEft) { desc = "Integrated Eftpos" }
    console.log(this.amount);
    if(this.form.valid) {
      this.activeModal.close({ type: "Eftpos", desc: desc, amount: +this.amount.value } as Payment);
    } else {
      this.form.markAsPristine();
    }
  }

  useRemainder() {
    this.amount.setValue(this.amountDue);
  }

  public fieldValidate(control: AbstractControl): boolean {
    return control.invalid;
  }

}