import { Injectable } from "@angular/core";
import { Actions, ofType, createEffect } from '@ngrx/effects';
import { HistoryClient, DailyClient } from 'src/app/pos-server.generated';
import { mergeMap, map, catchError, tap, filter } from 'rxjs/operators';
import * as dailyActions from './daily.actions';
import { EMPTY } from 'rxjs';

@Injectable()
export class DailyEffects {
  constructor(
    private actions$: Actions,
    private historyClient: HistoryClient,
    private dailyClient: DailyClient,
  ) {}

  getTodaysDaily$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dailyActions.getTodaysDaily),
      filter(action => !!action.date), 
      tap(action => {
        console.log('[DailyEffects] getTodaysDaily action triggered:', action);
      }),
      mergeMap(action =>
        this.dailyClient.getDaily(action.date).pipe(
          tap(response => {
            console.log('[DailyEffects] getDaily response:', response);
          }),
          map(response => {
            console.log('[DailyEffects] dispatching storeTodaysDaily:', response);
            return dailyActions.storeTodaysDaily({ payload: response });
          }),
          catchError(error => {
            console.error('[DailyEffects] getTodaysDaily error:', error);
            return EMPTY;
          })
        )
      )
    )
  );

  salesByStaff$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dailyActions.getSalesByStaff),
      filter(action => !!action.date),
      tap(action => {
        console.log('[DailyEffects] getSalesByStaff action triggered:', action);
        console.trace('Dispatch stack trace for getSalesByStaff');
      }),
      mergeMap(action =>
        this.dailyClient.salesByStaff(action.date).pipe(
          tap(res => {
            console.log('[DailyEffects] salesByStaff response:', res);
          }),
          map(data => {
            console.log('[DailyEffects] dispatching storeSalesByStaffQuery:', data);
            return dailyActions.storeSalesByStaffQuery({ payload: data });
          }),
          catchError(error => {
            console.error('[DailyEffects] salesByStaff error:', error);
            return EMPTY;
          })
        )
      )
    )
  );

  refundsByStaff$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dailyActions.getRefundsByStaff),
      filter(action => !!action.date),
      tap(action => {
        console.log('[DailyEffects] getRefundsByStaff action triggered:', action);
        console.trace('Dispatch stack trace for getRefundsByStaff');
      }),
      mergeMap(action =>
        this.dailyClient.refundsByStaff(action.date).pipe(
          tap(res => {
            console.log('[DailyEffects] refundsByStaff response:', res);
          }),
          map(data => {
            console.log('[DailyEffects] dispatching storeRefundsByStaffQuery:', data);
            return dailyActions.storeRefundsByStaffQuery({ payload: data });
          }),
          catchError(error => {
            console.error('[DailyEffects] refundsByStaff error:', error);
            return EMPTY;
          })
        )
      )
    )
  );

  salesByHour$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dailyActions.getSalesByHour),
      filter(action => !!action.date), 
      tap(action => {
        console.log('[DailyEffects] getSalesByHour action triggered:', action);
      }),
      mergeMap(action =>
        this.dailyClient.salesByHour(action.date).pipe(
          tap(res => {
            console.log('[DailyEffects] salesByHour response:', res);
          }),
          map(data => {
            console.log('[DailyEffects] dispatching storeSalesByHourQuery:', data);
            return dailyActions.storeSalesByHourQuery({ payload: data });
          }),
          catchError(error => {
            console.error('[DailyEffects] salesByHour error:', error);
            return EMPTY;
          })
        )
      )
    )
  );

  multiSales$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dailyActions.getMultiSales),
      filter(action => !!action.date), 
      tap(action => {
        console.log('[DailyEffects] getMultiSales action triggered:', action);
      }),
      mergeMap(action =>
        this.dailyClient.multiSales(action.date).pipe(
          tap(response => {
            console.log('[DailyEffects] multiSales response:', response);
          }),
          map(response => {
            console.log('[DailyEffects] dispatching storeMultiSales:', response);
            return dailyActions.storeMultiSales({ payload: response });
          }),
          catchError(error => {
            console.error('[DailyEffects] multiSales error:', error);
            return EMPTY;
          })
        )
      )
    )
  );

  refundsByHour$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dailyActions.getRefundsByHour),
      filter(action => !!action.date), 
      tap(action => {
        console.log('[DailyEffects] getRefundsByHour action triggered:', action);
      }),
      mergeMap(action =>
        this.dailyClient.refundsByHour(action.date).pipe(
          tap(res => {
            console.log('[DailyEffects] refundsByHour response:', res);
          }),
          map(data => {
            console.log('[DailyEffects] dispatching storeRefundsByHourQuery:', data);
            return dailyActions.storeRefundsByHourQuery({ payload: data });
          }),
          catchError(error => {
            console.error('[DailyEffects] refundsByHour error:', error);
            return EMPTY;
          })
        )
      )
    )
  );

  departmentSales$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dailyActions.getDepartmentSales),
      filter(action => !!action.date), 
      tap(action => {
        console.log('[DailyEffects] getDepartmentSales action triggered:', action);
      }),
      mergeMap(action =>
        this.dailyClient.departmentSales(action.date).pipe(
          tap(res => {
            console.log('[DailyEffects] departmentSales response:', res);
          }),
          map(data => {
            console.log('[DailyEffects] dispatching storeDepartmentSales:', data);
            return dailyActions.storeDepartmentSales({ payload: data });
          }),
          catchError(error => {
            console.error('[DailyEffects] departmentSales error:', error);
            return EMPTY;
          })
        )
      )
    )
  );

  departmentRefunds$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dailyActions.getDepartmentRefunds),
      filter(action => !!action.date), 
      tap(action => {
        console.log('[DailyEffects] getDepartmentRefunds action triggered:', action);
      }),
      mergeMap(action =>
        this.dailyClient.departmentRefunds(action.date).pipe(
          tap(res => {
            console.log('[DailyEffects] departmentRefunds response:', res);
          }),
          map(data => {
            console.log('[DailyEffects] dispatching storeDepartmentRefunds:', data);
            return dailyActions.storeDepartmentRefunds({ payload: data });
          }),
          catchError(error => {
            console.error('[DailyEffects] departmentRefunds error:', error);
            return EMPTY;
          })
        )
      )
    )
  );

  // Existing range effects
  getDailyRange$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dailyActions.getDailyRange),
      tap(action => {
        console.log('[DailyEffects] getDailyRange action triggered:', action);
      }),
      mergeMap(action =>
        this.dailyClient.getDailyRange(action.startDate, action.endDate).pipe(
          tap(res => {
            console.log('[DailyEffects] getDailyRange response:', res);
          }),
          map(data => {
            console.log('[DailyEffects] dispatching storeDailyRange:', data);
            return dailyActions.storeDailyRange({ payload: data });
          }),
          catchError(error => {
            console.error('[DailyEffects] getDailyRange error:', error);
            return EMPTY;
          })
        )
      )
    )
  );

  getDailyRangeTotals$ = createEffect(() =>
    this.actions$.pipe(
      ofType(dailyActions.getDailyRangeTotals),
      tap(action => {
        console.log('[DailyEffects] getDailyRangeTotals action triggered:', action);
      }),
      mergeMap(action =>
        this.dailyClient.getDailyRangeTotals(action.startDate, action.endDate).pipe(
          tap(res => {
            console.log('[DailyEffects] getDailyRangeTotals response:', res);
          }),
          map(data => {
            console.log('[DailyEffects] dispatching storeDailyRangeTotals:', data);
            return dailyActions.storeDailyRangeTotals({ payload: data });
          }),
          catchError(error => {
            console.error('[DailyEffects] getDailyRangeTotals error:', error);
            return EMPTY;
          })
        )
      )
    )
  );
}
