import { Injectable } from "@angular/core";
import { TokenStore } from "../store/token.store";
import { distinctUntilChanged, map } from "rxjs/operators";
import { ITokenPayload } from "../interceptors/auth.interceptor";
import jwtDecode from "jwt-decode";
import { Observable } from "rxjs";


interface IAuthContext {
    isAuthenticated: boolean;
    loginId: string;
    tillId: string;
    storeId: string;
}

@Injectable()
export class AuthContextProvider {
    constructor(private tokenStore: TokenStore){}

    getAuthContext(): Observable<IAuthContext> {
        return  this.tokenStore.getRefreshToken().pipe(map(refreshToken => {
        if(!refreshToken){
            return {
                isAuthenticated: false,
                loginId: "",
                tillId: "",
                storeId: "",
            }
        }

        let decodedRefreshToken: ITokenPayload;

        try{
            decodedRefreshToken = jwtDecode(refreshToken)
        } catch (error){
            return {
                isAuthenticated: false,
                loginId: "",
                tillId: "",
                storeId: "",
            }
        }

        if((decodedRefreshToken.exp-5) * 1000 <= Date.now()){
            return {
                isAuthenticated: false,
                loginId: "",
                tillId: "",
                storeId: ""
            }
        }

        return {
            isAuthenticated: true,
            loginId: decodedRefreshToken.loginId,
            tillId: decodedRefreshToken.tillId,
            storeId: decodedRefreshToken.storeId
        }

       }))
    }
}