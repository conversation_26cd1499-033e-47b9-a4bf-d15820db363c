import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AfterPayModalComponent } from '../after-pay-modal/after-pay-modal.component';
import { CashModalComponent } from '../cash-modal/cash-modal.component';
import { ChequeModalComponent } from '../cheque-modal/cheque-modal.component';
import { CustomerAccountModalComponent } from '../customer-account-modal/customer-account-modal.component';
import { EftposModalComponent } from '../eftpos-modal/eftpos-modal.component';
import { GiftCardModalComponent } from '../gift-card-modal/gift-card-modal.component';
import { PaymentType } from '../payment.service';
import { ZipPayModalComponent } from '../zip-pay-modal/zip-pay-modal.component';

@Component({
  selector: 'pos-payment-modal-button',
  templateUrl: './payment-modal-button.component.html',
  styleUrls: ['./payment-modal-button.component.scss']
})
export class PaymentModalButtonComponent implements OnInit {

  @Input() buttonInfo: PaymentModalButton;
  @Output() openModal = new EventEmitter<PaymentType>();

  constructor(){}

  ngOnInit() {
  }

  onClick(){
    console.log("Modal button for " + this.buttonInfo.text + " clicked.");
    this.openModal.emit(this.buttonInfo.type);
  }

  

}

export class PaymentModalButton{
  constructor(public text: string, public imgOrIconClass: string, public type: PaymentType, public useImage: boolean = false){}
}
