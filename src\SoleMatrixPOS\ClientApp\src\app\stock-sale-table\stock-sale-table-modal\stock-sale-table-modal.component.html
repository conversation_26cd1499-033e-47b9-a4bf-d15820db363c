<div class="container-fluid search-container p-3">
	<button type="button" class="btn btn-circle float-right" (click)="dismiss('Cross click')">
		<i class="fas fa-times fa-2x"></i>
	</button>
	<ng-container *ngIf="page == STOCK_SALE">
		<stock-sale-table-container></stock-sale-table-container>
		<button type="button" class="btn btn-info space-bt" (click)="switchPage(2)">Transfer Table</button>
	</ng-container>
	<ng-container *ngIf="page == TRANSFER">
		<app-transfer></app-transfer>
		<button type="button" class="btn btn-info space-bt" (click)="switchPage(1)">Stock Table</button>
	</ng-container>
</div>
