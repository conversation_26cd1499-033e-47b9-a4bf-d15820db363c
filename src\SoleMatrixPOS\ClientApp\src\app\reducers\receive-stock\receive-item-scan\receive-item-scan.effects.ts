import { Injectable } from '@angular/core';
import { Actions, Effect, ofType, createEffect } from '@ngrx/effects';
import * as receiveItemScanActions from './receive-item-scan.actions';
import {catchError, filter, map, mergeMap, tap, withLatestFrom} from 'rxjs/operators';
import {GenerateTransferNumberRequest, ReceiveStockClient} from '../../../pos-server.generated';
import { TableItemFromDto } from '../../../generic-stock-table/stock-table-item/stock-table-item';
@Injectable()
export class ReceiveItemScanEffects {
	constructor(
		private actions$: Actions,
		private receiveStockClient: ReceiveStockClient
	) {}

	retrieveItem$ = createEffect(() => this.actions$.pipe(
		ofType(receiveItemScanActions.submitItemBarcode),
		mergeMap(
			(action)=>this.receiveStockClient.getItemByBarcode(action.payload)
			.pipe(
				map(
					(response) => {
						console.log('Response was:');
						console.log(response);
						if(response == null){
							return receiveItemScanActions.invalidItemBarcode();
						}
						
						else {
							return receiveItemScanActions.addItem({item: TableItemFromDto(response)})
						}
						
					}
				)
			)
		)
	));

	submitReceival$ = createEffect(() => this.actions$.pipe(
		ofType(receiveItemScanActions.submitReceival),
		mergeMap(
			(action) => this.receiveStockClient.submitReceival(action.payload)
			.pipe(
				map((response)=>{
					return receiveItemScanActions.receivalSubmissionCompleted();
				})
			)
		)
	));
}