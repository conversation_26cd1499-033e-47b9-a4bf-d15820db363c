using AutoMapper;
using SoleMatrixPOS.Identity.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SoleMatrixPOS.Application.Identity
{
	public class IdentityMapping : Profile
	{
		public IdentityMapping()
		{
			CreateMap<RegisteredTill, RegisteredTillDto>()
				.ForMember(dest => dest.LoginId, opt => opt.MapFrom(src => src.Id))
				.ReverseMap();
		}
	}
}
