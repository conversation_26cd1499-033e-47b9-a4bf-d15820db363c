
    .buttons {
        width: 100%;
        table-layout: fixed;
        border-collapse: collapse;
    }

    .buttons button {
        width: 222px;
        height: 80px;
    }
    
    .buttons td { width: 50%; }

    //table td { align-content: center; text-align: center;}
    table th {
        align-content: left;
        justify-content: left;
    }

    #sale-table-header table th:nth-child(1) { width: 25%; } // item
    #sale-table-header table th:nth-child(2) { width: 20%; } // colour
    #sale-table-header table th:nth-child(3) { width: 20%; } // size
    #sale-table-header table th:nth-child(4) { width: 15%; } // quanityt
    #sale-table-header table th:nth-child(5) { width: 20%; } // price
    
    #sale-table-header table td:nth-child(1) { width: 25%; } // item
    #sale-table-header table td:nth-child(2) { width: 20%; } // colour
    #sale-table-header table td:nth-child(3) { width: 20%; } // size
    #sale-table-header table td:nth-child(4) { width: 15%; } // quanityt
    #sale-table-header table td:nth-child(5) { width: 20%; } // price

    #sale-table-body table th:nth-child(1) { width: 25%; } // item
    #sale-table-body table th:nth-child(2) { width: 20%; } // colour
    #sale-table-body table th:nth-child(3) { width: 20%; } // size
    #sale-table-body table th:nth-child(4) { width: 15%; } // quanityt
    #sale-table-body table th:nth-child(5) { width: 20%; } // price
    
    #sale-table-body table td:nth-child(1) { width: 25%; } // item
    #sale-table-body table td:nth-child(2) { width: 20%; } // colour
    #sale-table-body table td:nth-child(3) { width: 20%; } // size
    #sale-table-body table td:nth-child(4) { width: 15%; } // quanityt
    #sale-table-body table th:nth-child(1) { width: 25%; } // item

    #sale-table-footer table td:nth-child(5) { width: 20%; } // price    
    #sale-table-footer table th:nth-child(2) { width: 20%; } // colour
    #sale-table-footer table th:nth-child(3) { width: 20%; } // size
    #sale-table-footer table th:nth-child(4) { width: 15%; } // quanityt
    #sale-table-footer table th:nth-child(5) { width: 20%; } // price
    
    #sale-table-footer table td:nth-child(1) { width: 25%; } // item
    #sale-table-footer table td:nth-child(2) { width: 20%; } // colour
    #sale-table-footer table td:nth-child(3) { width: 20%; } // size
    #sale-table-footer table td:nth-child(4) { width: 15%; } // quanityt
    #sale-table-footer table td:nth-child(5) { width: 20%; } // price
