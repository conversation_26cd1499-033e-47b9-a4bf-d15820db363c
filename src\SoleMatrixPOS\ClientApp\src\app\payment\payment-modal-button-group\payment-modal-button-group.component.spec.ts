import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PaymentModalButtonGroupComponent } from './payment-modal-button-group.component';

describe('PaymentModalButtonGroupComponent', () => {
  let component: PaymentModalButtonGroupComponent;
  let fixture: ComponentFixture<PaymentModalButtonGroupComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PaymentModalButtonGroupComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PaymentModalButtonGroupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
