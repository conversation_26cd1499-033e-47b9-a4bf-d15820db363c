import { createFeatureSelector, createSelector } from '@ngrx/store';
import { PINAuthState } from './pin-auth.reducers';

export const selectPINAuthState = createFeatureSelector<PINAuthState>('pinAuth');

export const selectPINAuth = createSelector(
	selectPINAuthState,
	state => state.pinAuth
);

export const selectPINAuthLoading = createSelector(
	selectPINAuthState,
	state => state.loading
);

export const selectPINAuthError = createSelector(
	selectPINAuthState,
	state => state.error
);

//export const selectUpdatePINAuthStatus = createSelector(
//	selectPINAuthState,
//	state => state.updateStatus
//);
