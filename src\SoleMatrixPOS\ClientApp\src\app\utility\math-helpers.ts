/*
    Simple library for math helper functions
*/

/*
    Round a currency value to 2 decimal places.
    Adapted from https://stackoverflow.com/questions/11832914/how-to-round-to-at-most-2-decimal-places-if-necessary
*/
export function financialRound(value){
    return Math.round(value * 100) / 100;
}

/*
    Pretty print monetary value, by rounding and placing a dollar sign
    in front.
*/
export function toFinancialString(value){
    return "$" + financialRound(value).toString();
}