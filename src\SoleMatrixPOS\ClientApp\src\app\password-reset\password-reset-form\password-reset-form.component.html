<form [formGroup]="resetPasswordForm" (ngSubmit)="onSubmit()">
	<div class="d-flex flex-column w-100" style="gap: 0.5rem">
		<div class="form-group position-relative">
			<label for="password">New password</label>
			<input
				id="password"
				type="password"
				autocomplete="new-password"
				formControlName="password"
				class="form-control form-control-lg"
				[class.is-invalid]="submitted && f.password.errors"
			/>
			<div
				*ngIf="submitted && f.password.errors"
				class="invalid-feedback"
			>
				<span *ngIf="f.password.errors?.required"
					>Password is required</span
				>
			</div>
			<div class="mt-2">
				<ul>
					<li [class.text-success]="f.password.value?.match('(.*[A-Z].*)')">
						At least one uppercase character
					</li>
					<li [class.text-success]="f.password.value?.match('(.*[a-z].*)')">
						At least one lowercase character
					</li>
					<li [class.text-success]="f.password.value?.match('(.*[0-9].*)')">
						At least one number
					</li>
					<li [class.text-success]="f.password.value?.match('([^a-zA-Z0-9:space::])')">
						At least one symbol
					</li>
					<li [class.text-success]="f.password.value?.length >= 8">
						At least 8 characters
					</li>
				</ul>
			</div>
		</div>

		<div class="form-group position-relative">
			<label for="passwordConfirm">Confirm new password</label>
			<input
				id="passwordConfirm"
				type="password"
				autocomplete="off"
				formControlName="passwordConfirm"
				class="form-control form-control-lg"
				[class.is-invalid]="
					submitted && f.passwordConfirm.invalid
				"
			/>
			<div
				*ngIf="submitted && f.passwordConfirm.errors"
				class="invalid-feedback position-absolute"
			>
				<span *ngIf="f.passwordConfirm.errors?.required"
					>Confirm password is required</span
				>
				<span *ngIf="!f.passwordConfirm.errors?.required && f.passwordConfirm.errors?.passwordMismatch">Password mismatch</span>
			</div>
		</div>

		<div class="mt-2">
			<button class="btn btn-info btn-lg w-100">Submit</button>
		</div>
		<div class="alert alert-warning mt-2" *ngIf="serverError">
			{{ serverError }}
		</div>
	</div>
</form>
