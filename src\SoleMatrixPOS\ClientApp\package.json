{"name": "sole-mate-pos", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "lintfix": "tslint -c tslint.json --project . --fix --force", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular-devkit/build-optimizer": "^0.801.1", "@angular-devkit/build-webpack": "^0.801.1", "@angular/animations": "~8.1.1", "@angular/cdk": "^8.1.4", "@angular/cli": "^8.1.1", "@angular/common": "~8.1.1", "@angular/compiler": "~8.1.1", "@angular/core": "~8.1.1", "@angular/flex-layout": "^11.0.0-beta.33", "@angular/forms": "~8.1.1", "@angular/material": "^8.1.4", "@angular/platform-browser": "~8.1.1", "@angular/platform-browser-dynamic": "~8.1.1", "@angular/router": "~8.1.1", "@ng-bootstrap/ng-bootstrap": "^5.3.1", "@ngrx/effects": "^8.1.0", "@ngrx/store": "^8.1.0", "@ngrx/store-devtools": "^8.2.0", "@ngtools/webpack": "^8.1.1", "@ngx-translate/core": "^11.0.1", "@ngx-translate/http-loader": "^4.0.0", "@sweetalert2/ngx-sweetalert2": "^5.1.0", "bootstrap": "^4.3.1", "bootstrap-scss": "^4.3.1", "font-awesome": "^4.7.0", "jwt-decode": "^3.1.2", "ngx-infinite-scroll": "^8.0.2", "ngx-mat-menu": "^1.2.7", "npm": "^8.1.4", "primeicons": "^7.0.0", "primeng": "^7.1.3", "print-js": "^1.6.0", "rxjs": "~6.4.0", "sweetalert2": "^8.19.1", "tslib": "^1.9.0", "zone.js": "~0.9.1"}, "devDependencies": {"@angular-devkit/build-angular": "^0.801.1", "@angular/compiler-cli": "~8.1.1", "@angular/language-service": "~8.1.1", "@types/jasmine": "~3.3.8", "@types/jasminewd2": "~2.0.3", "@types/node": "~8.9.4", "codelyzer": "^5.0.0", "eslint": "^9.10.0", "jasmine-core": "~3.4.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~4.1.0", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "~2.0.1", "karma-jasmine": "~2.0.1", "karma-jasmine-html-reporter": "^1.4.0", "protractor": "~5.4.0", "ts-node": "~7.0.0", "tslint": "~5.15.0", "typescript": "~3.4.3"}}