import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { StockTableFieldsEnabled } from './stock-table-item/stock-table-item';
import { StockTableItemDto } from "../pos-server.generated"
@Component({
  selector: 'pos-generic-stock-table',
  templateUrl: './generic-stock-table.component.html',
  styleUrls: ['./generic-stock-table.component.scss']
})
export class GenericStockTableComponent implements OnInit {

  @Input() tableItems: StockTableItemDto[];
  @Input() fieldsEnabled: StockTableFieldsEnabled;

  @Output() tableItemDelete = new EventEmitter<number>();
  @Output() tableItemQuantityUpdate = new EventEmitter<QuantityUpdateEvent>();

  ngOnInit() {
  }

  onTableEntryDeleteClicked(index: number) {
    console.log("Attempting to delete index: " + index.toString());
    this.tableItemDelete.emit(index);
  }

  onTableEntryQuantityChanged(index: number, quantity: number) {
    this.tableItemQuantityUpdate.emit(new QuantityUpdateEvent(index, quantity));
  }

}

export class QuantityUpdateEvent {
  constructor(public index: number, public quantity: number) { }
}