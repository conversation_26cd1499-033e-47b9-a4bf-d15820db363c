import { Component, OnInit, EventEmitter, Output, Input, OnDestroy, } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import { Observable, Subject, of } from 'rxjs';
import { CartItemDto, StockItemDto, TranslogDto, CustOrderLineDTO } from 'src/app/pos-server.generated';
import { forkJoin } from 'rxjs';
import * as cartActions from 'src/app/reducers/sales/cart/cart.actions';
import * as orderSearchSelectors from 'src/app/reducers/order-item-search/order-item-search.selectors';
import * as orderSearchActions from 'src/app/reducers/order-item-search/order-item-search.actions';
import { takeWhile, debounceTime, distinctUntilChanged, map, filter, take, tap, catchError, switchMap } from 'rxjs/operators';
import * as customerClubSearchSelectors from 'src/app/reducers/customer-club/club-search/customer-club.selectors';
import * as customerClubSearchActions from 'src/app/reducers/customer-club/club-search/customer-club.actions';
import * as cartSelectors from 'src/app/reducers/sales/cart/cart.selectors';
import * as transactionActions from 'src/app/reducers/transaction/transaction.actions';
import * as orderActions from 'src/app/reducers/order-item/order.actions';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { OnHoldRefundComponent } from '../on-hold-refund/on-hold-refund.component';
import Swal from 'sweetalert2';
import { TransactionClient } from 'src/app/pos-server.generated';

import {
  CustOrderHeaderDTO,
  OrderSearchRequestDto,
  OrderSearchKeywordColumn,
  OrderSearchOrderByColumn,
  StockSearchOrderByDirectionEnumDto,
  CustomerClubDto,
  CreateOrderDto,
  ClientSearchKeywordColumnDto,
  ClientSearchRequestDto
} from 'src/app/pos-server.generated';


@Component({
  selector: 'pos-on-hold-search',
  templateUrl: './on-hold-search.component.html',
  styleUrls: ['./on-hold-search.component.scss']
})
export class OnHoldSearchComponent implements OnInit, OnDestroy {
  term = '';
  field = 'FirstName';

  orders$: Observable<CreateOrderDto[]>;
  searchOptions$: Observable<OrderSearchRequestDto>;
  searchLoading$: Observable<boolean>;
  totalDepositAmount: number = 0;
  collapsed: boolean;
  selectedClubMember: CustomerClubDto | null = null;

  public loading: boolean = true;

  private searchTerm$ = new Subject<string>();
  private alive = true;
  member: CustomerClubDto

  @Input() orders: CreateOrderDto[];
  @Input() options: OrderSearchRequestDto;

  @Input() selectedOrder: CreateOrderDto;

  @Output() onSelectedOrder = new EventEmitter<CreateOrderDto>();
  @Output() onEditedOrder = new EventEmitter<CreateOrderDto>();

  @Output() searchChanged = new EventEmitter<OrderSearchRequestDto>();

  constructor(
    private store: Store<AppState>, 
    private router: Router, 
    public activeModal: NgbActiveModal, 
    private modalService: NgbModal,
    private transactionClient: TransactionClient
  ) {}

  ngOnInit() {
    const savedField = localStorage.getItem('customerSpecialsSearchField');
    if (savedField) {
      if (savedField === 'Code') {
        this.field = 'OrderCode';
      } else if (savedField === 'QuoteStaff') {
        this.field = 'OrderStaff';
      } else {
        this.field = savedField;
      }
    }
    this.orders$ = this.store.select(orderSearchSelectors.searchedOrders).pipe(
      map((orders) => orders.filter((order) => 
        !order.orderHeader.cancelled && 
        !order.orderHeader.customerCollectionDate &&
        order.orderHeader.orderArrivalDate !== null  // Filter for on-hold (arrived but not collected)
      ))
    );

    this.store.dispatch(orderActions.setDeposit({ deposit: 0 }));

    this.searchOptions$ = this.store.select(orderSearchSelectors.searchOptions);
    this.searchLoading$ = this.store.select(orderSearchSelectors.searchLoading);

    this.searchTerm$
      .pipe(
        takeWhile(() => this.alive),
        debounceTime(200),
        distinctUntilChanged(),
        map(() => {
          const options: OrderSearchRequestDto = {
            searchString: this.term.toUpperCase(),
            first: 25,
            skip: 0,
            customerClubOrderByDirectionEnumDto: StockSearchOrderByDirectionEnumDto.DESC,
            orderSearchKeywordColumnDto: OrderSearchKeywordColumn[this.field],
            orderSearchOrderByColumnDto: OrderSearchOrderByColumn[this.field],
          };
          this.searchChanged.emit(options);
        })
      )
      .subscribe(() => {
        const options: OrderSearchRequestDto = {
          searchString: this.term.toUpperCase(),
          first: 25,
          skip: 0,
          customerClubOrderByDirectionEnumDto: StockSearchOrderByDirectionEnumDto.ASC,
          orderSearchKeywordColumnDto: OrderSearchKeywordColumn[this.field],
          orderSearchOrderByColumnDto: OrderSearchOrderByColumn[this.field],
        };
        this.doSearch(options);
      });

    this.searchLoading$.subscribe((s) => {
      this.loading = s;
    });

    this.search();
  }

  ngOnDestroy(): void {
    this.alive = false;
  }

  search() {
    let fieldToSave = this.field;
    if (this.field === 'OrderCode') {
      fieldToSave = 'Code';
    }
    localStorage.setItem('customerSpecialsSearchField', fieldToSave);
    this.searchTerm$.next(this.term);
  }

  goHome() {
    this.router.navigateByUrl('/home');
  }

  doSearch(options: OrderSearchRequestDto) {
    this.store.dispatch(orderSearchActions.search({ searchParams: options }));
  }

  selectOrder(order: CreateOrderDto) {
    if (!order || !order.orderLines || order.orderLines.length === 0) {
      console.error('Attempted to select an invalid or empty on-hold order:', order);
      Swal.fire('Info', 'The selected on-hold order is invalid or has no items.', 'info');
      return;
    }

    this.proceedWithSelectOrder(order);
  }

  proceedWithSelectOrder(order: CreateOrderDto) {
    console.log('Proceeding with On-Hold order selected:', order);
    let loadingCancelled = false;
    const swalPromise = Swal.fire({
      title: 'Loading On-Hold Order...', html: 'Please wait while the order is processed.',
      allowOutsideClick: false, allowEscapeKey: false, showCancelButton: true, cancelButtonText: 'Cancel Load',
      onOpen: () => { Swal.showLoading(); const btn = Swal.getCancelButton() as HTMLButtonElement; if (btn) btn.disabled = false; }
    });
    swalPromise.then((result) => {
      if (result.dismiss === Swal.DismissReason.cancel) {
        loadingCancelled = true; console.log('On-Hold order loading cancelled by user.');
        this.activeModal.dismiss('User cancelled on-hold order loading');
      }
    });

    this.store.dispatch(transactionActions.clearTransPayData());
    this.store.dispatch(orderActions.setDeposit({ deposit: 0 })); // Reset deposit for on-hold processing
    this.store.dispatch(orderSearchActions.selectOrder({ payload: order }));
    this.store.dispatch(orderActions.uploadOrderCode({ orderCode: order.orderHeader.orderCode }));
    this.onSelectedOrder.emit(order);

    if (loadingCancelled) return;

    this.transactionClient.getTransLogByTransNo(order.orderLines[0].transNo)
      .pipe(
        take(1),
        switchMap((fetchedTranslogs: TranslogDto[]) => {
          if (loadingCancelled) return of({ allTransPayDetails: [], translogs: [] });
          
          const processedDepositTransNos = new Set<number | undefined>();
          const transPayRequests$: Observable<any>[] = [];
          order.orderLines.forEach((line: CustOrderLineDTO) => {
            if (line.transNo && !processedDepositTransNos.has(line.transNo)) {
              processedDepositTransNos.add(line.transNo);
              this.store.dispatch(transactionActions.getTransPayByTransNo({ transNo: line.transNo }));
              transPayRequests$.push(
                this.store.select(state => state.transaction.transPayData).pipe(filter(details => !!details), take(1))
              );
            }
          });
          const transPayForkJoin$ = transPayRequests$.length > 0 ? forkJoin(transPayRequests$) : of([]);
          return transPayForkJoin$.pipe(map(allTransPayDetails => ({ allTransPayDetails, translogs: fetchedTranslogs })));
        }),
        catchError(err => {
          if (!loadingCancelled) Swal.close();
          console.error('Error fetching translogs or transpay details for on-hold:', err);
          Swal.fire('Error', 'Could not load on-hold order transaction history.', 'error');
          this.activeModal.close();
          return of(null); 
        })
      )
      .subscribe(details => {
        if (loadingCancelled || !details) return;
        const { allTransPayDetails, translogs } = details;

        const uniquePayments = new Map();
        allTransPayDetails.forEach((transPayArray: any[]) => {
          if (transPayArray && transPayArray.length > 0) {
            transPayArray.forEach((payment: any) => {
              const key = `${payment.paymentType}-${payment.voucherNumber || ''}`;
              if (!uniquePayments.has(key)) uniquePayments.set(key, payment);
            });
          }
        });
        this.totalDepositAmount = Array.from(uniquePayments.values()).reduce((sum: number, p: any) => sum + (p.payAmount || 0), 0);
        this.store.dispatch(orderActions.setDeposit({ deposit: this.totalDepositAmount }));
        console.log("Total deposit for on-hold calculated and dispatched:", this.totalDepositAmount);

        const itemProcessingObservables: Observable<any>[] = order.orderLines.map((line: CustOrderLineDTO) => {
          if (loadingCancelled) return of(null);

          const matchedTranslog = translogs.find(tl => 
            tl.styleCode === line.styleCode && 
            tl.colourCode === line.colourCode && 
            tl.sizeCode === line.orderSize
          );

          let effectiveOriginalPrice: number;
          let finalSellingPrice: number;
          let discountCode: string | null = null;
          let hasActualDiscount = false;

          if (matchedTranslog) {
            effectiveOriginalPrice = (matchedTranslog.nettSelling !== null && matchedTranslog.nettSelling !== undefined && matchedTranslog.nettSelling > 0) 
                                     ? matchedTranslog.nettSelling 
                                     : ((matchedTranslog.sellingPrice !== null && matchedTranslog.sellingPrice !== undefined) ? matchedTranslog.sellingPrice : 0);
            finalSellingPrice = (matchedTranslog.sellingPrice !== null && matchedTranslog.sellingPrice !== undefined) 
                                ? matchedTranslog.sellingPrice 
                                : effectiveOriginalPrice;
            hasActualDiscount = effectiveOriginalPrice > finalSellingPrice && matchedTranslog.gst !== null && matchedTranslog.gst > 0;
            if (hasActualDiscount && matchedTranslog.gst) {
              discountCode = Math.floor(matchedTranslog.gst).toString();
            }
          } else {
            effectiveOriginalPrice = line.sellingPrice !== null && line.sellingPrice !== undefined ? line.sellingPrice : 0;
            finalSellingPrice = effectiveOriginalPrice;
            console.warn(`No matching translog for on-hold line: ${line.styleCode}-${line.colourCode}-${line.orderSize}. Using order price.`);
          }
         
          const discountAmount = hasActualDiscount ? effectiveOriginalPrice - finalSellingPrice : 0;
          const discountPercent = hasActualDiscount && effectiveOriginalPrice > 0 ? (discountAmount / effectiveOriginalPrice) * 100 : 0;

          const stockItemForAdd: StockItemDto = {
            colourName: line.orderColour ? line.orderColour.trim() : '',
            styleCode: line.styleCode ? line.styleCode.trim() : '',
            styleDescription: line.orderStyle ? line.orderStyle.trim() : '',
            colourCode: line.colourCode ? line.colourCode.trim() : '',
            size: line.orderSize ? line.orderSize.trim() : '',
            price: effectiveOriginalPrice 
          };

          const dispatchObservables: Observable<any>[] = [];
          for (let i = 0; i < (line.quantity || 1); i++) {
            const tempIdForCartItemSearch = `${stockItemForAdd.styleCode}-${stockItemForAdd.colourCode}-${stockItemForAdd.size}-${Date.now()}-${i}`;
            const itemToAddWithTempId = { ...stockItemForAdd, tempId: tempIdForCartItemSearch };
            this.store.dispatch(cartActions.addItem({ stockItem: itemToAddWithTempId }));

            dispatchObservables.push(this.store.select(cartSelectors.cart).pipe(
              filter(cartItems => {
                if (loadingCancelled) return false;
                const foundItem = cartItems.find(ci => {
                    const stockMatches = ci.stockItem.styleCode === stockItemForAdd.styleCode &&
                                       ci.stockItem.colourCode === stockItemForAdd.colourCode &&
                                       ci.stockItem.size === stockItemForAdd.size;
                    if (!stockMatches) return false;
                    if ((ci.stockItem as any).tempId === tempIdForCartItemSearch) return true;
                    if (hasActualDiscount && discountCode && (!ci.discountCode || ci.discountCode !== discountCode)) return true;
                    if (!hasActualDiscount && !ci.discountCode) return true;
                    return false;
                });
                return !!foundItem;
              }),
              take(1),
              tap(cartItemsAfterAddResponse => {
                if (loadingCancelled) return;
                const confirmedCartItem = cartItemsAfterAddResponse.find(ci => {
                    const stockMatches = ci.stockItem.styleCode === stockItemForAdd.styleCode &&
                                       ci.stockItem.colourCode === stockItemForAdd.colourCode &&
                                       ci.stockItem.size === stockItemForAdd.size;
                    if (!stockMatches) return false;
                    if ((ci.stockItem as any).tempId === tempIdForCartItemSearch) return true;
                    if (hasActualDiscount && discountCode && (!ci.discountCode || ci.discountCode !== discountCode)) return true;
                    if (!hasActualDiscount && !ci.discountCode) return true;
                    return false;
                });
                if (confirmedCartItem) {
                  if ((confirmedCartItem.stockItem as any).tempId) {
                    delete (confirmedCartItem.stockItem as any).tempId;
                  }
                  if (hasActualDiscount && discountCode) {
                    console.log(`Applying historical discount for on-hold item ${line.styleCode}: Original: ${confirmedCartItem.originalPrice}, Final: ${finalSellingPrice}, Code: ${discountCode}`);
                    this.store.dispatch(cartActions.updateItemPrice({
                      stockItem: confirmedCartItem.stockItem,
                      newPrice: finalSellingPrice,
                      discountCode: discountCode,
                      discountPercent: Math.abs(discountPercent),
                      reason: 'Historical On-Hold Price' 
                    }));
                  }
                }
              }),
              catchError(err => { console.error(`Error processing cart item for on-hold ${stockItemForAdd.styleCode}:`, err); return of(null); })
            ));
          }
          return dispatchObservables.length > 0 ? forkJoin(dispatchObservables) : of(null);
        });

        forkJoin(itemProcessingObservables.filter(obs => obs !== null))
        .pipe(catchError(err => {
            if (!loadingCancelled) Swal.close();
            console.error('Error in on-hold item processing forkJoin:', err);
            Swal.fire('Error', 'An error occurred while adding on-hold items to cart.', 'error');
            this.activeModal.close();
            return of(null);
        }))
        .subscribe(itemResults => {
          if (loadingCancelled || !itemResults) return;

          const clientCode = order.orderHeader.clientCode;
          if (clientCode) {
            const searchRequest: ClientSearchRequestDto = {
              searchString: clientCode.toUpperCase(), first: 1, skip: 0,
              customerClubSearchKeywordColumnDto: ClientSearchKeywordColumnDto.Id,
            };
            this.store.dispatch(customerClubSearchActions.search({ searchParams: searchRequest }));
            this.store.select(customerClubSearchSelectors.searchedCustomerClubMembers).pipe(
              filter(response => !!response && response.length > 0),
              take(1),
              catchError(err => {
                if (!loadingCancelled) Swal.close();
                console.error('Error during customer search for on-hold:', err);
                Swal.fire('Error', 'An error occurred selecting the customer for on-hold.', 'error');
                this.activeModal.close(); return of(null);
              })
            ).subscribe(response => {
              if (loadingCancelled || !response) return;
              if (response.length > 0) {
                this.store.dispatch(customerClubSearchActions.selectCustomerClubMember({ payload: response[0] }));
              }
              if (!loadingCancelled) Swal.close();
              this.activeModal.close();
              // For On-Hold, navigate to sales/payment because a deposit might be involved or final payment is due
              this.router.navigate(['/sales/payment']); 
            });
          } else {
            if (!loadingCancelled) Swal.close();
            this.activeModal.close();
            this.router.navigate(['/sales/payment']); // Navigate to payment screen for on-hold
          }
        });
      }); 
  }

  cancelOrder(order: CreateOrderDto) {
    if (!order) { console.error('Invalid on-hold order for cancellation.'); return; }
    this.store.dispatch(orderSearchActions.selectOrder({ payload: order }));
    const modalRef = this.modalService.open(OnHoldRefundComponent, { size: 'lg', backdrop: 'static', centered: true });
    modalRef.componentInstance.order = order;
    modalRef.result.then((result) => {
      console.log('Modal closed:', result);
    }).catch((reason) => {
      console.log('Modal dismissed:', reason);
      if (reason === 'On Hold Submitted') {
        this.activeModal.dismiss();
      }
    });
  }
}