import { createFeatureSelector, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import { EndOfDayState } from './end-of-day-cash-up.reducer';

export const selectEndOfDayState = (state: AppState) => state.endCashDrawer;

export const selectFloatData = createSelector(
  selectEndOfDayState,
  (state: EndOfDayState) => state.floatData
);

export const selectCashDrawerTotal = createSelector(
  selectEndOfDayState,
  (state: EndOfDayState) => state.cashDrawerTotal
);
