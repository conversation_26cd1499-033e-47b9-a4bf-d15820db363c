<h3>Total Amount Table</h3>
<table class="table table-striped table-hover table-scroll">
    <!-- Header Table -->
    <thead>
        <tr class="table-info">
            <th class="title-border-square">Total</th>
            <th class="title-border-square">Sell</th>
            <th class="title-border-square">Value</th>
        </tr>
    </thead>
    <!-- Body Table -->
    <tbody>

        <ng-container *ngFor="let loc of location$ | async;">
            <tr>
                <ng-container *ngIf="header$ | async; let item">
                    <ng-container *ngFor="let total of total$ |async;">
                        <ng-container *ngIf="loc.locationCode == total.locationCode">
                            <ng-container>
                                <td class="border-square"><b>{{total.totalStock}}</b></td>
                                <td class="border-square"><b>{{item.retailPrice | currency}}</b></td>
                                <td class="border-square"><b>{{total.totalStock * item.retailPrice | currency}}</b></td>
                            </ng-container>
                        </ng-container>
                    </ng-container>
                </ng-container>
            </tr>
            <tr class="table-danger">
                <ng-container *ngIf="header$ | async; let item">
                    <ng-container *ngFor="let total of total$ |async;">
                        <ng-container *ngIf="loc.locationCode == total.locationCode">
                            <ng-container>
                                <td class="border-square sale-qty-color-square">{{total.totalSale}}</td>
                                <td class="border-square sale-qty-color-square">{{item.retailPrice |currency}}
                                </td>
                                <td class="border-square sale-qty-color-square">{{total.totalSale * item.retailPrice
                                        | currency}}</td>
                            </ng-container>
                        </ng-container>
                    </ng-container>
                </ng-container>
            </tr>
        </ng-container>
    </tbody>
</table>