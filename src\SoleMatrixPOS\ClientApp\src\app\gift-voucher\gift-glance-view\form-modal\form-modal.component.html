<div class="container">
    <form [formGroup]="giftFormC" autocapitalize="on">
        <div class="card mx-auto" style="margin-top: 1rem; margin-bottom: 1rem;">
            <div class="card-body">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                            <div class="form-group">
                                <label for="VoucherNumber">Gift Voucher Number: </label>
                                <div class="input-group">
                                    <input tabindex="1" id="voucherNumber" class="form-control" type="text" style="font-weight: bolder;"
                                        formControlName="giftVoucherNumber" required [readonly]="!customVoucherNumber"
                                        [ngClass]="{'is-invalid': f.giftVoucherNumber.touched && f.giftVoucherNumber.errors, 'is-valid': f.giftVoucherNumber.valid}">
                                    <div class="input-group-append">
                                        <button id="editVoucherNumber" type="submit" class="btn btn-outline-default" (click)="toggleCustomVoucherNumber()"><i class="fas fa-lg fa-fw fa-pencil mr-2"></i></button>
                                    </div>
                                    <div *ngIf="f.giftVoucherNumber.touched && f.giftVoucherNumber.errors"
                                    class="invalid-feedback">
                                        <div *ngIf="f.giftVoucherNumber.errors.required">Gift Voucher number is required</div>
                                        <div *ngIf="f.giftVoucherNumber.errors.lengthError">{{f.giftVoucherNumber.errors.lengthError}}</div>
                                </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                            <div class="form-group">
                                <div *ngIf="showCustomerClub" class="col-auto">
                                    <button *ngIf="selectedCustomerClubMember != null" type="button"
                                        (click)="launchCustomerClubModal()"
                                        class="btn-lg btn-default btn d-flex align-items-center border border-dark">
                                        <i class="fas fa-crown fa-lg text-danger"></i>
                                        <h4 class="ml-3">{{selectedCustomerClubMember.firstname}}<br />
                                            {{selectedCustomerClubMember.surname}}</h4>
                                    </button>
                                    <button *ngIf="selectedCustomerClubMember == null" type="button"
                                        (click)="launchCustomerClubModal()"
                                        class="btn-lg btn-default btn d-flex align-items-center border border-dark">
                                        <i class="fas fa-crown fa-lg"></i>
                                        <h4 class="ml-3">Customer<br />
                                            Club</h4>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="giftValue">Gift Voucher Value</label>
                                <input type="text" id="giftValue" class="form-control" formControlName="giftValue" placeholder="$">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>