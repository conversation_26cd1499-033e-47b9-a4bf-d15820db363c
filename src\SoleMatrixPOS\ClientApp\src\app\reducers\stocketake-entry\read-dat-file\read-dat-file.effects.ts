import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { EMPTY } from "rxjs";
import { catchError, map, mergeMap, tap } from "rxjs/operators";
import { StocktakeEntryClient } from "src/app/pos-server.generated";
import * as datFileAction from '../read-dat-file/read-dat-file.actions';

@Injectable()
export class ReadDatFileEffect {

    constructor(private action$: Actions, private stocktakeClient: StocktakeEntryClient) { }

    readDatfile$ = createEffect(
        () => this.action$.pipe(
            ofType(datFileAction.readDatFile),
            tap(() => console.log("datFileRead$")),
            mergeMap(
                (action) => this.stocktakeClient.readDatFile(action.fileName)
                    .pipe(
                        map(itemCart => datFileAction.readDatFileResponse({ payload: itemCart }),
                            catchError(() => EMPTY)
                        ))
            )

        )
    )

    validateBarcode$ = createEffect(
        () => this.action$.pipe(
            ofType(datFileAction.validateBarcode),
            tap(() => console.log("validateBarcode$")),
            mergeMap(
                (action) => this.stocktakeClient.validateBarcode({ barcode: action.barcode})
                    .pipe(
                        map(response => datFileAction.validateBarcodeResponse({ payload: response }),
                            catchError(() => EMPTY)
                        )
                    )
            )
        )
    )
}


