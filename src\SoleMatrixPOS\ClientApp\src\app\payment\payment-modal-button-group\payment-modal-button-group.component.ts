import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { PaymentModalButton } from '../payment-modal-button/payment-modal-button.component';

import * as paymentSelectors from '../../reducers/sales/payment/payment.selector'

import { AfterPayModalComponent } from '../after-pay-modal/after-pay-modal.component';
import { CashModalComponent } from '../cash-modal/cash-modal.component';
import { ChequeModalComponent } from '../cheque-modal/cheque-modal.component';
import * as orderItemSelectors from 'src/app/reducers/order-item/order.selectors';
import { CustomerAccountModalComponent } from '../customer-account-modal/customer-account-modal.component';
import { EftposModalComponent } from '../eftpos-modal/eftpos-modal.component';
import { CustomerPointsModalComponent } from '../customer-points-modal/customer-points-modal.component';
import { GiftCardModalComponent } from '../gift-card-modal/gift-card-modal.component';
import { PaymentType } from '../payment.service';
import { ZipPayModalComponent } from '../zip-pay-modal/zip-pay-modal.component';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Payment, PaymentService, Transaction } from '../payment.service';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import * as orderActions from 'src/app/reducers/order-item/order.actions'
import * as SysConfigSelectors from '../../reducers/sys-config/sys-config.selectors';
import { GetSystemStatusDto } from '../../pos-server.generated';
import { Observable, of } from 'rxjs';
import { first, switchMap, catchError, tap, map } from 'rxjs/operators';
import { sys } from 'typescript';
import * as sysConfigSelectors from '../../reducers/sys-config/sys-config.selectors';
import { CreditNoteIssuanceModalComponent } from '../credit-note-issuance-modal/credit-note-issuance-modal.component';
import Swal from 'sweetalert2';

@Component({
  selector: 'pos-payment-modal-button-group',
  templateUrl: './payment-modal-button-group.component.html',
  styleUrls: ['./payment-modal-button-group.component.scss']
})
export class PaymentModalButtonGroupComponent implements OnInit {
	nButton: number;
	nRow: number;
	isEven: boolean;
	DepositPaid: number;
	rowNumbers: number[];
	colNumbers: number[];
	buttonMatrix: any;
	integrated: boolean=false;
	sysConfig$: Observable<GetSystemStatusDto>;
	private currentDeposit: number = 0;
	private depositSet: boolean = false;
	private customDepositAmount: number = 0;
	private isCustomDepositSet: boolean = false;
  softCreditLimit: string = 'F';

	@Input() modalButtons: PaymentModalButton[];
	@Input() amountDue: number;
	@Input() transType: number;
	@Input() isLaybyMode: boolean = false;
	@Input() cartTotal: number;
	@Input() change: number = 0;
	@Input() laybyMinPolicyDeposit: number = 0; // For store's minimum deposit % of cart total

	@Output() paymentResult: EventEmitter<Payment> = new EventEmitter<Payment>();

	constructor(private modalService: NgbModal, private store: Store<AppState>) {
		// Setup subscription in constructor
		this.store.select(orderItemSelectors.selectOrderDeposit)
		  .subscribe(deposit => {
			this.currentDeposit = deposit || 0;
		  });
	}

	ngOnInit() {
		const deposit = this.retrieveDepositAmount();
		this.DepositPaid = deposit;
		console.log("retrieved deposit", this.DepositPaid);
		this.processIfDepositExists(this.DepositPaid);
		
		this.sysConfig$ = this.store.select(SysConfigSelectors.selectSysConfig);
		this.subscribeToState();
		this.nButton = this.modalButtons.length;
		this.nRow = Math.round(this.nButton / 2);
		this.isEven = (this.nButton % 2) == 0;

		// Generate values
		this.rowNumbers = Array.from(Array(this.nRow).keys()); 
		this.colNumbers = [0, 1];
		this.integrated = false;

    this.store.select(sysConfigSelectors.selectSoftCreditLimit)
      .subscribe(limit => {
        this.softCreditLimit = limit || 'F';
      });

		// Generate matrix
		// this.buttonMatrix = this.generateButtonMatrix();

	}
	// generateButtonMatrix(): any {
	// 	var mat = [];
	// 	for(let r = 0; r < this.nRow; r++){
	// 		var row = [];
	// 		for(let)
	// 	}
	// }

  subscribeToState() {
	  this.sysConfig$.subscribe((sysConfig: GetSystemStatusDto) => {
		if (sysConfig.integratedEFTProvider !== 'None' ? sysConfig.integratedEFTProvider : null){
			this.integrated = true;
		}
		else {
			this.integrated = false;
		}
	  });
  }

  launchPaymentModal(type: PaymentType) {
    if (this.isLaybyMode && type === PaymentType.Eftpos && this.change > 0) {
      Swal.fire({
        title: "Cannot Process Eftpos",
        text: "Eftpos payments cannot be processed when change is due in layby mode. You can cancel the layby and restart the process.",
        type: "warning"
      });
      return;
    }

    if (this.isLaybyMode && 
        !this.depositSet && 
        (type === PaymentType.Cash || type === PaymentType.Eftpos)) {
      this.showDepositModal(type);
    } else {
      this.openPaymentModal(type);
    }
  }

  private showDepositModal(paymentType: PaymentType) {
    // Default inputValue: if layby mode, suggest the policy minimum, otherwise current amountDue.
    // This is the amount the user will newly enter.
    let suggestedNewPayment = this.amountDue; // Default for non-layby or if policy deposit is already met/exceeded by pre-paid
    if (this.isLaybyMode) {
        // Suggest paying up to the policy minimum, considering what's already pre-paid.
        const neededForPolicyMin = this.laybyMinPolicyDeposit - this.currentDeposit;
        suggestedNewPayment = neededForPolicyMin > 0 ? neededForPolicyMin : 0; 
        // However, if amountDue (what's needed for current transaction total) is higher, suggest that.
        // This can happen if transaction.total was already set to a user-defined higher deposit.
        if (this.amountDue > suggestedNewPayment) {
            suggestedNewPayment = this.amountDue;
        }
    }

    // Determine which value to show as the initial input value - the greater of store min or pre-paid
    const displayValue = this.isLaybyMode ? 
      Math.max(this.laybyMinPolicyDeposit, this.currentDeposit).toFixed(2) : 
      this.amountDue.toFixed(2);

    Swal.fire({
      title: 'Set Combined Total Deposit Amount?',
      input: 'number',
      inputAttributes: {
        min: '0.01',
        step: '0.01'
      },
      inputValue: displayValue,
      html: `<p>Combined total amount customer would like to pay.</p>
             <p>Current amount due: ${this.amountDue.toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })}</p>
             <p>Pre-paid deposit: ${this.currentDeposit.toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })}</p>
             ${this.isLaybyMode ? `<p>Store minimum layby deposit: ${this.laybyMinPolicyDeposit.toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })}</p>` : ''}
             <p>Cart total: ${this.cartTotal.toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })}</p>`,
      showCancelButton: true,
      confirmButtonText: 'Continue',
      cancelButtonText: 'Cancel',
      showLoaderOnConfirm: true,
      preConfirm: (inputTotalDepositAmount) => {
        const enteredTotalDeposit = parseFloat(inputTotalDepositAmount);
        
        if (isNaN(enteredTotalDeposit)) {
          Swal.showValidationMessage('Please enter a valid amount');
          return false;
        }

        if (enteredTotalDeposit < 0) {
            Swal.showValidationMessage('Total deposit cannot be negative.');
            return false;
        }

        // Total deposit cannot be less than what's already pre-paid.
        if (enteredTotalDeposit < this.currentDeposit) {
          Swal.showValidationMessage(`Total deposit cannot be less than the pre-paid amount of ${this.currentDeposit.toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })}.`);
          return false;
        }
        
        if (enteredTotalDeposit > this.cartTotal) {
          Swal.showValidationMessage('Total deposit cannot exceed the cart total');
          return false;
        }
        
        // If in layby mode, check against the policy minimum deposit
        if (this.isLaybyMode && enteredTotalDeposit < this.laybyMinPolicyDeposit) {
          if (this.softCreditLimit === 'F') {
            Swal.showValidationMessage(`Total deposit of ${enteredTotalDeposit.toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })} is less than the store minimum layby deposit of ${this.laybyMinPolicyDeposit.toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })}.`);
            return false;
          }
        }

        return enteredTotalDeposit; // This is the TOTAL desired deposit.
      }
    }).then((result) => {
      if (result.value !== undefined) {
        const confirmedTotalDepositAmount = result.value;

        if (this.isLaybyMode && this.softCreditLimit === 'T' && confirmedTotalDepositAmount < this.laybyMinPolicyDeposit) {
          Swal.fire({
            title: 'Amount Below Minimum Due',
            text: `You have entered an amount of ${confirmedTotalDepositAmount.toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })}, which is below the minimum due of ${this.laybyMinPolicyDeposit.toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })}. Do you want to proceed with this amount?`,
            type: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, proceed',
            cancelButtonText: 'No, cancel'
          }).then((confirmationResult) => {
            if (confirmationResult.value) {
              this.applyDepositAmount(confirmedTotalDepositAmount, paymentType); // Pass the TOTAL confirmed deposit
            }
          });
        } else {
          this.applyDepositAmount(confirmedTotalDepositAmount, paymentType); // Pass the TOTAL confirmed deposit
        }
      }
    });
  }

  private applyDepositAmount(amount: number, paymentType: PaymentType) {
    this.depositSet = true;
 
    const depositUpdatePayment = new Payment();
    depositUpdatePayment.type = PaymentType.Deposit;
    depositUpdatePayment.amount = amount; 
    depositUpdatePayment.desc = "Deposit Update";
    this.paymentResult.emit(depositUpdatePayment);

    setTimeout(() => {
      this.openPaymentModal(paymentType);
    }, 100);
  }

  private openPaymentModal(type: PaymentType) {
    let modalRef: NgbModalRef;
    let name;
    switch(type) {
      case PaymentType.AfterPay:
        name = ("After-Pay");
        modalRef = this.modalService.open(AfterPayModalComponent, { size: 'xl', centered: true });
        break;
      case PaymentType.CustomerPoints:
        name = ("Customer-Points");
        modalRef = this.modalService.open(CustomerPointsModalComponent, { size: 'xl', centered: true });
        break;
      case PaymentType.Cash:
        name = ("Cash");
        modalRef = this.modalService.open(CashModalComponent, { size: 'xl', centered: true });
        break;
      case PaymentType.Cheque:
        name = ("Cheque");
        modalRef = this.modalService.open(ChequeModalComponent, { size: 'xl', centered: true });
        break;
      case PaymentType.CreditNote:
        name = ("Credit Note");

        if (this.transType == 2) {
          modalRef = this.modalService.open(CreditNoteIssuanceModalComponent, { size: 'xl', centered: true });
        } else {
          modalRef = this.modalService.open(GiftCardModalComponent, { size: 'xl', centered: true });
          modalRef.componentInstance.isCreditNote = true;
        }
        
        break;
      case PaymentType.CustomerAccount:
        name = ("Customer Account");
        modalRef = this.modalService.open(CustomerAccountModalComponent, { size: 'xl', centered: true });
        modalRef.componentInstance.transType = this.transType;
        break;
      case PaymentType.Eftpos:
        name = ("Eftpos");
        this.store.select(SysConfigSelectors.selectSysConfig).pipe(
          first(),
          map(sysConfig => sysConfig ? sysConfig.integratedEFTProvider : null)
        ).subscribe(integrated => {
          if (integrated != 'None') {
            modalRef = this.modalService.open(EftposModalComponent, { size: 'xl', centered: true });
            modalRef.componentInstance.intEft = true;
          }
          else {
            modalRef = this.modalService.open(EftposModalComponent, { size: 'xl', centered: true });
            modalRef.componentInstance.intEft = false;
          }
        });
        break;
      case PaymentType.GiftCard:
        name = ("Gift Card");
        modalRef = this.modalService.open(GiftCardModalComponent, { size: 'xl', centered: true });
        break;
      case PaymentType.ZipPay:
        name = ("Zip Pay");
        modalRef = this.modalService.open(ZipPayModalComponent, { size: 'xl', centered: true });
        break;
    }
    modalRef.componentInstance.name = name;
    modalRef.componentInstance.type = type;
    modalRef.componentInstance.amountDue = this.amountDue;
    modalRef.result.then((result: Payment) => {
      if (result) {
        this.paymentResult.emit(result);
      }
    });
  }

	retrieveDepositAmount(): number {
		const deposit = this.currentDeposit;
		if (deposit > 0) {
		  console.log('Retrieved deposit value:', deposit);
		  // Clear the deposit after retrieving
		  //this.store.dispatch(orderActions.clearDeposit());
		}
		return deposit;
	  }

	  processIfDepositExists(deposit: number) {
      if (deposit && deposit > 0) {
        console.log(`Processing transaction with existing deposit: ${deposit}`);
      		  
		  // Create a Payment object
        const payment = new Payment();
      payment.type = PaymentType.Deposit;
      payment.amount = deposit;
      payment.desc = "Pre-paid Order Deposit"; // Distinct description
      		  
		  // Emit the payment first
      this.paymentResult.emit(payment);
		  
		  // Then clear the deposit from the store
		} else {
		  console.log('No deposit found or invalid deposit value:', deposit);
    }
  }
}