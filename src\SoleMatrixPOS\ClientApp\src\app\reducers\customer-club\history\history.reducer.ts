import { HistoryDto, HistorySearchRequestDto, NoteSearchRequestDto,DailyDto} from 'src/app/pos-server.generated';
import { createReducer, on, Action } from '@ngrx/store';
import * as historyActions from './history.actions';

export class HistorySearchState {
    isLoading: boolean;
    history: HistoryDto[];
    options: HistorySearchRequestDto;
    result: boolean;
    isLoadingMore: boolean;
    loadMore?: boolean;
}

export const initialState: HistorySearchState = {
    isLoading: false,
    history: [],
    options: {
        searchString: '',
        clientNumber: ''
    },
    result: true,
    isLoadingMore: false,
    loadMore: false
}

export const historySearchReducer = createReducer(
    initialState,
    on(historyActions.init, (state) =>(initialState)),
    on(historyActions.search, (state, action) => ({
        ...(action.loadMore ? state : initialState),
        history: action.loadMore ? state.history : [],
        isLoading: true, 
        options: action.searchParams,
        loadMore: action.loadMore
    })),
    on(historyActions.searchClient, (state, action) => ({
        ...(action.loadMore ? state : initialState),
        history: action.loadMore ? state.history : [],
        isLoading: true, 
        options: action.searchParams,
        loadMore: action.loadMore
    })),
    on(historyActions.searchResponse, (state, action) => ({
        ...state, 
        isLoading: false, 
        history: action.loadMore ? [...state.history, ...action.payload] : action.payload,
        loadMore: action.loadMore
    })),
);

export function reducer(state: HistorySearchState | undefined, action: Action) {
    return historySearchReducer(state, action)
}
