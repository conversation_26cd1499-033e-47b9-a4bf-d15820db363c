import { AppState } from '../index';
import { createSelector } from '@ngrx/store';

export const select = (state: AppState) => state.transaction;

export const completed = createSelector(select, (s) => s.transactionCompleted);
export const transNo = createSelector(select, (s) => s.transNo);
export const transPayData = createSelector(select, (s) => s.transPayData);
export const creditNote = createSelector(select, (s) => s.transCreditNote);