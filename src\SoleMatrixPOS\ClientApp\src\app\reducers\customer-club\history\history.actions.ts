import { createAction, props } from '@ngrx/store';
import { HistorySearchRequestDto, HistoryDto, DailyDto } from '../../../pos-server.generated';

export const search = createAction('[HistorySearch] Search', props<{searchParams: HistorySearchRequestDto, loadMore?: boolean}>());
export const searchClient = createAction('[HistorySearch] SearchClient', props<{searchParams: HistorySearchRequestDto, loadMore?: boolean}>());
export const searchResponse = createAction('[HistorySearch] Response', props<{payload: HistoryDto[], loadMore?: boolean }>());
export const init = createAction('[HistorySearch] init')