using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR; 
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Client;
using SoleMatrixPOS.Dal.Interface.Models.SMate;
using SoleMatrixPOS.Application.AccountPayment.Queries;
using SoleMatrixPOS.Application.AccountPayment;
namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class AccountPaymentController : ControllerBase
	{
		private readonly IMediator _mediator;

		public AccountPaymentController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[HttpGet]
		public async Task<IEnumerable<CustomerMateDto>> GetAllCustomers()
		{
			return await _mediator.Send(new GetAllCustomersQuery());
		}

		[Route("/updateCustomerBalance")]
		[HttpPut]
		public async Task<IActionResult> UpdateCustomerBalance([FromBody] CustomerMatePaymentDto dto)
		{
			// ONLY USE THIS FOR NEW PAY OFF ACCOUNT DEBT
			// 1. INSERT CTRANS  4, 'U', 4. use new saleNo for readldocNo
			// 2. UPDATE CUSTOMER BALANCE (as ctrans trigger is disabled/not used now). previously a trigger was used to update the customer balance when inserting ctrans

			await _mediator.Send(new UpdateCustomerBalanceCommand(dto));
			return Ok();
		}
		[Route("/insertCtrans")]
		[HttpPut]
		public async Task<IActionResult> InsertCtrans([FromBody] CtransDto[] dtoArr)
		{
			// ONLY USE THIS FOR SALES/REFUNDS (PAY SALE WITH ACCOUNT, REFUND SALE WITH ACCOUNT AS ONE OF THE PAYMENT)
			// 1. INSERT CTRANS  1, 'O', 1, OR 2, 'U', 2.
			// 2. UPDATE CUSTOMER BALANCE (as ctrans trigger is disabled/not used now). previously a trigger was used to update the customer balance when inserting ctrans
			foreach (CtransDto  dto in dtoArr)
			{
				// Insert ctrans dto and update the balance of the customer code
				await _mediator.Send(new InsertCtransAndUpdateCommand(dto));
			}
			return Ok();
		}

		[Route("search")]
		[HttpPost]
		public async Task<IEnumerable<CustomerMateDto>> SearchCustomers([FromBody] AccountPaymentSearchRequestDto searchParams)
		{	
			return await _mediator.Send(new SearchCustomersQuery(searchParams));
		}
	}
}
