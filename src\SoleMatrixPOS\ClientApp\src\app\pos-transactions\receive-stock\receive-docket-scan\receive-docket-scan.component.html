<div class="container-fluid">

    <h5>SCAN TRANSACTION DOCKET</h5>

    <div class="scan-form">
        <label for="inputBarcode">Enter transaction barcode</label>
        <input name="inputBarcode" type="text" [formControl]="enterDocketBarcode" #input
            (keydown.enter)="submitDocketBarcode()" autofocus>
        <button (click)="submitDocketBarcode()">Submit</button>
        <br><br>
        <div *ngIf="!docketBarcodeValid && docketValidationAttempted">
            <p class="barcodeInvalidMsg">{{validationStatusMsg}}</p>
        </div>
    </div>
</div>