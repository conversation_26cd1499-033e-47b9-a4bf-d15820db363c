using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Enums;
using SoleMatrixPOS.Application.OrderItem;
using SoleMatrixPOS.Application.OrderItem.Queries;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class OrderSearchController : ControllerBase
	{
		private readonly IMediator _mediator;

		public OrderSearchController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[HttpPost]
		public async Task<IEnumerable<CreateOrderDto>> Get([FromBody] OrderSearchRequestDto orderSearchRequestDto)
		{
			return await _mediator.Send(new SearchOrdersQuery(orderSearchRequestDto));
		}
	}
}
