
import { createSelector } from '@ngrx/store';
import { AppState } from '..';

export const select = (state: AppState) => state.stockEnquiry;

//stock from StockEnquiryResultDto
export const stockEnquiry = createSelector(select, (s) => s.itemStocks);

export const stockEnquiryHeader = createSelector(select, (s) => s.itemStocks.stockEnquiryHeader);
export const stockEnquirySize = createSelector(select, (s) => s.itemStocks.stockEnquirySizes);
export const stockEnquiryLocation = createSelector(select, (s) => s.itemStocks.stockEnquiryLocation);
export const stockEnquiryQty = createSelector(select, (s) => s.itemStocks.stockEnquiryQty);
export const stockEnquiryTotal = createSelector(select, (s) => s.itemStocks.stockEnquiryTotal);
export const stockEnquiryTotalBySize = createSelector(select, (s) => s.itemStocks.stockEnquiryTotalBySize);
export const stockEnquiryColor = createSelector(select, (s) => s.itemStocks.stockEnquiryGetColor);


