import { Injectable } from '@angular/core';

@Injectable({
	providedIn: 'root'
})
export class TyroIClientService {
	private client: any;

	constructor() {
		var apiKey = "<API KEY>"; //TODO reeeeeee
		var posProductInfo = {
			posProductVendor: "Gracely Co",
			posProductName: "StyleMatrix",
			posProductVersion: "1.0.12",
		};
		this.client = new TYRO.IClient(apiKey, posProductInfo);
	}

	getClient(): any {
		return this.client;
	}
}
