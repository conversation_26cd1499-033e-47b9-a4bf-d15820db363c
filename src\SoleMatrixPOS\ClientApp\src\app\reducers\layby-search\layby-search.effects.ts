import {Injectable} from '@angular/core';
import {Actions, createEffect, ofType} from '@ngrx/effects';
import * as laybySearchActions from './layby-search.actions';
import {catchError, map, mergeMap, withLatestFrom} from 'rxjs/operators';
import {EMPTY} from 'rxjs';
import {LaybySearchClient} from '../../pos-server.generated';
import {Store} from '@ngrx/store';
import {AppState} from '../index';
import * as laybySearchSelectors from './layby-search.selectors';

@Injectable()
export class LaybySearchEffects {
	constructor(
		private store: Store<AppState>,
		private actions$: Actions,
		private laybySearchClient: LaybySearchClient) {
	}

	matchedLaybys$ = createEffect(() => this.actions$
		.pipe(
			ofType(laybySearchActions.search),
			withLatestFrom(
				this.store.select(laybySearchSelectors.searchQuery)
			),
			mergeMap(([action, options]) => this.laybySearchClient.searchLayby({...options})
				.pipe(
					map(foundItems => laybySearchActions.searchResponse({payload: foundItems}),
						catchError(() => EMPTY)
					)
				))));

	moreMatchedLaybys$ = createEffect(() => this.actions$
		.pipe(
			ofType(laybySearchActions.searchMore),
			withLatestFrom(
				this.store.select(laybySearchSelectors.searchedItemsCount),
				this.store.select(laybySearchSelectors.searchQuery)
			),
			mergeMap(([action, itemsCount, searchOptions]) => this.laybySearchClient.searchLayby({...searchOptions, skip: itemsCount})
				.pipe(
					map(foundItems => laybySearchActions.searchMoreResponse({payload: foundItems}),
						catchError(() => EMPTY)
					)
				))));

		getLaybyLines$ = createEffect(() =>
			this.actions$.pipe(
			ofType(laybySearchActions.getLaybyLines),
			mergeMap(action =>
				this.laybySearchClient.getLaybyLines(action.laybyCode).pipe(
				map(lines => laybySearchActions.getLaybyLinesResponse({ laybyLines: lines })),
				catchError(() => EMPTY)
				)
			)
			)
		);
  
}

