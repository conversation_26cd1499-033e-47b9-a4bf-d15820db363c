$stock-color-square: #c0c0c0;
$sale-color-square: #86cfda;
$sale-qty-color-square: #ff0000;

.stock-border-square {
  border: 1px solid $stock-color-square;
  text-align: center;
}

.sale-qty-color-square {
  color: #ff0000;
  font-weight: bold;
}

.sale-border-square {
  border: 1px solid $sale-color-square;
  text-align: center;
}

.title-border-square {
  text-align: center;
  border: 1px solid $sale-color-square;
}

.border-square {
  border: 1px solid #c0c0c0;
}

// This stops empty rows shrinking
td {
  height: 3em;
}

/////////////////
// Drag n Drop //
/////////////////

.example-box {
  border: 1px solid #ccc;
  text-align: center;
  cursor: pointer;
}

// This creates an empty space in the
// dragging destination
.cdk-drag-placeholder {
  border: none;
  display: none;
  text-align: none;
}

// This is what the cursor is displaying
.example-box.cdk-drag-preview {
  border: 2px solid #8dda86;
  text-align: center;
  border-radius: 10px;
  box-shadow: 0 0 10px #8dda86;
}

// This displays the current qty
.example-box.cdk-drop-list-dragging {
  border: 2px solid #dabf86;
  display: contents; // This one is okay
  text-align: center;
  border-radius: 10px;
  box-shadow: 0 0 10px #dabf86;
}

/////////////////
// Highlight   //
/////////////////

// The colour that will display when
// highlighting is on and this is a
// 'from' item
.highlight-from {
  border: 2px solid #8dda86;
  text-align: center;
  border-radius: 10px;
  box-shadow: 0 0 10px #8dda86;
}

// The colour that will display when
// highlighting is on and this is a
// 'to' item
.highlight-to {
  border: 2px solid #dabf86;
  text-align: center;
  border-radius: 10px;
  box-shadow: 0 0 10px #dabf86;
}

//////////////////////////////
// SweetAlert Popup Modal ///
/////////////////////////////

.swal2-confirm .btn .btn-info {
  margin-left: 10px !important;
}
