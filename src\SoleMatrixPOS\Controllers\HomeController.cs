using MediatR; 
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.History.Queries;
using SoleMatrixPOS.Application.History;
using SoleMatrixPOS.Filters;
using System.Collections.Generic;
using System.Threading.Tasks;
using SoleMatrixPOS.Application.Store.Queries;
using Microsoft.AspNetCore.Http;
using System;
using Microsoft.Extensions.Logging;
using SoleMatrixPOS.Application.Infrastructure;

namespace SoleMatrixPOS.Controllers
{

	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class HomeController : ControllerBase
	{
		private readonly IMediator _mediator;
		private readonly ILogger<HomeController> _logger;
		private readonly StaffCodeContext _staffCodeContext;
		public HomeController(IMediator mediator, ILogger<HomeController> logger, StaffCodeContext staffCodeContext)
		{
			_mediator = mediator;
			_logger = logger;
			_staffCodeContext = staffCodeContext;
		}

		[Route("GetStoreInfo")]
		[HttpPost]
		public async Task<StoreDetailsDto> GetStoreInfo()
		{
			return await _mediator.Send(new GetStoreDetails(_staffCodeContext.StoreDetailsDto.StoreId, _staffCodeContext.PINPadNo)); 
		}

		[Route("Stores")]
		[HttpPost]
		public async Task<List<StoreDetailsDto>> GetAllStores()
		{
			return await _mediator.Send(new GetAllStores());
		}
	}
}
