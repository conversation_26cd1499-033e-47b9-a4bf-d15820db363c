import { Component, OnInit, EventEmitter, Output, Input, OnDestroy } from '@angular/core';
import { takeUntil } from 'rxjs/operators';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import { Observable, Subject } from 'rxjs';
import { CartItemDto, StockItemDto } from 'src/app/pos-server.generated';
import Swal from 'sweetalert2';
import { forkJoin, of } from 'rxjs';
import { TransactionClient } from 'src/app/pos-server.generated';
import * as cartActions from 'src/app/reducers/sales/cart/cart.actions';
import * as quoteSearchSelectors from 'src/app/reducers/quote-item-search/quote-item-search.selectors';
import * as quoteSearchActions from 'src/app/reducers/quote-item-search/quote-item-search.actions';
import { takeWhile, debounceTime, distinctUntilChanged, map, filter, take, tap, switchMap, catchError } from 'rxjs/operators';
import * as customerClubSearchSelectors from 'src/app/reducers/customer-club/club-search/customer-club.selectors';
import * as customerClubSearchActions from 'src/app/reducers/customer-club/club-search/customer-club.actions';
import * as cartSelectors from 'src/app/reducers/sales/cart/cart.selectors';
import * as transactionActions from 'src/app/reducers/transaction/transaction.actions';
import * as quoteActions from 'src/app/reducers/quote-item/quote.actions';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CartItem } from 'src/app/reducers/sales/cart/cart.reducer';

import {
  QuoteHeaderDTO,
  QuoteLineDTO,
  QuoteSearchRequestDto,
  OrderSearchKeywordColumn,
  OrderSearchOrderByColumn,
  StockSearchOrderByDirectionEnumDto,
  CustomerClubDto,
  CreateQuoteDto,
  ClientSearchKeywordColumnDto,
  ClientSearchRequestDto
} from 'src/app/pos-server.generated';

@Component({
  selector: 'pos-customer-quotes-search',
  templateUrl: './customer-quotes-search.component.html',
  styleUrls: ['./customer-quotes-search.component.scss']
})
export class CustomerQuotesSearchComponent implements OnInit, OnDestroy {
  term = '';
  field = 'FirstName';

  quotes$: Observable<CreateQuoteDto[]> = this.store.select(quoteSearchSelectors.searchedQuotes).pipe(
    map((quotes) => quotes.filter((quote) => !quote.quoteHeader.quoteCancelled))
  );
  searchOptions$: Observable<QuoteSearchRequestDto>;
  searchLoading$: Observable<boolean>;
  totalAmount: number = 0;
  collapsed: boolean;
  selectedClubMember: CustomerClubDto | null = null;

  public loading: boolean = true;

  private searchTerm$ = new Subject<string>();
  private destroy$ = new Subject<void>();
  private alive = true;
  totalValueOfItems: number = 0;
  member: CustomerClubDto;

  @Input() quotes: CreateQuoteDto[];
  @Input() options: QuoteSearchRequestDto;
  @Input() selectedQuote: CreateQuoteDto;

  @Output() onSelectedQuote = new EventEmitter<CreateQuoteDto>();
  @Output() onEditedQuote = new EventEmitter<CreateQuoteDto>();
  @Output() searchChanged = new EventEmitter<QuoteSearchRequestDto>();

  constructor(
    private store: Store<AppState>,
    private router: Router,
    public activeModal: NgbActiveModal,
    private modalService: NgbModal,
    private transactionClient: TransactionClient
  ) {}

  ngOnInit() {
    const savedField = localStorage.getItem('customerSpecialsSearchField');
    if (savedField) {
      if (savedField === 'Code') {
        this.field = 'QuoteCode';
      } else if (savedField === 'OrderStaff') {
        this.field = 'QuoteStaff';
      }
      else {
        this.field = savedField;
      }
    }
    this.quotes$.pipe(
      takeUntil(this.destroy$)
    ).subscribe((quotes) => {
      console.log('Quotes retrieved:', quotes);
    });
    this.searchOptions$ = this.store.select(quoteSearchSelectors.searchOptions);
    this.searchLoading$ = this.store.select(quoteSearchSelectors.searchLoading);
    //TODO NEED TO DO PRINTING FOR QUOTES AND ORDERS NEED TO MAKE NEW ONES FOR THAT
    this.searchTerm$
      .pipe(
        takeWhile(() => this.alive),
        debounceTime(200),
        distinctUntilChanged(),
        map(() => {
          const options: QuoteSearchRequestDto = {
            searchString: this.term.toUpperCase(),
            first: 25,
            skip: 0,
            customerClubOrderByDirectionEnumDto: StockSearchOrderByDirectionEnumDto.ASC,
            orderSearchKeywordColumnDto: OrderSearchKeywordColumn[this.field],
            orderSearchOrderByColumnDto: OrderSearchOrderByColumn[this.field],
          };
          this.searchChanged.emit(options);
        })
      )
      .subscribe(() => {
        const options: QuoteSearchRequestDto = {
          searchString: this.term.toUpperCase(),
          first: 25,
          skip: 0,
          customerClubOrderByDirectionEnumDto: StockSearchOrderByDirectionEnumDto.ASC,
          orderSearchKeywordColumnDto: OrderSearchKeywordColumn[this.field],
          orderSearchOrderByColumnDto: OrderSearchOrderByColumn[this.field],
        };
        console.log('Calling doSearch with options:', options);
        this.doSearch(options);
      });

    this.searchLoading$.subscribe((s) => {
      this.loading = s;
    });

    this.search();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.alive = false;
  }

  search() {
    let fieldToSave = this.field;
    if (this.field === 'QuoteCode') {
      fieldToSave = 'Code';
    }
    localStorage.setItem('customerSpecialsSearchField', fieldToSave);
    this.searchTerm$.next(this.term);
  }

  goHome() {
    this.router.navigateByUrl('/home');
  }

  doSearch(options: QuoteSearchRequestDto) {
    this.store.dispatch(quoteSearchActions.search({ searchParams: options }));
  }

  selectQuote(quote: CreateQuoteDto) {
    if (quote && quote.quoteLines) {
      console.log('Quote selected:', quote);

      let loadingCancelled = false;
      const swalPromise = Swal.fire({
        title: 'Loading Quote...',
        html: 'Please wait while the quote is processed.',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showCancelButton: true,
        cancelButtonText: 'Cancel Load',
        onOpen: () => {
          Swal.showLoading();
          const cancelButton = Swal.getCancelButton() as HTMLButtonElement;
          if (cancelButton) {
            cancelButton.disabled = false;
          }
        }
      });

      swalPromise.then((result) => {
        if (result.dismiss === Swal.DismissReason.cancel) {
          loadingCancelled = true;
          console.log('Quote loading cancelled by user.');
          this.activeModal.dismiss('User cancelled quote loading');
        }
      });

      this.store.dispatch(quoteSearchActions.selectQuote({ payload: quote }));
      const quoteCode = quote.quoteHeader.quoteCode;
      this.store.dispatch(quoteActions.uploadQuoteCode({ quoteCode }));
      this.onSelectedQuote.emit(quote);

      if (loadingCancelled) return; // Check after initial dispatches and before HTTP

      this.transactionClient.getTransLogByTransNo(quote.quoteLines[0].transNo)
        .pipe(take(1))
        .subscribe({
          next: translogs => {
            if (loadingCancelled) return;

            if (!translogs || translogs.length === 0) {
              console.warn('No translogs found for quote:', quoteCode);
              if (!loadingCancelled) Swal.close();
              Swal.fire('Info', 'No transaction details found for this quote.', 'info');
              this.activeModal.close();
              return;
            }

            const itemProcessingObservables: Observable<any>[] = translogs.map(translog => {
              if (loadingCancelled) return of(null); // Stop processing for this item if cancelled

              let effectiveOriginalPrice: number;
              let finalSellingPrice: number;

              if (translog.nettSelling !== null && translog.nettSelling !== undefined && translog.nettSelling > 0) {
                effectiveOriginalPrice = translog.nettSelling;
                finalSellingPrice = (translog.sellingPrice !== null && translog.sellingPrice !== undefined) ? translog.sellingPrice : effectiveOriginalPrice;
              } else {
                effectiveOriginalPrice = (translog.sellingPrice !== null && translog.sellingPrice !== undefined) ? translog.sellingPrice : 0;
                finalSellingPrice = effectiveOriginalPrice;
              }

              const hasActualDiscount = effectiveOriginalPrice > finalSellingPrice && translog.gst && translog.gst > 0;
              const discountAmount = hasActualDiscount ? effectiveOriginalPrice - finalSellingPrice : 0;
              const discountPercent = hasActualDiscount && effectiveOriginalPrice > 0 ? (discountAmount / effectiveOriginalPrice) * 100 : 0;

              const stockItemForAdd: StockItemDto = {
                colourName: translog.colourCode ? translog.colourCode.trim() : '',
                styleCode: translog.styleCode ? translog.styleCode.trim() : '',
                styleDescription: translog.styleCode ? translog.styleCode.trim() : '',
                colourCode: translog.colourCode ? translog.colourCode.trim() : '',
                size: translog.sizeCode ? translog.sizeCode.trim() : '',
                price: effectiveOriginalPrice
              };

              this.store.dispatch(cartActions.addItem({ stockItem: stockItemForAdd }));

              return this.store.select(cartSelectors.cart).pipe(
                filter(cartItems => {
                  if (loadingCancelled) return false;
                  const foundItem = cartItems.find(ci =>
                    ci.stockItem.styleCode === stockItemForAdd.styleCode &&
                    ci.stockItem.colourCode === stockItemForAdd.colourCode &&
                    ci.stockItem.size === stockItemForAdd.size
                  );
                  return !!foundItem &&
                         foundItem.originalPrice === effectiveOriginalPrice &&
                         foundItem.bestValue === effectiveOriginalPrice;
                }),
                take(1),
                tap((cartItemsAfterAddResponse) => {
                  if (loadingCancelled) return;
                  if (hasActualDiscount) {
                    const confirmedCartItem = cartItemsAfterAddResponse.find(ci =>
                      ci.stockItem.styleCode === stockItemForAdd.styleCode &&
                      ci.stockItem.colourCode === stockItemForAdd.colourCode &&
                      ci.stockItem.size === stockItemForAdd.size
                    );
                    if (confirmedCartItem) {
                      this.store.dispatch(cartActions.updateItemPrice({
                        stockItem: confirmedCartItem.stockItem,
                        newPrice: finalSellingPrice,
                        discountCode: Math.floor(translog.gst).toString(),
                        discountPercent: Math.abs(discountPercent),
                        reason: 'Historical Discount'
                      }));
                    }
                  }
                }),
                catchError(err => {
                    console.error('Error processing item in cart selector pipe:', err);
                    // This error is within an item's processing. Decide if it should stop everything.
                    // For now, let it continue to forkJoin, which might then complete with fewer items.
                    return of(null); 
                })
              );
            });

            forkJoin(itemProcessingObservables.filter(obs => obs !== null && obs !== undefined))
            .pipe(catchError(err => {
                if (!loadingCancelled) Swal.close();
                console.error('Error in forkJoin item processing:', err);
                Swal.fire('Error', 'An error occurred while processing quote items.', 'error');
                this.activeModal.close();
                return of(null); // Gracefully handle forkJoin error
            }))
            .subscribe({
              next: results => {
                if (loadingCancelled || !results) return; // if forkJoin error occurred and returned of(null)

                const clientCode = quote.quoteHeader.clientCode;
                if (clientCode) {
                  const searchRequest: ClientSearchRequestDto = {
                    searchString: clientCode.toUpperCase(),
                    first: 1,
                    skip: 0,
                    customerClubSearchKeywordColumnDto: ClientSearchKeywordColumnDto.Id,
                  };
                  this.store.dispatch(customerClubSearchActions.search({ searchParams: searchRequest }));
                  this.store.select(customerClubSearchSelectors.searchedCustomerClubMembers)
                    .pipe(
                      filter(response => !!response && response.length > 0),
                      take(1),
                      catchError(err => {
                        if (!loadingCancelled) Swal.close();
                        console.error('Error during customer search select:', err);
                        Swal.fire('Error', 'An error occurred while selecting the customer.', 'error');
                        this.activeModal.close();
                        return of(null);
                      })
                    )
                    .subscribe({
                      next: response => {
                        if (loadingCancelled || !response) return;
                        if (response.length > 0) {
                           const customer: CustomerClubDto = response[0];
                           this.store.dispatch(customerClubSearchActions.selectCustomerClubMember({ payload: customer }));
                        }
                        if (!loadingCancelled) Swal.close();
                        this.activeModal.close();
                        this.router.navigate(['/sales']);
                      },
                      error: (clientSelectError) => { // Should be caught by catchError above
                        if (loadingCancelled) return;
                        console.error("Error selecting customer (final subscribe block):", clientSelectError);
                        if (!loadingCancelled) Swal.close();
                        Swal.fire('Error', 'Failed to finalize customer selection.', 'error');
                        this.activeModal.close();
                      }
                    });
                } else {
                  if (loadingCancelled) return;
                  Swal.close();
                  this.activeModal.close();
                  this.router.navigate(['/sales']);
                }
              } // forkJoin next
            }); // forkJoin subscribe
          }, // transactionClient next
          error: err => {
            if (loadingCancelled) return;
            console.error('Error fetching translogs:', err);
            Swal.close();
            Swal.fire('Error', 'Could not load quote details. Please try again.', 'error');
            this.activeModal.close();
          } // transactionClient error
        }); // transactionClient subscribe
    } else {
      console.error('Attempted to select an invalid or empty quote:', quote);
    }
  }

  cancelQuote(quote: CreateQuoteDto) {
    this.store.dispatch(quoteSearchActions.selectQuote({ payload: quote }));
    this.store.dispatch(quoteActions.cancelQuote({ quoteNumber: quote.quoteHeader.quoteCode }));
          Swal.fire({
            title: 'Quote Cancelled',
            text: 'The Quote has been successfully cancelled.',
            confirmButtonText: 'OK'
          }).then(() => {
            this.activeModal.close();
            this.router.navigateByUrl('/home');
          });
  }
}