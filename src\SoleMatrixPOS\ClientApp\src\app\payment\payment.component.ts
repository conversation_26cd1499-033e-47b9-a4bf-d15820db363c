import { Component, OnInit } from '@angular/core';
import { StockSearchModalComponent } from '../stock-search/stock-search-modal/stock-search-modal.component';
import { StockSaleTableModalComponent } from '../stock-sale-table/stock-sale-table-modal/stock-sale-table-modal.component';
import { CustomerClubModalComponent } from '../customer-club/customer-club-modal/customer-club-modal.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { CustomerClubDto, StockItemDto } from '../pos-server.generated';
import { Store } from '@ngrx/store';
import { AppState } from '../reducers';

import * as customerClubSearchSelectors from '../reducers/customer-club/club-search/customer-club.selectors';
import * as cartActions from '../reducers/sales/cart/cart.actions';
import * as paymentActions from '../reducers/sales/payment/payment.actions'
import * as cartSelectors from '../reducers/sales/cart/cart.selectors';
import { Stock } from '../stock-sale-table/classes/stock';
import { CartItem } from '../reducers/sales/cart/cart.reducer';
import { AfterPayModalComponent } from './after-pay-modal/after-pay-modal.component';
import { CashModalComponent } from './cash-modal/cash-modal.component';
import { ChequeModalComponent } from './cheque-modal/cheque-modal.component';
import { CustomerAccountModalComponent } from './customer-account-modal/customer-account-modal.component';
import { EftposModalComponent } from './eftpos-modal/eftpos-modal.component';
import { GiftCardModalComponent } from './gift-card-modal/gift-card-modal.component';
import { ZipPayModalComponent } from './zip-pay-modal/zip-pay-modal.component';

import { PaymentService, Payment, Transaction, PaymentType } from './payment.service';
import { ProcessPaymentModalComponent } from './process-payment-modal/process-payment-modal.component';
import { PaymentModalButton } from './payment-modal-button/payment-modal-button.component';
import Swal from 'sweetalert2';

@Component({
  selector: 'pos-payment',
  templateUrl: './payment.component.html',
  styleUrls: ['./payment.component.scss']
})
export class PaymentComponent implements OnInit {

	collapsed = true;

	private selectedCustomerClubMember$: Observable<CustomerClubDto>;
	public selectedCustomerClubMember: CustomerClubDto = null;

	public cart$: Observable<CartItem[]>;
	public total$: Observable<Number>;

	modalButtons: PaymentModalButton[] = [
		new PaymentModalButton("Cash", "fa-money-bill-wave", PaymentType.Cash),
		new PaymentModalButton("Eftpos", "fa-credit-card", PaymentType.Eftpos),
		new PaymentModalButton("Customer Account", 'fa-user-crown', PaymentType.CustomerAccount),
		//new PaymentModalButton("ZipPay", "../../../assets/zip-logo.svg", PaymentType.ZipPay, true),
	];

	public transaction: Transaction;
	public transaction$: Observable<Transaction>;

	constructor(private modalService: NgbModal, private router: Router, private store: Store<AppState>) { }

	ngOnInit() {
		console.log(this.modalButtons);
		this.selectedCustomerClubMember$ = this.store.select(customerClubSearchSelectors.selectedCustomerClubMember);
		this.selectedCustomerClubMember$.subscribe((s) => { this.selectedCustomerClubMember = s });
		
		this.cart$ = this.store.select(cartSelectors.cart);
		this.total$ = this.store.select(cartSelectors.total);
		this.total$.subscribe((t)=>{
			this.transaction = new Transaction(t as number);
			console.log("new transaction", this.transaction);
		});
		
		this.transaction$ = of(this.transaction);
	}

	handlePayment(payment: Payment){
		console.log("Attempting to add payment: ", payment);
		this.transaction.addPayment(payment);
	}

	goHome() {
		this.router.navigateByUrl('/home');
	}

	launchCustomerClubModal() {
		const modalRef = this.modalService.open(CustomerClubModalComponent, { size: 'xl', centered: true });
		modalRef.componentInstance.name = 'CustomerClubModal';
		modalRef.result.then((result) => {
			if (result) {
				console.log('result from modal:', result);
			}
		});
	}

	processPayment() {
		let modalRef = this.modalService.open(ProcessPaymentModalComponent, { size: 'xl', centered: true });
		modalRef.componentInstance.name = "Process Payment";
		modalRef.componentInstance.transaction = this.transaction;
		modalRef.result.then((result) => {
			if (result) {
				// process payment or not?
				console.log('result from modal:', result);
			}
		});
	}

	clickCount: number = 0;

	removePaymentOnDoubleClick(payment: Payment) {
		console.log("Received..!");
			this.transaction.removePayment(payment);
	}
}


