import { Injectable } from '@angular/core';
import { Actions, ofType, createEffect } from '@ngrx/effects';
import { map, mergeMap,catchError } from 'rxjs/operators';
import {EMPTY} from 'rxjs'
import { OpenTillClient } from 'src/app/pos-server.generated';
import * as openTillActions from './open-till.actions'
@Injectable()
export class OpenTillEffects {
	constructor(
		private actions$: Actions,
		private client: OpenTillClient
	) {}

	submitTransaction$ = createEffect(() => this.actions$.pipe(
		ofType(openTillActions.submitTransaction),
		mergeMap(
			(action) => this.client.addTransaction(action.payload)
		.pipe(
				map(
					(response) => {
                    	console.log(response)
						return openTillActions.transactionConfirmation();
					},catchError(() => EMPTY)
				)
			)
		)
	));
	submitPettyCash$ = createEffect(() => this.actions$.pipe(
		ofType(openTillActions.submitPettyCash),
		mergeMap(
			(action) => this.client.addPettyCashTransaction(action.payload)
		.pipe(
				map(
					(response) => {
                        console.log(response.data)
						return openTillActions.transactionConfirmation();
					},catchError(() => EMPTY)
				)
			)
		)
	));

}
	