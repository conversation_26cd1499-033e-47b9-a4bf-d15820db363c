import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal, ModalDismissReasons } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { ConfirmDialogService } from '../confirm-modal/confirm-dialog.service';
import * as transSelectors from 'src/app/reducers/transaction/transaction.selectors';
import * as transActions from 'src/app/reducers/transaction/transaction.actions';
import { CreateErrorModal } from '../error-modal/error-modal.component';
import { LogSTAuditDto, LogTranslogDto, PrintBarcodeDto, PrintInvalidDto, ReadDatFileDto, StockItemDto, StockSearchClient, TransactRequestDto } from '../pos-server.generated';
import { AppState } from '../reducers';
import * as itemCartAction from '../reducers/stocketake-entry/itemCart/itemCart.actions'
import { StockCartItem, stockCartItemToSTAudit } from '../reducers/stocketake-entry/itemCart/itemCart.reducers';
import * as itemCartSelector from '../reducers/stocketake-entry/itemCart/itemCart.selectors'
import { UrlHistoryService } from '../url-history.service';
import * as transactAction from '../reducers/stocketake-entry/write-transaction/write-transact.actions';
import * as transSelector from '../reducers/stocketake-entry/write-transaction/write-transact.selectors';
import * as readDatAction from "../reducers/stocketake-entry/read-dat-file/read-dat-file.actions";
import * as readDatSelector from "../reducers/stocketake-entry/read-dat-file/read-dat-file.selectors";
import Swal from 'sweetalert2';
import { ReadFileModalComponent } from './read-file-modal/read-file-modal.component';
import { BarcodeCart, CartToPrint } from '../reducers/stocketake-entry/read-dat-file/read-dat-file.reducers';
import * as PrintAction from '../reducers/stocketake-entry/print/print-actions';
import { takeUntil, tap } from 'rxjs/operators';

const STOCKTAKE_ENTRY_TRANSTYPE = 15;


@Component({
  selector: 'pos-stocktake-entry',
  templateUrl: './stocktake-entry.component.html',
  styleUrls: ['./stocktake-entry.component.scss']
})
export class StocktakeEntryComponent implements OnInit {

  cartItem$: Observable<StockCartItem[]>;
  cartItem: StockCartItem[];

  readyToProcess: boolean = false;
  reasons: Map<string, string[]>;
  total: number;
  transNo: number;
  transNo$: Observable<number>;

  datFiles$: Observable<ReadDatFileDto[]>;
  datFiles: ReadDatFileDto[];

  barcodeItem$: Observable<BarcodeCart[]>;
  barcodeItem: BarcodeCart[];

  errorWithBarcode: boolean = false;

  constructor(
    private store: Store<AppState>,
    private router: Router,
    private modalService: NgbModal,
    private urlHistory: UrlHistoryService,
    private confirmDialogService: ConfirmDialogService,
    private stockSearchClient: StockSearchClient,
  ) { }

  ngOnInit() {
    if (this.urlHistory.previousUrl == '/home') this.iniState();

    //display all item in the cart
    this.cartItem$ = this.store.select(itemCartSelector.cartItem);
    this.cartItem$.subscribe(value => this.cartItem = value);

    //get the read dat file 
    this.datFiles$ = this.store.select(readDatSelector.readDatFile);
    this.datFiles$.subscribe(value => this.datFiles = value);

    //check if there is item in the item cart
    this.store.select(itemCartSelector.noItems).subscribe(n => this.readyToProcess = (n > 0));

    //get the Unit total 
    this.store.select(itemCartSelector.UnitTotal).subscribe(val => this.total = val);

    //if transaction complete go back to Home and display the message
    this.store.select(transSelector.transCompleted).subscribe(value => {
      if (value) this.TransactionCompleted();
    });

    this.barcodeItem$ = this.store.select(readDatSelector.getvalidateBarcode);
    this.barcodeItem$.subscribe(value => this.barcodeItem = value);

    this.store.dispatch(transActions.getTransactionNo());

    this.store.select(transSelectors.transNo)
      .pipe(
        tap(transNo => {
          this.transNo = transNo;
          console.log('Transaction number updated:', transNo);
        })
      )
      .subscribe();

    // this.store.select(printSelector.completed).subscribe(value => {
    //   if(value) this.TransactionCompleted();
    // })

    this.store.dispatch(readDatAction.init());



  }

  iniState() {
    this.store.dispatch(itemCartAction.init());
    this.store.dispatch(readDatAction.init());
    this.store.dispatch(PrintAction.init());


  }

  itemLookup(item: StockItemDto) {
    this.store.dispatch(itemCartAction.addStockItem({ stockItem: item }));
  }

  onTableItemDeleteClicked(itemIndex: number) {
    let sItem = this.cartItem[itemIndex].stockItem;
    this.store.dispatch(itemCartAction.removeItem({ stockItem: sItem }));
  }

  attemptNext() {
    // Block processing and show error message
    Swal.fire({
      title: "Error",
      text: "This feature is currently unavailable. Please contact support.",
      type: "error"
    });
    
    // Original code commented out
    /*
    if (this.readyToProcess) {
      this.openConfirmationDialog();
    } else {
      CreateErrorModal(this.modalService, false, "Oops! You need to add at least one item to process!")
    }
    */
  }

  //open dialog box to confirm proceed
  openConfirmationDialog() {
    this.confirmDialogService.confirm('OK to Process?', 'Do you really want to process ?', 'OK to Process', 'Cancel', 'lg')
      .then((confirmed) => {
        console.log('User confirmed:', confirmed)
        if (confirmed) {
          console.log("procceed transaction!");

          let transDto: TransactRequestDto = this.createTransAudit();

          console.log("write transaction is submitting...");

          // Dispatch an action with the request
          this.store.dispatch(transactAction.submitTrans({ payload: transDto }));

        }
      })
      .catch(() => console.log('User dismissed the dialog (e.g., by using ESC, clicking the cross icon, or clicking outside the dialog)'));
  }

  //send back all items to store in STAudit and make one record for Translog
  createTransAudit(): TransactRequestDto {
    let lineNo: number = 1;
    let logAudit: LogSTAuditDto[] = [];
    //get all the list of item for STAudit
    for (let i = 0; i < this.cartItem.length; i++) {
      let itemCart = this.cartItem[i];
      logAudit.push(stockCartItemToSTAudit(itemCart, lineNo));
      lineNo++
    }
    //get only one record for Translog
    const logTranslog: LogTranslogDto = {
      lineNo: lineNo,
      quantity: this.total
    }
    lineNo++
    console.log(this.transNo);

    return {
      logSTAuditDto: logAudit,
      logTranslogDto: logTranslog,
      transType: STOCKTAKE_ENTRY_TRANSTYPE,
      transNo: this.transNo
    } as TransactRequestDto;

  }


  TransactionCompleted() {
    this.store.dispatch(transactAction.init());
    this.router.navigateByUrl("/home");
    Swal.fire({
      title: "Transaction Completed",
      text: `
      Transaction was successfully submitted.
    `
    });
  }

  closeResult = '';

  //Download stocktake dat file 
  onDownloadStocktake() {
    console.log("click download stocktake!")

    // store dispatch and execute to read file //file path or file location should be in C:/dev/solematrix/ . 
    this.store.dispatch(readDatAction.readDatFile({ fileName: "C:/dev/solematrix/symbol.dat" }))

    //create modal small window to display barcode and the quantity from symbol.date file
    this.modalService.open(ReadFileModalComponent, { ariaLabelledBy: 'modal-basic-title' }).result.then(
      (result) => {
        this.closeResult = `Closed with: ${result}`;
        console.log("Click Process!");

        //Process the barcode step 1 get barcode and quantity
        for (let i = 0; i < this.datFiles.length; i++) {
          let barcode = this.datFiles[i].barCode;
          let quantity = parseInt(this.datFiles[i].quantity);

          for (let n = 0; n < quantity; n++) {

            this.submitBarcode(barcode);
            //validate the barcode
              this.store.dispatch(readDatAction.validateBarcode({ barcode: barcode }));

              //loop the BarcodeCart

              //submit to cart

              //print invalid
    
          }

        }

      },
      (reasons) => {
        this.closeResult = `Dismissed ${this.getDismissReason(reasons)}`
      }
    )


  }



  submitBarcode(barcode: string) {

    console.log("Submitting item: " + barcode);

    let itemBarcode: StockItemDto = this.ItemBarCodeToDto(barcode);

    this.store.dispatch(itemCartAction.submitBarcode({ itembarcode: itemBarcode }));
  }

  ItemBarCodeToDto(barcode: string): StockItemDto {
    return {
      barcode: barcode
    }
  }

  // submitBarcode(value: string) {
  //   console.log("Submitting item: " + value);

  //   // Get item with barcode
  //   let query: Observable<StockItemDto> = this.performQuery(value);

  //   // Wait for query to complete
  //   query.subscribe((item: StockItemDto) => {
  //     // If the item is not null
  //     if (item) {
  //       console.log("Barcode scanner found item with barcode: " + value + " == " + item.barcode);

  //       //add to card
  //       this.store.dispatch(itemCartAction.submitBarcode({ itembarcode: item }));
  //     }

  //     // Otherwise, raise error
  //     else {
  //       console.log("Barcode was invalid.");
  //       this.errorWithBarcode = true;

  //       //print invalid item
  //       let itemPrint: PrintInvalidDto = this.printBarcode(item);
  //       //this.store.dispatch(PrintAction.printInvalidBarcode({ payload: itemPrint }));

  //     }


  //   });

  // }

  // private performQuery(query: string): Observable<StockItemDto> {
  //   // Use HTTP client to send request
  //   return this.stockSearchClient.getItemByBarcode(
  //     { barcode: query }
  //   );
  // }

  
  //need to implement the correct request
  printBarcode(item: StockItemDto): PrintInvalidDto {

    let items: PrintBarcodeDto[] = [];

    for (let i = 0; i < this.barcodeItem.length; i++) {

      let prev = this.barcodeItem[i - 1];
      let barcodeCart = this.barcodeItem[i];
      let next = this.barcodeItem[i + 1]

      if (barcodeCart.validatedBarcode.valid == false) {

        items.push({
          invalidBarcode: CartToPrint(barcodeCart),
          previousItem: CartToPrint(prev),
          nextItem: CartToPrint(next),
        })

      }
    }

    return {
      printBarcodes: items
    } as PrintInvalidDto;

  }




  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }
}





export function ItemCartFromDto(dto: StockItemDto): StockCartItem {
  return {
    stockItem: dto,
    quantity: 1,
    value: 0
  } as StockCartItem;
}



