import { Component, OnInit, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { Location } from '@angular/common';
import { Store } from '@ngrx/store';
import { Observable, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, takeWhile, tap, take } from 'rxjs/operators';
import { CustomerClubDto, HistoryDto, HistorySearchFields, HistorySearchRequestDto, SortDirection } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import * as historySelectors from '../../reducers/customer-club/history/history.selector';
import * as historyActions from '../../reducers/customer-club/history/history.actions';
import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { PrintReceiptModalComponent } from 'src/app/history/print-receipt-modal/print-receipt-modal.component';

@Component({
  selector: 'pos-customer-club-history',
  templateUrl: './customer-club-history.component.html',
  styleUrls: ['./customer-club-history.component.scss']
})
export class CustomerClubHistoryComponent implements OnInit, OnDestroy {
  field: string = 'Date'; // Default field set to 'Date'
  public term: string = ''; // Search term
  public selectedDates: Date[] | undefined; // Date range for date search
  public transactionHistory$: Observable<HistoryDto[]>;
  isLoading$: Observable<boolean>;
  private searchTerm$ = new Subject<string>();
  private alive = true;
  private previousSearchTerm: string = ''; // For tracking the previous search term
  member: CustomerClubDto = null;
  private pageSize = 25;
  private skip = 0;
  public allLoaded = false;
  private historyLength = 0;

  @Output() searchChanged = new EventEmitter<HistorySearchRequestDto>();

  constructor(
    private store: Store<AppState>, 
    private modalService: NgbModal,
    private location: Location
  ) { }

  ngOnInit() {
    // Set current date in selectedDates array when page loads
    const currentDate = new Date(); // Set current date as default
    const pastThreeMonthsDate = new Date();
    pastThreeMonthsDate.setMonth(currentDate.getMonth() - 3);
    this.selectedDates = [pastThreeMonthsDate, currentDate];

    this.isLoading$ = this.store.select(historySelectors.isLoading);

    // Subscribe to the selected customer member
    this.store.select(customerClubSearchSelectors.selectedCustomerClubMember)
      .pipe(takeWhile(() => this.alive))
      .subscribe(member => {
        this.member = member;
        console.log('Selected member detected in history:', this.member);
      });

    // Subscribe to transaction history
    this.transactionHistory$ = this.store.select(historySelectors.searchedHistory)
      .pipe(
        distinctUntilChanged(),
        tap(history => {
          if (history.length === 0) {
            // The list was just cleared while a new request is in-flight – keep loading enabled.
            this.allLoaded = false;
          } else if (history.length < this.pageSize) {
            // Fewer than requested records returned => no more data on the server.
            this.allLoaded = true;
          } else if (this.historyLength > 0 && history.length === this.historyLength) {
            // Attempted to load more but nothing was appended.
            this.allLoaded = true;
          } else {
            // There may still be more data available.
            this.allLoaded = false;
          }
          this.historyLength = history.length;
        })
      );

    // Debounce search term
    this.searchTerm$.pipe(
      debounceTime(500),
      distinctUntilChanged(),
      takeWhile(() => this.alive)
    ).subscribe(searchTerm => {
      if (searchTerm !== this.previousSearchTerm) {
        this.previousSearchTerm = searchTerm;
        this.search(); // Perform search
      }
    });

    // Automatically trigger search with current date
    this.search();
  }

  // Search function for both date and term-based searches
  search() {
    this.skip = 0;
    this.allLoaded = false;
    this.historyLength = 0;
    if (this.member) {
      let searchString = '';

      if (this.field === 'Date' && this.selectedDates) {
        // If a single date is selected
        if (!this.selectedDates[1]) {
          searchString = this.formatDate(this.selectedDates[0]);
        }
        // If a date range is selected
        else if (this.selectedDates[1]) {
          const formattedStartDate = this.formatDate(this.selectedDates[0]);
          const formattedEndDate = this.formatDate(this.selectedDates[1]);
          searchString = `${formattedStartDate} - ${formattedEndDate}`;
        }
      } else {
        searchString = this.term;
      }

      // Create search parameters
      const searchParams: HistorySearchRequestDto = {
        searchString: searchString || '',
        first: this.pageSize,
        skip: this.skip,
        clientNumber: this.member.clientCode,
        sortDirection: SortDirection.DESC,
        searchByField: HistorySearchFields[this.field],
        orderByField: HistorySearchFields[this.field],
      };

      this.doSearch(searchParams, false);
    } else {
      console.log('No customer selected, cannot perform search.');
    }
  }

  // Dispatch search action
  doSearch(searchParams: HistorySearchRequestDto, loadMore: boolean) {
    if (this.field === 'Date' && this.selectedDates) {
      if (!this.selectedDates[1]) {
        // Search with a single date
        searchParams.searchString = this.formatDate(this.selectedDates[0]);
      } else if (this.selectedDates[1]) {
        // Search with a date range
        const formattedStartDate = this.formatDate(this.selectedDates[0]);
        const formattedEndDate = this.formatDate(this.selectedDates[1]);
        searchParams.searchString = `${formattedStartDate} - ${formattedEndDate}`;
        searchParams.sortDirection = SortDirection.ASC;
      }
    }

    // Dispatch the search action
    this.store.dispatch(historyActions.searchClient({ searchParams, loadMore }));
  }

  onScroll(event: any) {
    const target = event.target as HTMLElement;
    const scrollPosition = target.scrollTop + target.offsetHeight;
    const scrollHeight = target.scrollHeight;

    const threshold = 50;

    if (scrollPosition >= scrollHeight - threshold) {
      this.loadMore();
    }
  }

  private loadMore() {
    this.isLoading$.pipe(take(1)).subscribe(loading => {
      if (loading || this.allLoaded) {
        return;
      }

      this.skip += this.pageSize;

      let searchString = '';
      if (this.field === 'Date' && this.selectedDates) {
        if (!this.selectedDates[1]) {
          searchString = this.formatDate(this.selectedDates[0]);
        } else if (this.selectedDates[1]) {
          const formattedStartDate = this.formatDate(this.selectedDates[0]);
          const formattedEndDate = this.formatDate(this.selectedDates[1]);
          searchString = `${formattedStartDate} - ${formattedEndDate}`;
        }
      } else {
        searchString = this.term;
      }

      const searchParams: HistorySearchRequestDto = {
        searchString: searchString || '',
        first: this.pageSize,
        skip: this.skip,
        clientNumber: this.member.clientCode,
        sortDirection: SortDirection.DESC,
        searchByField: HistorySearchFields[this.field],
        orderByField: HistorySearchFields[this.field],
      };

      if (this.field === 'Date' && this.selectedDates && this.selectedDates[1]) {
          searchParams.sortDirection = SortDirection.ASC;
      }

      this.doSearch(searchParams, true);
    });
  }

  // Handle search term changes
  onSearchTermChange(newTerm: string) {
    if (newTerm !== this.term) {
      this.term = newTerm;
      this.searchTerm$.next(this.term); // Debounce search
    }
  }

  // Helper function to format the date to 'YYYY-MM-DD'
  private formatDate(date: Date | undefined): string {
    if (!date) {
      return ''; // Handle undefined date
    }
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are zero-based
    const year = date.getFullYear();
    return `${year}-${month}-${day}`;
  }

  // Handle date changes from the p-calendar
  onDateChange(dates: Date[] | undefined) {
    this.selectedDates = dates;
    this.search();
  }

  openReceiptModal(transNo, transType, tillNo, storeId) {
    console.log('Opening receipt modal with:', { transNo, transType, tillNo, storeId });
    const modalRef = this.modalService.open(PrintReceiptModalComponent, { size: 'm', centered: true });
    modalRef.componentInstance.name = 'PrintReceiptModal';
    modalRef.componentInstance.transNo = transNo;
    modalRef.componentInstance.transType = transType;
    modalRef.componentInstance.tillNo = tillNo;
    modalRef.componentInstance.storeId = storeId;
    modalRef.result.then((result) => {
      if (result) {
        console.log('result from modal:', result);
      }
    }).catch((reason) => console.log(reason));
  }

  // Handle calendar keydown event - only process when Enter is pressed
  onCalendarKeyDown(event: KeyboardEvent) {
    // Only process when Enter key is pressed
    if (event.key === 'Enter') {
      const inputValue = (event.target as HTMLInputElement).value;
      
      // Check if input contains a date range (has a hyphen)
      if (inputValue && inputValue.includes('-')) {
        // Parse date range (e.g., "10/05/2023 - 15/05/2023")
        const [startDateStr, endDateStr] = inputValue.split('-').map(d => d.trim());
        
        try {
          // Parse dates based on the format dd/mm/yyyy (matching the dateFormat in the template)
          const startParts = startDateStr.split('/');
          const endParts = endDateStr.split('/');
          
          if (startParts.length === 3 && endParts.length === 3) {
            const startDate = new Date(
              parseInt(startParts[2]), // year
              parseInt(startParts[1]) - 1, // month (0-based)
              parseInt(startParts[0]) // day
            );
            
            const endDate = new Date(
              parseInt(endParts[2]), // year
              parseInt(endParts[1]) - 1, // month (0-based)
              parseInt(endParts[0]) // day
            );
            
            if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
              this.selectedDates = [startDate, endDate];
              this.search();
            }
          }
        } catch (e) {
          console.log('Error parsing date input:', e);
        }
      } else if (inputValue) {
        // Parse single date (e.g., "10/05/2023")
        try {
          const parts = inputValue.split('/');
          if (parts.length === 3) {
            const singleDate = new Date(
              parseInt(parts[2]), // year
              parseInt(parts[1]) - 1, // month (0-based)
              parseInt(parts[0]) // day
            );
            
            if (!isNaN(singleDate.getTime())) {
              this.selectedDates = [singleDate];
              this.search();
            }
          }
        } catch (e) {
          console.log('Error parsing single date input:', e);
        }
      }
    }
  }

  goBack(): void {
    this.location.back();
  }

  ngOnDestroy() {
    this.alive = false; // Stop subscriptions on component destroy
  }
}