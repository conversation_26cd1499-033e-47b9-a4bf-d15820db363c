import { Injectable } from "@angular/core";
import { Actions, ofType, createEffect } from '@ngrx/effects';
import { AccountPaymentClient } from 'src/app/pos-server.generated';
import { mergeMap, map, catchError } from 'rxjs/operators';
import * as accountPaymentActions from './account-payment.actions'
import { EMPTY } from 'rxjs';
import { of } from 'rxjs';

@Injectable()
export class AccountPaymentEffects {
    constructor(
        private actions$: Actions, 
        private accountPaymentClient: AccountPaymentClient
    ){}

    getAllCustomers$ = createEffect(() => this.actions$.pipe(
		ofType(accountPaymentActions.getAllCustomers),
		mergeMap(
			(action) => this.accountPaymentClient.getAllCustomers()
				.pipe(
					map(response => accountPaymentActions.storeAllCustomers({ payload: response })),
					catchError(error => of(accountPaymentActions.searchCustomersFailure({ error })))
				)
		)
	));

	updateCustomerBalance$ = createEffect(() => this.actions$.pipe(
		ofType(accountPaymentActions.updateCustomerBalance),
		mergeMap(
			(action) => this.accountPaymentClient.updateCustomerBalance(action.payload)
		.pipe(
				map(
					(response) => {
                        console.log(response)
						return accountPaymentActions.updateBalanceCompleted()
					},catchError(() => EMPTY)
				)
			)
		)
	));

	// insertCtrans$ = createEffect(() => this.actions$.pipe(
	// 	ofType(accountPaymentActions.insertCtrans),
	// 	mergeMap(
	// 		(action) => this.accountPaymentClient.insertCtrans(action.payload)
	// 	.pipe(
	// 			map(
	// 				(response) => {
    //                     console.log(response)
	// 					return accountPaymentActions.insertCtransConfirmed()
	// 				},catchError(() => EMPTY)
	// 			)
	// 		)
	// 	)
	// ));

	searchCustomers$ = createEffect(() =>
		this.actions$.pipe(
			ofType(accountPaymentActions.searchCustomers),
			mergeMap(action =>
				this.accountPaymentClient.searchCustomers(action.payload).pipe(
					map(customers => accountPaymentActions.searchCustomersSuccess({ customers })),
					catchError(error => of(accountPaymentActions.searchCustomersFailure({ error })))
				)
			)
		)
	);
}