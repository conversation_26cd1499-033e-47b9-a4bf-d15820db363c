// src/app/reducers/order-item-search/order-item-search.actions.ts

import { createAction, props } from '@ngrx/store';
import { CreateOrderDto, CreateQuoteDto } from 'src/app/pos-server.generated';
import { QuoteSearchRequestDto } from 'src/app/pos-server.generated';

export const init = createAction('[QuoteSearch] Init');

export const search = createAction(
  '[QuoteSearch] Search',
  props<{ searchParams: QuoteSearchRequestDto }>()
);

export const searchResponse = createAction(
  '[QuoteSearch] Response',
  props<{ payload: CreateQuoteDto[] }>()
);

export const selectQuote = createAction(
  '[QuoteSearch] SelectQuote',
  props<{ payload: CreateQuoteDto }>()
);

// New Actions for Payment and Cancellation
export const processPayment = createAction(
  '[QuoteDetails] Process Payment',
  props<{ paymentInfo: any }>() // Define a proper type for paymentInfo
);

export const cancelQuote = createAction(
  '[QuoteDetails] Cancel Quote',
  props<{ quoteCode: string }>()
);

export const cancelQuoteSuccess = createAction(
  '[QuoteDetails] Cancel Quote Success',
  props<{ quoteCode: string }>()
);

export const cancelQuoteFailure = createAction(
  '[QuoteDetails] Cancel Quote Failure',
  props<{ error: any }>()
);
