import { Component } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'pos-layby-zero-refund-confirmation',
  templateUrl: './layby-zero-refund-confirmation.component.html'
})
export class LaybyZeroRefundConfirmationComponent {
  constructor(public activeModal: NgbActiveModal) {}

  confirm() {
    this.activeModal.close('confirm');
  }

  cancel() {
    this.activeModal.dismiss('cancel');
  }
} 