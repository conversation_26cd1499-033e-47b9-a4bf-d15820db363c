import { createAction, props } from '@ngrx/store';
import { GetPINAuthDto } from 'src/app/pos-server.generated';

// Read PINAuth Actions
export const readPINAuth = createAction(
	'[PINAuth] Read PINAuth'
);

export const readPINAuthSuccess = createAction(
	'[PINAuth] Read PINAuth Success',
	props<{ pinAuth: GetPINAuthDto }>()
);

export const readPINAuthFailure = createAction(
	'[PINAuth] Read PINAuth Failure',
	props<{ error: any }>()
);

//// Update PINAuth Actions
//export const updatePINAuth = createAction(
//	'[PINAuth] Update PINAuth',
//	props<{ updatePINAuth: GetPINAuthDto }>()
//);

//export const updatePINAuthSuccess = createAction(
//	'[PINAuth] Update PINAuth Success'
//);

//export const updatePINAuthFailure = createAction(
//	'[PINAuth] Update PINAuth Failure',
//	props<{ error: any }>()
//);

//// Reset Update Status
//export const resetUpdateStatus = createAction(
//	'[PINAuth] Reset Update Status'
//);
