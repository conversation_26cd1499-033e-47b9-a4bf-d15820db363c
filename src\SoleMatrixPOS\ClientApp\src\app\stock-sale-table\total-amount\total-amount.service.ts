import { Injectable } from '@angular/core';
import { AppState } from '../redux/app.store';
//import { AppState, state } from '../test-state';
import { Store } from '@ngrx/store';
import { Location } from '../classes/location.model'
import { getImmutableLocations } from '../redux/location/location.selector';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TotalAmountService {

  public locations$: Observable<Location[]>;

  constructor(private store: Store<AppState>) {
    this.locations$ = this.getLocation();
  }

  public getLocation(): Observable<Location[]> {
    return this.store.select(getImmutableLocations);
  }
}
