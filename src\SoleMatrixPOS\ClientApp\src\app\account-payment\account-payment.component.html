<pos-nav-header pageName="Account Payment"></pos-nav-header>

<div class="content-wrapper flex-grow-1 pt-10">
	<div class="container-fluid">

		<div class="row text-align-center mt-2 mb-4">
			<h3 class="mr-2 text-danger">
				Search Customers by
			</h3>
			<div class="col-2 pr-0">
				<select class="form-control form-control-special" [(ngModel)]="field" (change)="search()">
					<option value="Name" selected>NAME</option>
					<option value="Phone">PHONE</option>
					<option value="Email">EMAIL</option>
					<option value="AccountCode">ACCOUNT CODE</option>
				</select>
			</div>

			<div class="col-3 pl-0">
				<!--(keyup)='keyUp.next($event)'-->
				<input type="text" class="form-control" [(ngModel)]="term" (keyup)="search()" autofocus />
			</div>


		</div>
		<div *ngIf="loading">
			<mat-spinner style="margin:0 auto;" mode="indeterminate"></mat-spinner>
		</div>
		<div class="row m-2" *ngIf="!loading">
			<div class="table-responsive">
				<table class="table table-striped ml-2 mr-2 table-hover">
					<thead>
						<tr>
							<th scope="col-1">Phone</th>
							<th scope="col-2">Name</th>
							<th scope="col-3">Street</th>
							<th scope="col-3">Suburb</th>
							<th scope="col-4">State</th>
							<th scope="col-5">Post Code</th>
							<th scope="col-6">Email</th>
							<th scope="col-7">Contact Name</th>
							<th scope="col-8">Department</th>
							<th scope="col-9"> </th>
						</tr>
					</thead>
					<tbody>
						<tr *ngFor="let cust of customers$|async; let i = index" (click)="selectCust(cust)">
							<!-- [ngClass]="{'selectedMember': selectedMember && member.clientCode == selectedMember.clientCode}"> -->
							<td>
								<ngb-highlight [result]="cust.mobileNo" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td>
								<ngb-highlight [result]="cust.customerName" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td>
								<ngb-highlight [result]="cust.street" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td>
								<ngb-highlight [result]="cust.suburb" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td>
								<ngb-highlight [result]="cust.state" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td>
								<ngb-highlight [result]="cust.postCode" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td>
								<ngb-highlight [result]="cust.email" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td>
								<ngb-highlight [result]="cust.contactName" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td>
								<ngb-highlight [result]="cust.department" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td>
						</tr>

					</tbody>
				</table>
			</div>
		</div>

	</div>
</div>