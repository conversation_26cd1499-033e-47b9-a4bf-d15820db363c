<nav class="navbar navbar-light navbar-expand-lg bg-light">
	<div class="container-fluid">
		<a class="navbar-brand" href="#">
			<img src="assets/logo.svg" alt="Solemate Logo" />
		</a>
		<button class="navbar-toggler" type="button" (click)="collapsed = !collapsed"
			aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
			<span class="navbar-toggler-icon"></span>
		</button>

		<div class="navbar-collapse collapsed" [class.collapse]="collapsed" id="navbarSupportedContent">
			<ul class="navbar-nav ml-auto">
				<li class="nav-item active">
					<a class="nav-link" href="#"  [translate]="'home-header.buttons.Support'">Support</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" [routerLink]="['/clock-out']" [translate]="'home-header.buttons.ClockOut'">Clock Out</a>
				</li>
				<li *ngIf="(staffAccess$ | async).endOfDay" class="nav-item">
					<a class="nav-link" href="['/end-of-day-float']" [translate]="'home-header.buttons.EndOfDay'">End of Day</a>
				</li>

				<li class="nav-item" ngbDropdown>
					<a class="nav-link" style="cursor: pointer" ngbDropdownToggle id="navbarDropdown1" role="button"
					[translate]="'home-header.buttons.Logout'">
						Logout
					</a>
					<div ngbDropdownMenu aria-labelledby="navbarDropdown1" class="dropdown-menu dropdown-menu-right">
						<h6 class="dropdown-header">
							<span *ngIf="identity$ | async as identity">{{ identity.username }}</span> |
							<span *ngIf="staff$ | async as staff">{{ staff.name }}</span>
						</h6>
						<button class="dropdown-item" (click)="switchUser()" [translate]="'home-header.buttons.SwitchUser'">Switch User</button>
					</div>
				</li>

			</ul>
		</div>
	</div>
</nav>
