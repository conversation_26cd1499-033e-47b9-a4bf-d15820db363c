import { Component, OnInit } from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { Store } from '@ngrx/store';
import { StaffLoginState, StaffState } from 'src/app/reducers/staff/staff.reducer';
import { AppState } from 'src/app/reducers';
import { Router } from '@angular/router';
import * as staffActions from '../../reducers/staff/staff.actions';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
	selector: 'pos-clock-out',
	templateUrl: './clock-out.component.html',
	styleUrls: ['./clock-out.component.scss']
})
export class ClockOutComponent implements OnInit {
	date: Date;
  
	staff$: Observable<StaffState>;
	staffLoginStateSub: Subscription;
  
	constructor(
	  public activeModal: NgbActiveModal,
	  private store: Store<AppState>,
	  private router: Router
	) {
	  this.date = new Date();
	}
  
	ngOnInit() {
	  this.staff$ = this.store.select(s => s.staff);
  
	  this.staffLoginStateSub = this.store.select(s => s.staff.staffLoginState).subscribe(staffLoginState => {
		if(staffLoginState === StaffLoginState.LoggedOut){
		  this.activeModal.dismiss();
		  return this.router.navigateByUrl('/staff-login');
		}
	  });
	}
  
	ngOnDestroy() {
	  if (this.staffLoginStateSub) {
		this.staffLoginStateSub.unsubscribe();
	  }
	}
  
	ClockOut() {
	  this.store.dispatch(staffActions.clockOut());
	  this.activeModal.close();
	}
  }