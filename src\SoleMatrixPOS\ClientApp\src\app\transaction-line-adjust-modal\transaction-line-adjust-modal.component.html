<div class="modal-header navbar navbar-light navbar-expand-lg bg-light">
    <h4 class="modal-title">{{modalTitle}}</h4>
    <button type="button" class="close" aria-label="Close" (click)="dismiss()">
        <span aria-hidden="true">&times;</span>
    </button>
</div>

<div class="modal-body">
    <!-- Item Details Card -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row mb-2">
                <div class="col-4">Stock Code:</div>
                <div class="col-8">{{stockItem.styleCode}}</div>
            </div>
            <div class="row mb-2">
                <div class="col-4">Description:</div>
                <div class="col-8">{{stockItem.styleDescription}}</div>
            </div>
            <div class="row mb-2">
                <div class="col-4">Colour:</div>
                <div class="col-8">{{stockItem.colourName}}</div>
            </div>
            <div class="row mb-2">
                <div class="col-4">Size:</div>
                <div class="col-8">{{stockItem.size}}</div>
            </div>
            <div class="row mb-2">
                <div class="col-4">Original Price:</div>
                <div class="col-8">{{originalPrice | currency}}</div>
            </div>
        </div>
    </div>

    <!-- Discount Form -->
    <form [formGroup]="adjustForm">
        <div class="row mb-3">
            <div class="col-4">
                <label for="reason" class="form-label">Reason:</label>
            </div>
            <div class="col-8">
                <select class="form-control" formControlName="reason"
                    [class.is-invalid]="adjustForm.get('reason').invalid && adjustForm.get('reason').touched">
                    <option value="">Select a reason</option>
                    <option *ngFor="let discount of discountTypes" [ngValue]="discount">
                        {{discount.discountCode}} - {{discount.discountReason}}
                    </option>
                </select>
                <div class="invalid-feedback" *ngIf="adjustForm.get('reason').errors?.required">
                    Please select a reason for the discount
                </div>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-4">
                <label for="discountPercent" class="form-label">Discount %:</label>
            </div>
            <div class="col-8">
                <div class="input-group">
                    <input type="number" id="discountPercent" class="form-control" formControlName="discountPercent"
                        [class.is-invalid]="adjustForm.get('discountPercent').invalid && adjustForm.get('discountPercent').touched"
                        min="0" step="0.1">
                    <button class="btn btn-outline-secondary" type="button" (click)="confirmApplyToAll('percent')"
                        [disabled]="!adjustForm.get('reason').valid || 
                                    !isDiscountValid"
                        title="Apply this percentage to all items">
                        Apply to All
                    </button>
                    <div class="invalid-feedback" *ngIf="adjustForm.get('discountPercent').errors?.max">
                        Maximum allowed discount for {{selectedDiscountType?.discountReason}} is {{selectedDiscountType?.maxDiscPercent}}%
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-4">
                <label for="discountAmount" class="form-label">Discount $:</label>
            </div>
            <div class="col-8">
                <div class="input-group">
                    <input type="number" id="discountAmount" class="form-control" formControlName="discountAmount"
                        [class.is-invalid]="adjustForm.get('discountAmount').invalid && adjustForm.get('discountAmount').touched"
                        min="0" step="0.01">
                </div>
                <div class="invalid-feedback" *ngIf="adjustForm.get('discountAmount').errors?.max">
                    Maximum allowed discount is {{selectedDiscountType?.maxDiscPercent}}%
                    ({{adjustForm.get('discountAmount').errors?.max?.max | currency}})
                </div>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-4">
                <label for="quantity" class="form-label">Quantity:</label>
            </div>
            <div class="col-8">
                <div class="input-group">
                    <!-- Minus button for non-refund mode -->
                    <button *ngIf="!isRefundAdjust" 
                            class="btn btn-outline-secondary quantity-btn" 
                            type="button"
                            (click)="adjustQuantity(-1)">
                        <i class="fas fa-minus"></i>
                    </button>
                    
                    <input type="number"
                           class="form-control text-center"
                           formControlName="quantity"
                           [min]="1"
                           (input)="onQuantityChange($event.target.value)"
                    />
                    
                    <!-- Plus button for non-refund mode -->
                    <button *ngIf="!isRefundAdjust" 
                            class="btn btn-outline-secondary quantity-btn" 
                            type="button"
                            (click)="adjustQuantity(1)">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <small class="form-text text-muted" *ngIf="isRefundAdjust">
                    Must be a negative number for returns
                </small>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-4">
                <label for="price" class="form-label">New Price:</label>
            </div>
            <div class="col-8">
                <div class="input-group">
                    <input type="number" id="price" class="form-control" formControlName="price"
                        [class.is-invalid]="adjustForm.get('price').invalid && adjustForm.get('price').touched"
                        min="0.01" step="0.01">
                    <button class="btn btn-outline-secondary" type="button" (click)="resetPrice()"
                        title="Reset to original price">
                        <i class="fas fa-undo"></i>
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-secondary btn-lg" (click)="cancel()">Cancel</button>
    <button type="button" class="btn btn-primary btn-lg" 
            (click)="save()" 
            [disabled]="!formValid || !isDiscountValid"
            [title]="!formValid ? 'Form is not valid' : 'Save changes'">
        Save
    </button>
</div>