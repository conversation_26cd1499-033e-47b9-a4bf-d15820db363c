import { Component } from '@angular/core';
import { AppState } from '../../stock-sale-table/redux/app.store';
//import { AppState, state } from '../../stock-sale-table/test-state';
import { Store } from '@ngrx/store';
import { Transfer } from '../../stock-sale-table/redux/transfer/transfer.model';
import * as TransferActions from '../../stock-sale-table/redux/transfer/transfer.action';
import { getTransfers } from '../../stock-sale-table/redux/transfer/transfer.selector'
import * as LocationActions from '../../stock-sale-table/redux/location/location.action';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-transfer-list',
  templateUrl: './transfer-list.component.html',
  styleUrls: ['./transfer-list.component.scss']
})

export class TransferListComponent {

  transfers$: Observable<Transfer[]>;

  constructor(private store: Store<AppState>) { 
    this.readTransfersState();
  }
  
  /**
   * Dispatches a tranfer action to the store using the passed in values
   * 
   * @param {string} from 
   * @param {string} to 
   * @param {string} name 
   * @param {string} size 
   * @param {number} qty 
   */
  public addTransfer(from: string, to: string, name: string, size: string, qty: number) {

    const action = new TransferActions
      .AddTransferAction(from, to, name, size, qty);

    this.store.dispatch(action);
  
  }

  /**
   * Assigns the trasnfers state from the store to the
   * transfer array observable.
   */
  public readTransfersState() {
    this.transfers$ = this.store.select(getTransfers);
  }

  /**
   * Swaps the stock in the transfer table back and
   * removes the transfer instance from the transfers array
   * 
   * @param {number} id 
   */
  public removeTransfer(id: number) {

    let from;
    let to;
    let name;
    let size;
    let qty;

    this.transfers$.subscribe(
      (transfers)=> {
        transfers.forEach((transfer)=>{
          if(transfer.id === id){
            from = transfer.to    // Swapped here
            to = transfer.from
            name = transfer.name
            size = transfer.size
            qty = transfer.qty
          }
        }); 
      }
    )

    const action = new TransferActions
      .RemoveTransferAction(id, from, to, name, size, qty);

    this.store.dispatch(action);

  }

  /**
   * Highlights a transfer in the transfer table and dispatches
   * a corresponding action to highlight the transfer in the 
   * transfer table
   * 
   * @param {number} id 
   */
  public highlightTransfer(id: number) {

    let from;
    let to;
    let name;
    let size;
    let qty;

    this.transfers$.subscribe(
      (transfers)=> {
        transfers.forEach((transfer)=>{
          if(transfer.id === id){
            from = transfer.from
            to = transfer.to
            name = transfer.name
            size = transfer.size
            qty = transfer.qty
          }
        }); 
      }
    )
    
    const action = new TransferActions
      .HighlightTransferAction(id, from, to, name, size, qty);

    this.store.dispatch(action);
  }

}
