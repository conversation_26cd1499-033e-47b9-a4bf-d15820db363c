import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from '../../reducers';
import * as staffActions from '../../reducers/staff/staff.actions';
import * as StoreAction from '../../reducers/store-info/store-info.actions';
import { Observable, Subscription } from 'rxjs';
import { StaffLoginState, StaffState } from '../../reducers/staff/staff.reducer';
import { HomeClient, StoreDetailsDto, GetPINAuthDto, HouseKeepingClient } from 'src/app/pos-server.generated';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/core/services/auth.service';

@Component({
	selector: 'pos-staff-login',
	templateUrl: './staff-login.component.html',
	styleUrls: ['./staff-login.component.scss']
})
export class StaffLoginComponent implements OnInit {

	staffCode = '';
	storeId = '';           // Selected Store Id
	pinPadId = '';            // Selected PIN Pad ID
	stores: StoreDetailsDto[] = [];        // List of stores
	pinPads: GetPINAuthDto[] = [];         // List of PIN pads based on store selection

	staffLoginMessage$: Observable<string>;
	staffState$: Observable<StaffState>;
	staffLoginStateSub: Subscription
	staffLoginMessageSub: Subscription; 
	loading = false;  // New loading flag

	constructor(
		private store: Store<AppState>,
		private homeClient: HomeClient,
		private houseKeepingClient: HouseKeepingClient,
		private cdr: ChangeDetectorRef,
		private router: Router,
		private authService: AuthService, 

	) { }

	ngOnInit() {
		this.staffLoginMessage$ = this.store.select(s => s.staff.loginMessage);
		this.staffState$ = this.store.select(s => s.staff);
		this.staffLoginStateSub = this.store.select(s => s.staff.staffLoginState).subscribe(staffLoginState => {
			// Once a result comes back, stop the spinner
			if(staffLoginState === StaffLoginState.ClockIn){
				this.loading = false;
				this.router.navigateByUrl('/clock-in');
			}
			if(staffLoginState === StaffLoginState.Complete){
				this.loading = false;
				this.router.navigateByUrl('/home');
			}
		})

		this.staffLoginMessageSub = this.store.select(s => s.staff.loginMessage).subscribe(message => {
			if (message) {
			  // Assuming any message indicates an error (or at least that processing is done)
			  this.loading = false;
			}
		  });

		this.loadStores();
		// Prepopulate storeName and pinPadId from localStorage if available
		const savedStoreId = localStorage.getItem('storeId');
		const savedPinPadId = localStorage.getItem('PINPadId');

		if (savedStoreId) {
			this.storeId = savedStoreId;
			// Load PIN pads based on saved store
			this.onStoreChange();
		}

		if (savedPinPadId) {
			this.pinPadId = savedPinPadId;
		}
	}

	ngOnDestroy() {
		this.staffLoginStateSub.unsubscribe()
	}

	loadStores() {
		this.homeClient.getAllStores().subscribe(
			(stores: StoreDetailsDto[]) => {
				this.stores = stores;
			},
			(error) => {
				console.error('Error loading stores:', error);
			}
		);
	}

	onStoreChange() {
		// Check if storeName is defined
		if (!this.storeId) {
			console.error("The storeName must be defined.");
			return; // Exit the method if storeName is not set
		}
		// Load PIN pads based on the selected storeName
		this.houseKeepingClient.getPINAuthByStoreId(this.storeId).subscribe(
			(pinPads: GetPINAuthDto[]) => {
				this.pinPads = pinPads;
				this.cdr.markForCheck();
			},
			(error) => {
				console.error('Error loading PIN pads:', error);
			}
		);
	}


	submit() {
		// Set loading true when login is submitted
		this.loading = true;
		this.store.dispatch(staffActions.staffLogin({ code: this.staffCode }));
	}

	handleLogout() {
		this.authService.logout()
	} 
}
