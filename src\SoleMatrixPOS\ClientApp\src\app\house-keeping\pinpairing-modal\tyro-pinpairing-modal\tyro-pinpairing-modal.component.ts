import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SysControlUpdateDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import { Store } from '@ngrx/store';
import { EftposService } from '../../../eftpos/eftpos.service';

@Component({
	selector: 'pos-pinpairing-modal',
	templateUrl: './tyro-pinpairing-modal.component.html',
	styleUrls: ['./tyro-pinpairing-modal.component.scss']
})
export class TyroPINPairingModalComponent implements OnInit {

	pairingForm: FormGroup = this.fb.group({
		mid: ['', Validators.required],
		tid: ['', Validators.required],
	});

	constructor(
		public activeModal: NgbActiveModal,
		private fb: FormBuilder,
		private store: Store<AppState>,
		private eftposService: EftposService
	) { }

	ngOnInit() {
	}

	submitPINPairCode() {
		console.log(this.eftposService)
		if (this.pairingForm.valid) {
			const formValues = this.pairingForm.value;
			this.eftposService.tyroPairPINPad(formValues.mid, formValues.tid);
			this.activeModal.close(formValues);
		}
	}

	close() {
		this.activeModal.close();
	}

	dismiss(reason: string) {
		this.activeModal.dismiss(reason);
	}
}
