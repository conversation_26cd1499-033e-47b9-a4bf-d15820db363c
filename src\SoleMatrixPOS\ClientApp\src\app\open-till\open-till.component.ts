import { Component, OnInit } from "@angular/core";
import { FormControl, FormBuilder, Validators } from "@angular/forms";
import { Store } from '@ngrx/store';
import { TranslogDto, TransrefDto, OpenTillTransactionDto, PettyCashTransactionDto, TranspayDto } from "../pos-server.generated";
import { AppState } from "../reducers";
import * as openTillActions from "../reducers/open-till/open-till.actions"
import * as staffActions from "../reducers/staff/staff.actions"
import * as receiptActions from "../reducers/receipt-printing/receipt.actions"
import { Router } from '@angular/router';
import { PaymentType } from "../payment/payment.service";
import Swal from 'sweetalert2';
import * as openTillSelectors from "../reducers/open-till/open-till.selectors"
import { PrintingService, SolemateReceiptOptions } from "../printing/printing.service";
import { OpenCashDrawerAction, ReceiptBatch, BarcodeAction, CutAction, CutType, TextAction } from "../printing/printing-definitions";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import * as staffSelectors from '../reducers/staff/staff.selectors';
import { StaffLoginDto } from "../pos-server.generated";

const OPEN_TILL_TRANSTYPE = 17;
const PETTY_CASH_TRANSTYPE = 10;

@Component({
	selector: "pos-open-till",
	templateUrl: "./open-till.component.html",
	styleUrls: ["./open-till.component.scss"],
})
export class OpenTillComponent implements OnInit {
	// Predefined reasons for opening till
	predefinedReasons: string[] = [
		'Start of Shift',
		'Cash Drawer Replacement',
		'Daily Opening',
	];

	pettyCash = false;
	maxLength = 30;
	minLength = 5;
	inputInvalid = false;

	txtReason = new FormControl("", [
		(control) => {
			const value = control.value || '';
			if (value.length > this.maxLength) {
				return { maxLengthExceeded: true };
			}
			if (value.trim().length < this.minLength) {
				return { minLengthNotMet: true };
			}
			return null;
		}
	]);

	openTillForm = this.fb.group({
		amount: this.fb.control(null, [
			Validators.required
		])
	});

	private staff: StaffLoginDto;

	constructor(
		private fb: FormBuilder,
		private router: Router,
		private store: Store<AppState>,
		public activeModal: NgbActiveModal,
		private printService: PrintingService
	) { }

	ngOnInit() {
		this.store.select(openTillSelectors.completed).subscribe(
			value => { if (value) this.openTillTransactionCompleted(); }
		);

		this.store.select(staffSelectors.selectStaffLoginDto)
			.subscribe(staff => this.staff = staff);
	}

	// Called when a predefined reason is selected (from dropdown)
	selectPredefinedReason(reason: string) {
		this.txtReason.setValue(reason.substring(0, this.maxLength));
	}

	initPettyCash() {
		this.pettyCash = true;
		this.openTillForm.get('amount').enable();
	}

	removePettyCash() {
		this.pettyCash = false;
		this.openTillForm.get('amount').disable();
		this.openTillForm.get('amount').setValue(null);
	}

	submitForm() {
		// Validate reason
		if (!this.txtReason.value ||
			this.txtReason.value.trim().length < this.minLength ||
			this.txtReason.errors) {
			this.inputInvalid = true;
			return;
		}

		// Prepare reason
		const reason = this.txtReason.value.trim().substring(0, this.maxLength);

		// Submit transaction based on petty cash or not
		if (!this.pettyCash) {
			let trans: OpenTillTransactionDto = this.createOpenTillTransaction(reason);
			this.store.dispatch(openTillActions.submitTransaction({ payload: trans }));
		} else {
			// Validate amount for petty cash
			if (this.openTillForm.get('amount').invalid) {
				return;
			}

			const amount = this.openTillForm.get('amount').value;
			let trans: PettyCashTransactionDto = this.createPettyCashTransaction(reason);

			const staffName = this.staff ? this.staff.name : '';
			this.printService.printPettyCashReceipt(amount, reason, staffName);

			this.store.dispatch(openTillActions.submitPettyCash({ payload: trans }));
		}

		// Open till
		this.store.dispatch(receiptActions.executeBatch({
			payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
		}));

		this.activeModal.close({
			reason: reason,
		});
	}

	createOpenTillTransaction(reason: string) {
		let openTillTransRef: TransrefDto;
		let openTillTransLog: TranslogDto;
		const lineNo: number = 1;

		openTillTransLog = {
			styleCode: "Reason",
			lineNo: lineNo
		} as TranslogDto;

		openTillTransRef = {
			lineNo: lineNo,
			transReference: reason
		} as TransrefDto;

		return {
			translog: openTillTransLog,
			transRef: openTillTransRef,
			transType: OPEN_TILL_TRANSTYPE
		} as OpenTillTransactionDto;
	}

	createPettyCashTransaction(reason: string) {
		let pettyCashTransRef: TransrefDto;
		let pettyCashTransLog: TranslogDto;
		let pettyCashTransPay: TranspayDto;

		const lineNo: number = 1;
		pettyCashTransLog = {
			styleCode: "Reason",
			lineNo: lineNo
		} as TranslogDto;

		pettyCashTransRef = {
			lineNo: lineNo,
			transReference: reason
		} as TransrefDto;

		pettyCashTransPay = {
			payAmount: this.openTillForm.get('amount').value,
			paymentType: PaymentType['Cash']
		} as TranspayDto;

		return {
			translog: pettyCashTransLog,
			transRef: pettyCashTransRef,
			transpay: pettyCashTransPay,
			transType: PETTY_CASH_TRANSTYPE
		} as PettyCashTransactionDto;
	}

	openTillTransactionCompleted(): void {
		this.store.dispatch(openTillActions.init());
		this.activeModal.close();
		this.store.dispatch(staffActions.clearStaffLogin());
		Swal.fire({
			title: "Till Opened",
			text: "Till Successfully Opened."
		});
	}

	dismiss() {
		this.activeModal.dismiss('cancel');
	}

	// Getter to check if submit should be enabled
	get canSubmit(): boolean {
		return this.txtReason.value &&
			this.txtReason.value.trim().length >= this.minLength &&
			(!this.pettyCash || (this.pettyCash && this.openTillForm.get('amount').valid));
	}
}

export interface OpenTillResult {
	reason: string;
}