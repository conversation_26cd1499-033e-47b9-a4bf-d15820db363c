import { Action, createReducer, on } from "@ngrx/store"
import * as giftVoucherAction from './gift-voucher.actions'
import { GiftVoucherCreationDto, GiftVoucherResultDto } from "src/app/pos-server.generated"
import { Payment } from "src/app/payment/payment.service";


export class GiftVoucherState {
    isCreditNote: boolean;
    giftVoucherCreation: GiftVoucherCreationDto;
    giftVoucherResult: GiftVoucherResultDto;
    isLoading: boolean;
    payments: Payment[];
    balances: number[];
    completed: boolean;
}

export const initialState: GiftVoucherState = {
    isCreditNote: false,
    giftVoucherCreation: null,
    giftVoucherResult: null,
    isLoading: false,
    payments: [],
    balances: [null, null, null],

    completed: false,

} as GiftVoucherState;


export const GiftVoucherReducer = createReducer(initialState,
    on(giftVoucherAction.init, (state) => initialState),
    //get voucher number
    on(giftVoucherAction.getVoucherNoResponse, (state, action) => {
        return { ...state, getVoucherNo: action.voucherNo }
    }),

    on(giftVoucherAction.addGiftVoucher, (state, action) => {
        const balanceValues = calculateBalances(action.giftCard, state.payments);
        return {...state, giftVoucherCreation: action.giftCard, balances: balanceValues};
    }),

    //done for adding for pending payment from cash or eftpost
    on(giftVoucherAction.addPayment, (state, action) => {
        let newPayments = addToPendingPayments(state.payments, action.payment);
        let balanceAfterPay = calculateBalances(state.giftVoucherCreation, newPayments);

        return { ...state, payments: newPayments, balances: balanceAfterPay };

	}),

	on(giftVoucherAction.removePayment, (state, action) => {
		let newPayments = []
		for (let p of state.payments) {
			if (p != action.payment) {
				newPayments.push(p)
			}
		}
		let balanceAfterPay = calculateBalances(state.giftVoucherCreation, newPayments);

		return { ...state, payments: newPayments, balances: balanceAfterPay };
	}),

    //submit or generate gift voucher
    on(giftVoucherAction.submit, (state) => {
        return { ...state, isLoading: true }
    }),
    on(giftVoucherAction.submitCompleted, (state, action) => {
        return { ...state, isLoading: false, completed: true, giftVoucherResult: action.payload }
    }),
    on(giftVoucherAction.creditNoteReceived, (state, action) => {
        return { ...state, isLoading: false, completed: true, giftVoucherResult: action.payload, isCreditNote: true }
    })

)

export function reducers(state: GiftVoucherState | undefined, action: Action) {
    return GiftVoucherReducer(state, action);
}

export enum GiftTypes {
    item = 1,
    payment = 2
}

function addToPendingPayments(existing: Payment[], newP: Payment): Payment[] {
    let newPayments = [];
    let foundDuplicate = false;

    for (let p of existing) {
        if (p.type == newP.type) {
            foundDuplicate = true;
            newPayments.push({ ...p, amount: p.amount + newP.amount } as Payment);
        } else {
            newPayments.push({ ...p } as Payment);
        }
    }

    if (!foundDuplicate) newPayments.push(newP);

    return newPayments;
}

function calculateBalances(item: GiftVoucherCreationDto, pendingPayment: Payment[]): [number, number, number] {
    let total: number = 0, paid: number = 0, due: number = 0;

    total = item.voucherFunds;

    for (let payment of pendingPayment) {
        paid += payment.amount;
    }

    due = total - paid;

    return [total, paid, due];
}
