import {Component} from '@angular/core'
import { ISupportListData } from './support-list/support-list.component'


@Component({
    selector: "app-support",
    templateUrl: "./support.component.html",
    styleUrls: ['./support.component.scss']
})
export class SupportComponent {
    constructor(){}

    supportListsData: ISupportListData[] = [
        {
            title: "Tutorials",
            description: "Watch our easy-to-follow video tutorials to learn how to use key features",
            internalUrl: "",
            url: "",
            list: [    {
                    title: "Start Of Day",
                    description: "",
                    internalUrl: "",
                    url: "https://www.youtube.com/watch?v=DxSkDY8FAOk&list=PLAfNmH1tz1TqENUNRBfV3JMMKR2FLFg_N&index=3",
                    list: []
                },
                {
                    title: "Basic Sale",
                    description: "",
                    internalUrl: "",
                    url: "https://www.youtube.com/watch?v=70GRqj20Hb0&list=PLAfNmH1tz1TqENUNRBfV3JMMKR2FLFg_N&index=1",
                    list: []
                },   
                {
                title: "Returns - Issue credit note",
                description: "",
                internalUrl: "",
                url: "https://www.youtube.com/watch?v=rFtX2OBE6qw&list=PLAfNmH1tz1TqENUNRBfV3JMMKR2FLFg_N&index=7",
                list: []
                },
                {
                title: "Returns - Money",
                description: "",
                internalUrl: "",
                url: "https://www.youtube.com/watch?v=xiTDU4Mzdxo&list=PLAfNmH1tz1TqENUNRBfV3JMMKR2FLFg_N&index=8",
                list: []
                },
                {
                    title: "Exchange - No money",
                    description: "",
                    internalUrl: "",
                    url: "https://www.youtube.com/watch?v=EMay5ikCjx0&list=PLAfNmH1tz1TqENUNRBfV3JMMKR2FLFg_N&index=2",
                    list: []
                },
                {
                    title: "Exchange - Sale",
                    description: "",
                    internalUrl: "",
                    url: "https://www.youtube.com/watch?v=NMn-XvbO3ac&list=PLAfNmH1tz1TqENUNRBfV3JMMKR2FLFg_N&index=5",
                    list: []
                },
                {
                title: "Exchange - Refund",
                description: "",
                internalUrl: "",
                url: "https://www.youtube.com/watch?v=-ICm5bsbMeU&list=PLAfNmH1tz1TqENUNRBfV3JMMKR2FLFg_Nindex=4",
                list: []
                },
                {
                title: "Gift Voucher - Issue",
                description: "",
                internalUrl: "",
                url: "https://www.youtube.com/watch?v=ZI04LSB4Lw0&list=PLAfNmH1tz1TqENUNRBfV3JMMKR2FLFg_N&index=6",
                list: []
                },
            
                {
                title: "Stock - Send Stock",
                description: "",
                internalUrl: "",
                url: "https://www.youtube.com/watch?v=ynLS6S9HHtE&list=PLAfNmH1tz1TqENUNRBfV3JMMKR2FLFg_N&index=9",
                list: []
                },
            ]
        },
        {
            title: "Other Links",
            description: "",
            internalUrl: "",
            url: "",
            list: [
                {
                    title: "StyleMatrix Website",
                    description: "",
                    internalUrl: "",
                    url: "https://stylematrix.io/contact-us/",
                    list: []
                }
            ]
        }
    ]

}