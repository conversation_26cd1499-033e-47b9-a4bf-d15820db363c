<nav class="navbar navbar-light navbar-expand-lg bg-light">
	<div class="container-fluid">
		<div class="navbar-brand" (click)="goHome()">
			<img src="assets/logo.svg" alt="Solemate Logo" />
		</div>
		<button class="navbar-toggler" type="button" (click)="collapsed = !collapsed"
				aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
			<span class="navbar-toggler-icon"></span>
		</button>

		<div class="pt-3 pl-5">
			<h2>{{_pageName}}</h2>
		</div>

		<div class="navbar-collapse collapsed" [class.collapse]="collapsed" id="navbarSupportedContent" >
			<ul class="navbar-nav ml-auto" >
				<li class="nav-item" *ngIf="storeInfo$ | async as store">
					<span class="navbar-text" *ngIf="staff$ | async as staff">{{staff.name}}</span>
					<span class="navbar-text">{{store.storeId}}</span>
					<span class="navbar-text">{{store.storeName}}</span>
					<span class="navbar-text"><b>{{store.tillNumber}}</b></span>
					<span class="navbar-text">{{store.currentDate | date}}</span>
					<span class="navbar-text">{{store.currentTime}}</span>
				</li>

				<li class="nav-item active">
					<a class="nav-link" href="#">Support <span class="sr-only">(current)</span></a>
				</li>

			</ul>
		</div>
	</div>
</nav>
