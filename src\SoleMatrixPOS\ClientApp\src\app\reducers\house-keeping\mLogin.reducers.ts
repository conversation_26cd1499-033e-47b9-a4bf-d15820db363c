import { createReducer, on, Action } from "@ngrx/store";
import { ManagerLoginResult, MloginDto, MloginResponseDto, SystemResponseDto } from "src/app/pos-server.generated";
import * as houseActions from './mLogin.actions';

export enum ManagerLoginState {
    LoggedOut = 1,
    //manager is fully logged in and can proceed to the main screen
    Complete = 4,
}

export class ManagerState {
    mLoginState: ManagerLoginState;
    mLoginDto: MloginDto;
    isLoading: boolean;
    loginMessage: string;
    systemResponse: SystemResponseDto;
}

export const initialState: ManagerState = {
    mLoginState: ManagerLoginState.LoggedOut,
    mLoginDto: null,
    isLoading: false,
    loginMessage: '',
    systemResponse: {
        getSystemStatus: null,
        titleReceipt: null,
        headerReceipt: null,
        footerReceipt: null,
        laybyReceipt: null,

    } as SystemResponseDto,
} as ManagerState;

export const managerReducer = createReducer(initialState,
    on(houseActions.init, state => ({ ...initialState })),
    on(houseActions.mLogin, state => ({ ...initialState, isLoading: true })),

    // state changes from processing managerlogin response
    on(houseActions.mLoginResponse, (state, action) => {
        // manager login is complete
        if (action.payload.mLoginResultCode === ManagerLoginResult.Complete) {
            return {
                ...state,
                isLoading: false,
                mLoginDto: action.payload.mLogin,
                mLoginState: ManagerLoginState.Complete,
                loginMessage: 'Success!',
                systemResponse: action.payload.systemResponse,
            };

            // manager login failed
        } else if (action.payload.mLoginResultCode === ManagerLoginResult.CodeNotFound) {
            return { ...state, isLoading: false, loginMessage: 'Login Failed!' };
        }
    }),
    on(houseActions.mLogout, state => ({ ...state, isLoading: false, mLoginState:ManagerLoginState.LoggedOut })),
    
    
    );

export function reducer(state: ManagerState | undefined, action: Action) {
    return managerReducer(state, action);
}
