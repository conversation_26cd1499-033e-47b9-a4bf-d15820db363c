using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR; 
using Microsoft.AspNetCore.Authentication.JwtBearer;
using SoleMatrixPOS.Application.OrderItem.Commands;
using SoleMatrixPOS.Application.Transaction.Commands;
using SoleMatrixPOS.Application.OrderItem;
using SoleMatrixPOS.Application.Transaction;
using SoleMatrixPOS.Application.Orders.Commands;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using SoleMatrixPOS.Application.Transaction.Queries;
using SoleMatrixPOS.Application.OrderItem.Queries;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class OrderController : ControllerBase
	{
		private readonly IMediator _mediator;

		public OrderController(IMediator mediator)
		{
			_mediator = mediator;
		}


		// IMPORTANT
		// orders don't actually get their orderarrivaldate updated when they arrive... if they do then we need a new field to distinguish... and then handle in each command

		[HttpPut]
		public async Task<IActionResult> AddOrderWithTransaction([FromBody] CreateOrderRequest createOrderRequest)
		{
			await _mediator.Send(new CreateOrderCommand(createOrderRequest.OrderDto, createOrderRequest.TransactionDto));

			return Ok();
		}

		[HttpPost("cancel")]
		public async Task<IActionResult> CancelOrder([FromBody] string OrderNumber)
		{
			// Use Mediator to send the CancelOrderCommand
			await _mediator.Send(new CancelOrderCommand(OrderNumber));
			return Ok();
		}

		[HttpGet("getOrderNo")]
		public async Task<IActionResult> GetOrderNo()
		{
			string orderCode = await _mediator.Send(new GetOrderNoQuery());
			return Ok(orderCode);
		}

		[HttpPost("complete")]
		public async Task<IActionResult> CompleteOrder([FromBody] string OrderCode)
		{
			// Use Mediator to send the CompleteOrderCommand
			await _mediator.Send(new CompleteOrderCommand(OrderCode));
			return Ok();
		}
	}

	public class CreateOrderRequest
	{
		public CreateOrderDto OrderDto { get; set; }
		public TransactionDto TransactionDto { get; set; }
	}
}
