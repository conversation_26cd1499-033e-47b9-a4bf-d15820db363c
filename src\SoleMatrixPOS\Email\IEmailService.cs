using System;
using System.Threading.Tasks;

namespace SoleMatrixPOS.Email
{
	public interface IEmailService
	{
		public Task<EmailResult> SendReceiptEmailAsync(string to, DateTime date, string transactionNo, byte[] pdfReceiptBytes, string title);
		public Task<EmailResult> SendPasswordResetEmailAsync(string to, string tillId, string token, int expiryMinutes);
	}

	public enum EmailResultReason
	{
		Ok,
		InvalidEmail,
		ProviderError
	}

	public struct EmailResult
	{
		public bool Succeeded { get; set; }
		public EmailResultReason Reason { get; set; }

		public string Message { get; set; }
	}
}
