<div class="fixed-container">
    <table class="table table-striped table-hover table-scroll">
        <thead>
            <tr>
                <th scope="col">Total Amount</th>
            </tr>
        </thead>
        <tbody class="body-half-screen">

            <!-- TODO: totals$ doesn't exist -->
            <!-- <ng-container *ngFor="let total of totals$ | async">
                <tr>
                    <td>
                        {{ total.stock | removeZeros }}
                    </td>
                </tr>

                <tr>
                    <td>
                        {{ total.sales | removeZeros }}
                    </td>
                </tr>
            </ng-container> -->

        </tbody>
    </table>
</div>