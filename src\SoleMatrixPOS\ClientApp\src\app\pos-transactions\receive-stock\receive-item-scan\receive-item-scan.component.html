<div class="container-fluid">
    <h2>SCAN ITEMS</h2>    
    <div class="row text-center">
        <div class="col">
            <p>From: {{senderStoreName}}</p>
        </div>
        <div class="col">
            <label for="inputItemBarcode">Item Barcode: </label>
            <input [formControl]="enterItemBarcode" name="inputItemBarcode" type="text" #barcodeInput>
            <button type="button" class="btn btn-link" (click)="openSearch()">
                <span class="fa-stack">
                    <i class="fas fa-circle fa-stack-2x text-danger"></i>
                    <i class="fas fa-search fa-stack-1x fa-inverse"></i>
                </span>
            </button>
        </div>
        <div class="col">
            <p>Transfer No: {{docketBarcode}}</p>
        </div>
    </div>
    <div *ngIf="!itemBarcodeValidity" class="row text-center">
        <p class="msgErr">Item barcode was invalid.</p>
    </div>
    <pos-generic-stock-table [tableItems]="stockTableItems" [fieldsEnabled]="fieldsEnabled" (tableItemDelete)="deleteItem($event)">
    </pos-generic-stock-table>
</div>

<div class="footer-wrapper">
	<div class="container-fluid">
        <div class="row">
            <div class="col float-right stock-total-items">
                
                <h3>
                    Number of Items Scanned: {{stockItemCount}}
                </h3>

            </div>
            <div class="col float-right">
                
                <button (click)="onConfirmStockReceivalClick()" class="btn-lg btn-default btn d-flex align-items-center confirm-stock-button">
                    <h3>Confirm Stock Receival</h3>
                </button>

            </div>
        </div>
    </div>
</div>