using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR; 
using Microsoft.AspNetCore.Authentication.JwtBearer;
using SoleMatrixPOS.Application.QuoteItem.Commands;
using SoleMatrixPOS.Application.Transaction.Commands;
using SoleMatrixPOS.Application.QuoteItem;
using SoleMatrixPOS.Application.Transaction;
using SoleMatrixPOS.Application.OrderItem;
using SoleMatrixPOS.Application.OrderItem.Queries;
using SoleMatrixPOS.Application.QuoteItem.Queries;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class QuoteController : ControllerBase
	{
		private readonly IMediator _mediator;

		public QuoteController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[HttpPost("add")]
		public async Task<IActionResult> AddQuoteWithTransaction([FromBody] CreateQuoteRequestDto createQuoteRequest)
		{
			await _mediator.Send(new CreateQuoteCommand(createQuoteRequest.QuoteDto, createQuoteRequest.TransactionDto));
			return Ok();
		}

		[HttpPost("cancel")]
		public async Task<IActionResult> CancelQuote([FromBody] string QuoteNumber)
		{
			// Use Mediator to send the CancelQuoteCommand
			await _mediator.Send(new CancelQuoteCommand(QuoteNumber));
			return Ok();
		}

		[HttpGet("getQuoteNo")]
		public async Task<IActionResult> GetQuoteNo()
		{
			string quoteCode = await _mediator.Send(new GetQuoteCodeQuery());
			return Ok(quoteCode);
		}

		[HttpPost("complete")]
		public async Task<IActionResult> CompleteQuote([FromBody] string QuoteCode)
		{
			// Use Mediator to send the CompleteQuoteCommand
			await _mediator.Send(new CompleteQuoteCommand(QuoteCode));
			return Ok();
		}
	}
}
