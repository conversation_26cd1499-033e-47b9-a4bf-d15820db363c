using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using SoleMatrixPOS.Application.Layby.Commands;
using SoleMatrixPOS.Application.Layby;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class LaybyRefundController : ControllerBase
	{
		private readonly IMediator _mediator;

		public LaybyRefundController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[HttpPut]
		public async Task<IActionResult> CancelLayby([FromBody] CancelLaybyDto cancelLaybyDto)
		{
			await _mediator.Send(new CancelLaybyCommand(cancelLaybyDto));
			return Ok();
		}
	}
}
