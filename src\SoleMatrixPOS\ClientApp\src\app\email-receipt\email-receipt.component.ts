import { Component, EventEmitter, Output, Input } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { HttpClient } from '@angular/common/http';
import { Giftvoucher, LaybyPaymentEmailRequest, CustomerClubDto } from '../pos-server.generated';
import { finalize } from 'rxjs/operators';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-email-receipt',
  templateUrl: './email-receipt.component.html',
  styleUrls: ['./email-receipt.component.scss']
})
export class EmailReceiptComponent {

  email: string;
  isLoading = false; // Flag for loading state

  // Accept gift voucher properties directly
  @Input() voucherNo: string;
  @Input() voucherFunds: number;
  @Input() voucherTypeTitle: string;
  @Input() laybyType: string;
  @Input() customerSelected: CustomerClubDto;
  @Input() pointsEarned: number;
  @Input() newCustomerPointsTotal: number;
  @Input() orderNo: string;
  @Input() laybyCode: string;
  @Input() accountDetails: { code: string, name: string };
  
  // Still accept the giftVoucher object for compatibility
  @Input() giftVoucher: any; 

  @Input() laybyReceipt: LaybyPaymentEmailRequest;

  // Accept receiptTrans as input from parent component
  @Input() receiptTrans: any;
  @Input() customerEmail: string;

  @Output() emailSubmitted = new EventEmitter<string>();

  constructor(
    public activeModal: NgbActiveModal,
    private http: HttpClient,
    private modalService: NgbModal  // Inject NgbModal for launching the success modal
  ) {}

  ngOnInit(): void {
    if (this.customerEmail) {
      this.email = this.customerEmail;
    }
  }

  onSendReceipt() {
    if (this.email) {
      this.isLoading = true;  // Start the loading indicator

      // Check if direct voucher properties are provided
      if (this.voucherNo && this.voucherFunds !== undefined) {
        this.sendGiftVoucherRequest(this.email, this.voucherNo, this.voucherFunds, this.voucherTypeTitle)
          .pipe(finalize(() => this.isLoading = false))
          .subscribe(
            response => {
              console.log('Gift voucher email sent successfully:', response);
              this.emailSubmitted.emit(this.email);
              this.openSuccessModal();  
              this.activeModal.close(this.email);
            },
            error => {
              console.error('Error sending gift voucher email:', error);
              this.activeModal.dismiss();
            }
          );
      }
      // Check if it's a gift voucher object
      else if (this.giftVoucher) {
        let voucherNumber = this.giftVoucher.voucherNumber;
        let voucherValue = this.giftVoucher.voucherValue;
        let voucherTypeTitle = this.giftVoucher.voucherTypeTitle;
        
        // Handle different property naming conventions from different components
        if (!voucherNumber && this.giftVoucher.voucherNo) {
          voucherNumber = this.giftVoucher.voucherNo;
        }
        if (!voucherValue && this.giftVoucher.voucherFunds) {
          voucherValue = this.giftVoucher.voucherFunds;
        }
        
        this.sendGiftVoucherRequest(this.email, voucherNumber, voucherValue, voucherTypeTitle)
          .pipe(finalize(() => this.isLoading = false))
          .subscribe(
            response => {
              console.log('Gift voucher email sent successfully:', response);
              this.emailSubmitted.emit(this.email);
              this.openSuccessModal();
              this.activeModal.close(this.email);
            },
            error => {
              console.error('Error sending gift voucher email:', error);
              this.activeModal.dismiss();
            }
          );
      }
      // Check if receiptTrans has only transNo or full details
      else if (this.isTransNoOnly(this.receiptTrans)) {
        this.sendEmailReceiptByTransNo(this.email, this.receiptTrans.transNo)
          .pipe(finalize(() => this.isLoading = false))
          .subscribe(
            response => {
              console.log('Email sent successfully:', response);
              this.emailSubmitted.emit(this.email);
              this.openSuccessModal();
              this.activeModal.close(this.email);
            },
            error => {
              console.log(this.email, this.receiptTrans);
              console.error('Error sending email with transNo:', error);
              this.activeModal.dismiss();
            }
          );
      } 
      else if (this.laybyReceipt) {
        this.laybyReceipt.email = this.email;
        this.laybyReceipt.receiptType = this.laybyType;
        if (this.laybyType === 'Cancel') {
          this.sendLaybyReturnReceipt(this.laybyReceipt)
          .pipe(finalize(() => this.isLoading = false))
          .subscribe(
            response => {
              console.log('Layby receipt email sent successfully:', response);
              this.emailSubmitted.emit(this.email);
              this.openSuccessModal();
              this.activeModal.close(this.email);
            },
            error => {
              console.error('Error sending layby receipt email:', error);
              this.activeModal.dismiss();
            }
          );
        }
        else {
        this.sendLaybyReceipt(this.laybyReceipt)
          .pipe(finalize(() => this.isLoading = false))
          .subscribe(
            response => {
              console.log('Layby receipt email sent successfully:', response);
              this.emailSubmitted.emit(this.email);
              this.openSuccessModal();
              this.activeModal.close(this.email);
            },
            error => {
              console.error('Error sending layby receipt email:', error);
              this.activeModal.dismiss();
            }
          );
        }
      } 
      else {
        let clientCode = ""
        let clientName = ""
        if (this.customerSelected) {
          clientCode = this.customerSelected.clientCode;
          clientName = this.customerSelected.firstname + " " + this.customerSelected.surname;
        }
        console.log(this.orderNo);  

        let accountCode: string = null;
        let accountName: string = null;
        if (this.accountDetails) {
          accountCode = this.accountDetails.code;
          accountName = this.accountDetails.name;
        }

        this.sendEmailReceipt(this.email, this.receiptTrans, clientCode, clientName, this.pointsEarned, this.newCustomerPointsTotal, this.orderNo, accountCode, accountName, this.laybyCode)
          .pipe(finalize(() => this.isLoading = false))
          .subscribe(
            response => {
              console.log('Email sent successfully:', response);
              this.emailSubmitted.emit(this.email);
              this.openSuccessModal();
              this.activeModal.close(this.email);
            },
            error => {
              console.log(this.email, this.receiptTrans);
              console.error('Error sending email:', error);
              this.activeModal.dismiss();
            }
          );
      }
    }
  }

  onCancel() {
    this.activeModal.dismiss();
  }

  // Send email with full receipt details
  sendEmailReceipt(email: string, receiptTrans: any, clientCode: string, clientName: string, pointsEarned: number, newCustomerPointsTotal: number, orderCode: string, accountCode: string, accountName: string, laybyCode: string) {
    const url = '/api/email/send-receipt';  // API endpoint for sending full receipt details
    const body = { email, receiptDto: receiptTrans, clientCode, clientName, pointsEarned, orderCode, customerPoints: newCustomerPointsTotal, accountCode, accountName, laybyCode};
    return this.http.post(url, body);
  }

  // Send gift voucher email matching the C# GiftVoucherRequest model
  sendGiftVoucherRequest(email: string, voucherNumber: string, voucherValue: number, voucherTypeTitle: string) {
    const url = '/api/email/send-gift-voucher';
    const body = {
      Email: email,
      VoucherNumber: voucherNumber,
      VoucherValue: voucherValue,
      VoucherType: voucherTypeTitle
    };
    console.log('Sending gift voucher request:', body);
    return this.http.post(url, body);
  }

  sendLaybyReceipt(laybyReceipt: LaybyPaymentEmailRequest) {
    const url = '/api/email/layby-payment-receipt';
    const body = laybyReceipt;
    console.log('Sending layby receipt request:', body);
    return this.http.post(url, body);
  }

  sendLaybyReturnReceipt(laybyReceipt: LaybyPaymentEmailRequest) {
    const url = '/api/email/layby-return-receipt';
    const body = laybyReceipt;
    console.log('Sending layby receipt request:', body);
    return this.http.post(url, body);
  }


  // Send email with just transNo
  sendEmailReceiptByTransNo(email: string, transNo: number) {
    const url = '/api/email/send-receipt-by-transno';  // API endpoint for sending receipt using transNo
    console.log(email, transNo);
    const body = { email, transNo };
    return this.http.post(url, body);
  }

  // Utility function to check if only transNo is passed
  isTransNoOnly(receiptTrans: any): boolean {
    return receiptTrans && receiptTrans.transNo && (!receiptTrans.logs || !receiptTrans.pays);
  }

  // Opens a success modal after sending the email successfully.
  openSuccessModal() {
    Swal.fire({
      title: 'Email Sent',
      text: 'The email has been sent successfully.',
      type: 'success',  
    });
  }
}
