import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StockSaleTableComponent } from './stock-sale-table.component';
import { RemoveZerosPipe } from '../pipes/remove-zeros.pipe';
import { StockSaleTableService } from './stock-sale-table.service';
import { By } from '@angular/platform-browser';
import { Observable, of } from 'rxjs';
import { Size } from '../classes/size';
import { Location } from '../classes/location.model';
import { Stock } from '../classes/stock';
import { Sale } from '../classes/sale';

type Trading = "T" | "F";
type Toggle = "on" | "off";

class MockStockSaleTableService {
  public sizes$: Observable<Size[]>;
  public locations$: Observable<Location[]>;

  constructor() { 
      this.sizes$ = of(this.sizes)
      this.locations$ = of(this.locations);
  }

  private sizes = [
    new Size("1"),
    new Size("2"),
    new Size("3"),
    new Size("4")
  ]

  private locations = [
    {
      name: "Location 1",
      trading: "T" as  Trading,
      rank: 1, 
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 2",
      trading: "T" as  Trading,
      rank: 1, 
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 3",
      trading: "T" as  Trading,
      rank: 1, 
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 4",
      trading: "T" as  Trading,
      rank: 1, 
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
  ]

}

describe('MainTableComponent', () => {
  let component: StockSaleTableComponent;
  let fixture: ComponentFixture<StockSaleTableComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ StockSaleTableComponent, RemoveZerosPipe ],
      providers: [ 
        StockSaleTableComponent, { 
          provide: StockSaleTableService, 
          useClass: MockStockSaleTableService
        } 
      ]
    });

    component = TestBed.get(StockSaleTableComponent);
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display table data', ()=> {
    fixture = TestBed.createComponent(StockSaleTableComponent);
    fixture.detectChanges();

    const tableElements = fixture.debugElement.queryAll(By.css('table'));
    expect(tableElements.length).toBe(1);
  });

  describe('Table Header', ()=>{

    it('should contain "Location" and 4 separate sizes ', ()=>{
  
      fixture = TestBed.createComponent(StockSaleTableComponent);
      fixture.detectChanges();
      const tableElements = fixture.debugElement.queryAll(By.css('.table-info'));

      expect(tableElements[0].nativeElement.textContent.trim()).toContain("Location");
      expect(tableElements[0].nativeElement.textContent.trim()).toContain("1");
      expect(tableElements[0].nativeElement.textContent.trim()).toContain("2");
      expect(tableElements[0].nativeElement.textContent.trim()).toContain("3");
      expect(tableElements[0].nativeElement.textContent.trim()).toContain("4");

    });

  });

  describe('Stock/Sale', ()=> {

    it('should contain a number of location names', ()=>{
  
      fixture = TestBed.createComponent(StockSaleTableComponent);
      fixture.detectChanges();
      const tableElements = fixture.debugElement.queryAll(By.css('.border-square'));

      expect(tableElements[0].nativeElement.textContent.trim()).toContain("Location 1");
      expect(tableElements[2].nativeElement.textContent.trim()).toContain("Location 2");
      expect(tableElements[4].nativeElement.textContent.trim()).toContain("Location 3");
      expect(tableElements[6].nativeElement.textContent.trim()).toContain("Location 4");

    });

    it('should contain a number of sales', ()=>{
  
      fixture = TestBed.createComponent(StockSaleTableComponent);
      fixture.detectChanges();
      const tableElements = fixture.debugElement.queryAll(By.css('.border-square'));

      expect(tableElements[1].nativeElement.textContent.trim()).toContain("SALE");
      expect(tableElements[3].nativeElement.textContent.trim()).toContain("SALE");
      expect(tableElements[5].nativeElement.textContent.trim()).toContain("SALE");
      expect(tableElements[7].nativeElement.textContent.trim()).toContain("SALE");

    });

  });

  describe('Table Data', ()=> {

    it('should contain a number of quantitites', ()=> {

      fixture = TestBed.createComponent(StockSaleTableComponent);
      fixture.detectChanges();
      const tableElements = fixture.debugElement.queryAll(By.css('.stock-border-square'));
  
      expect(tableElements[0].nativeElement.textContent.trim()).toContain("1");
      expect(tableElements[1].nativeElement.textContent.trim()).toContain("2");
      expect(tableElements[2].nativeElement.textContent.trim()).toContain("3");
      expect(tableElements[3].nativeElement.textContent.trim()).toContain("4");

    });

  });

});
