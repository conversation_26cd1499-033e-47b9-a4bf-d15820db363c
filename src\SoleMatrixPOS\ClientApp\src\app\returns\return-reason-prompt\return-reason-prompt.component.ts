import { Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'pos-return-reason-prompt',
  templateUrl: './return-reason-prompt.component.html',
  styleUrls: ['./return-reason-prompt.component.scss']
})
export class ReturnReasonPromptComponent implements OnInit {

  constructor(public activeModal: NgbActiveModal) { }

  // Prebuilt return reasons
  predefinedReasons: string[] = ['Damaged', 'Change of Mind', 'Wrong Item Delivered', 'Not as Described'];

  txtReason = new FormControl("", [
    (control) => {
      const value = control.value || '';
      return value.length > 30 ? { maxLengthExceeded: true } : null;
    }
  ]);
  chkApplyToAll = new FormControl("");

  inputInvalid: boolean = false;
  maxLength: number = 30;

  ngOnInit() { }

  // Called when a predefined reason is selected (from dropdown)
  selectPredefinedReason(reason: string) {
    this.txtReason.setValue(reason.substring(0, this.maxLength));
  }

  dismiss(reason: string) {
    this.activeModal.dismiss("cancel");
  }

  submitReason() {
    if (!this.txtReason.value || !this.txtReason.value.trim() || this.txtReason.errors) {
      this.inputInvalid = true;
    } else { 
      this.complete({
        reason: this.txtReason.value.trim().substring(0, this.maxLength),
        applyToAll: this.chkApplyToAll.value
      });
    }
  }

  complete(reason: ReasonPromptResult){
    this.activeModal.close(reason);
  }
}

export interface ReasonPromptResult {
  reason: string;
  applyToAll: boolean;
}
