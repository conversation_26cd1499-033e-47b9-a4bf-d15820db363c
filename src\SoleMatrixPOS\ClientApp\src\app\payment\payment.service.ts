import { EventEmitter, Injectable } from '@angular/core';
import { BehaviorSubject, of, Subject, Subscription } from 'rxjs';
import { Timestamp } from 'rxjs/internal/operators/timestamp';
import { Store } from '@ngrx/store';
import { StockItemDto, TranslogDto, TranspayDto } from '../pos-server.generated';
import * as SysConfigSelectors from '../reducers/sys-config/sys-config.selectors';
import { CartItem } from '../reducers/sales/cart/cart.reducer';

export enum PaymentType {
	Eftpos = "Eftpos",
	Cash = "Cash",
	ZipPay = "ZipPay",
	AfterPay = "AfterPay",
	GiftCard = "Gift Card",
	CreditNote = "Credit Note",
	CustomerAccount = "Customer Account",
	Cheque = "Cheque",
	Deposit = "Deposit",
	CustomerPoints = "Points",
	Surcharge = "Surcharge"
}

export interface PaymentContext {
    giftVoucherNo: string
}

export class Payment {
    amount: number;
    type: PaymentType;
    desc: string = "";
	context: PaymentContext = {} as PaymentContext;
	paid: boolean = false;
	customerDetails?: { // Add optional customer details
		clientCode: string;
		name: string;
	};

    compare(other: Payment): boolean {
        return this.amount === other.amount
            && this.type === other.type;
    }

    public copy() {
        return {
            ...this
        } as Payment;
    }
}

/*
  Returns a value rounded to 2 d.p.
  Intended for rounding financial values
*/
export function financialRound(value: number): number {
    return Math.round(value * 100) / 100;
}

const unmergables: PaymentType[] = [
    PaymentType.CreditNote,
	PaymentType.GiftCard,
]
export class Transaction {

  // --- New properties for Layby handling ---
  /**
   * The required deposit percent for layby (for example, 0.2 for 20%).
   * Defaults to 1 (i.e. full payment) when layby is not activated.
   */
  public depositPercent: number = 1;
  /**
   * A subscription for the layby deposit selector.
   */
  private laybyDepositSub: Subscription;
  /**
   * The store must be set on the transaction instance before using activateLayby.
   * For example, you might assign this from an Angular service.
   */
  public store: Store<any>;
	// --- End new properties ---

	public surchargeAmount: number = 0;

  constructor(total: number) {
    this._total = total;
    // The initial amount due is the total (rounded)
    this._amountDue = financialRound(total);
    this._rounding = this._total - this._amountDue;
  }

    /*
      Attempts to add payment 'payment'.
      Returns true if the payment was added successfully, and false otherwise.
    */
	public addPayment(payment: Payment, multiEft: boolean = false, applyFullAmount: boolean = false): boolean {
        // Check if the payment will overshoot
        let paymentOvershot = this.amountDue - financialRound(payment.amount) < 0;
		console.log("hmmm", this.amountDue);
        // Only allow overshoot if payment is cash (change) or applyFullAmount is true
        if (!paymentOvershot || payment.type == PaymentType.Cash || payment.type == PaymentType.Surcharge || applyFullAmount) {
			// If the payment type is EFTPOS, always add it separately if multiEft is setup. Currently only sales supports this. TODO JASON make everything else work.
			if (payment.type === PaymentType.Eftpos && multiEft) {
				this._payments.push(payment);
			} else {
				// Check if this payment type is already used and merge if appropriate.
				let mergeSimilarTypes = false;
				for (let existingPayment of this._payments) {
					// Merge if the payments are the same type and either:
					// - the type is mergeable, or
					// - the contexts are equal.
					if (existingPayment.type == payment.type && (
						!unmergables.some(t => t == existingPayment.type) ||
						Object.keys(existingPayment.context).every(k => existingPayment.context[k] == payment.context[k])
					)) {
						mergeSimilarTypes = true;
						existingPayment.amount += payment.amount;
						break;
					}
				}

				if (!mergeSimilarTypes) {
					this._payments.push(payment);
				}
			}

            this.recalculate();
            
            // If applyFullAmount is true and this is a layby (deposit < 1), 
            // apply the excess to deposit up to total cart value
            if (applyFullAmount && this._amountDue < 0 && this.depositPercent < 1) {
                // Calculate total payments vs total cart value
                const totalPayments = this.getTotalPaymentValue();
                
                // If total payments exceed total cart value, generate change for the excess
                if (totalPayments > this._total) {
                    this._change = financialRound(totalPayments - this._total);
                    this._amountDue = 0;
                } else {
                    // Otherwise, apply the excess to the deposit
                    this._amountDue = 0;
                    this._change = 0;
                }
            }

            return true;
        } else {
            return false;
        }
    }

    public setStore(store: Store<any>) {
        this.store = store;
    }

    public reset() {
        this._payments = [];
        this._isTotalALaybyTarget = false;
        this.recalculate();
    }

	public acceptedEft(eftPaid: number) {
		this._successfulEft += eftPaid;
		this.recalculate();
	}

	public refundEft(eftPaid: number) {
		this._successfulEft -= eftPaid;
	}

    public removePayment(payment: Payment) {
        this._payments.forEach((v, i) => {
            if (v === payment) {
                this._payments.splice(i, 1);
                this.recalculate();
            }
        });
    }

    public toTranspayDtos(isRefund: boolean = false): TranspayDto[] {
        let paymentDtos: TranspayDto[] = [];
        for (let payment of this._payments) {
            // For cash payments, reduce the amount by any change if it's the payment that caused the overpayment
            let paymentAmount = payment.amount;
            if (payment.type === PaymentType.Cash && this._change > 0) {
                // Only reduce the last cash payment by the change amount
                if (payment === this._payments[this._payments.length - 1]) {
                    paymentAmount = financialRound(payment.amount - this._change);
                }
            }
            if (isRefund) {
                paymentAmount = financialRound(paymentAmount * -1);
            }

            // Create the base TranspayDto object
            const transpayDto: TranspayDto = {
                payAmount: paymentAmount,
                paymentType: payment.type,
                context: {
                    ...payment.context
                },
                voucherNumber: undefined // Initialize voucherNo
            };

            // If the payment type is Gift Card or Credit Note, assign the voucher number from the context
            if (payment.type === PaymentType.GiftCard || payment.type === PaymentType.CreditNote) {
                // Check if context exists and has the giftVoucherNo property before accessing it
                if (payment.context && payment.context.giftVoucherNo !== undefined) {
                    transpayDto.voucherNumber = payment.context.giftVoucherNo;
                }
            }

            paymentDtos.push(transpayDto);
        }

        return paymentDtos;
    }

    /**
     * Updates the transaction's totals.
     */
    private recalculate() {
        this._amountTendered = this.getTotalPaymentValue();
        console.log(`[Recalculate] Start: _total=${this._total}, _amountTendered=${this._amountTendered}, _depositPercent=${this.depositPercent}, _isTotalALaybyTarget=${this._isTotalALaybyTarget}`);

        if (this._isTotalALaybyTarget) { // If _total is a specific layby deposit target
            this._amountDue = financialRound(this._total - this._amountTendered);
            console.log(`[Recalculate] Path A (isTotalALaybyTarget): _amountDue=${this._amountDue}`);
        } else if (this.depositPercent < 1) { // Standard layby logic based on cart total and policy %
            this._amountDue = financialRound(this._total * this.depositPercent - this._amountTendered);
            console.log(`[Recalculate] Path B (depositPercent < 1): _amountDue=${this._amountDue}`);
        } else { // Standard non-layby transaction, or layby where total is already the target deposit
            this._amountDue = financialRound(this._total - this._amountTendered);
            console.log(`[Recalculate] Path C (else): _amountDue=${this._amountDue}`);
        }
        
        this._change = this._amountDue < 0 ? -this._amountDue - this.surchargeAmount : 0;
        console.log(`[Recalculate] End: _amountDue=${this._amountDue}, _change=${this._change}`);
  }

    getTotalPaymentValue(): number {
        let total = 0;
        this._payments.forEach(p => {
            total += p.amount;
        });
        return total;
    }

    get total() {
        return this._total;
    }

    public updateTotal(newTotal: number, isLaybyDepositTarget: boolean = false) {
        this._total = newTotal;
        this._isTotalALaybyTarget = isLaybyDepositTarget;
        this.recalculate();
    }

    get rounding() {
        return this._rounding;
    }

    get amountTendered() {
        return this._amountTendered;
    }

    get amountDue() {
        return this._amountDue;
	}

	get successfulEft() {
		return this._successfulEft;
	}

    set amountDue(amount: number) {
        // Ensure amount is not negative
        const validAmount = Math.max(0, amount);

        // Round to 2 decimal places
        this._amountDue = Math.round((validAmount + Number.EPSILON) * 100) / 100;
        console.log(`Amount due set to: ${this._amountDue}`);
    }

    get change() {
        return this._change;
    }

    get payments() {
        return this._payments;
    }

    /**
     * Activates (or deactivates) layby mode.
     *
     * When activated, the method subscribes to the store selector to retrieve the
     * required deposit percentage (e.g., 0.2 for 20%), updates the `depositPercent` property,
     * and adjusts the amount due so that only the deposit is required.
     *
     * When deactivated (i.e. called with activate === false), it unsubscribes from the store,
     * resets the deposit percentage to 1 (i.e. full amount due), and recalculates the totals.
     *
     * Note: The `store` and the `SysConfigSelectors` (with a member `selectLaybyDeposit`)
     * must be available on this class before calling this method.
     *
     * @param activate If true, layby mode is activated; if false, it is deactivated.
     */
    public activateLayby(activate: boolean = true) {
        if (activate) {
            if (!this.store) {
                throw new Error('Store is not set on Transaction instance.');
            }
            this._isTotalALaybyTarget = false;
            this.laybyDepositSub = this.store.select(SysConfigSelectors.selectLaybyDeposit)
                .subscribe((laybyDeposit: number) => {
                    // Only apply policy deposit calculation if a specific layby deposit target is NOT already set
                    if (!this._isTotalALaybyTarget) {
                        this.depositPercent = laybyDeposit / 100;
                        // Amount due is policy % of cart total, minus what's paid towards that policy amount.
                        this.amountDue = financialRound(this._total * this.depositPercent - this.getTotalPaymentValue());
                    }
                });
        } else {
            if (this.laybyDepositSub) {
                this.laybyDepositSub.unsubscribe();
                this.laybyDepositSub = null;
            }
            this.depositPercent = 1;
            this._isTotalALaybyTarget = false;
            this.recalculate();
        }
    }

    private _payments: Payment[] = [];
    private _total: number = 0;
    private _rounding: number = 0;
    private _amountTendered: number = 0;
	private _amountDue: number = 0;
	private _depositDue: number = 0;
    private _change: number = 0;
	private _successfulEft: number = 0;
    private _item: CartItem;
    private _isTotalALaybyTarget: boolean = false;
}

@Injectable({
    providedIn: 'root'
})
export class PaymentService {
    // PaymentService implementation goes here
}
