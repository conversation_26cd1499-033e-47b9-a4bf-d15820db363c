import { TestBed, inject } from '@angular/core/testing';

import { TotalAmountService } from './total-amount.service';
import { provideMockStore } from '@ngrx/store/testing';
import { Store } from '@ngrx/store';
//import { AppState } from 'src/redux/app.store';
import { AppState } from '../test-state';
import { Sale } from '../classes/sale';
import { Stock } from '../classes/stock';
import { of } from 'rxjs';

type Trading = "T" | "F";
type Toggle = "on" | "off";

describe('TotalAmountService', () => {

  let masterService: TotalAmountService;
  let valueServiceSpy: jasmine.SpyObj<Store<AppState>>;

  const locations = [
    {
      name: "Location 1",
      trading: "T" as Trading,
      rank: 1,
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 2",
      trading: "T" as Trading,
      rank: 1,
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 3",
      trading: "T" as Trading,
      rank: 1,
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 4",
      trading: "T" as Trading,
      rank: 1,
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
  ]

  beforeEach(() => {

    const spy = jasmine.createSpyObj('Store', ['select']);

    TestBed.configureTestingModule({
      providers: [
        TotalAmountService,
        { provide: Store, useValue: spy }
      ]
    });

    masterService = TestBed.get(TotalAmountService);
    valueServiceSpy = TestBed.get(Store);
  });

  it('should retrieve total amount table', inject([TotalAmountService], (service: TotalAmountService) => {

    const mockObservableLocations = of(locations);

    const spyLocation = spyOn(service, 'getLocation').and.returnValue(mockObservableLocations);

    service.getLocation().subscribe((data) => {
      expect(data).toEqual(locations);
    });

  }));
});
