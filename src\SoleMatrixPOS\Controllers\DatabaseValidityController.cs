using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SoleMatrixPOS.Domain.RepositoryInterfaces;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class DatabaseValidityController : ControllerBase
	{
		private readonly IDatabaseValidityRepository _databaseValidityRepository;

		public DatabaseValidityController(
			IDatabaseValidityRepository databaseValidityRepository)
		{
			_databaseValidityRepository = databaseValidityRepository;
		}

		[HttpGet("Test")]
		public async Task<IActionResult> CheckDatabaseHasRelevantTableColumns()
		{
			string versionDate = "03/03/2025";
			// update versionDate when there are any changes to the test
			try
			{
				var tableTests = new List<TableTest>
				{
					new TableTest
					{
						TableName = "LINKLYSESSION",
						ContextName = "sys",
						ExpectedColumns = new[] {
							"SESSIONID", "SECRET", "POSID", "STATUS", "CREATEDAT", "TRANSNO", "CUSTRECEIPT", "MERCHRECEIPT"
						}
					},
					new TableTest
					{
						TableName = "SYSCONTROL",
						ContextName = "sys",
						ExpectedColumns = new[] {
							"STORE_ID", "CLIENT_DATABASE", "DEMO_Q1", "DEMO_Q2", "DEMO_Q3", "DEMO_Q4",
							"SUSPEND_SALE", "SUSPEND_RTN", "SUSPEND_TFR", "SUSPEND_LAYPAY", "TENDER_CENTREV",
							"POINTS_PER_DOLLAR", "NON_COLOURSIZE", "CUSTOMER_ACCOUNTS", "AUTO_DISC",
							"MANAGER_PASSWORD", "INTEGRATED_EFT_PROVIDER", "ORDER_DEPOSIT", "ON_HOLD_DEPOSIT",
							"LAYBY_DEPOSIT", "ALWAYS_OPEN_CASH_DRAWER", "CUSTOMER_ORDER_NO_STOCK_MOVEMENT",
							"OPEN_LAYBY", "POINT_ON_ALL_SALES", "PRICE_PROMPT", "NO_ROUNDING",
							"BLOCK_EXCHANGE_BUTTON", "HEADER_IMAGE", "FOOTER_IMAGE", "CONSOLIDATE_C_TRAN",
							"CUSTOMER_NAME_ON_RECEIPT", "CUSTOMER_ACCOUNT_PRINT_ON_A4", "DEMOGRAPHIC_QUESTION_ASK",
							"CUSTOMER_CLUB_MANDATORY", "CUSTOMER_CLUB_BIRTHDAY", "BASIC_CASH_UP",
							"SOFT_CREDIT_LIMIT", "LAYBY_NUMBER_OF_WEEKS", "ENTER_ORDER_NO_AND_NO_DEPOSIT",
							"DOLLAR_PER_X_POINTS", "INTEGRATED_RECEIPT", "INTEGRATED_SURCHARGE"
						}
					},
					new TableTest
					{
						TableName = "PINAUTH",
						ContextName = "sys",
						ExpectedColumns = new[] {
							"STOREID", "STORECOMMONNAME", "PINPADNO", "PINPADID", "PINPADCOMMONNAME", "INTEGRATEDEFTSECRET", "INTEGRATEDEFTTOKEN", "INTEGRATEDEFTTOKENEXPIRY", "TERMINALID"
						}
					},
					new TableTest
					{
						TableName = "NEXTSALENO",
						ContextName = "sys",
						ExpectedColumns = new[] {
							"SALE_NO", "SUSPEND_NO", "TERMINAL_NO"
						} // ensure this table has defaultValue set to something for each column... preferably higher than 1 so that we dont conflict with old data..
					},
					new TableTest
					{
						TableName = "RECEIPTLOGO",
						ContextName = "pos",
						ExpectedColumns = new[] {
							"STORE_ID", "RECEIPT_PNG"
						}
					},
					new TableTest
					{
						TableName = "DAILYBUDGET",
						ContextName = "pos",
						ExpectedColumns = new[] {
							"BUDGET_DATE", "STORE_ID", "BUDGET_AMOUNT"
						}
					},
					// Add more table tests here as needed
				};

				var issues = new List<TableIssue>();

				foreach (var test in tableTests)
				{
					try
					{
						var tableDetails = await _databaseValidityRepository.GetTableDetails(test.TableName, test.ContextName);
						var actualColumns = tableDetails.Select(c => c.COLUMN_NAME.Trim()).ToList();

						var missingColumns = test.ExpectedColumns.Except(actualColumns, StringComparer.OrdinalIgnoreCase).ToList();

						if (missingColumns.Any())
						{
							issues.Add(new TableIssue
							{
								TableName = test.TableName,
								ContextName = test.ContextName,
								MissingColumns = missingColumns
							});
						}
					}
					catch (Exception ex)
					{
						issues.Add(new TableIssue
						{
							TableName = test.TableName,
							ContextName = test.ContextName,
							Error = ex.Message
						});
					}
				}

				var result = new
				{
					VersionDate = versionDate, 
					Success = !issues.Any(),
					Issues = issues.Any() ? issues : null
				};

				return Ok(result);
			}
			catch (Exception ex)
			{
				return StatusCode(500, new { Success = false, Error = ex.Message });
			}
		}

		private class TableTest
		{
			public string TableName { get; set; }
			public string ContextName { get; set; }
			public string[] ExpectedColumns { get; set; }
		}

		private class TableIssue
		{
			public string TableName { get; set; }
			public string ContextName { get; set; }
			public List<string> MissingColumns { get; set; }
			public string Error { get; set; }
		}
	}
}
