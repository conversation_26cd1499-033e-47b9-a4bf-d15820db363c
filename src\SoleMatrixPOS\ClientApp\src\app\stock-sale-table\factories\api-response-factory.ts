/**
 * The purpose of this class is to assemble the json responses from MysqlApiService 
 * into usable typescript objects that survive the transpilation into javascript
 */
import { StockSale } from '../classes/stock-sale';
import { Stock } from '../classes/stock';
import { Sale } from '../classes/sale';
import { Location } from '../redux/location/location.model';
import { Size } from '../classes/size';

export class ApiResponseFactory {

    /**
     * Builds number[] from json response
     * @param  {any}      jsonCosts
     * @return {number[]}
     */
    public buildCosts(jsonCosts: any[]): number[] {

        let costs: number[] = [];

        jsonCosts.forEach((cost) => {
            costs.push(
                cost.LAST_RECEIPTED_COST
            )
        });

        return costs;

    }


    /**
     * Builds Size object from json response
     * @param  {any}        jsonStockSale
     * @return {StockSale[]}
     */
    public buildSizes(jsonSizes: any[]): Size[] {

        let sizes: Size[] = [];

        jsonSizes.forEach((size) => {
            sizes.push(
                new Size(
                    size.SIZE
                )
            )
        });

        return sizes;

    }

    /**
     * Builds StockSale object from json response
     * @param  {any}        jsonStockSale
     * @return {StockSale[]}
     */
    public buildStockSale(jsonStockSale: any[]): StockSale[] {

        let stockSales: StockSale[] = [];

        jsonStockSale.forEach((stockSale) => {
            stockSales.push(
                new StockSale(
                    stockSale.LOCATION_NAME,
                    stockSale.SSTOCK_ON_HAND,
                    stockSale.SYTD_SALES_QTY,
                    stockSale.SIZE_CODE,
                    stockSale.COLOUR_CODE
                )
            )
        });

        return stockSales;

    }

    /**
     * Builds Store and Location object from JSON response
     * @param   {any}   jsonStore 
     * @returns {Location}
     */
    public buildLocation(jsonLocation: any, stockSale?: StockSale[]): Location {

        let stock: Stock[] = [];
        let sales: Sale[] = [];

        if (typeof stockSale != 'undefined') {
            stockSale.forEach((stockSale) => {
                if (stockSale.locationName === jsonLocation.LOCATION_NAME) {
                    stock.push(new Stock(stockSale.size, stockSale.stockQty));
                    sales.push(new Sale(stockSale.size, stockSale.salesQty));
                }
            });
        }

        let store = {
            name: jsonLocation.LOCATION_NAME,
            trading: jsonLocation.TRADING_LOCN,
            rank: jsonLocation.RANKING_NO,
            stock: stock,
            sales: sales,
            toggle: null
        }

        return store;
    }

}
