import { createAction, props } from '@ngrx/store';
import {CustomerMateDto,CustomerMatePaymentDto,CtransDto} from 'src/app/pos-server.generated';
import { AccountPaymentSearch } from 'src/app/account-payment/account-payment.component';
import { CustomerPayment } from 'src/app/account-payment/customer-modal/customer-modal.component';

export const init = createAction('[Account Payment] Init');

export const searchCustomers = createAction(
  '[Account Payment] Search Customers',
  props<{ payload: AccountPaymentSearch }>()
);

export const searchCustomersSuccess = createAction(
  '[Account Payment] Search Customers Success',
  props<{ customers: CustomerMateDto[] }>()
);

export const searchCustomersFailure = createAction(
  '[Account Payment] Search Customers Failure',
  props<{ error: any }>()
);

export const getAllCustomers = createAction('[AccountPayment] getAllCustomers', props<{}>());
export const storeAllCustomers = createAction('[AccountPayment] storeAllCustomers',props<{payload:CustomerMateDto[]}>())
export const applySearch = createAction('[AccountPayment] applySearch', props<{payload:AccountPaymentSearch}>());
export const storeSelectedCustomer = createAction('[AccountPayment] storeSelecedCustomer', props<{payload:CustomerPayment}>());
export const updateCustomerBalance = createAction('[AccountPayment] updateCustomerBalance', props<{payload:CustomerMatePaymentDto}>());
export const updateBalanceCompleted = createAction('[AccountPayment] updateBalanceCompleted');
export const insertCtrans = createAction('[AccountPayment] insertCtrans', props<{payload:CtransDto}>());
export const insertCtransConfirmed = createAction('[AccountPayment] insertCtransConfirmed ');