<pos-nav-header pageName="SEND STOCK"></pos-nav-header>

<div class="content-wrapper flex-grow-1">
	<div class="container-fluid">
	  <div *ngIf="getTransferNumber$ | async" class="transfer-number-section">
		<div class="transfer-number">
		  <label class="form-label">Transfer Number:</label>
		  <span class="number"><strong>{{transferNumber}}</strong></span>
		</div>
		<div class="transfer-number d-inline-flex">
		  <div>
			<div>
				<label class="form-label">Sending to:</label>
				<span class="number"><strong>{{getLocation.storeName}}</strong></span>
			</div>
		  </div>
		  <div class="ml-4">
			<button (click)="resetTransfer()" class="btn btn-primary">
			  Select Store
			</button>
		  </div>
		</div>
	  </div>
  
	  <div *ngIf="getTransferNumber$ | async" class="item-lookup-section">
		<div class="lookup-wrapper">
		  <pos-item-lookup (result)="itemLookup($event)"></pos-item-lookup>
		</div>
	  </div>
  
	  <div *ngIf="showSelectors$ | async" class="select-boxes-container">
		<div class="select-box-wrapper">
		  <label class="select-label">Select Reason</label>
		  <select class="form-control form-control-special" size="5" [formControl]="setReason">
			<option *ngFor="let reason of getReason$ | async" [ngValue]="reason">
			  {{reason.transreasonDesc}}
			</option>
		  </select>
		</div>
  
		<div class="send-to-wrapper">
		  <span class="send-to-label">Send To</span>
		</div>
  
		<div class="select-box-wrapper">
		  <label class="select-label">Select Destination</label>
		  <select class="form-control form-control-special" size="5" [formControl]="setDestination">
			<option *ngIf="getLockStore$ | async as lockStore" [ngValue]="lockStore">
			  {{lockStore.storeName}}
			</option>
			<ng-container *ngIf="!(getLockStore$ | async)">
			  <option *ngFor="let des of getDestination$ | async" [ngValue]="des">
				{{des.storeId}} - {{des.storeName}}
			  </option>
			</ng-container>
		  </select>
		</div>
	  </div>
	</div>
  
	<div *ngIf="getTransferNumber$ | async" class="item-lookup-section">
		<pos-stock-cart-table></pos-stock-cart-table>
	</div>
  </div>

<pos-footer (onNextClick)="processSendStock()"></pos-footer>
