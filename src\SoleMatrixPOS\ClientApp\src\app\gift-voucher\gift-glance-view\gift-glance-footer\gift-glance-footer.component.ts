import { Component, OnInit, Input } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormGroup } from '@angular/forms';
import { GiftVoucherClient, GiftVoucherCreationDto } from 'src/app/pos-server.generated';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import * as giftVoucherAction from '../../../reducers/gift-voucher/gift-voucher.actions';
import * as customerClubSearchSelectors from '../../../reducers/customer-club/club-search/customer-club.selectors';
// If you already have CreateErrorModal defined elsewhere, you can remove this extra definition.
import { CreateErrorModal } from 'src/app/error-modal/error-modal.component';

@Component({
  selector: 'pos-gift-glance-footer',
  templateUrl: './gift-glance-footer.component.html',
  styleUrls: ['./gift-glance-footer.component.scss']
})
export class GiftGlanceFooterComponent implements OnInit {

  // Expecting the reactive form to be passed in via an input property.
  @Input() giftFormC: FormGroup;

  // Holds the selected customer club member (assumed to have a clientCode property)
  selectedCustomerClubMember: any = null;

  // This placeholder is used to indicate a generated voucher number
  GENERATED_NUMBER_PLACEHOLDER: string = "<Generated>";

  constructor(
    private router: Router,
    private modalService: NgbModal,
    private giftVoucherClient: GiftVoucherClient,
    private store: Store<AppState>
  ) { }

  ngOnInit() {
    // Subscribe to the selected customer club member from the store
    this.store.select(customerClubSearchSelectors.selectedCustomerClubMember)
      .subscribe((s) => {
        this.selectedCustomerClubMember = s;
      });
  }

  // Back button: navigates back to the home page.
  backBtnClick() {
    this.router.navigateByUrl('/home');
  }

  // Next button: checks the form validity, verifies the voucher number,
  // and if valid dispatches the gift voucher creation action and routes to payment.
  async nextBtnClick() {
    if (this.giftFormC.valid) {
      const voucherNoValue = (this.giftFormC.get('giftVoucherNumber').value as string).trim();
      this.giftVoucherClient.existsGiftVoucher(false, voucherNoValue)
        .subscribe({
          next: voucher => {
            if (voucher) {
              // Show an error modal if the voucher already exists.
              CreateErrorModal(this.modalService, false, "Gift Voucher number already in use");
              return;
            } else {
              // Create the gift voucher object from the form values.
              const giftVoucher: GiftVoucherCreationDto = this.createGiftVoucher();
              // Dispatch the action to add the gift voucher.
              this.store.dispatch(giftVoucherAction.addGiftVoucher({ giftCard: giftVoucher }));
              // Navigate to the gift payment page.
              this.router.navigateByUrl('/gift/payment');
            }
          },
          error: err => {
            console.error("Error during voucher search:", err);
            return;
          }
        });
    } else {
      console.log(this.giftFormC);
      CreateErrorModal(this.modalService, false, "You will need to fill the form to proceed!.");
    }
  }

  // Helper function to create a GiftVoucherCreationDto from the form values.
  createGiftVoucher(): GiftVoucherCreationDto {
    const voucherNoValue = (this.giftFormC.get('giftVoucherNumber').value as string).trim();
    return {
      clientCode: this.selectedCustomerClubMember ? this.selectedCustomerClubMember.clientCode : null,
      voucherFunds: parseFloat(this.giftFormC.get('giftValue').value),
      voucherNo: (voucherNoValue === this.GENERATED_NUMBER_PLACEHOLDER) ? null : voucherNoValue
    } as GiftVoucherCreationDto;
  }
}
