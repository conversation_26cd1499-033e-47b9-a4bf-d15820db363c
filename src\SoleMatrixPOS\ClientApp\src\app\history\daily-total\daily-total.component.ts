import { Component, OnInit, ViewChild } from '@angular/core';
import { toFinancialString, financialRound } from '../../utility/math-helpers';
import { DailyDto, SalesByStaffQueryDto, SalesByHourQueryDto, MultiSaleDto, DailyTotalsDto } from 'src/app/pos-server.generated';
import { Observable, from, Subject } from 'rxjs';
import { select, Store } from '@ngrx/store';
import * as dailyActions from '../../reducers/daily/daily.actions'
import * as dailySelectors from '../../reducers/daily/daily.selectors'
import { AppState } from 'src/app/reducers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { take } from 'rxjs/operators';
import { Calendar } from 'primeng/calendar';

@Component({
  selector: 'pos-daily-total',
  templateUrl: './daily-total.component.html',
  styleUrls: ['./daily-total.component.scss']
})
export class DailyTotalComponent implements OnInit {
  @ViewChild('calendar', { static: false }) pCalendar: Calendar;
  
  public dailyTotal$: Observable<DailyDto>;
  public salesByStaff$: Observable<SalesByStaffQueryDto[]>;
  public salesByHour$: Observable<SalesByHourQueryDto[]>;
  public grossSales$: Observable<number>;
  public totalItems$: Observable<number>;
  public totalCusts$: Observable<number>;
  public multiSales$: Observable<MultiSaleDto>;
  // Add new observables for range data
  public dailyRange$: Observable<DailyDto[]>;
  public dailyRangeTotals$: Observable<DailyTotalsDto>;
  public isRangeMode$: Observable<boolean>;
  
  public selectedDates: Date[];
  public loading = false;
  public isRangeSelected = false;

  constructor(private store: Store<AppState>, public activeModal: NgbActiveModal) { }

  ngOnInit() {
    // Instead of setting today's date as default, use the dates passed from History component
    // The default will only be used if no dates were passed
    if (!this.selectedDates || this.selectedDates.length === 0) {
      this.selectedDates = [new Date(), null];
    }
    
    this.dailyTotal$ = this.store.select(dailySelectors.storeTodaysDaily);
    this.salesByHour$ = this.store.select(dailySelectors.storeSalesByHour);
    this.salesByStaff$ = this.store.select(dailySelectors.storeSalesByStaff);
    this.grossSales$ = this.store.select(dailySelectors.getGrossSales);
    this.totalItems$ = this.store.select(dailySelectors.getItems);
    this.totalCusts$ = this.store.select(dailySelectors.getCusts);
    this.multiSales$ = this.store.select(dailySelectors.getMultiSales);
    this.dailyRange$ = this.store.select(dailySelectors.storeDailyRange);
    this.dailyRangeTotals$ = this.store.select(dailySelectors.storeDailyRangeTotals);
    this.isRangeMode$ = this.store.select(dailySelectors.isRangeMode);
  
    // Initial load of data with the passed dates
    this.loadDailyData();
  }

  // Add method to toggle calendar manually
  toggleCalendar(event: Event) {
    event.stopPropagation();
    if (this.pCalendar) {
      this.pCalendar.overlayVisible = !this.pCalendar.overlayVisible;
    }
  }

  loadDailyData() {
    this.loading = true;
    
    // First, clear any existing data
    this.store.dispatch(dailyActions.clearDailyData());
    
    // Check if we have a date range selected
    const hasStartDate = this.selectedDates && this.selectedDates[0];
    const hasEndDate = this.selectedDates && this.selectedDates[1];
    
    this.isRangeSelected = !!hasStartDate && !!hasEndDate;
    
    // Set the range mode in the store
    this.store.dispatch(dailyActions.setRangeMode({ isRangeMode: this.isRangeSelected }));
    
    if (this.isRangeSelected) {
      // Use the range methods if both start and end dates are selected
      // Normalize dates to midnight (00:00:00)
      const startDate = this.normalizeToMidnight(this.selectedDates[0]);
      const endDate = this.normalizeToMidnight(this.selectedDates[1]);
      
      // Only dispatch range-related actions
      this.store.dispatch(dailyActions.getDailyRange({
        startDate: startDate,
        endDate: endDate
      }));
      
      this.store.dispatch(dailyActions.getDailyRangeTotals({
        startDate: startDate,
        endDate: endDate
      }));
      
      // Wait for the range totals to load before setting loading to false
      this.store.select(dailySelectors.storeDailyRangeTotals)
        .pipe(take(1))
        .subscribe(
          () => {
            setTimeout(() => {
              this.loading = false;
            }, 100);
          },
          () => {
            this.loading = false;
          }
        );
    } else {
      // Use the single date method
      if (hasStartDate) {
        // Normalize date to midnight (00:00:00)
        const selectedDate = this.normalizeToMidnight(this.selectedDates[0]);
        console.log(selectedDate);
        
        // Pass the normalized Date object
        this.store.dispatch(dailyActions.getTodaysDaily({ date: selectedDate }));
        this.store.dispatch(dailyActions.getSalesByHour({ date: selectedDate }));
        this.store.dispatch(dailyActions.getSalesByStaff({ date: selectedDate }));
        this.store.dispatch(dailyActions.getRefundsByStaff({ date: selectedDate }));
        this.store.dispatch(dailyActions.getRefundsByHour({ date: selectedDate }));
        this.store.dispatch(dailyActions.getDepartmentRefunds({ date: selectedDate }));
        this.store.dispatch(dailyActions.getDepartmentSales({ date: selectedDate }));
        this.store.dispatch(dailyActions.getMultiSales({ date: selectedDate }));
      } 
      // Wait for the daily total to load before setting loading to false
      this.store.select(dailySelectors.storeTodaysDaily)
        .pipe(take(1))
        .subscribe(
          () => {
            setTimeout(() => {
              this.loading = false;
            }, 500);
          },
          () => {
            this.loading = false;
          }
        );
    }
  }
  
  // Add helper method to normalize dates to midnight
  private normalizeToMidnight(date: Date): Date {
    const normalizedDate = new Date(date);
    normalizedDate.setHours(0, 0, 0, 0);
    return normalizedDate;
  }
  
  onDateSelect() {
    this.loadDailyData();
  }

  close() {
    this.activeModal.close();
  }

  dismiss(reason: string) {
    this.activeModal.dismiss(reason);
  }

  // TODO: Fix how negative values are dealt with?
  toFinancialString(val) {
    if (val < 0) {
      return `-${toFinancialString(-1 * val)}`;
    } else {
      return toFinancialString(val);
    }
  }

  getTotal(daily: DailyDto | null) {
    if (!daily) return 0;

    const totalsArr: number[] = [
      daily.eftTotal,
      daily.cashTotal,
    ];
    let sum = 0;
    for (let total of totalsArr) {
      sum += financialRound(total);
    }
    return sum;
  }

  // Helper function to format the date
  private formatDate(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are zero-based
    const year = date.getFullYear();
    return `${year}-${month}-${day}`;
  }

  transformDate(stringDate: Date | null) {
    if (!stringDate) return '';
    const date = new Date(stringDate);
    return date.toDateString();
  }
  
  getDateRangeDescription(): string {
    if (!this.selectedDates || !this.selectedDates[0]) {
      return 'Today';
    }
    
    const startDate = this.selectedDates[0].toDateString();
    
    if (!this.selectedDates[1]) {
      return startDate;
    }
    
    const endDate = this.selectedDates[1].toDateString();
    return `${startDate} to ${endDate}`;
  }
}