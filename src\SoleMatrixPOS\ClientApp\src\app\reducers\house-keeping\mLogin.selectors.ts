import { createSelector } from "@ngrx/store";
import { AppState } from "..";

export const select = (state: AppState) => state.manager;
export const mLogin = createSelector(select, (s) => s.mLoginDto);
export const system = createSelector(select, (s) => s.systemResponse);


export const selectSysConfig = createSelector(select, (s) => s.systemResponse.getSystemStatus);
export const selectReceipTittle = createSelector(select, (s) => s.systemResponse.titleReceipt);
export const selectReceipHeader = createSelector(select, (s) => s.systemResponse.headerReceipt);
export const selectReceipFooter = createSelector(select, (s) => s.systemResponse.footerReceipt);
export const selectReceipLayby = createSelector(select, (s) => s.systemResponse.laybyReceipt);

