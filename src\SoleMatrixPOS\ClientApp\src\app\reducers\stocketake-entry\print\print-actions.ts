import { createAction, props } from "@ngrx/store";
import { BarcodeResultDto, PrintBarcodeDto, PrintInvalidDto } from "src/app/pos-server.generated";

export const init = createAction("[Print] Init");
export const printInvalidBarcode = createAction("[Print] print invalid",props<{payload: PrintInvalidDto}>())
export const printInvalidConfirmed = createAction("[Receipt] Invalid barcode printConfirmed");
