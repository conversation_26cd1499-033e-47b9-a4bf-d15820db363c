using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using SoleMatrixPOS.Domain.Identity.Models;
using SoleMatrixPOS.Identity.Models;

namespace SoleMatrixPOS.Domain.Identity.Interface
{
	public interface ITokenService
	{
		public TokenDto GenerateToken(RegisteredTill till, IList<Claim> claims);
		public TokenDto GenerateRefreshToken(RegisteredTill till);
		public string HashRefreshToken(string payload);
	}
}
