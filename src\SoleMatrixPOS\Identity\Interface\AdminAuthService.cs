using iTextSharp.text;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using SixLabors.ImageSharp;
using SoleMatrixPOS.Domain.Identity.Models;
using SoleMatrixPOS.Email;
using SoleMatrixPOS.Identity.Models;
using SQLitePCL;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace SoleMatrixPOS.Identity.Interface
{
	public class AdminAuthService : IAdminAuthService
	{
		private readonly UserManager<RegisteredTill> _tillManager;
		private readonly RoleManager<TillRole> _roleManager;
		private readonly IEmailService _emailService;
		private readonly IConfiguration _configuration;
		public AdminAuthService(UserManager<RegisteredTill> tillManager, RoleManager<TillRole> roleManager, IEmailService emailService, IConfiguration configuration)
		{
			_tillManager = tillManager;
			_roleManager = roleManager;
			_emailService = emailService;
			_configuration = configuration;
		}

		public async Task<AdminAuthResponse> RegisterUser(RegisteredTill till, string roleName)
		{
			// TODO: check email validity and return response with AdminAuthResponseReason.InvalidEmail

			if (await _tillManager.FindByIdAsync(till.Id) != null)
				return new AdminAuthResponse
				{
					Succeeded = false,
					Message = $"Till ID <{till.Id}> already in use",
					Reason = AdminAuthResponseReason.TillExists
				};
			
			IdentityResult res = await _tillManager.CreateAsync(till);
			if (!res.Succeeded)
				return new AdminAuthResponse
				{
					Succeeded = false,
					Reason = AdminAuthResponseReason.TillCreationError,
					Message = string.Join(", ", res.Errors),
				};

			res = await _tillManager.AddToRoleAsync(till, roleName);
			if (!res.Succeeded)
				return new AdminAuthResponse
				{
					Succeeded = false,
					Reason = AdminAuthResponseReason.RoleAssignmentError,
					Message = string.Join(", ", res.Errors),
				};


			return new AdminAuthResponse
			{
				Succeeded = true,
				Reason = AdminAuthResponseReason.Ok,
				Message = $"Till with ID <{till.Id}> created successfully",
			};
		}

		public async Task<AdminAuthResponse> ResetPasswordViaEmail(string tillId)
		{
			// 1. Find till
			RegisteredTill? till = await _tillManager.FindByIdAsync(tillId);
			if (till == null)
				return new AdminAuthResponse
				{
					Succeeded = false,
					Reason = AdminAuthResponseReason.TillNotFound,
					Message = $"Till with ID <{tillId}> not found"
				};

			// 2. Generate token
			string token = await _tillManager.GeneratePasswordResetTokenAsync(till);

			// 3. Send email
			// TODO: enable default token provider to be configurable to expire in a few minutes, not a day
			EmailResult result = await _emailService.SendPasswordResetEmailAsync(till.Email, till.Id, token, 1440);

			if (!result.Succeeded)
			{
				// Return error with inner error
				return new AdminAuthResponse
				{
					Succeeded = false,
					Reason = AdminAuthResponseReason.EmailSendFailure,
					Message = $"Password reset email couldn't be sent for till with ID <{tillId}>",
					Inner = result
				};
			}

			return new AdminAuthResponse
			{
				Succeeded = true,
				Reason = AdminAuthResponseReason.Ok,
				Message = $"Password was successfully reset for till with ID <{tillId}>"
			};
		}
	}
}
