<div class="container">
	<div class="container-fluid p-5">
	  <div class="row align-items-center">
		<div class="col-1">
		  <i class="fas fa-award fa-2x text-primary align-items-center"></i>
		</div>
		<div class="col-5">
		  <h2 class="text-secondary align-items-center">Redeem Points</h2>
		</div>
		<div class="col-6">
		  <button type="button" class="btn btn-circle float-right" (click)="dismiss('Cross click')">
			<i class="fas fa-times fa-2x"></i>
		  </button>
		</div>
	  </div>
	  <hr style="height: 2px;">
  
	  <div *ngIf="pointsError" class="alert alert-danger">
		{{ pointsError }}
	  </div>
  
	  <div *ngIf="!pointsError">
		<div class="alert alert-info mb-4">
		  <div class="row">
			<div class="col-6">
			  <strong>Customer:</strong> {{ selectedCustomer?.firstname }} {{ selectedCustomer?.surname }}
			</div>
			<div class="col-6 text-right">
			  <strong>Available Points:</strong> {{ selectedCustomer?.clientPoints || 0 }}
			</div>
		  </div>
		  <div class="row mt-2">
			<div class="col-12">
			  <strong>Points Value:</strong> {{ pointsValuePerDollar }} points = $1.00
			</div>
		  </div>
		  <div class="row mt-2">
			<div class="col-12">
			  <strong>Maximum Value:</strong> ${{ maxPointsValue | number:'1.2-2' }}
			</div>
		  </div>
		</div>
  
		<form [formGroup]="form">
		  <label for="points">Points to Redeem</label>
		  <div class="form-group">
			<div class="input-group">
			  <input autofocus name="points" id="points" type="number" 
					class="form-control form-control-lg"
					[ngClass]="{'is-invalid': fieldValidate(points)}" 
					formControlName="Points">
  
			  <div class="input-group-append">
				<button id="remainder" type="button" class="btn btn-outline-default" (click)="useRemainder()">
				  Use Maximum
				</button>
			  </div>
  
			  <div class="invalid-feedback" *ngIf="points.invalid && (points.dirty || points.touched)">
				<div *ngIf="points.errors?.min">
				  Points must be at least 1
				</div>
				<div *ngIf="points.errors?.max">
				  Cannot exceed available points ({{ selectedCustomer?.clientPoints }})
				</div>
				<div *ngIf="points.errors?.required">
				  Points required
				</div>
				<div *ngIf="points.errors?.pattern">
				  Points must be a whole number
				</div>
			  </div>
			</div>
		  </div>
  
		  <div *ngIf="points.valid && points.value" class="alert alert-success mb-4">
			<strong>Value:</strong> ${{ calculateDollarValue(points.value) | number:'1.2-2' }}
		  </div>
  
		  <div class="row justify-content-center p-4">
			<div>
			  <button class="btn btn-outline-default" type="button" (click)="apply()"
					  [disabled]="!form.valid || !selectedCustomer">
				<i class="fas fa-lg fa-fw fa-check-circle text-success mr-2"></i>
				Apply Points
			  </button>
			</div>
		  </div>
		</form>
	  </div>
	</div>
  </div>