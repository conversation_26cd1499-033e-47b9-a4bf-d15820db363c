import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TyroPinpairingModalComponent } from './tyro-pinpairing-modal.component';

describe('TyroPinpairingModalComponent', () => {
  let component: TyroPinpairingModalComponent;
  let fixture: ComponentFixture<TyroPinpairingModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ TyroPinpairingModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TyroPinpairingModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
