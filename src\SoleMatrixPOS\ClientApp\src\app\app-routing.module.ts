import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { HomeComponent } from './home/<USER>/home.component';
import { NotFoundComponent } from './not-found/not-found.component';
import { TestComponent } from './test/test.component';
import { StaffLoginComponent } from './home/<USER>/staff-login.component';
import { ClockInComponent } from './home/<USER>/clock-in.component';
import { FloatRecordEnterComponent } from './home/<USER>/float-record-enter.component';
import { ClockOutComponent } from './home/<USER>/clock-out.component';
import { SendStockComponent } from './pos-transactions/send-stock/send-stock.component';
import { ReceiveStockComponent } from './pos-transactions/receive-stock/receive-stock.component';
import { CustomerClubComponent } from './customer-club/customer-club.component';
import { CustomerClubMemberComponent } from './customer-club/customer-club-member/customer-club-member.component';
import { CustomerClubHistoryComponent } from './customer-club/customer-club-history/customer-club-history.component';
import { HistoryComponent } from './history/history.component';
import { PaymentComponent } from './payment/payment.component';
import { OrderItemComponent } from './customer-specials/customer-orders/order-item/order-item.component';
import { ReturnsComponent } from './returns/returns.component';
import { ReturnsProcessRefundComponent } from './returns/returns-process-refund/returns-process-refund.component';
import { SalesComponent } from './sales/sales.component';
import { SalesProcessPaymentComponent } from './sales/sales-process-payment/sales-process-payment.component';
import { ExchangeInComponent } from './exchange/exchange-in/exchange-in.component';
import { ExchangeOutComponent } from './exchange/exchange-out/exchange-out.component';
import { LaybyPaymentComponent } from './layby-payment/layby-payment.component';
import { StockEnquiryComponent } from './stock-enquiry/stock-enquiry.component';
import { OpenTillComponent } from "./open-till/open-till.component";
import { AccountPaymentComponent } from "./account-payment/account-payment.component";
import { AccountProcessPaymentComponent } from "./account-payment/account-process-payment/account-process-payment.component";
import { PasswordEntryComponent } from './house-keeping/password-entry/password-entry.component';
import { HouseKeepingComponent } from './house-keeping/house-keeping.component';
import { StocktakeEntryComponent } from './stocktake-entry/stocktake-entry.component';
import { CustomerSpecialsComponenet } from './customer-specials/customer-specials.component';
import { GiftVoucherComponent } from './gift-voucher/gift-voucher.component';
import { EndOfDayFloatComponent } from './end-of-day/end-of-day-float/end-of-day-float.component';
import { EndOfDayCashDrawerComponent } from './end-of-day/end-of-day-cash-drawer/end-of-day-cash-drawer.component';
import { EndOfDayCashUpComponent } from './end-of-day/end-of-day-cash-up/end-of-day-cash-up.component';
import { GiftGlanceViewComponent } from './gift-voucher/gift-glance-view/gift-glance-view.component';
import { CustomerQuoteComponent } from './customer-specials/customer-quotes/customer-quote/customer-quote.component';
import { PlaceOnHoldComponent } from './customer-specials/on-hold/place-on-hold/place-on-hold.component';
import { LoginComponent } from './login/login.component';
import { AuthGuard } from './core/guards/auth.guard';
import { StaffStateGuard } from './core/guards/staff-state.guard';
import { PasswordResetComponent } from './password-reset/password-reset.component';
import { PasswordResetSuccessComponent } from './password-reset-success/password-reset-success.component';
import { GiftVoucherIssueComponent } from './gift-voucher/gift-voucher-issue/gift-voucher-issue.component';
import { LaybySearchComponent } from './layby-payment/layby-search/layby-search.component';
import { SupportComponent } from './support/support.component';

const routes: Routes = [	
	{ path: "login", component: LoginComponent },
	{ path: "password-reset", component: PasswordResetComponent},
	{ path: "password-reset-success", component: PasswordResetSuccessComponent}, 
	{ path: "staff-login", component: StaffLoginComponent, canActivate: [AuthGuard] },	
	{ path: "support", component: SupportComponent, canActivate: [AuthGuard]},
	{ path: "home", component: HomeComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "clock-in", component: ClockInComponent, canActivate: [AuthGuard,StaffStateGuard] },
	{ path: "daily-float-enter", component: FloatRecordEnterComponent, canActivate: [AuthGuard,StaffStateGuard] },
	{ path: "clock-out", component: ClockOutComponent, canActivate: [AuthGuard, StaffStateGuard] },

	{ path: "account-payment", component: AccountPaymentComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "end-of-day-float", component: EndOfDayFloatComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "layby/payment", component: LaybySearchComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "end-of-day-cash-drawer", component: EndOfDayCashDrawerComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "end-of-day-cash-up", component: EndOfDayCashUpComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "customer-club/history", component: CustomerClubHistoryComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "exchange/in", component: ExchangeInComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "exchange/out", component: ExchangeOutComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "sales", component: SalesComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "customer-specials", component: CustomerSpecialsComponenet, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "customer-specials/order-item", component: OrderItemComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "sales/payment", component: SalesProcessPaymentComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "returns", component: ReturnsComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "returns/refund", component: ReturnsProcessRefundComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "send-stock", component: SendStockComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "receive-stock", component: ReceiveStockComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "customer-club", component: CustomerClubComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "history", component: HistoryComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "open-till", component: OpenTillComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "payment", component: PaymentComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "account/payment", component: AccountProcessPaymentComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "new-member", component: CustomerClubMemberComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "edit-member", component: CustomerClubMemberComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: 'stock-enquiry', component: StockEnquiryComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "test", component: TestComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: 'house-password', component: PasswordEntryComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: 'house-keeping', component: HouseKeepingComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: 'pos-stocktake-entry', component: StocktakeEntryComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: 'gift/glance', component: GiftGlanceViewComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: 'gift/payment', component: GiftVoucherComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: 'gift/issue', component: GiftVoucherIssueComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: 'customer-specials/quote-item', component: CustomerQuoteComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: 'customer-specials/on-hold', component: PlaceOnHoldComponent, canActivate: [AuthGuard, StaffStateGuard] },
	{ path: "layby", component: LaybyPaymentComponent, canActivate: [AuthGuard, StaffStateGuard] },


	{ path: "", redirectTo: "/login", pathMatch: "full" },
	{ path: '404', component: NotFoundComponent },
	{ path: '**', redirectTo: '/404' },
];

@NgModule({
	imports: [RouterModule.forRoot(routes)],
	exports: [RouterModule],
})
export class AppRoutingModule { }
