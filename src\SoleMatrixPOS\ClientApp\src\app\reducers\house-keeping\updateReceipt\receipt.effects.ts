import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { EMPTY, of } from "rxjs";
import { catchError, map, mergeMap } from "rxjs/operators";
import { HouseKeepingClient } from "src/app/pos-server.generated";
import * as receiptAction from "./receipt.actions";

@Injectable()
export class ReceiptEffects {

    constructor(
        private action$: Actions,
        private managerClient: HouseKeepingClient,
    ) { }


        updateReceipt$ = createEffect(() => this.action$.pipe(
            ofType(receiptAction.updateRec),
            mergeMap((action) => this.managerClient.updateReceipt(action.updateRec)
                .pipe(
                    map(x => receiptAction.updateRecSuccess({result: x }),
                    catchError(err => {
                        return of(receiptAction.updateError({error: err}))
                    })
                    )
                )
            )
        ));



}