import { TestBed, inject } from '@angular/core/testing';

import { TransferTableService } from './transfer-table.service';
import { Store } from '@ngrx/store';
import { Size } from '../../stock-sale-table/classes/size';
//import { AppState } from '../../../../redux/app.store';
import { AppState, state } from '../../stock-sale-table/test-state';
import { Stock } from '../../stock-sale-table/classes/stock';
import { Sale } from '../../stock-sale-table/classes/sale';
import { of } from 'rxjs';

type Trading = "T" | "F";
type Toggle = "on" | "off";

describe('TransferTableService', () => {

  let masterService: TransferTableService;
  let valueServiceSpy: jasmine.SpyObj<Store<AppState>>;

  let sizes = [
    new Size("1"),
    new Size("2"),
    new Size("3"),
    new Size("4")
  ]

  let locations = [
    {
      name: "Location 1",
      trading: "T" as  Trading,
      rank: 1, 
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 2",
      trading: "T" as  Trading,
      rank: 1, 
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 3",
      trading: "T" as  Trading,
      rank: 1, 
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 4",
      trading: "T" as  Trading,
      rank: 1, 
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
  ]

  beforeEach(() => {

    const spy = jasmine.createSpyObj('Store', ['select']);

    TestBed.configureTestingModule({
      providers: [
        TransferTableService,
        { provide: Store, useValue: spy }
      ]
    });

    masterService = TestBed.get(TransferTableService);
    valueServiceSpy = TestBed.get(Store);
  });

  it('should be created', () => {
    const service: TransferTableService = TestBed.get(TransferTableService);
    expect(service).toBeTruthy();
  });

  it('should retrieve main table', inject([TransferTableService], (service: TransferTableService) => {

    const mockObservableSizes = of(sizes);
    const mockObservableLocations = of(locations);

    const spySize = spyOn(service, 'getSize').and.returnValue(mockObservableSizes);
    const spyLocation = spyOn(service, 'getLocation').and.returnValue(mockObservableLocations);

    service.getSize().subscribe((data) => {
      expect(data).toEqual(sizes);
    });
    service.getLocation().subscribe((data) => {
      expect(data).toEqual(locations);
    });

  }));

});
