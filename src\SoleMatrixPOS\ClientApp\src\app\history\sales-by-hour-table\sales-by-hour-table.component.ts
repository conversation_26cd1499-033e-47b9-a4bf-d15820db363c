import { Component, OnInit,Input } from '@angular/core';
import { SalesByHourQueryDto } from 'src/app/pos-server.generated';
import { Observable, from, Subject } from 'rxjs';
import { Store } from '@ngrx/store';
import * as historySelectors from '../../reducers/customer-club/history/history.selector';
import * as historyActions from '../../reducers/customer-club/history/history.actions';
import * as dailyActions from '../../reducers/daily/daily.actions'
import * as dailySelectors from '../../reducers/daily/daily.selectors'
import { AppState } from 'src/app/reducers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { toFinancialString } from 'src/app/utility/math-helpers';

@Component({
  selector: 'pos-sales-by-hour-table',
  templateUrl: './sales-by-hour-table.component.html',
  styleUrls: ['./sales-by-hour-table.component.scss']
})

export class SalesByHourTableComponent implements OnInit {
  public salesByHour$: Observable<SalesByHourQueryDto[]>;
  public refundsByHour$: Observable<SalesByHourQueryDto[]>;
  public currentView: 'sales' | 'refunds' = 'sales';

  constructor(private store: Store<AppState>){}

  ngOnInit(): void {
    this.salesByHour$ = this.store.select(dailySelectors.storeSalesByHour);
    this.refundsByHour$ = this.store.select(dailySelectors.storeRefundsByHour);
    this.store.dispatch(dailyActions.getSalesByHour({}));
    this.store.dispatch(dailyActions.getRefundsByHour({}));
  }
  
  toggleView(): void {
    this.currentView = this.currentView === 'sales' ? 'refunds' : 'sales';
  }
  
  toFinancialString(val) {
    if (val < 0) {
      return `-${toFinancialString(-1*val)}`
    }else {
      return toFinancialString(val)
    }
  }
}
