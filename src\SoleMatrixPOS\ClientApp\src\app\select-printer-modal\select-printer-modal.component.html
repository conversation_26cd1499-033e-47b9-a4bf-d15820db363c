<div class="modal-header">
  <h4 class="modal-title">Select Printer</h4>
  <button type="button" class="close" aria-label="Close" (click)="dismissModal('Cross click')">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <div class="form-group">
    <label for="printerDropdown">Select Printer</label>
    <select id="printerDropdown"
            class="form-control"
            [(ngModel)]="selectedPrinter"
            (change)="onPrinterChange()">
      <option [ngValue]="null">No Printer</option>
      <option *ngFor="let printer of printers" [value]="printer">{{ printer }}</option>
    </select>
  </div>
  <div class="form-group">
    <label for="printerWidthDropdown">Print Width</label>
    <select id="printerWidthDropdown"
            class="form-control"
            [(ngModel)]="printerWidth"
            (change)="onPrinterWidthChange()">
      <option *ngFor="let width of printerWidthOptions" [value]="width">{{ width }}</option>
    </select>
  </div>
  <div *ngIf="testPrintStatusMessage" 
       [ngClass]="{'text-success': testPrintStatusType === 'success', 'text-danger': testPrintStatusType === 'error'}" 
       class="mt-2">
    {{ testPrintStatusMessage }}
  </div>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-info mr-auto" (click)="onTestPrint()">Test Print</button>
  <button type="button" class="btn btn-outline-dark" (click)="continueWithoutPrinter()">Continue Without Printer</button>
  <button type="button" class="btn btn-primary" (click)="save()">Save</button>
</div>
 