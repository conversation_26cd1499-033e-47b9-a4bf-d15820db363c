<div class="footer-wrapper">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-auto back-button">
                <button type="button" class="btn-hidden" (click)="backBtnClick()">
                    <div class="btn btn-lg btn-circle btn-default">
                        <i class="fas fa-caret-left fa-4x color-gradient-grey mr-2"></i>
                    </div>
                </button>
            </div>

            <div class="col-auto">
                <button *ngIf="selectedCustomerClubMember != null" type="button" (click)="launchCustomerClubModal()"
                    class="btn-lg btn-default btn d-flex align-items-center">
                    <i class="fas fa-crown fa-lg text-danger"></i>
                    <div class="ml-3">
                        <h4 class="mb-0">{{selectedCustomerClubMember.firstname}}<br />
                            {{selectedCustomerClubMember.surname}}</h4>
                        <small class="text-muted">Points: {{selectedCustomerClubMember.clientPoints || 0}}</small>
                    </div>
                </button>
                <button *ngIf="selectedCustomerClubMember == null" type="button" (click)="launchCustomerClubModal()"
                    class="btn-lg btn-default btn d-flex align-items-center">
                    <i class="fas fa-crown fa-lg"></i>
                    <h4 class="ml-3">Customer<br />
                        Club</h4>
                </button>
            </div>

            <div class="col-auto" *ngIf="!(isExchangeMode$ | async)">
                <button type="button" (click)="launchLayby()"
                    class="btn-lg btn-default btn d-flex align-items-center">
                    <i [ngClass]="{'text-danger': laybyActive$ | async}" class="fas fa-shopping-cart fa-lg"></i>
                    <h4 class="ml-3">Layby</h4>
                </button>
            </div>

            <div class="col-auto d-flex">
                <button type="button" class="btn-lg btn-default btn d-flex align-items-center"
                    (click)="launchCommentModal()">
                    <i [ngClass]="{'text-danger': saleComment}" class="fas fa-comment fa-lg"></i>
                    <h4 class="ml-3">Note</h4>
                </button>
                <button *ngIf="saleComment" type="button" 
                    (click)="removeComment()"
                    class="btn-lg btn-default btn d-flex align-items-center"
                    ngbTooltip="Remove sale note">
                    <i class="fas fa-times-circle fa-lg"></i>
                </button>
            </div>

            <div class="col-auto ml-auto">
            </div>

            <div class="col-auto">
                <button type="button" class="btn-lg btn-default btn d-flex align-items-center"
                    [disabled]="(isExchangeMode$ | async)" [ngClass]="{'disabled': (isExchangeMode$ | async)}">
                    <i class="fas fa-pause fa-lg text-blue" style="color:#01A0C7;"></i>
                    <h4 class="ml-3">Suspend<br>Order</h4>
                </button>
            </div>

            <div *ngIf="(laybyActive && softCreditLimit === 'T') || readyToProcess" class="col-auto next-button">
                <button type="button" class="btn-hidden" (click)="processBtnClick()">
                    <div class="btn btn-lg btn-circle btn-default">
                        <i class="fas fa-caret-right fa-4x color-gradient-green ml-2"></i>
                    </div>
                    <h4 class="text-light">Process</h4>
                </button>
            </div>
        </div>
    </div>
</div>