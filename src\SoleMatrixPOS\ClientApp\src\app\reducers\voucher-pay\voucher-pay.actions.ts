import { createAction, props } from "@ngrx/store";
import { GiftVoucherResultDto } from "src/app/pos-server.generated";
import { Payment } from "src/app/payment/payment.service";

export const init = createAction('[Voucher-Pay] init');

export const searchVoucher = createAction('[Voucher-Pay] Search Voucher', props<{code: string, isCreditNote: boolean}>());
export const searchVoucherResponse = createAction('[Voucher-Pay] Search Voucher Response', props<{giftVoucher: GiftVoucherResultDto}>());

// New actions for tracking Voucher usage
export const trackVoucherPayment = createAction(
  '[Voucher-Pay] Track Voucher Payment',
  props<{payment: Payment, transactionId: string, voucherType: 'giftcard' | 'creditnote'}>()
);

export const clearVoucherLookup = createAction('[Voucher-Pay] Clear Voucher Lookup');

export const removeVoucherPayment = createAction(
  '[Voucher-Pay] Remove Voucher Payment',
  props<{payment: Payment, transactionId: string, voucherType: 'giftcard' | 'creditnote'}>()
);

export const resetTransactionPayments = createAction(
  '[Voucher-Pay] Reset Transaction Payments', 
  props<{transactionId: string}>()
);
// ...existing code...

export const clearAllVoucherPayments = createAction(
    '[Voucher-Pay] Clear All Voucher Payments'
  );