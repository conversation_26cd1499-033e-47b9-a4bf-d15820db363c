<div class="card">
    <table class="table table-striped">
        <thead>
            <tr>
                <th>From</th>
                <th>To</th>
                <th>Size</th>
                <th>Qty</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            <ng-container *ngFor="let transfer of transfers$ | async">
                <tr (mouseenter) ="highlightTransfer(transfer.id)" (mouseleave) ="highlightTransfer(transfer.id)" >
                    <td class="highlight-from">
                        {{transfer.from}}
                    </td>
                    <td class="highlight-to">
                        {{transfer.to}}
                    </td>
                    <td>
                        {{transfer.size}}
                    </td>
                    <td>
                        {{transfer.qty}} 
                    </td>   
                    <td>
                        <button (click)="highlightTransfer(transfer.id); removeTransfer(transfer.id)" class="btn btn-danger">Remove</button>
                    </td>        
                </tr>               
            </ng-container>
            <td></td>
        </tbody>
    </table>        
</div>