import { Component } from '@angular/core';
import { StockSaleTableService } from './stock-sale-table.service';
import { withLatestFrom } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { Size } from '../classes/size';
import { Location } from '../classes/location.model'

@Component({
  selector: 'stock-sale-table',
  templateUrl: './stock-sale-table.component.html',
  styleUrls: ['./stock-sale-table.component.scss']
})
export class StockSaleTableComponent {

  public totalSales$: Observable<number[]>
  public totalStocks$: Observable<number[]>
  public locations$: Observable<Location[]>
  public sizes$: Observable<Size[]>
  public totalStock$;
  public totalSale$;

  constructor(public mainTableService: StockSaleTableService) {
    this.locations$ = this.mainTableService.locations$;
    this.sizes$ = this.mainTableService.sizes$;
    this.totalStock$ = this.sumTotalStock();
    this.totalSale$ = this.sumTotalSale();
  }

  /**
   * sumTotalStock
   */
  public sumTotalStock(): number[] {

    this.totalStocks$ = this.calculateTotalStocks(this.locations$, this.sizes$)

    return [];
  }

  /**
  * sumTotalSale
  */
  public sumTotalSale(): number[] {

    this.totalSales$ = this.calculateTotalSales(this.locations$, this.sizes$)

    return [];
  }

  /**
   * Creates an array of Total objects that package the total
   * stock
   * @param   {Observable<Site[]>} sites 
   * @param   {Observable<Site[]>} sizes 
   * @return  {Observable<Total[]>} 
   */
  public calculateTotalStocks(sites: Observable<Location[]>, sizes: Observable<Size[]>): Observable<number[]> {

    let totals = [];

    sites.pipe(
      withLatestFrom(sizes)
    ).subscribe(
      // siteSize parameter to combine two data arrays include Size and Site
      (siteSize) => {
        // check conditions in sizeSite array data
        if (siteSize[0].length < 1 || siteSize[1].length < 1) {
        } else {
          let locations = siteSize[0];
          let sizes = siteSize[1];
          // Loops to calculate totalStock 
          for (let i = 0; i < sizes.length; i++) {
            let total = 0;
            for (let j = 0; j < locations.length; j++) {
              if (locations[j].trading === 'T') {
                total += locations[j].stock[i].qty;
              }
            }
            totals.push(total);
          }
        }
      }
    );

    return of(totals); // Return an observable to compensate for async
  }

  /**
   * Creates an array of Total objects that package the total
   * sale
   * @param   {Observable<Site[]>} sites 
   * @param   {Observable<Site[]>} sizes 
   * @return  {Observable<Total[]>} 
   */
  public calculateTotalSales(sites: Observable<Location[]>, sizes: Observable<Size[]>): Observable<number[]> {

    let totals = [];

    sites.pipe(
      withLatestFrom(sizes)
    ).subscribe(
      // siteSize parameter to combine two data arrays include Size and Site
      (siteSize) => {
        // check conditions in sizeSite array data
        if (siteSize[0].length < 1 || siteSize[1].length < 1) {
        } else {
          let locations = siteSize[0];
          let sizes = siteSize[1];
          // Loops to calculate totalStock 
          for (let i = 0; i < sizes.length; i++) {
            let total = 0;
            for (let j = 0; j < locations.length; j++) {
              if (locations[j].trading === 'T') {
                total += locations[j].sales[i].qty;
              }
            }
            totals.push(total);
          }
        }
      }
    );

    return of(totals); // Return an observable to compensate for async
  }

}
