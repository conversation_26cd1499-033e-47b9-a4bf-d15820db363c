import { Component, OnInit, Input } from '@angular/core';
import { Store } from '@ngrx/store';
import { FormBuilder, Validators } from "@angular/forms";
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { AppState } from 'src/app/reducers';
import { CtransDto, CustomerMateDto } from 'src/app/pos-server.generated';
import { Router } from '@angular/router';
import * as accountPaymentActions from '../../reducers/account-payments/account-payment.actions'
import { financialRound, PaymentType } from 'src/app/payment/payment.service';
import { toFinancialString } from 'src/app/utility/math-helpers';
import { Payment } from 'src/app/payment/payment.service';
import * as paymentActions from '../../reducers/sales/payment/payment.actions'
@Component({
  selector: 'pos-customer-modal',
  templateUrl: './customer-modal.component.html',
  styleUrls: ['./customer-modal.component.scss']
})
export class CustomerModalComponent implements OnInit {
  @Input() public customer: CustomerMateDto;
  // sale related input
  @Input() public sale: boolean;
  @Input() public isRefund: boolean;
  @Input() public amountDue: number;
  @Input() public type: PaymentType;

  paymentForm = this.fb.group({
    // TODO: Why doesnt this pattern work?
    amount: [0.01, [Validators.min(0.01), Validators.pattern("[^-]+")]],
  });

  get amount() { return this.paymentForm.get('amount'); }


  constructor(private fb: FormBuilder, public activeModal: NgbActiveModal,
    private store: Store<AppState>, private router: Router,) { }

  ngOnInit() {
    if (this.sale) {
      this.useRemainder();// Opens with the remaining amount due rather than 0.01
    } else {
      this.amount.setValue(this.sumBalances(this.customer) > 0 ? this.sumBalances(this.customer) : 0);
    }
  }

  close() {
    this.activeModal.close()
  }

  dismiss(reason: string) {
    this.activeModal.dismiss(reason);
  }

  submitForm(customer: CustomerMateDto) {
    if (this.paymentForm.invalid) {
      return;
    }
    console.log("Submit Form")
    // console.log(this.paymentForm.value.amount)
    // console.log(customer)
    let customerPayment = new CustomerPayment();
    customerPayment.amount = financialRound(this.paymentForm.value.amount)
    customerPayment.customer = customer
    // console.log(`Sale = ${this.sale}`)
    // console.log(`Amount = ${this.paymentForm.value.amount}`)
    // Different actions if sales or account payment
    if (this.sale) {
      console.log("before")
      this.store.dispatch(paymentActions.addCtrans({ payload: this.createCtrans(customer, this.paymentForm.value.amount) }))
      console.log("after")
      // Include customer details in the closed result
      this.activeModal.close({
        type: this.type,
        amount: this.paymentForm.value.amount,
        customerDetails: { // Add customer info here
          clientCode: customer.customerCode,
          name: customer.customerName
        }
      } as unknown as Payment)
    } else {
      this.store.dispatch(accountPaymentActions.storeSelectedCustomer({ payload: customerPayment }))
      this.dismiss("Form submitted")
      this.router.navigateByUrl("/account/payment");
    }
  }

  toFinancialString(val) {
    if (val < 0) {
      return `-${toFinancialString(-1 * val)}`
    } else {
      return toFinancialString(val)
    }
  }
  // Creates an uncompleted ctrans object
  createCtrans(customer: CustomerMateDto, amount: number): CtransDto {
    const cTrans = {
      customerCode: customer.customerCode,
      transactionType: 1,
      ctransStatus: "O",
      subtransType: 1,
      age: 0,
      outstandingAmount: amount,
      amount: amount,
    } as CtransDto
    console.log(cTrans)
    return cTrans
  }

  sumBalances(customer: CustomerMateDto): number {
    return customer.currentBalance + customer.thirtyDayBalance + customer.sixtyDayBalance + customer.ninetyDayBalance;
  }

  useRemainder() {
    this.amount.setValue(this.amountDue);
  }

  calculateNewBalance(): number { // for account-payment or refund to account
    const currentAmount = this.paymentForm.get('amount').value || 0;
    return this.sumBalances(this.customer) - currentAmount;
  }

  calculateNewBalanceForSale(): number { // for paying a sale with account
    const currentAmount = this.paymentForm.get('amount').value || 0;
    return this.sumBalances(this.customer) + currentAmount;
  }

}

export class CustomerPayment {
  customer: CustomerMateDto;
  amount: Number
}

