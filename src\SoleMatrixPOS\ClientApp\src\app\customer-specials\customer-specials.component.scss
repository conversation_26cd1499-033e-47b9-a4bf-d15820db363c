.tab-group {
    overflow: hidden; // Ensures the child button corners don't stick out
    border-top-left-radius: 0.25rem; // Match card's border-radius
    border-top-right-radius: 0.25rem; // Match card's border-radius
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.tab-button {
    transition: all 0.3s ease;
    border: none; // Remove default button border
    background: transparent;

    &:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }
}

.active-tab {
    background-color: white;
    color: black;
    border-bottom: 2px solid #007bff;
    font-weight: 500;
}

.inactive-tab {
    background-color: #2c3e50; // Dark blue color
    color: white;
    border-bottom: none; // Ensure no bottom border for inactive tabs
}

.tab-button i {
    margin-right: 8px; // Space between icon and text
}

// Ensure the card has no top margin if it's directly under the tabs
.card {
    margin-top: 0 !important;
}

:host {
    display: flex;
    flex-direction: column;
    height: 100vh; // Or 100% if a parent container defines height
    width: 100%;
}

.customer-specials-wrapper {
    display: flex;
    flex-direction: column;
    flex-grow: 1; // Allows this wrapper to grow and fill space
    width: 100%;
}

.form-area-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center; // Centers content vertically
    align-items: center; // Centers content horizontally
    flex-grow: 1; // Allows this area to take up available space
    width: 100%;
    padding: 1rem; // Optional padding around the centered content
}

.form-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: 4px;

    &:focus {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
}

.button-group {
    margin-top: 2rem;

    .btn {
        min-width: 150px;

        &:not(:last-child) {
            margin-right: 1rem;
        }
    }
}

// Modal styles
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.close {
    border: none;
    background: transparent;
    font-size: 1.5rem;

    &:hover {
        color: #dc3545;
    }
}

// Responsive adjustments
@media (max-width: 768px) {
    .button-group {
        .btn {
            width: 100%;
            margin-bottom: 1rem;

            &:not(:last-child) {
                margin-right: 0;
            }
        }
    }
}

.search-section {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
}