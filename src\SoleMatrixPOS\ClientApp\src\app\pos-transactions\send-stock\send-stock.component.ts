import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AppState } from '../../reducers';
import { FormControl } from '@angular/forms';
import { ItemToProcess, SendStockState } from '../../reducers/send-stock/send-stock.reducer';
import { Observable, Subscription, combineLatest } from 'rxjs';
import { map, filter, take } from 'rxjs/operators';
import * as sendStockActions from '../../reducers/send-stock/send-stock.actions';
import { LocationDto, SendStockItemDto, SendStockRequestDto, StockItemDto, TransferReasonDto, WriteTranslogDto, TranslogDto } from 'src/app/pos-server.generated';
import { ConfirmDialogService } from 'src/app/confirm-modal/confirm-dialog.service';
import * as sendStockSelector from 'src/app/reducers/send-stock/send-stock.selectors';
import * as itemCartAction from '../../reducers/stocketake-entry/itemCart/itemCart.actions'
import * as itemCartSelector from '../../reducers/stocketake-entry/itemCart/itemCart.selectors'
import { ProcessPaymentModalComponent } from 'src/app/payment/process-payment-modal/process-payment-modal.component';
import Swal from 'sweetalert2';
import { CreateErrorModal } from 'src/app/error-modal/error-modal.component';
import { StockCartItem } from 'src/app/reducers/stocketake-entry/itemCart/itemCart.reducers';
import { PrintingService } from 'src/app/printing/printing.service';
import * as SysConfigSelectors from '../../reducers/sys-config/sys-config.selectors';

const SEND_STOCK_TRANSTYPE = 8;

@Component({
	selector: 'pos-send-stock',
	templateUrl: './send-stock.component.html',
	styleUrls: ['./send-stock.component.scss']
})
export class SendStockComponent implements OnInit, OnDestroy {


	cart$: Observable<StockCartItem[]>;
	cart: StockCartItem[];
	readyToProcess: boolean = false;

	constructor(
		private modalService: NgbModal,
		private router: Router,
		private store: Store<AppState>,
		private confirmService: ConfirmDialogService,
		private printService: PrintingService
	) { }

	transferStarted$: Observable<boolean>;
	showSelectors$: Observable<boolean>;
	getReason$: Observable<TransferReasonDto[]>;
	getDestination$: Observable<LocationDto[]>;
	getLockStore$: Observable<LocationDto>;
	getLocation$: Observable<LocationDto>;
	getLocation: LocationDto;
	getTransferNumber$: Observable<string>;
	transferNumber: string;
	saveReason: string;
	selectedDestination: LocationDto;

	setReason = new FormControl('');
	setDestination = new FormControl('');
	setTranferNumber = new FormControl('');

	subscriptions: Subscription[] = [];
	sendStockState$: Observable<SendStockState>;
	total: number;

	private translogItems: TranslogDto[] = [];
	sendingStoreName: string;

	ngOnInit() {

		//init to get transfer reason
		this.store.dispatch(sendStockActions.init());
		this.store.dispatch(itemCartAction.init());

		//access send stock store state
		this.sendStockState$ = this.store.select(s => s.sendStock);

		//get reason to transfer
		this.getReason$ = this.store.select(sendStockSelector.getReason);


		// Add new observables for UI state
		this.transferStarted$ = combineLatest([
			this.store.select(sendStockSelector.getTransferNo),
			this.store.select(sendStockSelector.setDestination)
		]).pipe(
			map(([transferNo, destination]) => !!(transferNo && destination))
		);

		this.showSelectors$ = this.transferStarted$.pipe(
			map(started => !started)
		);

		//init or dispatch or request to store location or destination
		this.getDestination$ = this.store.select(sendStockSelector.getDestination);

		//get lock store
		this.getLockStore$ = this.store.select(sendStockSelector.getLockStore);

		//get selected destination
		this.getLocation$ = this.store.select(sendStockSelector.setDestination);
		this.getLocation$.subscribe(loc => {
			this.getLocation = loc;
			console.log("Selected location: " + JSON.stringify(loc));
		})

		//request the transfer number
		//get the transfer Number 
		this.getTransferNumber$ = this.store.select(sendStockSelector.getTransferNo);
		this.getTransferNumber$.subscribe(transNo => {
			this.transferNumber = transNo;
		})

		this.store.select(itemCartSelector.UnitTotal).subscribe(total => this.total = total);

		// Subscribe to store name from sysConfig
		this.store.select(SysConfigSelectors.selectSysConfig)
			.subscribe(config => {
				this.sendingStoreName = config && config.storeName || '';
			});

		this.subscribeToState();
		this.subscribeToControls();
	}

	subscribeToState() {
		this.subscriptions.push(this.sendStockState$.subscribe(s => {
			this.setReason.setValue(s.reason, { emitEvent: false });
			this.setDestination.setValue(s.destination, { emitEvent: false });
		}));

		this.cart$ = this.store.select(itemCartSelector.cartItem);

		this.subscriptions.push(this.cart$.subscribe(cart => {
			this.cart = cart;
			// console.log("cart items: " + JSON.stringify(cart));
		}));


		this.store.select(itemCartSelector.noItems).subscribe(n => this.readyToProcess = (n > 0));
		//this.store.select(sendStockSelector.getTransferNo).subscribe(n => this.readyToProcess = (parseInt(n) > 0));


	}

	subscribeToControls() {
		this.subscriptions.push(this.setReason.valueChanges.subscribe(v => {

			this.store.dispatch(sendStockActions.setReason({ reason: v }));
		}));

		this.subscriptions.push(this.setDestination.valueChanges.subscribe(v => {

			//set reason after user select reason
			//console.log("Set destination" + JSON.stringify(v));
			this.store.dispatch(sendStockActions.setDestination({ destination: v }));
		}));

		this.subscriptions.push(
			this.store.select(sendStockSelector.transCompleted)
				.pipe(
					// Take only the first true value
					filter(trans => trans === true),
					take(1)
				)
				.subscribe(trans => {
					if (trans) {
						this.TransactionCompleted();
					}
				})
		);
	}

	resetTransfer() {
		this.setReason.setValue(null, { emitEvent: false });
		this.setDestination.setValue(null, { emitEvent: false });

		this.store.dispatch(sendStockActions.resetTransfer());

		// Re-initialize to get fresh state
		this.store.dispatch(sendStockActions.init());
	}

	ngOnDestroy(): void {
		// Clean up all subscriptions
		for (let sub of this.subscriptions) {
			sub.unsubscribe();
		}
	}

	processSendStock() {
		if (this.readyToProcess) {
			this.openConfimationDialog();
		} else {
			CreateErrorModal(this.modalService, false, "Oops!, you need to add at least one item to process!");
		}
	}

	openConfimationDialog() {
		let modalRef = this.modalService.open(ProcessPaymentModalComponent, { size: 'xl', centered: true });

		modalRef.componentInstance.headerText = 'Ok to process?';
		modalRef.componentInstance.processButtonText = 'Process Stock Transfer';

		modalRef.result.then((result) => {
			console.log("Transaction is submitting...");
			// Dispatch an action with the request
			let trans: SendStockRequestDto = this.createTransaction();
			this.store.dispatch(sendStockActions.submit({ sendStock: trans }));
		}).catch(res => console.log("Error occurred: ", res));
	}


	createTransaction(): SendStockRequestDto {
		let lineNo: number = 0;
		let stockItem: SendStockItemDto[] = [];
		this.translogItems = [];

		//get all items
		for (let i = 0; i < this.cart.length; i++) {
			let item = this.cart[i];
			stockItem.push(ItemToProcess(item, lineNo));

			this.translogItems.push({
				styleCode: item.stockItem.styleCode,
				colourCode: item.stockItem.colourCode,
				sizeCode: item.stockItem.size,
				quantity: item.quantity,
				sellingPrice: item.stockItem.price,
				lineNo: lineNo,
				clientCode: null,
				transNo: null,
				gst: null,
				discReasonCode: null,
				nettSelling: null
			} as TranslogDto);

			lineNo++;
		}

		const logTranslog: WriteTranslogDto = {
			lineNo: lineNo,
			quantity: this.total
		}

		return {
			transType: SEND_STOCK_TRANSTYPE,
			transferNumber: this.transferNumber,
			destinationLocationCode: this.getLocation.storeId,
			stockItems: stockItem,
			translogDto: logTranslog,
			translogItems: this.translogItems
		} as SendStockRequestDto;
	}


	itemLookup(item: StockItemDto) {
		this.addItemToCart(item);
	}

	addItemToCart(item: StockItemDto) {
		this.store.dispatch(itemCartAction.addStockItem({ stockItem: item }));
	}

	async TransactionCompleted() {
		if (!this.transferNumber || !this.getLocation || !this.getLocation.storeName || !this.sendingStoreName) {
			console.error('Missing required data for printing');
			this.router.navigateByUrl("/home");
			return;
		}

		const completedTransferNumber = this.transferNumber;

		await this.printService.printTransferNumber(
			completedTransferNumber,
			this.getLocation.storeName,
			this.translogItems,
			this.sendingStoreName
		);

		console.log('Print Transfer Number success');

		await Swal.fire({
			title: "Transaction Completed, Transfer Number: " + completedTransferNumber,
			type: "success",
			text: `Transaction was successfully submitted.`
		});

		this.store.dispatch(sendStockActions.init());
		this.router.navigateByUrl("/home");
	}
}
