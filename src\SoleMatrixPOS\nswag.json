{"runtime": "Net60", "defaultVariables": null, "documentGenerator": {"aspNetCoreToOpenApi": {"project": "SoleMatrixPOS.csproj", "msBuildProjectExtensionsPath": null, "configuration": "$(Configuration)", "runtime": null, "targetFramework": null, "noBuild": true, "verbose": true, "workingDirectory": null, "requireParametersWithoutDefault": false, "apiGroupNames": null, "defaultPropertyNameHandling": "<PERSON><PERSON><PERSON>", "defaultReferenceTypeNullHandling": "<PERSON><PERSON>", "defaultDictionaryValueReferenceTypeNullHandling": "NotNull", "defaultResponseReferenceTypeNullHandling": "NotNull", "defaultEnumHandling": "Integer", "flattenInheritanceHierarchy": false, "generateKnownTypes": true, "generateEnumMappingDescription": false, "generateXmlObjects": false, "generateAbstractProperties": false, "generateAbstractSchemas": true, "ignoreObsoleteProperties": false, "allowReferencesWithProperties": false, "excludedTypeNames": [], "serviceHost": null, "serviceBasePath": null, "serviceSchemes": [], "infoTitle": "My Title", "infoDescription": null, "infoVersion": "1.0.0", "documentTemplate": null, "documentProcessorTypes": [], "operationProcessorTypes": [], "typeNameGeneratorType": null, "schemaNameGeneratorType": null, "contractResolverType": null, "serializerSettingsType": null, "useDocumentProvider": true, "documentName": "v1", "aspNetCoreEnvironment": null, "createWebHostBuilderMethod": null, "startupType": null, "allowNullableBodyParameters": true, "output": "wwwroot/SoleMatePOS-api.json", "outputType": "Swagger2", "assemblyPaths": [], "assemblyConfig": null, "referencePaths": [], "useNuGetCache": false}}, "codeGenerators": {"openApiToTypeScriptClient": {"className": "{controller}Client", "moduleName": "", "namespace": "", "typeScriptVersion": 2.7, "template": "Angular", "promiseType": "Promise", "httpClass": "HttpClient", "useSingletonProvider": false, "injectionTokenType": "InjectionToken", "rxJsVersion": 6.0, "dateTimeType": "Date", "nullValue": "<PERSON><PERSON>", "generateClientClasses": true, "UseNullCoalesceExpression": false, "generateClientInterfaces": true, "generateOptionalParameters": false, "exportTypes": true, "wrapDtoExceptions": false, "exceptionClass": "ApiException", "clientBaseClass": null, "wrapResponses": false, "wrapResponseMethods": [], "generateResponseClasses": true, "responseClass": "SwaggerResponse", "protectedMethods": [], "configurationClass": null, "useTransformOptionsMethod": false, "useTransformResultMethod": false, "generateDtoTypes": true, "operationGenerationMode": "MultipleClientsFromOperationId", "markOptionalProperties": true, "generateCloneMethod": false, "typeStyle": "Interface", "classTypes": [], "extendedClasses": [], "extensionCode": null, "generateDefaultValues": true, "excludedTypeNames": [], "excludedParameterNames": [], "handleReferences": false, "generateConstructorInterface": true, "convertConstructorInterfaceData": false, "importRequiredTypes": true, "useGetBaseUrlMethod": false, "baseUrlTokenName": "API_BASE_URL", "queryNullValue": "", "inlineNamedDictionaries": false, "inlineNamedAny": false, "templateDirectory": null, "typeNameGeneratorType": null, "propertyNameGeneratorType": null, "enumNameGeneratorType": null, "serviceHost": null, "serviceSchemes": null, "output": "ClientApp/src/app/pos-server.generated.ts"}}}