<div class="container">
	<!-- Header row -->
	<div class="row justify-content-center bg-primary p-2 mb-3">
		<h5 class="justify-content-center">Tyro Configuration</h5>
	</div>

	<!-- "Pair Tyro Terminal" button to open pairing modal -->
	<button type="button" class="btn btn-link mb-3" (click)="openPairing()">
		Pair Tyro Terminal
	</button>

	<!-- Use an async pipe to wait for config from the store -->
	<div *ngIf="tyroConfig$ | async as config">
		<!-- Template-driven form -->
		<form #tyroConfigForm="ngForm" (ngSubmit)="saveTyroConfig(tyroConfigForm.value)">

			<div class="row justify-content-center mt-3 mb-3">
				<div class="col-md-6">
					<table class="table table-striped">
						<thead>
							<tr>
								<td></td>
								<td>Yes</td>
								<td>No</td>
							</tr>
						</thead>
						<tbody>
							<!-- Integrated Receipt -->
							<tr>
								<td>Integrated Receipt</td>
								<td>
									<input type="radio"
										   id="integratedReceipt-on"
										   name="integrated_Receipt"
										   value="T"
										   [ngModel]="integratedReceipt" />
								</td>
								<td>
									<input type="radio"
										   id="integratedReceipt-off"
										   name="integrated_Receipt"
										   value="F"
										   [ngModel]="integratedReceipt" />
								</td>
							</tr>

							<!-- Integrated Surcharge -->
							<tr>
								<td>Integrated Surcharge</td>
								<td>
									<input type="radio"
										   id="integratedSurcharge-on"
										   name="integrated_Surcharge"
										   value="T"
										   [ngModel]="integratedSurcharge" />
								</td>
								<td>
									<input type="radio"
										   id="integratedSurcharge-off"
										   name="integrated_Surcharge"
										   value="F"
										   [ngModel]="integratedSurcharge" />
								</td>
							</tr>

							<!-- Add more toggles here if needed -->
						</tbody>
					</table>
				</div>
			</div>

			<!-- Buttons: Request logs, Save config -->
			<div class="row justify-content-center mt-3 mb-3">
				<div class="col text-right">
					<button type="button"
							class="btn btn-secondary mr-3"
							(click)="requestIClientLogs()">
						Request iClient Logs
					</button>
					<button class="btn btn-outline-primary"
							type="submit">
						<i class="fas fa-lg fa-fw fa-check-circle text-success mr-2"></i>
						Save Tyro Config
					</button>
				</div>
			</div>
		</form>
	</div>

	<iframe id="iClientFrame" src=""
			style="height: 400px; width: 400px; display: none">
	</iframe>
</div>
