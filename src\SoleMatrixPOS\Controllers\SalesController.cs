using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Stock;
using SoleMatrixPOS.Application.Stock.Dtos;
using SoleMatrixPOS.Application.Stock.ReceiveStock.Queries;
using SoleMatrixPOS.Filters;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[RequireStaffCodeFilter]
	[ApiController]
	public class SalesController : ControllerBase
	{
		private readonly IMediator _mediator;

		public SalesController(IMediator mediator)
		{
			_mediator = mediator;
		}
	}
}
