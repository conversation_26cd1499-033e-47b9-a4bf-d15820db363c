import { AppState } from '../../index';
import { createSelector } from '@ngrx/store';
import { PointsUpdateStatus } from './customer-update.reducer';

export const select = (state: AppState) => state.customerUpdate;

export const status = createSelector(select, s => s.memberUpdateStatus);

export const customer = createSelector(select, s => s.member);

// New selectors for points update functionality
export const pointsUpdateStatus = createSelector(select, s => s.pointsUpdateStatus);

export const lastUpdatedClientCode = createSelector(select, s => s.lastUpdatedClientCode);

export const newPointsTotal = createSelector(select, s => s.newPointsTotal);

export const updateError = createSelector(select, s => s.error);

export const isPointsUpdateInProgress = createSelector(
  pointsUpdateStatus,
  status => status === PointsUpdateStatus.Loading
);

export const isPointsUpdateSuccess = createSelector(
  pointsUpdateStatus,
  status => status === PointsUpdateStatus.Success
);