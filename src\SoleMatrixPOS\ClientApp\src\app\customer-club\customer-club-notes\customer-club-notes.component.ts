import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { Observable, from, Subject } from 'rxjs';
import { CustomerClubDto, NoteSearchRequestDto, NoteDto } from 'src/app/pos-server.generated';
import { on, Store } from '@ngrx/store';
import { AppState } from '../../reducers';
import * as noteSearchActions from '../../reducers/customer-club/notes/notes.actions';
import * as noteSearchSelectors from '../../reducers/customer-club/notes/notes.selectors';

import { takeWhile, debounceTime, distinctUntilChanged, map } from 'rxjs/operators';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NoteModalComponent } from '../note-modal/note-modal.component';

@Component({
  selector: 'pos-customer-club-notes',
  templateUrl: './customer-club-notes.component.html',
  styleUrls: ['./customer-club-notes.component.scss']
})
export class CustomerClubNotesComponent implements OnInit {

  @Input() options: NoteSearchRequestDto;
	@Output() searchChanged = new EventEmitter<NoteSearchRequestDto>();

  public term: string;
  
  public notes$: Observable<NoteDto[]>;
	public searchOptions$: Observable<NoteSearchRequestDto>;
	public deletionResponse$: Observable<boolean>;
	public addResponse$: Observable<boolean>;

  private searchTerm$ = new Subject<string>();
  private _member: CustomerClubDto; // Probably just need the id- don't need access to the Dto itself as we'll just be making a call with it's id
	private alive = true;

  @Input()
  set member(member: CustomerClubDto) {
    this._member = member;
  }
  
  constructor(private modalService: NgbModal, private store: Store<AppState>) 
  { 

  }

	ngOnInit() {
    this.notes$ = this.store.select(noteSearchSelectors.searchedNotes);
    this.deletionResponse$ = this.store.select(noteSearchSelectors.deleteResult);
    this.addResponse$ = this.store.select(noteSearchSelectors.addResult);

    this.deletionResponse$.subscribe(s=>{
      this.search();
    });

    this.addResponse$.subscribe(s=>{
      this.search();
    });

		this.searchTerm$.pipe(
			takeWhile(() => this.alive),
			debounceTime(500),
			distinctUntilChanged(),
			map(() => this.searchChanged.emit({  
        searchString:  this.term,
        clientNumber: this._member.clientCode
			})),
		).subscribe();
 
		this.search();
	}

	ngOnDestroy(): void {
		this.alive = false;
	}

  addNote() {
    const modalRef = this.modalService.open(NoteModalComponent, { size: 'lg', centered: true });
    modalRef.componentInstance.name = 'AddNote';
    modalRef.componentInstance.customerClubNumber = this._member.clientCode;
    modalRef.result.then((result) => {
      if (result) {
        console.log('result from modal:', result);
      }
    // need to await update or trigger search on success
      //this.search();
    });
  }

  deleteNote(note: any) {
    this.store.dispatch(noteSearchActions.deleteNote({ payload: note}));
    // need to await update or trigger search on success
    //this.search();
  }

  search() {
		this.searchTerm$.next(this.term);
		this.doSearch(this.options);
	}

	doSearch(options: NoteSearchRequestDto) {
		this.store.dispatch(noteSearchActions.search({ searchParams: {  
      searchString:  this.term,
      clientNumber: this._member.clientCode
    }}));
	}

}
