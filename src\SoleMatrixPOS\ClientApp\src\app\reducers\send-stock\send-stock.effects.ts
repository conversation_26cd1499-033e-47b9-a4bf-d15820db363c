import { Injectable } from '@angular/core';
import { Actions, Effect, ofType, createEffect } from '@ngrx/effects';
import * as sendStockActions from './send-stock.actions';
import { catchError, filter, map, mergeMap, tap, withLatestFrom } from 'rxjs/operators';
import { GenerateTransferNumberRequest, SendStockClient, SendStockRequestDto } from '../../pos-server.generated';
import { EMPTY } from 'rxjs';
import { Store } from '@ngrx/store';
import { AppState } from '../index';

@Injectable()
export class SendStockEffects {
    constructor(
        private actions$: Actions,
        private sendStockClient: SendStockClient,
        private store: Store<AppState>
    ) { }

    // init - fetch transfer reasons
    init$ = createEffect(() => this.actions$.pipe(
        ofType(sendStockActions.init),
        mergeMap((action) => this.sendStockClient.getTransferReasonList()
            .pipe(
                catchError(() => EMPTY),
                map(response => sendStockActions.setReasonsLookup({ reasons: response }))
            )
        )
    ));

    // after reason has been set, we can lookup some destinations
    afterReasonSet$ = createEffect(() => this.actions$.pipe(
        ofType(sendStockActions.setReason),
        filter(action => !!action.reason), // Only proceed if reason is not null
        tap(a => console.log('location search with reason', a.reason)),
        mergeMap((action) => this.sendStockClient.getLocationList(action.reason.transreasonCode)
            .pipe(
                catchError(() => EMPTY),
                map(response => sendStockActions.setDestinationsLookup({ destinations: response }))
            )
        )
    ));

    // after destinations have been loaded, if we're locked to one store, set the destination
    $afterDestinationsLoaded = createEffect(() => this.actions$.pipe(
        ofType(sendStockActions.setDestinationsLookup),
        filter(action => action.destinations.isDestinationLockedToOneStore),
        map(action => sendStockActions.setDestination({ destination: action.destinations.lockedDestinationStore }))
    ));

    // after destination has been set we can generate a transfer number
    $afterDestinationSet = createEffect(() => this.actions$.pipe(
        ofType(sendStockActions.setDestination),
        filter(action => !!action.destination), // Only proceed if destination is not null
        withLatestFrom(this.store.select(s => s.sendStock)),
        filter(([action, state]) => !!state.reason), // Additional check for reason
        mergeMap(([action, sendStockState]) => this.sendStockClient.generateTransferNumber({
            destinationStoreId: action.destination.storeId,
            transferReasonCode: sendStockState.reason.transreasonCode
        } as GenerateTransferNumberRequest)
            .pipe(
                catchError(() => EMPTY),
                map(response => sendStockActions.setTransferNumber({ transferNumber: response }))
            )
        )
    ));

    $proceedStocks = createEffect(() => this.actions$.pipe(
        ofType(sendStockActions.submit),
        tap(() => console.log("proceed send stock")),
        mergeMap((action) => this.sendStockClient.proceed(action.sendStock)
            .pipe(
                map(response => sendStockActions.submitResponse(),
                    catchError(() => EMPTY),
                )
            )
        )
    ));
}
