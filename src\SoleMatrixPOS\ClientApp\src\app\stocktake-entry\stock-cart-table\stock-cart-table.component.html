<div *ngIf="tableLoading">
    <mat-spinner style="margin:0 auto;" mode="indeterminate">Reading file...</mat-spinner>
</div>
<div *ngIf="!tableLoading" class="table-responsive">
    <table class="table table-striped" style="margin: 0">
        <thead>
            <tr>
                <!-- Todo: reorder these to fit -->
                <th scope="col">Style</th>
                <th scope="col">Description</th>
                <th scope="col">Colour</th>
                <th scope="col">Colour Code</th>
                <th scope="col">Department</th>
                <th scope="col">Maker Code</th>
                <th scope="col">Size</th>
                <th scope="col">Qty</th>
                <th scope="col"></th>
            </tr>
        </thead>
        <!-- *ngFor="let item of cart$ | async; let i = index;" -->
        <!--  -->
        <tbody *ngFor="let item of cartItem$ | async; let i =index;">
            <tr>
                <td>{{item.stockItem.styleCode}}</td>
                <td >{{item.stockItem.styleDescription}}</td>
                <td >{{item.stockItem.colourName}}</td>
                <td >{{item.stockItem.colourCode}}</td>
                <td >{{item.stockItem.departmentCode}}</td>
                <td >{{item.stockItem.makerCode}}</td>
                <td >{{item.stockItem.size}}</td>
                <td >{{item.quantity}}</td>
                <td>
                    <span class="fas fa-trash fa-lg deleteButton" (click)="onTableItemDeleteClicked(i)"></span>
                </td>
            </tr>
            <tr *ngFor="let reason of getReasons(item.stockItem); let j = index">
                <td>{{reason}}</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td>
                    <span class="fas fa-strash fa-lg deleteButton" (click)="onTableReasonDeleteClicked(i, j)"></span>
                </td>
            </tr>
        </tbody>
    </table>
</div>

