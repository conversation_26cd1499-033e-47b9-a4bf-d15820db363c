import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {StockItemDto, StockSearchRequestDto} from '../../pos-server.generated';

@Component({
  selector: 'pos-stock-search-table',
  templateUrl: './stock-search-table.component.html',
  styleUrls: ['./stock-search-table.component.scss']
})
export class StockSearchTableComponent implements OnInit {

	@Input() items: StockItemDto[];
	@Input() options: StockSearchRequestDto;
	@Input() isLoading: boolean;
	@Output() selectedItemChanged = new EventEmitter<StockItemDto>();
	@Output() scrolledDown = new EventEmitter<void>();

	throttle = 500;
	scrollDistance = 1;

	constructor() {
	}

	ngOnInit() {
	}

	selectItem(item: StockItemDto) {
		this.selectedItemChanged.emit(item);
	}

	onScrollDown() {
		console.log('scrolled down!!');
		this.scrolledDown.emit();
	}
}
