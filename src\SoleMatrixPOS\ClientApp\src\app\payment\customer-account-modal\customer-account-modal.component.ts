import { Component, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, Validators } from '@angular/forms';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import { Payment, PaymentType } from '../payment.service';
import { CustomerMateDto } from '../../pos-server.generated';
import { Observable } from 'rxjs';
import * as accountPaymentActions from '../../reducers/account-payments/account-payment.actions';
import * as accountPaymentSelectors from '../../reducers/account-payments/account-payment.selectors';
import { AccountPaymentSearch } from 'src/app/account-payment/account-payment.component';
import { CustomerModalComponent } from 'src/app/account-payment/customer-modal/customer-modal.component';

@Component({
  selector: 'pos-customer-account-modal',
  templateUrl: './customer-account-modal.component.html',
  styleUrls: ['./customer-account-modal.component.scss']
})
export class CustomerAccountModalComponent implements OnInit {
  field = 'Name';
  term = '';
  first = 10;
  skip = 0;
  customers$: Observable<CustomerMateDto[]>;
  loading$: Observable<boolean>;

  @Input() type: PaymentType;
  @Input() amountDue: number;
  @Input() transType: number;

  get amount() { return this.form.get('Amount'); }

  constructor(
    public activeModal: NgbActiveModal,
    private store: Store<AppState>,
    private formBuilder: FormBuilder,
    private modalService: NgbModal
  ) {
    this.customers$ = this.store.select(accountPaymentSelectors.selectCustomers);
    this.loading$ = this.store.select(accountPaymentSelectors.selectLoading);
  }

  public form = this.formBuilder.group({
    Amount: [undefined, [
      Validators.required,
      Validators.min(0.01),
      Validators.pattern(/^\d*[.]{0,1}\d{0,2}$/)
    ]]
  });

  ngOnInit() {
    this.search();
  }

  search() {
    const searchQuery: AccountPaymentSearch = {
      searchString: this.term,
      first: this.first,
      skip: this.skip,
      searchField: this.field as 'Phone' | 'Name' | 'Email'
    };

    this.store.dispatch(accountPaymentActions.searchCustomers({ payload: searchQuery }));
  }

  selectCust(cust: CustomerMateDto) {
    this.openCustomerModal(cust);
  }

  useRemainder() {
    this.amount.setValue(this.amountDue);
  }

  public fieldValidate(control: AbstractControl): boolean {
    return control.invalid;
  }

  openCustomerModal(cust: CustomerMateDto) {
    const modalRef = this.modalService.open(CustomerModalComponent, {
      windowClass: 'daily-modal-window',
      size: 'l',
      centered: true
    });
    modalRef.componentInstance.name = 'CustomerModal';
    modalRef.componentInstance.customer = cust;
    modalRef.componentInstance.isRefund = this.transType === 2;
    modalRef.componentInstance.sale = true; // set to true if sale or refund. only false if opened via account payment modal.
    modalRef.componentInstance.type = this.type;
    modalRef.componentInstance.amountDue = this.amountDue;
    modalRef.result.then((result) => {
      this.activeModal.close(result);
    }).catch((reason) => console.log(reason));
  }
}
