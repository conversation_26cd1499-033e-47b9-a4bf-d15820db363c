using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Notes;
using System.Collections;
using SoleMatrixPOS.Application.Notes.Queries;
using SoleMatrixPOS.Application.Notes.Commands;

namespace SoleMatrixPOS.Controllers
{
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController]
    public class NoteController : ControllerBase
    {
        private readonly IMediator _mediator;

        public NoteController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPut]
        public async Task<IActionResult> AddNote([FromBody] NoteDto noteDto)
        {
            await _mediator.Send(new CreateNoteCommand(noteDto));
            return Ok(); // need to return some kind of result?
        }

		[HttpPost]
        public async Task<IEnumerable<NoteDto>> Get([FromBody] NoteSearchRequestDto noteSearchRequestDto)
        {
            return await _mediator.Send(new SearchNotesQuery(noteSearchRequestDto));
        }

		[HttpDelete]
        public async Task<IActionResult> Delete([FromBody] NoteDto noteDto)
        {
            await _mediator.Send(new DeleteNoteCommand(noteDto));
			return Ok();
        }

    }
}
