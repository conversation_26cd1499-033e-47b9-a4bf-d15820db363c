<div style="height: 550px;" class="container-fluid search-container p-3">		
	<div class="row">
		<div class="col-1">
			<i class="fas fa-plus text-info mr-2 fa-2x fa-inverse"></i>
		</div>
		<div class="col">
			<h1 [translate]="'note-modal.NewNote'">New Note</h1>
		</div>
		<div class="col">
			<button type="button" class="btn btn-circle float-right" (click)="dismiss('Cross click')">
				<i class="fas fa-times fa-2x"></i>
			</button>
		</div>
	</div>
	<form [formGroup]="noteForm" autocapitalize="on">

		<hr>
		<div class="row">
			<label class="col">
				<div class="mb-2" [translate]="'note-modal.Note'">Note</div>
			<textarea style="height: 200px;" class="form-control ng-untouched ng-pristine ng-valid" formControlName="Note">
			</textarea>
			</label>
		</div>

		<div class="row">
			<label class="col">
				<div class="mb-2" [translate]="'note-modal.Date'">Date</div>
				<input type="text" class="form-control ng-untouched ng-pristine ng-valid" formControlName="Date"
					[readonly]="true">
			</label>
			<label class="col">
				<div class="mb-2" [translate]="'note-modal.Time'">Time</div>
				<input type="text" class="form-control ng-untouched ng-pristine ng-valid" formControlName="Time"
					[readonly]="true">
			</label>
		</div>
		<div class="row mt-5">
			<div class="col text-center">
				<div><button class="btn btn-outline-default" type="button" (click)="onSubmit()"
					[translate]="'note-modal.SaveChanges'">
						<i class="fas fa-lg fa-fw fa-check-circle text-success mr-2"
							></i>
						Save Changes</button>
				</div>
			</div>
		</div>
	</form>
</div>