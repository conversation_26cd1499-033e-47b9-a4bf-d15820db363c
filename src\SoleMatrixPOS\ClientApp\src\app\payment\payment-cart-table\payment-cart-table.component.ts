import { Component, Input, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { EventEmitter } from '@angular/core';
import { Observable } from 'rxjs';
import { AppState } from 'src/app/reducers';
import { CartItem } from 'src/app/reducers/sales/cart/cart.reducer';
import * as cartSelectors from '../../reducers/sales/cart/cart.selectors'
import * as paymentSelectors from '../../reducers/sales/payment/payment.selector'
import { Payment, PaymentType, Transaction } from '../payment.service';
import * as SysConfigSelectors from 'src/app/reducers/sys-config/sys-config.selectors';
import { LaybyState } from 'src/app/reducers/layby/layby.reducer';
import * as CustomerClubSearchSelectors from 'src/app/reducers/customer-club/club-search/customer-club.selectors';
import { CustomerClubDto } from 'src/app/pos-server.generated';
import * as customerClubActions from 'src/app/reducers/customer-club/club-search/customer-club.actions';
import * as giftCardPayActions from 'src/app/reducers/voucher-pay/voucher-pay.actions';
import * as giftCardPaySelectors from 'src/app/reducers/voucher-pay/voucher-pay.selectors';

import { first } from 'rxjs/operators';
import Swal from 'sweetalert2';


@Component({
  selector: 'pos-payment-cart-table',
  templateUrl: './payment-cart-table.component.html',
  styleUrls: ['./payment-cart-table.component.scss']
})
export class PaymentCartTableComponent implements OnInit {
  layby$: Observable<LaybyState>;

  constructor(private store: Store<AppState>) { }

  clickCount = 0; 
  pointsValuePerDollar: number;

  cart$: Observable<CartItem[]>;
  newPoints: number;
  currentPoints: number;
  clientCode: string;
  cartTotal$: Observable<number>;
  selectedCustomer$: Observable<CustomerClubDto>;
  
  @Input() transaction$: Observable<Transaction>;
  @Output() removePayment: EventEmitter<Payment> = new EventEmitter<Payment>();

  private previousCustomerId: string = null;

  subscribeToState() {
    this.cart$ = this.store.select(cartSelectors.cart);
    this.cartTotal$ = this.store.select(cartSelectors.total);
    //this.transaction$ = this.store.select(paymentSelectors.transaction);
	  this.layby$ = this.store.select(s => s.layby);
	  console.log(this.transaction$);
    this.selectedCustomer$ = this.store.select(CustomerClubSearchSelectors.selectedCustomerClubMember);
  }

	ngOnInit() {
    this.subscribeToState();
  
    this.store.select(SysConfigSelectors.DollarPerPoints).subscribe(value => {
      this.pointsValuePerDollar = value;
    });

    // Only remove customer points when the customer actually changes
    this.selectedCustomer$.subscribe(customer => {
      if (customer) {
        const currentCustomerId = customer.clientCode;
        
        // Only remove points if the customer has changed
        if (this.previousCustomerId !== null && this.previousCustomerId !== currentCustomerId) {
          console.log(`Customer changed from ${this.previousCustomerId} to ${currentCustomerId}, removing existing points payments`);
          
          this.transaction$.pipe(first()).subscribe(transaction => {
            transaction.payments.forEach(payment => {
              if (payment.type === PaymentType.CustomerPoints) {
                console.log('Removing previous customer points payment:', payment);
                this.removePayment.emit(payment);
              }
            });
          });
        }
        
        // Update previous customer ID
        this.previousCustomerId = currentCustomerId;
      }
    });
  }

  paymentClick(payment: Payment) {
    console.log("Payment ", payment, " was clicked.");
    ++this.clickCount;
    setTimeout(() => {
      if (this.clickCount === 1) {
        // Single click handling
      } else if (this.clickCount === 2) {
        if (payment.type == PaymentType.Deposit) {
          Swal.fire({
            title: 'Are you sure?',
            text: "Are you sure you want to remove the prepaid deposit?",
            showCancelButton: true,
            confirmButtonText: 'Yes',
            cancelButtonText: 'No'
          }).then((result) => {
            if (result.value) {
              console.log("Emitting!");
              this.removePayment.emit(payment);
            }
          });
        }
        else if (payment.type == PaymentType.CustomerPoints) {
          this.selectedCustomer$.subscribe(customer => {
            this.clientCode = customer.clientCode;
            this.currentPoints = customer.clientPoints;
          });
          this.newPoints = payment.amount * this.pointsValuePerDollar;
          this.newPoints = this.currentPoints + this.newPoints;
          console.log(this.currentPoints, this.newPoints);
          console.log("Updating points for customer ",  this.newPoints);
          this.store.dispatch(customerClubActions.updateCustomerPoints({ 
            customerId: this.clientCode,
            points: this.newPoints
          }));
          this.removePayment.emit(payment);
        }
        else {
          console.log("Emitting!");
          this.removePayment.emit(payment);
        }
        
        if (payment.type === PaymentType.GiftCard) {
          this.store.dispatch(giftCardPayActions.clearAllVoucherPayments());
        }
      }
      this.clickCount = 0;
    }, 250);
  }

  // So that we can use enum in render:
  public get paymentType(): typeof PaymentType {
    return PaymentType;
  }

}
