import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import * as SysSelector from '../../reducers/house-keeping/mLogin.selectors';
import { ReceiptFooterDto, ReceiptHeaderDto, ReceiptLaybyDto, ReceiptTitleDto, ReceiptUpdateDto } from 'src/app/pos-server.generated';
import * as receipUpdateAction from '../../reducers/house-keeping/updateReceipt/receipt.actions';
import { take } from 'rxjs/operators';

@Component({
  selector: 'pos-receipt',
  templateUrl: './receipt.component.html',
  styleUrls: ['./receipt.component.scss']
})
export class ReceiptComponent implements OnInit {

  // Local arrays for receipt data
  receiptTitles: ReceiptTitleDto[] = [];
  receiptHeaders: ReceiptHeaderDto[] = [];
  receiptFooters: ReceiptFooterDto[] = [];
  receiptLaybys: ReceiptLaybyDto[] = [];

  // Track the index of the currently edited row for each section
  clickedIndexT: number = null;
  clickedIndexH: number = null;
  clickedIndexF: number = null;
  clickedIndexL: number = null;

  // Backup variables to store the original text before editing
  backupTitle: string = '';
  backupHeader: string = '';
  backupFooter: string = '';
  backupLayby: string = '';

  constructor(private store: Store<AppState>) { }

  ngOnInit() {
    // Load initial data from the store into local arrays
    this.store.select(SysSelector.selectReceipTittle).pipe(take(1)).subscribe(data => {
      this.receiptTitles = data;
    });
    this.store.select(SysSelector.selectReceipHeader).pipe(take(1)).subscribe(data => {
      this.receiptHeaders = data;
    });
    this.store.select(SysSelector.selectReceipFooter).pipe(take(1)).subscribe(data => {
      this.receiptFooters = data;
    });
    this.store.select(SysSelector.selectReceipLayby).pipe(take(1)).subscribe(data => {
      this.receiptLaybys = data;
    });
  }

  // --- Edit Mode and Backup Methods ---

  editTitle(index: number) {
    // Store the current text in backup before editing begins
    this.backupTitle = this.receiptTitles[index].recLine;
    this.clickedIndexT = index;
  }

  editHeader(index: number) {
    this.backupHeader = this.receiptHeaders[index].recLine;
    this.clickedIndexH = index;
  }

  editFooter(index: number) {
    this.backupFooter = this.receiptFooters[index].recLine;
    this.clickedIndexF = index;
  }

  editLayby(index: number) {
    this.backupLayby = this.receiptLaybys[index].recLine;
    this.clickedIndexL = index;
  }

  // --- Cancel Methods: Revert changes using the backup ---

  onCancelT() {
    if (this.clickedIndexT !== null) {
      this.receiptTitles[this.clickedIndexT].recLine = this.backupTitle;
    }
    this.clickedIndexT = null;
  }

  onCancelH() {
    if (this.clickedIndexH !== null) {
      this.receiptHeaders[this.clickedIndexH].recLine = this.backupHeader;
    }
    this.clickedIndexH = null;
  }

  onCancelF() {
    if (this.clickedIndexF !== null) {
      this.receiptFooters[this.clickedIndexF].recLine = this.backupFooter;
    }
    this.clickedIndexF = null;
  }

  onCancelL() {
    if (this.clickedIndexL !== null) {
      this.receiptLaybys[this.clickedIndexL].recLine = this.backupLayby;
    }
    this.clickedIndexL = null;
  }

  // --- Process Save Methods: Update the local data and dispatch the store action ---

  processChangesTitle(data: string) {
    const receiptUpdateDto: ReceiptUpdateDto = {
      recType: "T",
      lineNo: this.clickedIndexT + 1,
      recLine: data
    };
    // Update local data immediately
    this.receiptTitles[this.clickedIndexT].recLine = data;
    this.store.dispatch(receipUpdateAction.updateRec({ updateRec: receiptUpdateDto }));
    this.clickedIndexT = null;
  }

  processChangesHeader(data: string) {
    const receiptUpdateDto: ReceiptUpdateDto = {
      recType: "H",
      lineNo: this.clickedIndexH + 1,
      recLine: data
    };
    this.receiptHeaders[this.clickedIndexH].recLine = data;
    this.store.dispatch(receipUpdateAction.updateRec({ updateRec: receiptUpdateDto }));
    this.clickedIndexH = null;
  }

  processChangesFooter(data: string) {
    const receiptUpdateDto: ReceiptUpdateDto = {
      recType: "F",
      lineNo: this.clickedIndexF + 2,
      recLine: data
    };
    this.receiptFooters[this.clickedIndexF].recLine = data;
    this.store.dispatch(receipUpdateAction.updateRec({ updateRec: receiptUpdateDto }));
    this.clickedIndexF = null;
  }

  processChangesLayby(data: string) {
    const receiptUpdateDto: ReceiptUpdateDto = {
      recType: "L",
      lineNo: this.clickedIndexL + 1,
      recLine: data
    };
    this.receiptLaybys[this.clickedIndexL].recLine = data;
    this.store.dispatch(receipUpdateAction.updateRec({ updateRec: receiptUpdateDto }));
    this.clickedIndexL = null;
  }
}
