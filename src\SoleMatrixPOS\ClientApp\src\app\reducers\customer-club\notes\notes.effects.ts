import { Injectable } from "@angular/core";
import { Actions, ofType, createEffect } from '@ngrx/effects';
import { NoteClient } from 'src/app/pos-server.generated';
import * as noteSearchActions from './notes.actions';
import { mergeMap, map, catchError } from 'rxjs/operators';
import { EMPTY } from 'rxjs';

@Injectable()
export class NotesEffects {

    matchingNotes$ = createEffect(
        ()=> this.actions$.pipe(
            ofType(noteSearchActions.search),
            mergeMap(
                (action) => this.noteClient.get(action.searchParams).pipe(
                    map(
                        foundNotes => noteSearchActions.searchResponse({payload: foundNotes}),
                        catchError(()=> EMPTY)
                    )
                )
            )
        )
    );

    addNote$ = createEffect(
        () => this.actions$.pipe(
            ofType(noteSearchActions.createNote),
            mergeMap((action) => this.noteClient.addNote(action.payload)
                .pipe(
                    map(x => noteSearchActions.createNoteResponse(),
                        catchError(() => EMPTY)
                    )
                )
            )
        )
    );

    deleteNote$ = createEffect(
        () => this.actions$.pipe(
            ofType(noteSearchActions.deleteNote),
            mergeMap((action) => this.noteClient.delete(action.payload)
                .pipe(
                    map(x => noteSearchActions.deleteNoteResponse(),
                        catchError(() => EMPTY)
                    )
                )
            )
        )
    );
    constructor(
        private actions$: Actions, 
        private noteClient: NoteClient){}
}