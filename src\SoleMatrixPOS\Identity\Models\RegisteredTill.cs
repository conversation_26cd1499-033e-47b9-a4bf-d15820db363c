using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SoleMatrixPOS.Identity.Models
{
	public class RegisteredTill : IdentityUser
	{
		public string StoreId { get; set; }
		public string TillId { get; set; }
		public string RefreshTokenHash { get; set; }
		public DateTime? RefreshTokenExpiry { get; set; }
		public string? PasswordResetToken { get; set; } = null;
	}
}
