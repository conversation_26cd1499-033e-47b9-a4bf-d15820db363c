import { createReducer, on, Action } from '@ngrx/store';
import { TransactionDto } from 'src/app/pos-server.generated';
import * as receiptActions from "./receipt.actions";
export class ReceiptState {
    receiptCompleted: boolean;
    transaction:TransactionDto
}

export const initialState: ReceiptState = {
    receiptCompleted: false,
    transaction:null
}

export const receiptReducer = createReducer(initialState,
    on(receiptActions.init, (state) => initialState),
    on(receiptActions.reprintConfirmed,(state) => ({...state,receiptCompleted:true}))
)