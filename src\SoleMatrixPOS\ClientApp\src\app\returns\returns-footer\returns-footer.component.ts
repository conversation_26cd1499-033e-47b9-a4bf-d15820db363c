import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { CustomerClubModalComponent } from 'src/app/customer-club/customer-club-modal/customer-club-modal.component';
import { CustomerClubDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import { CartItem } from 'src/app/reducers/sales/cart/cart.reducer';
import { clearStaffLogin } from 'src/app/reducers/staff/staff.actions';
import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';
import * as cartSelectors from '../../reducers/sales/cart/cart.selectors'

@Component({
  selector: 'pos-returns-footer',
  templateUrl: './returns-footer.component.html',
  styleUrls: ['./returns-footer.component.scss']
})
export class ReturnsFooterComponent implements OnInit {
  selectedCustomerClubMember$: any;

  constructor(private store: Store<AppState>, private router: Router, private modalService: NgbModal) { }

  selectedCustomerClubMember: CustomerClubDto = null;

  noItems$: Observable<number>;
  totalValue$: Observable<number>;

  readyToProcess: boolean = false;

  subscribeToState(){
    this.selectedCustomerClubMember$ = this.store.select(customerClubSearchSelectors.selectedCustomerClubMember);
		this.selectedCustomerClubMember$.subscribe((s) => { 
      this.selectedCustomerClubMember = s;
    });

    this.noItems$ = this.store.select(cartSelectors.noItems);
    this.totalValue$ = this.store.select(cartSelectors.total);

    this.noItems$.subscribe(n => this.readyToProcess = (n > 0));
  }

  ngOnInit() {
    this.subscribeToState();
  }

  goToLoginPage(){
    this.store.dispatch(clearStaffLogin());
    // this.router.navigateByUrl('/staffLogin');
  }

  launchCustomerClubModal() {
		const modalRef = this.modalService.open(CustomerClubModalComponent, { size: 'xl', centered: true });
		modalRef.componentInstance.name = 'CustomerClubModal';
		modalRef.result.then((result) => {
			if (result) {
				console.log('result from modal:', result);
			}
		}).catch(error => {
      console.log("Error occurred: ", error);
    });
	} 

  processBtnClick(){
    // Check if there are any items being returned
    if(this.readyToProcess){
      this.router.navigateByUrl("returns/refund");
    } else{
      // Throw error modal
      alert("No items processed yet.");
    }
  }

}
