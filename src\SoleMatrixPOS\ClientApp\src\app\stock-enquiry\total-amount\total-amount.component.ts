import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { flatMap } from 'rxjs/operators';
import { StockEnquiryHeaderDto, StockEnquiryLocationGridDto, StockEnquiryQtyDto, StockEnquirySizeDto, StockEnquiryTotalDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import * as StockEnquiryAction from '../../reducers/stock-enquiry/stock-enquiry.actions';
import * as StockEnquireSelector from '../../reducers/stock-enquiry/stock-enquiry.selectors';

@Component({
  selector: 'pos-total-amount',
  templateUrl: './total-amount.component.html',
  styleUrls: ['./total-amount.component.scss']
})
export class TotalAmountComponent implements OnInit {

  header$: Observable<StockEnquiryHeaderDto>;
  location$: Observable<StockEnquiryLocationGridDto[]>;
  total$: Observable<StockEnquiryTotalDto[]>;


  constructor(private store: Store<AppState>) {
  }

  ngOnInit() {
    this.store.dispatch(StockEnquiryAction.init());

    this.header$ = this.store.select(StockEnquireSelector.stockEnquiryHeader);
    this.location$ = this.store.select(StockEnquireSelector.stockEnquiryLocation);
    this.total$ = this.store.select(StockEnquireSelector.stockEnquiryTotal);

  }

  
}
