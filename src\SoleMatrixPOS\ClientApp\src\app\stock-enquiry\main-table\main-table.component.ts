import { Component, Input, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { StockEnquiryLocationGridDto, StockEnquiryQtyDto, StockEnquiryResultDto, StockEnquirySizeDto, StockEnquiryTotalBysizeDto } from 'src/app/pos-server.generated';
//import { StockEnquiryInfoDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import * as StockEnquireSelector from '../../reducers/stock-enquiry/stock-enquiry.selectors';
import * as StockEnquiryAction from '../../reducers/stock-enquiry/stock-enquiry.actions';



@Component({
  selector: 'pos-main-table',
  templateUrl: './main-table.component.html',
  styleUrls: ['./main-table.component.scss']
})
export class MainTableComponent implements OnInit {

  size$: Observable<StockEnquirySizeDto[]>;
  location$: Observable<StockEnquiryLocationGridDto[]>;
  qty$: Observable<StockEnquiryQtyDto[]>;
  totalBySize$: Observable<StockEnquiryTotalBysizeDto[]>;

  constructor(private store: Store<AppState>) {
  }

  ngOnInit() {
    this.store.dispatch(StockEnquiryAction.init());
    
    this.size$ = this.store.select(StockEnquireSelector.stockEnquirySize);
    this.location$ = this.store.select(StockEnquireSelector.stockEnquiryLocation);
    this.qty$ = this.store.select(StockEnquireSelector.stockEnquiryQty);
    this.totalBySize$ = this.store.select(StockEnquireSelector.stockEnquiryTotalBySize);

  }

 

}



















