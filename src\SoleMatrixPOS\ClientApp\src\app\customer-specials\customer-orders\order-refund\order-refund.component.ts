import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { Store, select } from '@ngrx/store';
import Swal from 'sweetalert2';
import { Observable, Subject } from 'rxjs';
import * as staffActions from 'src/app/reducers/staff/staff.actions';
import { Payment, PaymentType, Transaction } from 'src/app/payment/payment.service';
import { OpenCashDrawerAction } from 'src/app/printing/printing-definitions';
import { takeUntil, map, first } from 'rxjs/operators';
import { PaymentModalButton } from 'src/app/payment/payment-modal-button/payment-modal-button.component';
import { CreateOrderDto, EftposClient, GetReceiptDto } from 'src/app/pos-server.generated';
import * as orderSearchSelectors from 'src/app/reducers/order-item-search/order-item-search.selectors';
import * as transactionSelectors from 'src/app/reducers/transaction/transaction.selectors';
import * as cartSelectors from 'src/app/reducers/sales/cart/cart.selectors'; // Add cart selectors
import * as transactionActions from 'src/app/reducers/transaction/transaction.actions';
import * as orderActions from 'src/app/reducers/order-item/order.actions'
import { AppState } from 'src/app/reducers';
import { CashModalComponent } from 'src/app/payment/cash-modal/cash-modal.component';
import { EftposModalComponent } from 'src/app/payment/eftpos-modal/eftpos-modal.component';
import { take } from 'rxjs/operators';
import { CartItem, cartItemToTranslog } from 'src/app/reducers/sales/cart/cart.reducer';
import * as receiptActions from 'src/app/reducers/receipt-printing/receipt.actions';
import { TransactionDto, TranslogDto, TranspayDto, GiftVoucherResultDto } from '../../../pos-server.generated';
import * as transActions from '../../../reducers/transaction/transaction.actions';
import * as customerClubSearchSelectors from 'src/app/reducers/customer-club/club-search/customer-club.selectors';
import { CtransDto } from '../../../pos-server.generated';
import * as sysSelectors from '../../../reducers/sys-config/sys-config.selectors';
import { CustomerClubDto } from '../../../pos-server.generated';
import { EmailReceiptComponent } from 'src/app/email-receipt/email-receipt.component';
import * as orderSelectors from '../../../reducers/order-item/order.selectors'; // Import order selectors
import { CustOrderHeaderDTO, CustOrderLineDTO, ReceiptTransactionDto, CreateOrderRequest } from '../../../pos-server.generated';
import { EftposService, mapCartToLinklyBasket } from '../../../eftpos/eftpos.service';
import { WaitingForEftposModalComponent } from '../../../payment/waiting-for-eftpos-modal/waiting-for-eftpos-modal.component';
import { ReceiptBatch, TextAction, CutAction, CutType, BarcodeAction, FeedAction } from '../../../printing/printing-definitions';
import { PrintingService, SolemateReceiptOptions } from 'src/app/printing/printing.service';
import { tap } from 'rxjs/operators';
import { financialRound } from 'src/app/payment/payment.service';
import * as customerClubSearchActions from 'src/app/reducers/customer-club/club-search/customer-club.actions';
import * as customerClubUpdateActions from 'src/app/reducers/customer-club/customer-update/customer-update.actions';
import { PointsUpdatePayload } from 'src/app/reducers/customer-club/customer-update/customer-update.actions';
import { CreditNoteIssuanceModalComponent } from 'src/app/payment/credit-note-issuance-modal/credit-note-issuance-modal.component';
import { GiftVoucherIssueComponent } from 'src/app/gift-voucher/gift-voucher-issue/gift-voucher-issue.component';
import * as voucherActions from 'src/app/reducers/gift-voucher/gift-voucher.actions';

@Component({
    selector: 'app-order-refund',
    templateUrl: './order-refund.component.html',
    styleUrls: ['./order-refund.component.scss'],
})
export class OrderRefundComponent implements OnInit, OnDestroy {
    @Input() transNo!: number;
    @Input() clientName!: string;
    private destroy$ = new Subject<void>();
    modalButtons: PaymentModalButton[];

    sysStatus: any;
    public sysStatus$: Observable<any>;

    public intEftAmount = 0;

    selectedOrder$: Observable<CreateOrderDto | null>;
    transPayData$: Observable<any | null>;

    // Variables to hold the TransPay data
    payAmount: number | null = null;
    paymentType: string | null = null;
    voucherNumber: string | null = null;
    totalRefundedInModals: number = 0;
    transaction: Transaction; // Transaction to handle payments

    private selectedCustomerClubMember$: Observable<CustomerClubDto>;
    public selectedCustomerClubMember: CustomerClubDto = null;

    // Properties to store orderHeader and orderLines
    orderHeader: CustOrderHeaderDTO;
    orderLines: CustOrderLineDTO[];

    public cart$: Observable<CartItem[]>;
    cart: CartItem[] = [];

    intEftReceipts: GetReceiptDto[];
    newTransNo: number;
    alwaysOpenCashTill: string = 'F';

    transaction$: Observable<Transaction>;
    private transactionDto: TransactionDto;
    cTrans$: Observable<CtransDto[]>;
    cTrans: CtransDto[];
    totalCashRefunded: number = 0;
    totalEftposRefunded: number = 0;
    totalCreditNoteRefunded: number = 0;

    totalPaid: number = 0; // Total amount paid for the order
    orderCode: string | null = null;
    clientCode: string | null = null;
    refundError: string | null = null; // Error message for invalid refund amount
    refundMethods: string[] = ['Cash', 'EFTPOS']; // Available refund methods
    selectedMethod: string | null = null; // Selected refund method

    // Properties for points calculation
    PointsPerDollar: number = 0;
    newCustomerPointsTotal: number | null = null;
    pointsDeducted: number = 0;
    private customerClubMemberForPoints: CustomerClubDto | null = null; // To store fetched customer details
    creditNoteForModal: GiftVoucherResultDto | null = null;

    constructor(
        public activeModal: NgbActiveModal,
        private store: Store<AppState>,
        private router: Router,
        private modalService: NgbModal,
        private eftposService: EftposService,
        private printService: PrintingService
    ) {
        this.selectedOrder$ = this.store.pipe(select(orderSearchSelectors.selectedOrder));
        this.transPayData$ = this.store.pipe(select(transactionSelectors.transPayData));
        this.selectedCustomerClubMember$ = this.store.pipe(select(customerClubSearchSelectors.selectedCustomerClubMember));
        this.store.select(transactionSelectors.creditNote) // Subscribe to creditNote selector
            .pipe(takeUntil(this.destroy$))
            .subscribe(creditNote => {
                this.creditNoteForModal = creditNote;
            });
    }

    ngOnInit() {

        this.store.dispatch(transActions.getTransactionNo());
        this.store.select(transactionSelectors.transNo)
            .pipe(
                takeUntil(this.destroy$),
                tap(transNo => {
                    this.newTransNo = transNo;
                    console.log('Transaction number updated:', transNo);
                })
            )
            .subscribe();

        this.sysStatus$ = this.store.select(sysSelectors.selectSysConfig);
        this.sysStatus$.subscribe((sysconfig) => {
            this.sysStatus = sysconfig
        });

        // Fetch total paid amount using transNo
        this.modalButtons = [
            new PaymentModalButton('Cash', 'fas fa-money-bill-wave', PaymentType.Cash, false),
            new PaymentModalButton('EFTPOS', 'fas fa-credit-card', PaymentType.Eftpos, false),
            new PaymentModalButton('Credit Note', 'fas fa-sticky-note', PaymentType.CreditNote, false)
        ];

        this.fetchTotalPaid();
        console.log(this.totalPaid)

        // Subscribe to PointsPerDollar config
        this.store.select(sysSelectors.PointsPerDollar)
            .pipe(takeUntil(this.destroy$))
            .subscribe(limit => {
                this.PointsPerDollar = limit || 0;
            });
    }

    fetchTotalPaid() {
        this.selectedOrder$
            .pipe(takeUntil(this.destroy$))
            .subscribe(order => {
                if (!order) {
                    // If no order is selected, navigate back to the search page
                    this.router.navigate(['/order-search']);
                } else {
                    this.orderCode = order.orderHeader.orderCode;
                    this.clientCode = order.orderHeader.clientCode
                    this.orderLines = order.orderLines || [];
                    if (order.orderLines && order.orderLines.length > 0) {
                        order.orderLines.forEach(line => {
                            if (line.transNo) {
                                this.store.dispatch(transactionActions.getTransPayByTransNo({ transNo: line.transNo }));
                            }
                        });
                    }

                    this.store.dispatch(customerClubSearchActions.search({ 
                        searchParams: {
                            searchString: order.orderHeader.clientCode,
                            customerClubSearchKeywordColumnDto: 3,
                            customerClubSearchOrderByColumnDto: 3,
                            customerClubOrderByDirectionEnumDto: 0,
                            first: 1,
                            skip: 0
                        }
                    }));
            
                    // Subscribe to the search results to select the first member
                    this.store.select(customerClubSearchSelectors.searchedCustomerClubMembers)
                        .pipe(
                            takeUntil(this.destroy$),
                            tap(searchResults => {
                                if (searchResults && searchResults.length > 0) {
                                    console.log('Customer club search results:', searchResults);
                                    // Select the first member from the search results
                                    this.store.dispatch(customerClubSearchActions.selectCustomerClubMember({ 
                                        payload: searchResults[0] 
                                    }));
                                }
                            })
                        )
                        .subscribe();
            
                    // Subscribe to the selected customer club member
                    this.selectedCustomerClubMember$
                        .pipe(takeUntil(this.destroy$))
                        .subscribe(member => {
                            if (member) {
                                this.selectedCustomerClubMember = member;
                                this.customerClubMemberForPoints = member;
                                console.log('Customer Club Member found:', member);
                            }
                        });
                }
            });

        this.transPayData$
            .pipe(takeUntil(this.destroy$))
            .subscribe(transPayData => {
                if (transPayData && transPayData.length > 0) {
                    // Sum up all payment amounts from the TransPay data array
                    this.totalPaid = transPayData.reduce((sum, payment) => {
                        return sum + (payment.payAmount || 0);
                    }, 0);

                    // Store the first payment's details for reference
                    this.payAmount = transPayData[0].payAmount;
                    this.paymentType = transPayData[0].paymentType;
                    this.voucherNumber = transPayData[0].voucherNumber;
                } else {
                    this.totalPaid = 0;
                    this.payAmount = null;
                    this.paymentType = null;
                    this.voucherNumber = null;
                }
            });
    }

    dismissModal() {
        this.activeModal.dismiss('Modal closed by user');
        this.destroy$.next(); // Signal all subscriptions to complete
        this.destroy$.complete();
    }

    validateRefundAmount() {
        if (this.totalRefundedInModals > this.totalPaid) {
            this.refundError = 'Refund amount cannot exceed total paid amount.';
        } else if (this.totalRefundedInModals < 0) {
            this.refundError = 'Refund amount cannot be negative.';
        } else {
            this.refundError = null;
        }
    }

    canProcessRefund(): boolean {
        return this.totalRefundedInModals >= 0 && this.totalRefundedInModals <= this.totalPaid;
    }

    getAmountRemaining(): number {
        return Math.max(this.totalPaid - this.totalRefundedInModals, 0);
    }

    openEmailModal(receiptTrans: ReceiptTransactionDto, pointsDeducted: number, newCustomerPointsTotal: number | null): Promise<void> {
        return new Promise((resolve) => {
            const modalRef = this.modalService.open(EmailReceiptComponent, {
                size: 'lg',
                backdrop: 'static'
            });

            // Pass receiptTrans to the EmailReceiptComponent
            modalRef.componentInstance.receiptTrans = receiptTrans;
            // Pass customer details if fetched
            modalRef.componentInstance.customerSelected = this.customerClubMemberForPoints;
            modalRef.componentInstance.pointsEarned = pointsDeducted; // Pass deducted points (negative value)
            modalRef.componentInstance.newCustomerPointsTotal = newCustomerPointsTotal; // Pass new total points

            // Check if a customer club member is selected and pass the email
            if (this.selectedCustomerClubMember && this.selectedCustomerClubMember.email) {
                modalRef.componentInstance.customerEmail = this.selectedCustomerClubMember.email;
            }

            modalRef.result.then(() => {
                console.log('Email receipt sent.');
                resolve();  // Resolve the promise once the modal is closed
            }).catch(() => {
                resolve();  // Resolve the promise if the modal is dismissed
            });
        });
    }

    handleRefund() {
        if (this.canProcessRefund()) {
            if (this.totalRefundedInModals < this.totalPaid) {
                Swal.fire({
                    title: 'Warning',
                    text: 'The refund amount is less than the total paid amount. Are you sure you want to proceed?',
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, proceed',
                    cancelButtonText: 'No, cancel'
                }).then((result) => {
                    if (result.value) {
                        console.log("Proceeding with Refund amount", this.totalRefundedInModals);
                        this.checkIntegratedEftpos();
                    }
                });
            } else {
                console.log("Refund amount", this.totalRefundedInModals);
                this.checkIntegratedEftpos();
            }
        }
        else {
            console.log("refund failed")
        }
    }

    checkIntegratedEftpos(): void {
        if (this.intEftAmount != 0) {
            const modalRef = this.modalService.open(WaitingForEftposModalComponent, {
                size: 'md',
                centered: true,
                backdrop: 'static',
                keyboard: false
            });
            console.log(this.newTransNo)
            // Determine which integrated EFTPOS provider to use
            switch (this.sysStatus.integratedEFTProvider) {
                case "Linkly":
                    modalRef.componentInstance.tenderAmount = this.intEftAmount;
                    modalRef.componentInstance.totalAmount = this.totalRefundedInModals;
                    modalRef.componentInstance.store = this.store;
                    modalRef.componentInstance.discountAmt = 0; // TODO: calculate discount if needed
                    modalRef.componentInstance.surchargeAmt = this.intEftAmount * 0; // TODO: adjust surcharge calculation if required
                    modalRef.componentInstance.taxAmt = this.intEftAmount * 0; // TODO: adjust tax calculation based on config
                    modalRef.componentInstance.transNo = this.newTransNo;

                    // Map the current cart to the format required by Linkly
                    const mappedItems = mapCartToLinklyBasket(this.cart);
                    modalRef.componentInstance.items = mappedItems;
                    modalRef.componentInstance.transType = "Refund";
                    break;

                case "Tyro":
                    // TODO: Implement Tyro integration logic here if needed.
                    break;

                default:
                    console.log("Integrated EFTPOS not configured");
                    return;
            }

            // Handle the result from the waiting-for-EFTPOS modal.
            modalRef.result.then((result: { paymentResult: Payment, surchargePayment: Payment }) => {
                if (result) {
                    console.log("EFTPOS payment result:", result);
                    this.eftposService.getReceipts(this.newTransNo, false)
                        .subscribe((receipts: GetReceiptDto[]) => {
                            this.intEftReceipts = receipts;
                            this.orderTransactionCompleted()
                            this.printService.printEftposReceipt(receipts, true);
                        });
                } else {
                    // this.totalRefundedInModals -= this.intEftAmount;
                    // this.totalEftposRefunded = 0;
                    // this.intEftAmount = 0;

                    this.store.dispatch(transActions.resetTransactionConfirmation());
                    this.store.dispatch(transActions.getTransactionNo());

                    console.log("EFTPOS payment failed or was cancelled");
                }
            }).catch(error => {
                console.error("Error in waiting-for-EFTPOS modal:", error);
            });

        }
        else {
            this.orderTransactionCompleted()

        }
    }

    orderTransactionCompleted(): void {
        // Calculate points deduction before finalizing
        this.calculateAndSubtractPoints(this.totalRefundedInModals);

        const transactionDto: TransactionDto = this.getSaleTransaction();
        const saleDateTime = new Date();
        this.finalizeTransaction(transactionDto);

        let trans: ReceiptTransactionDto = {
            logs: transactionDto.translogs,
            pays: transactionDto.payments,
            saleDateTime: saleDateTime,
            transType: 2,
            refs: []
        };

        Swal.fire({
            title: "Order Cancelled",
            text: "The Order Was Successfully Refunded.",
            showCancelButton: true,
            cancelButtonText: "Email Receipt",
            confirmButtonText: "Print Receipt",
        }).then(async (result) => {
            if (result.value) {
                console.log("Points Deducted", this.pointsDeducted)
                console.log("New Customer Points Total", this.newCustomerPointsTotal)
                await this.printService.printOrderRefundReceipt(
                    "Order Refund",
                    this.orderLines,
                    this.orderCode,
                    this.clientName,
                    this.newTransNo.toString(),
                    transactionDto.payments,
                    SolemateReceiptOptions.default(),
                    0,
                    this.clientCode,
                    this.pointsDeducted,
                    this.newCustomerPointsTotal
                );
            } else if (result.dismiss === Swal.DismissReason.cancel) {
                await this.openEmailModal(trans, this.pointsDeducted, this.newCustomerPointsTotal);
            }
            await this.handleCreditNoteIssuanceAndFinalize();
        });
    }

    finalizeTransaction(transactionDto: TransactionDto): void {
        // Dispatch the cancelOrder action to set the order as cancelled
        this.store.dispatch(orderActions.cancelOrder({ orderNumber: this.orderCode }));
        this.store.dispatch(transActions.submitTransaction({ payload: transactionDto }));

        this.store.select(sysSelectors.OpenCashTill)
            .pipe(take(1))
            .subscribe(limit => {
                this.alwaysOpenCashTill = limit || 'F';
            });

        if (this.alwaysOpenCashTill === 'T') {
            this.store.dispatch(receiptActions.executeBatch({
                payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
            }));
        }
        else {
            if (transactionDto.payments.some(payment => payment.paymentType === 'Cash')) {
                this.store.dispatch(receiptActions.executeBatch({
                    payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
                }));
            }
        }
    }

    launchPaymentModal(type: PaymentType) {
        let modalRef;

        switch (type) {
            case PaymentType.Cash:
                modalRef = this.modalService.open(CashModalComponent, { size: 'xl', centered: true });
                break;
            case PaymentType.Eftpos:
                this.store.select(sysSelectors.selectSysConfig).pipe(
                    first(), // Get the first emitted value and complete
                    map(sysConfig => sysConfig && sysConfig.integratedEFTProvider !== 'None' ? sysConfig.integratedEFTProvider : null)
                ).subscribe(integrated => {
                    if (integrated && integrated != "None") {
                        modalRef = this.modalService.open(EftposModalComponent, { size: 'xl', centered: true });
                        modalRef.componentInstance.intEft = true
                    }
                    else {
                        modalRef = this.modalService.open(EftposModalComponent, { size: 'xl', centered: true });
                        modalRef.componentInstance.intEft = false
                    }
                });
                break;
            case PaymentType.CreditNote:
                modalRef = this.modalService.open(CreditNoteIssuanceModalComponent, { size: 'xl', centered: true });
                modalRef.componentInstance.isLayby = true;
                break;
        }
        if (modalRef) {
            modalRef.componentInstance.amountDue = this.getAmountRemaining();
            modalRef.result.then((result: Payment) => {
                if (result) {
                    if (type === PaymentType.Cash) {
                        this.totalCashRefunded += result.amount;
                        this.totalRefundedInModals += result.amount;
                    } else if (type === PaymentType.Eftpos) {
                        this.totalEftposRefunded += result.amount;
                        this.totalRefundedInModals += result.amount;
                        if (result.desc == "Integrated Eftpos") {
                            this.intEftAmount += result.amount;
                        }
                    } else if (type === PaymentType.CreditNote) {
                        this.totalCreditNoteRefunded += result.amount;
                        this.totalRefundedInModals += result.amount;
                    }
                }
            }).catch((error) => {
                console.log('Payment Cancelled:', error);
            });
        }
    }

    getSaleTransaction(): TransactionDto {
        // Prepare the payments array (TranspayDto)
        const payments: TranspayDto[] = [];

        // If a refund method is selected and valid, add it to the payments array
        if (this.totalRefundedInModals > 0) {
            if (this.totalCashRefunded > 0) {
                payments.push({
                    paymentType: 'Cash',
                    payAmount: -this.totalCashRefunded, // Add the refund amount for Cash
                    voucherNumber: null, // Cash doesn't require a voucher number
                    transNo: this.newTransNo
                });
            }

            if (this.totalEftposRefunded > 0) {
                payments.push({
                    paymentType: 'Eftpos',
                    payAmount: -this.totalEftposRefunded, // Add the refund amount for EFTPOS
                    voucherNumber: this.voucherNumber, // Provide the EFTPOS voucher number
                    transNo: this.newTransNo
                });
            }

            if (this.totalCreditNoteRefunded > 0) {
                payments.push({
                    paymentType: 'Credit Note',
                    payAmount: -this.totalCreditNoteRefunded,
                    voucherNumber: null, // Backend generates this
                    transNo: this.newTransNo
                });
            }
        }

        // Prepare the transaction logs array (TranslogDto)
        const translogs: TranslogDto[] = this.orderLines.map((line) => ({
            styleCode: line.styleCode,
            colourCode: line.colourCode,
            sizeCode: line.sizeCode,
            quantity: line.quantity,
            sellingPrice: this.totalRefundedInModals,
            lineNo: line.lineNo,
            clientCode: this.clientCode,
            transNo: this.newTransNo
        }));

        // Construct the TransactionDto
        const transaction: TransactionDto = {
            transType: 2, // Transaction type 2 (refund)
            translogs: translogs,
            payments: payments,
        };

        console.log('Constructed Transaction:', transaction);
        return transaction;
    }

    // Method to calculate and subtract points
    calculateAndSubtractPoints(refundAmount: number): void {
        const roundedRefundAmount = financialRound(refundAmount);

        console.log("Points Per Dollar", this.PointsPerDollar)
        console.log("Client Code", this.clientCode)
        console.log("Refund Amount", roundedRefundAmount)
        console.log("Selected Customer Club Member", this.selectedCustomerClubMember)
        if (this.clientCode && roundedRefundAmount > 0 && this.PointsPerDollar > 0) {
            const pointsToDeduct = Math.floor(roundedRefundAmount / this.PointsPerDollar);
            console.log("Points To Deduct", pointsToDeduct)
            if (pointsToDeduct > 0) {
                try {
                    // Use the customer club member found in ngOnInit
                    if (this.selectedCustomerClubMember) {
                        const currentPoints = this.selectedCustomerClubMember.clientPoints != null ? this.selectedCustomerClubMember.clientPoints : 0;
                        this.pointsDeducted = -pointsToDeduct;
                        this.newCustomerPointsTotal = currentPoints - pointsToDeduct;

                        const pointsUpdatePayload: PointsUpdatePayload = {
                            clientCode: this.clientCode,
                            pointsToAdjust: -pointsToDeduct
                        };
                        console.log('Dispatching points update:', pointsUpdatePayload);
                        this.store.dispatch(customerClubUpdateActions.updatePoints({ payload: pointsUpdatePayload }));
                    } else {
                        console.warn(`Could not find customer club member with clientCode: ${this.clientCode} for points deduction.`);
                        this.pointsDeducted = 0;
                        this.newCustomerPointsTotal = null;
                    }
                } catch (error) {
                    console.error('Error fetching customer for points deduction:', error);
                    this.pointsDeducted = 0;
                    this.newCustomerPointsTotal = null;
                }
            } else {
                this.pointsDeducted = 0;
                this.newCustomerPointsTotal = this.customerClubMemberForPoints ? this.customerClubMemberForPoints.clientPoints : null;
            }
        } else {
            this.pointsDeducted = 0;
            this.newCustomerPointsTotal = null;
        }
    }

    private async handleCreditNoteIssuanceAndFinalize(): Promise<void> {
        return new Promise<void>((resolve) => {
            if (this.creditNoteForModal && this.totalCreditNoteRefunded > 0) {
                this.store.dispatch(voucherActions.creditNoteReceived({ payload: this.creditNoteForModal }));
                const modalRef = this.modalService.open(GiftVoucherIssueComponent, {
                    windowClass: 'daily-modal-window',
                    size: 'lg', // Adjusted size
                    centered: true,
                    backdrop: 'static',
                    keyboard: false
                });
                modalRef.result.then(() => {
                    this.store.dispatch(staffActions.clearStaffLogin());
                    this.activeModal.dismiss('Order Submitted with Credit Note');
                    resolve();
                }).catch(() => {
                    this.store.dispatch(staffActions.clearStaffLogin());
                    this.activeModal.dismiss('Order Submitted, Credit Note Issuance Modal Closed');
                    resolve();
                });
            } else {
                this.store.dispatch(staffActions.clearStaffLogin());
                this.activeModal.dismiss('Order Submitted');
                resolve();
            }
        });
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }
}
