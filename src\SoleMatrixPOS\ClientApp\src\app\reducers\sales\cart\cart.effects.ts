import { Injectable } from "@angular/core";
import { Actions, ofType, createEffect } from '@ngrx/effects';
import { StockSearchClient, BestPriceSearchRequestDto } from 'src/app/pos-server.generated';
import * as cartActions from './cart.actions';
import { mergeMap, map, catchError, tap } from 'rxjs/operators';
import { EMPTY } from 'rxjs';

@Injectable()
export class CartEffects {

    matchingNotes$ = createEffect(
        () => this.actions$.pipe(
            ofType(cartActions.addItem),
            tap(() => console.log("matchingNotes$")),
            mergeMap(
                (action) => this.stockClient.getCartItem({
                    styleCode: action.stockItem.styleCode,
                    colourCode: action.stockItem.colourCode,
                    sizeCode: action.stockItem.size,
                    locationCode: 'OO', // need to find this somewhere... TODO
                    clientCode: '', // WHERE TO GET THIS!?!?
                } as BestPriceSearchRequestDto).pipe(
                    map(
                        cartItem => cartActions.addItemResponse({ cartItem: cartItem }),
                        catchError(() => EMPTY)
                    )
                )
            )
        )
    );
    matchingReturnNotes$ = createEffect(
        () => this.actions$.pipe(
            ofType(cartActions.addReturnItem),
            tap(() => console.log("matchingReturnNotes$")),
            mergeMap(
                (action) => this.stockClient.getCartItem({
                    styleCode: action.stockItem.styleCode,
                    colourCode: action.stockItem.colourCode,
                    sizeCode: action.stockItem.size,
                    locationCode: 'OO', // need to find this somewhere... TODO
                    clientCode: '', // WHERE TO GET THIS!?!?
                } as BestPriceSearchRequestDto).pipe(
                    map(
                        cartItem => cartActions.addReturnItemResponse({ cartItem: cartItem }),
                        catchError(() => EMPTY)
                    )
                )
            )
        )
    );
    constructor(
        private actions$: Actions,
        private stockClient: StockSearchClient) { }
}