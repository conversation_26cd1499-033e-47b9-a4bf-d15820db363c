import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Observable, combineLatest } from 'rxjs';
import { GetPINAuthDto, GetSystemStatusDto, StaffLoginDto } from '../../pos-server.generated';
import { Store } from '@ngrx/store';
import { AppState } from '../../reducers';
import * as staffSelectors from '../../reducers/staff/staff.selectors';
import * as SysConfigSelectors from '../../reducers/sys-config/sys-config.selectors';
import * as PinAuthSelectors from '../../reducers/pin-auth/pin-auth.selectors';
import { take, tap } from 'rxjs/operators';
import { Payment, PaymentType } from '../payment.service';
import { ChangeDetectorRef } from '@angular/core';
import { EftposService, TyroQuestion } from '../../eftpos/eftpos.service';
import { CreateErrorModal } from '../../error-modal/error-modal.component';

@Component({
	selector: 'pos-waiting-for-eftpos-modal',
	templateUrl: './waiting-for-eftpos-modal.component.html',
	styleUrls: ['./waiting-for-eftpos-modal.component.scss']
})
export class WaitingForEftposModalComponent implements OnInit {

	@Input() totalAmount: number;
	@Input() tenderAmount: number;
	@Input() totalEftAmount: number;
	@Input() discountAmt: number;
	@Input() surchargeAmt: number;
	@Input() taxAmt: number;
	@Input() transNo: number;
	@Input() items: any[];
	@Input() transType: string; // "Purchase" or "Refund"

	private modalClosed = false;

	constructor(
		public activeModal: NgbActiveModal,
		private modalService: NgbModal,
		private store: Store<AppState>,
		private cdr: ChangeDetectorRef,
		private eftposService: EftposService,
	) { }

	managerSys$: Observable<GetSystemStatusDto>;
	staff$: Observable<StaffLoginDto>;
	staffCode: string;
	staffName: string;
	status: string;
	pinAuth$: Observable<GetPINAuthDto>;
	pinAuth: GetPINAuthDto;
	integratedEftProvider: string;

	surchargeAmount: number;

	public showQuestionButtons = false;
	public questionText: string = '';
	public questionOptions: string[] = [];
	private answerCallback: (answer: string) => void;
	public statusMessage: string = '';

	ngOnInit() {
		console.log(this.transType);
		this.managerSys$ = this.store.select(SysConfigSelectors.selectSysConfig);
		this.staff$ = this.store.select(staffSelectors.selectStaffLoginDto);
		this.pinAuth$ = this.store.select(PinAuthSelectors.selectPINAuth);
		this.pinAuth$.subscribe(result => {
			if (result) {
				this.pinAuth = result;
			}
		});

		combineLatest([this.managerSys$, this.staff$]).pipe(take(1)).subscribe(([managerSys, staff]) => {
			this.integratedEftProvider = managerSys.integratedEFTProvider;
			this.staffCode = staff.code;
			this.staffName = staff.name;
			console.log(this.transType);
			console.log('Items:', this.items);
			switch (this.transType) {
				case "Purchase": {
					console.log(1);
					this.initiateEftposPurchase(this.integratedEftProvider);
					break;
				}
				case "Refund": {
					this.initiateEftposRefund(this.integratedEftProvider);
					break;
				}
				default: {
					console.error("Unknown transaction type");
					this.activeModal.close(false);
					break;
				}
			}
		});

		this.eftposService.question$.subscribe((question: TyroQuestion) => {
			this.questionText = question.text;
			this.questionOptions = question.options;
			this.answerCallback = question.answerCallback;
			this.showQuestionButtons = true;
		});

		this.eftposService.statusMessage$.subscribe(status => {
			this.statusMessage = status;
		});
	}

	answerQuestion(option: string): void {
		if (this.answerCallback) {
			// Clear the UI once an answer is chosen.
			this.showQuestionButtons = false;
			this.questionOptions = [];
			// Pass the answer back via the callback.
			this.answerCallback(option);
		}
	}

	ngOnDestroy() {
		this.modalClosed = true;
	}

	// For purchases
	async initiateEftposPurchase(integratedEFTProvider: string) {
		console.log("Initiating purchase, tender amount:", this.tenderAmount);
		switch (integratedEFTProvider.toLowerCase()) {
			case "linkly":
				try {
					// Call the purchase API
					const result = await this.eftposService.processPurchase(
						this.totalAmount,
						this.tenderAmount,
						this.totalEftAmount,
						this.taxAmt,
						this.discountAmt,
						this.surchargeAmt,
						this.transNo,  // TODO: verify transNo source
						'AUD',
						'0',
						'0',
						this.staffCode + '|' + this.staffName,
						'0000',
						this.items,
						"" // Not Using
					).toPromise();
					console.log(result);
					// Following forces through a success if a pinpad is connected
					//if (true) {
					//	const paymentResult = new Payment();
					//	paymentResult.amount = this.tenderAmount;
					//	paymentResult.type = PaymentType.Eftpos;
					//	paymentResult.desc = `Transaction processed`;

					//	if (!this.modalClosed) {
					//		this.activeModal.close({ paymentResult });
					//		this.modalClosed = true;
					//	}
					//	return
					//}
					this.status = result.status;
					if (this.status === "APPROVED" || this.status === "SIGNATURE APPROVED") {

						// Wait for polling to complete.
						//await this.pollTransactionStatus(this.transNo, integratedEFTProvider);

						// Build the Payment object for a successful purchase.
						const paymentResult = new Payment();
						paymentResult.amount = this.tenderAmount;
						paymentResult.type = PaymentType.Eftpos;
						paymentResult.desc = `Transaction processed`;

						if (!this.modalClosed) {
							this.activeModal.close({ paymentResult });
							this.modalClosed = true;
						}
					} else {
						console.error("Purchase call did not return a success response.");
						if (!this.modalClosed) {
							this.modalClosed = true;
							const errorModalRef = CreateErrorModal(this.modalService, true, "Eftpos payment unsuccessful: Error " + this.status, true, "* Accept Anyway *")
							errorModalRef.result
								.then((accepted: boolean) => {
									if (accepted) {
										// If Errors but Confirm Anyway is clicked
										const despiteErrorPayment = new Payment();
										despiteErrorPayment.amount = this.tenderAmount;
										despiteErrorPayment.type = PaymentType.Eftpos;
										despiteErrorPayment.desc = `Transaction processed (Error ignored)`;
										despiteErrorPayment.paid = true;
										this.activeModal.close({despiteErrorPayment});
									}
									else {
										this.activeModal.close(false);
									}
								})
								.catch(() => {
								});
						}
					}
				} catch (error) {
					console.error('Error processing EFTPOS purchase:', error);
					if (!this.modalClosed) {
						this.activeModal.close(false);
						this.modalClosed = true;
						CreateErrorModal(this.modalService, true, "Eftpos payment unsuccessful: Error " + this.status)

					}
				}
				break;

			case "tyro":
				try {
					const result = await this.eftposService.tyroProcessPayment(
						this.tenderAmount,
						this.pinAuth.merchantId,
						this.pinAuth.terminalId,
						this.pinAuth.integratedEFTSecret,
						this.transNo
					).toPromise();
					this.status = result.status;
					this.surchargeAmount = result.surchargeAmount;
					if (this.status === "APPROVED" || this.status === "SIGNATURE APPROVED") {
						// Wait for polling to complete.

						// Build the Payment object for a successful purchase.
						const paymentResult = new Payment();
						paymentResult.amount = this.tenderAmount;
						paymentResult.type = PaymentType.Eftpos;
						paymentResult.desc = `Transaction processed`;

						const surchargePayment = new Payment();

						if (this.surchargeAmount) {
							surchargePayment.amount = this.surchargeAmount;
							surchargePayment.type = PaymentType.Surcharge;
							surchargePayment.desc = "Tyro Surcharge";
						}

						if (!this.modalClosed) {
							this.activeModal.close({ paymentResult, surchargePayment });
							this.modalClosed = true;
						}
					}
					else {
						console.error("Purchase call did not return a success response.");
						if (!this.modalClosed) {
							this.activeModal.close(false);
							this.modalClosed = true;
							CreateErrorModal(this.modalService, true, "Eftpos payment unsuccessful: Error " + this.status)
						}
					}
				}
				catch (error) {
					console.error('Error processing EFTPOS purchase:', error);
					if (!this.modalClosed) {
						this.activeModal.close(false);
						this.modalClosed = true;
						CreateErrorModal(this.modalService, true, "Eftpos payment unsuccessful: Error " + this.status)

					}
				}
				break;
		}
	}
	// For refunds
	async initiateEftposRefund(integratedEFTProvider: string) {
		console.log("Initiating refund, tender amount:", this.tenderAmount);
		try {
			const result = await this.eftposService.processRefund(
				this.tenderAmount,              // Refund amount
				this.transNo,        // Transaction reference
				'AUD',                          // Currency code
				'0',                            // Receipt auto-print flag
				'' // Not Using
			).toPromise();
			this.status = result.status;
			console.log(result.status);

			// Following forces through a success if a pinpad is connected
			//if(true){
			//	const paymentResult = new Payment();
			//	paymentResult.amount = this.tenderAmount;
			//	paymentResult.type = PaymentType.Eftpos;
			//	paymentResult.desc = `Transaction processed`;

			//	if (!this.modalClosed) {
			//		this.activeModal.close({ paymentResult });
			//		this.modalClosed = true;
			//	}
			//	return
			//}

			if (result.status === "APPROVED" || result.status === "SIGNATURE APPROVED") {

				//await this.pollTransactionStatus(this.transNo, integratedEFTProvider);

				const paymentResult = new Payment();
				paymentResult.amount = this.tenderAmount;
				paymentResult.type = PaymentType.Eftpos;
				paymentResult.desc = `Refund processed`;

				if (!this.modalClosed) {
					this.activeModal.close(paymentResult);
					this.modalClosed = true;
				}
			} else {
				console.error("Refund call did not return a 202 Accepted response.");
				if (!this.modalClosed) {
					this.activeModal.close(false);
					this.modalClosed = true;
					CreateErrorModal(this.modalService, true, "Eftpos refund unsuccessful: Error  " + this.status)
				}
			}
		} catch (error) {
			console.error('Error processing EFTPOS refund:', error);
			if (!this.modalClosed) {
				this.activeModal.close(false);
				this.modalClosed = true;
				CreateErrorModal(this.modalService, true, "Eftpos refund unsuccessful: Error " + this.status)
			}
		}
	}

	sendKey(key: string): void {
		console.log('cancel');
		this.eftposService.sendKey(this.transNo, key);
	}

	cancelTyroTransaction(): void {
		console.log('cancel');
		this.eftposService.cancelTyroTransaction();
	}

	//// Polling: waits for the transaction status to become Complete or Failed. DOING ON BACKEND
	//pollTransactionStatus(transNo: number, integratedEFTProvider: string): Promise<void> {
	//	return new Promise<void>(async (resolve, reject) => {
	//		const timeout = 60000; // Maximum polling duration: 60 seconds
	//		const interval = 200;  // Poll every 200 ms
	//		const startTime = Date.now();

	//		try {
	//			while (true) {
	//				if (Date.now() - startTime > timeout) {
	//					console.error('Polling timed out');
	//					resolve();
	//					return;
	//				}

	//				const session = await this.eftposService
	//					.getTransactionStatus(transNo, integratedEFTProvider)
	//					.toPromise();
	//				console.log('Current status:', session);

	//				if (session.status === 'Complete' || session.status === 'Failed') {
	//					if (session.status === 'Complete') {
	//						console.log('Transaction completed successfully');
	//					} else {
	//						console.log('Transaction failed');
	//					}
	//					resolve();
	//					return;
	//				} else {
	//					await new Promise(r => setTimeout(r, interval));
	//				}
	//			}
	//		} catch (error) {
	//			reject(error);
	//		}
	//	});
	//}
}
