import { createAction, props } from '@ngrx/store';
import {TransferReasonDto, TransferDestinationResponseDto, StaffLoginResponseDto, LocationDto, StockItemDto, SendStockRequestDto} from '../../pos-server.generated';


export const init = createAction('[Send Stock] init');

export const setReasonsLookup = createAction('[Send Stock] Set Reasons Lookup', props<{ reasons: TransferReasonDto[] }>());
export const setReason = createAction('[Send Stock] Set Reason', props<{ reason: TransferReasonDto }>());


export const setDestinationsLookup = createAction('[Send Stock] Set Destinations Lookup',
	props <{ destinations: TransferDestinationResponseDto }>());
export const setDestination = createAction('[Send Stock] Set Destination', props<{ destination: LocationDto }>());


export const setTransferNumber = createAction('[Send Stock] Set Transfer Number', props<{transferNumber: string}>());
export const resetTransfer = createAction('[Send Stock] Reset Transfer');

export const submit = createAction('[Send Stock] submit', props<{sendStock: SendStockRequestDto}>());
export const submitResponse = createAction('[Send Stock] submit Response');
