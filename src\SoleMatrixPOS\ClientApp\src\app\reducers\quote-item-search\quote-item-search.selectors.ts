import { createSelector } from '@ngrx/store';
import { AppState } from '../index';
import { QuoteSearchState } from './quote-item-search.reducer';

export const selectQuoteItemSearchState = (state: AppState) => state.customerQuoteSearch;

export const searchedQuotes = createSelector(
  selectQuoteItemSearchState,
  (state: QuoteSearchState) => state.quotes
);

export const searchOptions = createSelector(
  selectQuoteItemSearchState,
  (state: QuoteSearchState) => state.options
);

export const searchLoading = createSelector(
  selectQuoteItemSearchState,
  (state: QuoteSearchState) => state.isLoading
);

export const selectedOrder = createSelector(
  selectQuoteItemSearchState,
  (state: QuoteSearchState) => state.selected
);

export const QuoteError = createSelector(
  selectQuoteItemSearchState,
  (state: QuoteSearchState) => state.error
);
