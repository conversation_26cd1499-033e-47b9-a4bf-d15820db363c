<div class="d-flex justify-content-center align-items-center vh-100 position-relative ">
	<div class="position-absolute" style="top:1rem;right:1rem;" >
		<button class="btn btn-primary d-flex align-items-center justify-content-center ratio-1x1" (click)="handleLogout()">
			<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-box-arrow-right" viewBox="0 0 16 16">
  				<path fill-rule="evenodd" d="M10 12.5a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v2a.5.5 0 0 0 1 0v-2A1.5 1.5 0 0 0 9.5 2h-8A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-2a.5.5 0 0 0-1 0z"/>
  				<path fill-rule="evenodd" d="M15.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708.708L14.293 7.5H5.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708z"/>
			</svg>
		</button>
	</div>
	<div class="card" style="width:15rem">
	<h3 class="card-header">
		Staff Code
	</h3>
	<div class="card-body">
		<form (submit)="submit()" autocomplete="off">

			<!-- Store Selection Dropdown -->
			<!--<div class="form-group">
				<label for="storeSelect">Select Store:</label>
				<select id="storeSelect" class="form-control" [(ngModel)]="storeName" name="storeName" (change)="onStoreChange()">
					<option value="" disabled>Select a Store</option>
					<option *ngFor="let store of stores" [value]="store.storeName">
						{{ store.storeName }}
					</option>
				</select>
			</div>-->

			<!-- PIN Pad Selection Dropdown -->
			<!--<div class="form-group" *ngIf="pinPads.length > 0">
				<label for="pinPadSelect">Select PIN Pad:</label>
				<select id="pinPadSelect" class="form-control" [(ngModel)]="pinPadId" name="pinPadId">
					<option value="" disabled>Select a PIN Pad</option>
					<option *ngFor="let pinPad of pinPads" [value]="pinPad.pinPadId">
						{{ pinPad.pinPadCommonName }}
					</option>
				</select>
			</div>-->

			<!-- Staff Code Input -->
			 <div class="mb-3">
				 <div class="form-group">
					 <!--<label for="staffCode">Enter Staff Code:</label>-->
						 <input type="password" id="staffCode" class="form-control form-control-lg" [(ngModel)]="staffCode" name="staffCode" autocomplete="new-password" autofocus />
				 </div>
			 </div>

			 <div>
				<button class="btn btn-info btn-lg w-100" type="submit">
					<ng-container *ngIf="!loading; else loadingSpinner">
						Login
					</ng-container>
					<ng-template #loadingSpinner>
						<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
						Loading...
					</ng-template>
				</button>
			</div>


			<div class="alert alert-warning my-2" role="alert" *ngIf="staffLoginMessage$ | async as message">
				{{ message }}
			</div>
		</form>

		<!-- Login Message -->

	</div>
</div>
</div>
