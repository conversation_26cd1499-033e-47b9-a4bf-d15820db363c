import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { WaitingForEftposModalComponent } from '../../payment/waiting-for-eftpos-modal/waiting-for-eftpos-modal.component';
import { Transaction } from '../../payment/payment.service';
import { GetReceiptDto, SuspendSaleClient } from '../../pos-server.generated';
import { EftposService, mapCartToLinklyBasket } from '../eftpos.service';
import { Store } from '@ngrx/store';
import { AppState } from '../../reducers';
import { CartItem } from '../../reducers/sales/cart/cart.reducer';
import { PrintingService } from '../../printing/printing.service';

@Component({
  selector: 'pos-check-cancel-sale-modal',
  templateUrl: './check-cancel-sale-modal.component.html',
  styleUrls: ['./check-cancel-sale-modal.component.scss']
})
export class CheckCancelSaleModalComponent implements OnInit {

	@Input() transaction: Transaction;
	@Input() intEftProvider: string;
	@Input() cartTotal: number;
	@Input() transNo: number;
	@Input() cart: CartItem[];

	constructor(
		public activeModal: NgbActiveModal,
		private modalService: NgbModal,
		private store: Store<AppState>,
		private eftposService: EftposService,
		private printingService: PrintingService,
		private suspendSaleClient: SuspendSaleClient
	) { }

	ngOnInit() {
	}

	dismissModal() {
		this.activeModal.close(false)
	}

	async refund() {
		const totalEftAmount = this.transaction.successfulEft;
		let allSuccessful = true;
		let first = true;

		const payments = [...this.transaction.payments]
		for (let currentPayment of payments) {
			if (currentPayment.paid != true) {
				continue
			}
			const modalRef = this.modalService.open(WaitingForEftposModalComponent, {
				size: 'md',
				centered: true,
				backdrop: 'static',
				keyboard: false
			});
			switch (this.intEftProvider) {
				case "Linkly":
					modalRef.componentInstance.tenderAmount = currentPayment.amount;
					modalRef.componentInstance.totalAmount = this.cartTotal;
					modalRef.componentInstance.totalEftAmount = totalEftAmount;
					modalRef.componentInstance.store = this.store;
					modalRef.componentInstance.discountAmt = 0; // TODO: calculate discount if needed
					modalRef.componentInstance.surchargeAmt = currentPayment.amount * 0; // TODO: adjust surcharge calculation if required
					modalRef.componentInstance.taxAmt = currentPayment.amount * 0; // TODO: adjust tax calculation based on config
					modalRef.componentInstance.transNo = this.transNo; // TODO: generate or retrieve a transaction number

					// Map the current cart to the format required by Linkly
					const mappedItems = mapCartToLinklyBasket(this.cart);
					modalRef.componentInstance.items = mappedItems;
					modalRef.componentInstance.transType = "Refund";
					break;

				case "Tyro":
					modalRef.componentInstance.tenderAmount = currentPayment.amount;
					modalRef.componentInstance.transNo = this.transNo;
					modalRef.componentInstance.transType = "Refund";
					break;

				default:
					console.log("Integrated EFTPOS not configured");
					return;
			}

			try {
				if (!first) { // Tyro issues without this
					await new Promise(resolve => setTimeout(resolve, 3000));
				}

				const result: any = await modalRef.result;
				if (!result) {
					allSuccessful = false;
					this.suspendSaleClient.resetSuspendSaleStatus(false).subscribe();

					console.log("EFTPOS payment failed or was cancelled");
					break
				} else {
					console.log("EFTPOS payment result:", result);
					first = false;
					this.transaction.refundEft(currentPayment.amount);
					this.transaction.removePayment(currentPayment);
					this.suspendSaleClient.resetSuspendSaleStatus(false).subscribe();
					this.eftposService.getReceipts(this.transNo, false) // TODO JASON figure out what to do here
						.subscribe((receipts: GetReceiptDto[]) => {
							this.printingService.printEftposReceipt([receipts[receipts.length - 1]], true);
						});
					this.suspendSaleClient.updateSuspendEftPaid(this.transaction.successfulEft).subscribe(); // Remove from suspend sale
				}
			} catch (error) {
				console.error("Error in waiting-for-EFTPOS modal:", error);
				allSuccessful = false;
				this.suspendSaleClient.resetSuspendSaleStatus(false).subscribe();
				break;
			}
		}
		if (allSuccessful) {
			// TODO Remove the suspend sale
			// this.suspendSaleClient.deleteSuspendSale();
				this.activeModal.close(true);
		}
	}

}
