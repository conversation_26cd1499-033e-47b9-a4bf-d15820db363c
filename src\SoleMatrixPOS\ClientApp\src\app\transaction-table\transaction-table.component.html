<div class="table-responsive" style="max-height: 45vh; overflow-y: auto">
	<table class="table table-striped" style="margin: 0">
		<thead
			style="
				position: sticky;
				top: 0;
				background-color: white;
				z-index: 1;
			"
		>
			<tr>
				<th scope="col">Style</th>
				<th scope="col">Description</th>
				<th scope="col">Colour</th>
				<th scope="col">Colour Code</th>
				<th scope="col">Department</th>
				<th scope="col">Maker Code</th>
				<th scope="col">Size</th>
				<th scope="col">Qty</th>
				<th scope="col">RRP</th>
				<th scope="col">Value</th>
				<th scope="col">Total Price</th>
				<th scope="col"></th>
			</tr>
		</thead>
		<tbody *ngFor="let item of cart$ | async; let i = index">
			<tr (click)="onTableItemClicked(item)" style="cursor: pointer">
				<td>{{ item.stockItem.styleCode }}</td>
				<td>{{ item.stockItem.styleDescription }}</td>
				<td>{{ item.stockItem.colourName }}</td>
				<td>{{ item.stockItem.colourCode }}</td>
				<td>{{ item.stockItem.departmentCode }}</td>
				<td>{{ item.stockItem.makerCode }}</td>
				<td>{{ item.stockItem.size }}</td>
				<td>{{ item.quantity }}</td>
				<td>{{ item.stockItem.rrp | currency }}</td>
				<td>{{ item.bestValue | currency }}</td>
				<td>{{ item.bestValue * item.quantity | currency }}</td>
				<td>
					<span
						class="fas fa-trash fa-lg deleteButton"
						(click)="
							onTableItemDeleteClicked(i);
							$event.stopPropagation()
						"
					></span>
				</td>
			</tr>
			<!-- Discount Row -->
			<tr *ngIf="item.discount > 0" class="discount-row">
				<td></td>
				<td colspan="6" class="text-muted">
					<small>{{
						item.discountReason
							? item.discountCode + " - " + item.discountReason
							: item.discountCode
					}}</small>
				</td>
				<td></td>
				<td class="text-right text-muted">
					<span style="font-size: 1.2em">Discount:</span>
				</td>
				<td class="text-danger">
					<span style="font-size: 1.2em"
						>-{{ item.discount | currency }}</span
					>
				</td>
				<td class="text-danger">
					<span style="font-size: 1.2em"
						>-{{ item.discount * item.quantity | currency }}</span
					>
				</td>
				<td>
					<span
						class="fas fa-times fa-lg deleteButton"
						(click)="removeDiscount(item); $event.stopPropagation()"
						title="Remove discount"
					>
					</span>
				</td>
			</tr>
			<!-- Adjustment row -->
			<tr *ngIf="item.discount < 0" class="discount-row">
				<td></td>
				<td colspan="6" class="text-muted">
					<small>{{
						item.discountReason
							? item.discountCode + " - " + item.discountReason
							: item.discountCode
					}}</small>
				</td>
				<td></td>
				<td class="text-right text-muted">
					<span style="font-size: 1.2em">Adjusted:</span>
				</td>
				<td class="text-danger">
					<span style="font-size: 1.2em"
						>+{{ -item.discount | currency }}</span
					>
				</td>
				<td class="text-danger">
					<span style="font-size: 1.2em"
						>+{{ -item.discount * item.quantity | currency }}</span
					>
				</td>
				<td>
					<span
						class="fas fa-times fa-lg deleteButton"
						(click)="removeDiscount(item); $event.stopPropagation()"
						title="Remove discount"
					>
					</span>
				</td>
			</tr>
			<!-- Reasons row -->
			<tr
				*ngFor="let reason of getReasons(item.stockItem); let j = index"
			>
				<td>Reason</td>
				<td>{{ reason }}</td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td>
					<span
						class="fas fa-trash fa-lg deleteButton"
						(click)="onTableReasonDeleteClicked(i, j)"
					></span>
				</td>
			</tr>
		</tbody>
	</table>
</div>
