<pos-nav-header pageName="Client History"></pos-nav-header>

<div class="content-wrapper flex-grow-1 pt-10">
    <div class="container-fluid">
        <!-- Client Name Display -->
        <div class="row text-align-center mt-2 mb-2 d-flex" *ngIf="member">
            <div class="col-12">
                <h4 class="text-primary mb-0">{{ member.firstname }} {{ member.surname }}</h4>
            </div>
        </div>

        <div class="row text-align-center mt-2 mb-4 d-flex">
            <h3 class="mt-3 mb-0 mr-2 ml-4 mr-4 text-secondary">Search</h3> <div class="col-2 pr-0">
                <select class="form-control form-control-special" [(ngModel)]="field" (change)="search()">
                    <option value="Date" selected>DATE</option>
                    <option value="Type">TYPE</option>
                    <option value="Style">STYLE</option>
                    <option value="Colour">COLOUR</option>
                    <option value="Size">SIZE</option>
                    <option value="Qty">QTY</option>
                    <option value="Price">PRICE</option>
                    <option value="Staff">STAFF</option>
                </select>
            </div>

            <div class="col-3 pl-0">
                <input *ngIf="field !== 'Date'" type="text" class="form-control" [(ngModel)]="term" (keyup)="search()" />

                <p-calendar appendTo="body" *ngIf="field === 'Date'" [(ngModel)]="selectedDates" 
                    (ngModelChange)="onDateChange($event)" selectionMode="range" 
                    dateFormat="dd/mm/yy" (onSelect)="search()" (keydown)="onCalendarKeyDown($event)"
                    placeholder="Select Date Range" 
                    [style]="{'width': '100%', 'height': '87%'}" 
                    [inputStyle]="{'width': '100%', 'height': '87%'}"> 
                </p-calendar>
            </div>
        </div>

        <div class="row m-2">
            <div class="table-responsive" style="max-height: 62vh; overflow-y: auto;" (scroll)="onScroll($event)">
                <table class="table table-striped ml-2 mr-2 table-hover">
                    <thead>
                        <tr>
                            <th scope="col-1">Date</th>
                            <th scope="col-1">Time</th>
                            <th scope="col-2">Type</th>
                            <th scope="col-2">Style Code</th>
                            <th scope="col-2">Style Description</th>
                            <th scope="col-2">Colour</th>
                            <th scope="col-1">Size</th>
                            <th scope="col-1">Qty</th>
                            <th scope="col-2">Price</th>
                            <th scope="col-1"> </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let transaction of transactionHistory$ | async; let i = index">
                            <td><ngb-highlight [result]="transaction.transactionDate | date:'d/MM/yyyy'" [highlightClass]="'search-highlight'"></ngb-highlight></td>
                            <td><ngb-highlight [result]="transaction.transactionTime" [highlightClass]="'search-highlight'"></ngb-highlight></td>
                            <td><ngb-highlight [result]="transaction.transName" [highlightClass]="'search-highlight'"></ngb-highlight></td>
                            <td><ngb-highlight [result]="transaction.styleCode" [highlightClass]="'search-highlight'"></ngb-highlight></td>
                            <td><ngb-highlight [result]="transaction.stockDescription" [highlightClass]="'search-highlight'"></ngb-highlight></td>
                            <td><ngb-highlight [result]="transaction.colourCode" [highlightClass]="'search-highlight'"></ngb-highlight></td>
                            <td><ngb-highlight [result]="transaction.sizeCode" [highlightClass]="'search-highlight'"></ngb-highlight></td>
                            <td><ngb-highlight [result]="transaction.quantity" [highlightClass]="'search-highlight'"></ngb-highlight></td>
                            <td><ngb-highlight [result]="transaction.sellingPrice | currency" [highlightClass]="'search-highlight'"></ngb-highlight></td>
                            <td>
                                <button type="button" class="btn btn-secondary"
                                    *ngIf="transaction.transType !== '7' && transaction.transType !== '8' && transaction.transType !== '17'"
                                    (click)="openReceiptModal(transaction.transNo, transaction.transType, transaction.tillNo, transaction.storeId)">
                                    RECEIPT
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="text-center mt-3" *ngIf="isLoading$ | async">
                    <mat-spinner diameter="30"></mat-spinner>
                </div>
                <div *ngIf="allLoaded && !(isLoading$ | async)" class="text-center text-muted mt-2">
                    No more results.
                </div>
            </div>
        </div>
    </div>
</div>

<div class="footer-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-auto">
                <button type="button" class="btn btn-secondary" (click)="goBack()">
                    <i class="fas fa-arrow-left mr-1"></i>Back
                </button>
            </div>
        </div>
    </div>
</div>