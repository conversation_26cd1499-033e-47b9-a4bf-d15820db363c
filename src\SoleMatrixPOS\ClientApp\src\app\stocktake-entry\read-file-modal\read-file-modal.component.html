<div class="modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="overflow-y: scroll; max-height:85%;  margin-top: 50px; margin-bottom:50px;">
        <div class="modal-content">

            <div class="modal-header navbar navbar-light navbar-expand-lg bg-light">
                <h4 class="modal-title" id="modal-basic-title">Scanned List..</h4>
                <button type="button" class="btn-close" aria-label="Close" (click)="exit()"
                    (click)="activeModal.dismiss('Cross click')"><i class="fa fa-times"
                        style="color: red;"></i></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <div *ngIf="loading">
                            <mat-spinner style="margin:0 auto;" mode="indeterminate"> Reading file...</mat-spinner>
                        </div>
                        <div *ngIf="!loading">

                            <h5>Item Barcodes</h5>
                            <!-- <ul>
                                <li *ngFor="let item of datFiles$ | async">
                                    <span>{{item.barCode}}</span> - {{item.quantity}}
                                </li>
                            </ul> -->

                            <table class="table">
                                <tr>
                                    <th scope="col">Barcode</th>
                                    <th scope="col">Quantity</th>
                                </tr>

                                <tr *ngFor="let item of datFiles$ | async">
                                    <td>{{item.barCode}} </td>
                                    <td>{{item.quantity}}</td>
                                </tr>

                            </table>
                        </div>
                    </div>
                </form>
            </div>

            <div class="ml-4">
                <div class="valid-barcode-msg">
                    {{itemScanned}} unit(s) Scanned
                </div>
            </div>

            <div class="modal-footer">
                <!-- <button class="btn btn-info" [disabled]="Validated" (click)="getCount()">Get Count</button> -->

                <!-- <button class="btn btn-danger" (click)="printInvalid()">Print Invalid</button> -->
                <button class="btn btn-danger" (click)="exit()" (click)="activeModal.dismiss('Cross click')">Back</button>
                <button type="button" class="btn btn-outline-info"
                    (click)="activeModal.close('Save click')">Process</button>
            </div>
        </div>
    </div>
</div>