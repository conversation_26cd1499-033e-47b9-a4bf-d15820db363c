import { Action } from "@ngrx/store";
import { type } from "os";
import { Header } from "../../classes/header.model";

export const LOAD_HEADER_REQUESTED: string = "[HEADER] loadReq";
export const LOAD_HEADER: string = "[HEADER] load";

export class LoadHeaderRequested implements Action{
    readonly type = LOAD_HEADER_REQUESTED;

    constructor() {}
}

export class LoadHeaderAction implements Action{
    readonly type = LOAD_HEADER;

    constructor(public headers: Header[]){

    }
}

export type SiteActionType = LoadHeaderRequested | LoadHeaderAction;