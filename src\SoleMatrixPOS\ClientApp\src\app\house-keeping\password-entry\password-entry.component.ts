import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AppState } from 'src/app/reducers';
import * as houseActions from '../../reducers/house-keeping/mLogin.actions';
import { ManagerState, ManagerLoginState } from '../../reducers/house-keeping/mLogin.reducers';
import { HouseKeepingClient, ManagerLoginResult } from 'src/app/pos-server.generated';

@Component({
  selector: 'pos-password-entry',
  templateUrl: './password-entry.component.html',
  styleUrls: ['./password-entry.component.scss']
})
export class PasswordEntryComponent implements OnInit {

  loginForm: FormGroup;
  loading = false;
  submitted = false;
  wrongPassword = false;

  managerLoginMessage$: Observable<string>;
  managerState$: Observable<ManagerState>;

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private store: Store<AppState>,
    private houseKeepingClient: HouseKeepingClient,
    public activeModal: NgbActiveModal
  ) { }

  ngOnInit() {
    this.managerLoginMessage$ = this.store.select(s => s.manager.loginMessage);
    this.managerState$ = this.store.select(s => s.manager);
    
    // Add direct subscription to loginMessage for debugging
    this.managerLoginMessage$.subscribe(message => {
      console.log('Login message:', message);
      this.wrongPassword = message === 'Login Failed!';
    });

    this.loginForm = this.formBuilder.group({
      password: ['', Validators.required]
    });

    // Reset error when user types
    this.loginForm.get('password').valueChanges.subscribe(() => {
      this.wrongPassword = false;
    });
  }

  // convenience getter for easy access to form fields
  get f() { return this.loginForm.controls; }

  onSubmit(data) {
    this.submitted = true;
    this.wrongPassword = false; // Reset error state

    if (this.loginForm.invalid) {
      return;
    }

    this.loading = true; // Set loading to true when submitting

    // Direct API call instead of relying on ngrx effects
    this.houseKeepingClient.login(data.password)
      .pipe(
        catchError(error => {
          this.wrongPassword = true;
          this.loading = false;
          return throwError(error);
        })
      )
      .subscribe(response => {
        this.loading = false;
        if (response.mLoginResultCode === ManagerLoginResult.Complete) {
          this.store.dispatch(houseActions.mLoginResponse({ payload: response }));
          // Close modal with success result
          this.activeModal.close(response);
        } else {
          this.wrongPassword = true;
        }
      });
  }

  dismiss() {
    // Use the modal's dismiss method instead of router navigation
    this.activeModal.dismiss('Cross click');
  }
}
