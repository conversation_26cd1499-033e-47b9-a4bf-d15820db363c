import { Action, createReducer, on } from "@ngrx/store";
import { ReceiptUpdateDto } from "src/app/pos-server.generated";
import * as receiptAction from './receipt.actions';

// Assuming these types have at least a 'recLine' property
export interface ReceiptTitleDto { recLine: string; }
export interface ReceiptHeaderDto { recLine: string; }
export interface ReceiptFooterDto { recLine: string; }
export interface ReceiptLaybyDto { recLine: string; }

export enum UpdateReceiptStatus {
  None,
  Success,
  Error
}

// Extend the state to hold the full receipt data
export interface UpdateReceiptState {
  receiptTitle: ReceiptTitleDto[];
  receiptHeader: ReceiptHeaderDto[];
  receiptFooter: ReceiptFooterDto[];
  receiptLayby: ReceiptLaybyDto[];
  updateRecDto: ReceiptUpdateDto | null;
  saveMessage: string;
  updateRecStatus: UpdateReceiptStatus;
}

export const initialState: UpdateReceiptState = {
  receiptTitle: [],   // populate with initial data if needed
  receiptHeader: [],
  receiptFooter: [],
  receiptLayby: [],
  updateRecDto: null,
  saveMessage: '',
  updateRecStatus: UpdateReceiptStatus.None,
};

export const UpdateReceiptReducer = createReducer(
  initialState,

  on(receiptAction.init, state => ({ ...initialState })),

  on(receiptAction.updateRec, (state, action) => ({ ...state })),

  on(receiptAction.updateRecSuccess, (state, action) => {
    const updatedRec = action.result;
    // Determine which array to update based on recType
    switch (updatedRec.recType) {
      case 'T': {
        const updatedTitle = state.receiptTitle.map((item, index) =>
          index === (updatedRec.lineNo! - 1)
            ? { ...item, recLine: updatedRec.recLine }
            : item
        );
        return { 
          ...state, 
          receiptTitle: updatedTitle, 
          updateRecDto: updatedRec, 
          updateRecStatus: UpdateReceiptStatus.Success 
        };
      }
      case 'H': {
        const updatedHeader = state.receiptHeader.map((item, index) =>
          index === (updatedRec.lineNo! - 1)
            ? { ...item, recLine: updatedRec.recLine }
            : item
        );
        return { 
          ...state, 
          receiptHeader: updatedHeader, 
          updateRecDto: updatedRec, 
          updateRecStatus: UpdateReceiptStatus.Success 
        };
      }
      case 'F': {
        const updatedFooter = state.receiptFooter.map((item, index) =>
          index === (updatedRec.lineNo! - 1)
            ? { ...item, recLine: updatedRec.recLine }
            : item
        );
        return { 
          ...state, 
          receiptFooter: updatedFooter, 
          updateRecDto: updatedRec, 
          updateRecStatus: UpdateReceiptStatus.Success 
        };
      }
      case 'L': {
        const updatedLayby = state.receiptLayby.map((item, index) =>
          index === (updatedRec.lineNo! - 1)
            ? { ...item, recLine: updatedRec.recLine }
            : item
        );
        return { 
          ...state, 
          receiptLayby: updatedLayby, 
          updateRecDto: updatedRec, 
          updateRecStatus: UpdateReceiptStatus.Success 
        };
      }
      default:
        // In case no matching type, just update the dto and status
        return { 
          ...state, 
          updateRecDto: updatedRec, 
          updateRecStatus: UpdateReceiptStatus.Success 
        };
    }
  }),

  on(receiptAction.updateError, (state, action) => ({
    ...state,
    updateRecStatus: UpdateReceiptStatus.Error
  })),

  on(receiptAction.loggedout, state => ({
    ...state,
    updateRecStatus: UpdateReceiptStatus.None
  })),
);

export function reducer(state: UpdateReceiptState | undefined, action: Action) {
  return UpdateReceiptReducer(state, action);
}
