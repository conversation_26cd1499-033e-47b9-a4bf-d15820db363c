import {Injectable} from '@angular/core';
import {Actions, createEffect, ofType} from '@ngrx/effects';
import * as stockSearchActions from '../stock-search/stock-search.actions';
import {catchError, map, mergeMap, withLatestFrom} from 'rxjs/operators';
import {EMPTY, of} from 'rxjs';
import {StockSearchClient} from '../../pos-server.generated';
import {Store} from '@ngrx/store';
import {AppState} from '../index';
import * as stockSearchSelectors from '../../reducers/stock-search/stock-search.selectors';

@Injectable()
export class StockSearchEffects {
	constructor(
		private store: Store<AppState>,
		private actions$: Actions,
		private stockSearchClient: StockSearchClient) {
	}

	matchedItems$ = createEffect(() => this.actions$
		.pipe(
			ofType(stockSearchActions.search),
			withLatestFrom(
				this.store.select(stockSearchSelectors.searchOptions)
			),
			mergeMap(([action, options]) => {
				return this.stockSearchClient.get({...options})
					.pipe(
						map(foundItems => stockSearchActions.searchResponse({payload: foundItems})),
						catchError(() => EMPTY)
					);
			})
		));

	matchedStockEnquiryItems$ = createEffect(() => this.actions$
		.pipe(
			ofType(stockSearchActions.searchStockEnquiry),
			mergeMap(action => {
				return this.stockSearchClient.getNoSize({
					...action.searchParams,
					skip: 0
				}).pipe(
					map(items => stockSearchActions.searchStockEnquirySuccess({ items })),
					catchError(() => of(stockSearchActions.searchStockEnquirySuccess({ items: [] })))
				);
			})
		));

	moreMatchedItems$ = createEffect(() => this.actions$
		.pipe(
			ofType(stockSearchActions.searchMore),
			withLatestFrom(
				this.store.select(stockSearchSelectors.searchedItemsCount),
				this.store.select(stockSearchSelectors.searchOptions)
			),
			mergeMap(([action, itemsCount, searchOptions]) => {
				return this.stockSearchClient.get({...searchOptions, skip: itemsCount})
					.pipe(
						map(foundItems => stockSearchActions.searchMoreResponse({payload: foundItems})),
						catchError(() => EMPTY)
					);
			})
		));

	searchStockEnquiry$ = createEffect(() =>
		this.actions$.pipe(
			ofType(stockSearchActions.searchStockEnquiry),
			mergeMap(action =>
				this.stockSearchClient.getNoSize({
					...action.searchParams,
					skip: 0
				}).pipe(
					map(items => stockSearchActions.searchStockEnquirySuccess({ items })),
					catchError(() => of(stockSearchActions.searchStockEnquirySuccess({ items: [] })))
				)
			)
		)
	);

	searchStockEnquiryMore$ = createEffect(() =>
		this.actions$.pipe(
			ofType(stockSearchActions.searchStockEnquiryMore),
			withLatestFrom(
				this.store.select(stockSearchSelectors.searchedItems),
				this.store.select(stockSearchSelectors.searchOptions)
			),
			mergeMap(([_, items, searchOptions]) =>
				this.stockSearchClient.getNoSize({
					...searchOptions,
					skip: items.length
				}).pipe(
					map(newItems => {
						if (newItems.length === 0) {
							return stockSearchActions.searchStockEnquiryMoreSuccess({ items: [] });
						}
						return stockSearchActions.searchStockEnquiryMoreSuccess({ items: newItems });
					}),
					catchError(() => of(stockSearchActions.searchStockEnquiryMoreSuccess({ items: [] })))
				)
			)
		)
	);
}

