import { Injectable } from '@angular/core';
import { ReceiptHelpersClient, ReceiptTemplateDto } from '../pos-server.generated';

const LOGO_LAST_MODIFIED_LOCALSTORE_KEY: string = "logo_last_modified";
const LOGO_PAYLOAD_LOCALSTORE_KEY: string = "logo_payload";

@Injectable({ providedIn: 'root' })
export class PrintCacheService {
    private baseUrl = 'http://localhost:4032';

    constructor(private receiptHelpersClient: ReceiptHelpersClient) { }

    async getLogoPayload(): Promise<string> {
        try {
            let lastModified = localStorage.getItem(LOGO_LAST_MODIFIED_LOCALSTORE_KEY);
            const result = await this.receiptHelpersClient.getEncodedReceiptLogo({
                lastModified
            }).toPromise();

            let res: string;
            if (result.shouldUpdate) {
                localStorage.setItem(LOGO_PAYLOAD_LOCALSTORE_KEY, result.encodedLogo);
                localStorage.setItem(LOGO_LAST_MODIFIED_LOCALSTORE_KEY, result.lastModified);
                res = result.encodedLogo;
            } else {
                res = localStorage.getItem(LOGO_PAYLOAD_LOCALSTORE_KEY);
                if (!res) {
                    // If there's no cached logo, try again with override
                    localStorage.removeItem(LOGO_LAST_MODIFIED_LOCALSTORE_KEY);
                    return await this.getLogoPayload();
                }
            }
            return res;
        } catch (error) {
            console.error("Error retrieving logo payload:", error);
            // On error, clear any stale values and return null
            localStorage.removeItem(LOGO_PAYLOAD_LOCALSTORE_KEY);
            localStorage.removeItem(LOGO_LAST_MODIFIED_LOCALSTORE_KEY);
            return "";
        }
    }

    async getTemplate(): Promise<ReceiptTemplateDto> {
        return await this.receiptHelpersClient.getReceiptTemplate().toPromise();
    }
}
