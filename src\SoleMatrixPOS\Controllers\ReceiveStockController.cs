using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Stock;
using SoleMatrixPOS.Application.Stock.Dtos;
using SoleMatrixPOS.Application.Stock.ReceiveStock.Commands;
using SoleMatrixPOS.Application.Stock.ReceiveStock.Queries;
using SoleMatrixPOS.Application.Stock.SendStock.Commands;
using SoleMatrixPOS.Application.Stock.SendStock.Queries;
using SoleMatrixPOS.Filters;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[RequireStaffCodeFilter]
	[ApiController]
	public class ReceiveStockController: ControllerBase
	{
		private readonly IMediator _mediator;

		public ReceiveStockController(IMediator mediator)
		{
			_mediator = mediator;
		}


		[Route("Validate")]
		[HttpPost]
		public async Task<ValidateBarcodeResponseDto> ValidateBarcode([FromBody] ValidateBarcodeRequestDto request, CancellationToken ct)
		{
			return await _mediator.Send(new ValidateBarcodeCommand(request), ct);
		}

		[Route("GetItemByBarcode")]
		[HttpPost]
		public async Task<StockItemDto> GetItemByBarcode([FromBody] ItemBarcodeSearchRequestDto request, CancellationToken ct)
		{
			return await _mediator.Send(new ItemBarcodeSearchQuery(request), ct);
		}

		[Route("SubmitReceival")]
		[HttpPut]
		public async Task<OkResult> SubmitReceival([FromBody] SubmitReceivalRequestDto request, CancellationToken ct)
		{
			await _mediator.Send(new SubmitStockItemsCommand(request));
			return Ok();
		}
	}
}
