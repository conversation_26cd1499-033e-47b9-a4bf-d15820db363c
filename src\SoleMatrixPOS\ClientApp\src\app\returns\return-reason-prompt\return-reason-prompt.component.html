<div class="modal-header">
    <h4 class="modal-title">Return Reason</h4>
    <button type="button" class="close" aria-label="Close" (click)="dismiss('cancel')">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<div class="modal-body">
    <label for="predefined-reasons">Select a reason:</label>
    <select id="predefined-reasons" class="form-control" (change)="selectPredefinedReason($event.target.value)">
        <option value="">-- Select a reason --</option>
        <option *ngFor="let reason of predefinedReasons" [value]="reason">{{ reason }}</option>
    </select>
    
    <br/>
    
    <label for="reason-input">Or enter your own (max 30 characters):</label>
    <input id="reason-input" type="text" class="form-control" 
           [formControl]="txtReason" 
           [ngClass]="{'is-invalid': inputInvalid || txtReason.errors?.maxLengthExceeded}" 
           placeholder="Enter reason"
           maxlength="30"/>
    <div *ngIf="inputInvalid && !txtReason.value?.trim()" class="invalid-feedback">
        Please enter a valid reason.
    </div>
    <div *ngIf="txtReason.errors?.maxLengthExceeded" class="invalid-feedback">
        Reason must be 30 characters or less.
    </div>
    <small class="form-text text-muted">Characters: {{ (txtReason.value || '').length }}/{{ maxLength }}</small>

    <br/>
    
    <div class="form-check">
        <input id="apply-to-all" type="checkbox" class="form-check-input" [formControl]="chkApplyToAll">
        <label class="form-check-label" for="apply-to-all">Apply to all returns</label>
    </div>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="dismiss('cancel')">Cancel</button>
    <button type="button" class="btn btn-primary" (click)="submitReason()">Submit</button>
</div>