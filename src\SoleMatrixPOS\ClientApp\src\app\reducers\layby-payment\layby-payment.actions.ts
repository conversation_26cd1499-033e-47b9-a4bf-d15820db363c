import {createAction, props} from '@ngrx/store';
import { Payment } from 'src/app/payment/payment.service';
import {LaybylineDto, LaybyPaymentWithTransactionDto, MakeLaybyPaymentResultDto, LaybySearchQueryDto, LaybySearchResultDto, MakeLaybyPaymentsDto, StockItemDto, StockSearchRequestDto} from '../../pos-server.generated';

export const init = createAction('[LaybyPayment] Init');
export const setLayby = createAction('[LaybyPayment] Set Layby', props<{layby: LaybySearchResultDto}>());
export const getLaybyLinesResponse = createAction('[LaybyPayment] Get Layby Lines Response', props<{lines: LaybylineDto[]}>());

export const addPendingPayment = createAction('[LaybyPayment] Add Pending Payment', props<{payment: Payment}>());

// Action to initiate the submission
export const submitPayments = createAction(
    '[LaybyPayment] Submit Payments',
    props<{payload: LaybyPaymentWithTransactionDto}>()
);

// Action for successful submission, now with payload
export const submitPaymentsSuccess = createAction(
    '[LaybyPayment] Submit Payments Success', // Renamed for clarity
    props<{ payload: MakeLaybyPaymentResultDto }>() // <--- CARRIES THE RESULT
);

// Action for failed submission
export const submitPaymentsFailure = createAction(
    '[LaybyPayment] Submit Payments Failure',
    props<{ error: any }>()
);

export const laybyRefund = createAction('[LaybyPayment] Layby Refund', props<{payload: MakeLaybyPaymentsDto}>());
export const submitPaymentsCompleted = createAction('[LaybyPayment] Submit Payments Completed');
export const submitRefundCompleted = createAction('[LaybyPayment] Submit Payments Completed');
export const setLaybyOrderInProgress = createAction(
    "[Layby] Set Order In Progress",
    props<{inProgress: boolean}>()
);