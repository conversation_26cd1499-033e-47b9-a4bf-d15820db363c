import { NoteDto, NoteSearchRequestDto } from 'src/app/pos-server.generated';
import { createReducer, on, Action } from '@ngrx/store';
import * as noteActions from './notes.actions'

export class NoteSearchState {
    isLoading: boolean;
    notes: NoteDto[];
    options: NoteSearchRequestDto;
    result: boolean;
}

export const initialState: NoteSearchState = {
    isLoading: false,
    notes: [],
    options: {
        searchString: '',
        clientNumber: ''
    },
    result: true
}

export const noteSearchReducer = createReducer(
    initialState,
    on(noteActions.search, (state, action) => ({...initialState, isLoading: true, options: action.searchParams})),
    on(noteActions.searchResponse, (state, action) => ({...state, isLoading: false, notes: action.payload })),

	on(noteActions.createNote, (state, action) => ({ ...state, isLoading: true, member: action.payload, result: false})),
    on(noteActions.createNoteResponse, (state, action) => ({...state, isLoading: false, result: true})),

    on(noteActions.deleteNote, (state, action) => ({ ...state, isLoading: true, member: action.payload, result: false })),
    on(noteActions.deleteNoteResponse, (state, action) => ({...state, isLoading: false, result: true})),
);

export function reducer(state: NoteSearchState | undefined, action: Action) {
    return noteSearchReducer(state, action)
}
