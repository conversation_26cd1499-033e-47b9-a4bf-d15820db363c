import { AfterViewInit, Component, EventEmitter, OnDestroy, OnInit, Output } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ActivatedRoute, Router } from '@angular/router';
import { AppState } from '../reducers';
import { Store } from '@ngrx/store';
import { CreateErrorModal } from '../error-modal/error-modal.component';
import { OpenCashDrawerAction } from '../printing/printing-definitions';
import { CustomerClubDto, ReceiptGiftVoucherDto, Giftvoucher, Translog, TranspayDto, TransactionDto, GiftVoucherPurchaseRequestDto, GiftVoucherCreationDto, TranslogDto, GetReceiptDto, EftposClient, StockItemDto, ReceiptTransactionDto } from '../pos-server.generated';
import { PaymentModalButton } from '../payment/payment-modal-button/payment-modal-button.component';
import { Payment, PaymentType, Transaction } from '../payment/payment.service';
import * as giftVoucherAction from '../reducers/gift-voucher/gift-voucher.actions';
import * as giftVoucherSelector from '../reducers/gift-voucher/gift-voucher.selectors';
import * as customerClubSearchSelectors from '../reducers/customer-club/club-search/customer-club.selectors';
import Swal from 'sweetalert2';
import { ProcessPaymentModalComponent } from '../payment/process-payment-modal/process-payment-modal.component';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Observable, Subject } from 'rxjs';
import { takeUntil, tap } from 'rxjs/operators';
import * as transActions from '../reducers/transaction/transaction.actions';
import * as transSelectors from '../reducers/transaction/transaction.selectors';
import * as receiptActions from '../reducers/receipt-printing/receipt.actions'
import { GiftVoucherState } from '../reducers/gift-voucher/gift-voucher.reducers';
import { EftposService, mapCartToLinklyBasket } from '../eftpos/eftpos.service';
import * as sysSelectors from '../reducers/sys-config/sys-config.selectors';
import { WaitingForEftposModalComponent } from '../payment/waiting-for-eftpos-modal/waiting-for-eftpos-modal.component';
import { CartItem } from '../reducers/sales/cart/cart.reducer';
import { ReceiptBatch, TextAction, CutAction, CutType, BarcodeAction, FeedAction } from '../printing/printing-definitions';
import { EmailReceiptComponent } from '../email-receipt/email-receipt.component';
import * as paymentActions from '../reducers/sales/payment/payment.actions';
import { PrintingService, SolemateReceiptOptions } from '../printing/printing.service';
import { GiftVoucherIssueComponent } from './gift-voucher-issue/gift-voucher-issue.component';
const GIFTV_TRANSTYPE = 6;
let transTypeToUse = GIFTV_TRANSTYPE;

@Component({
	selector: 'pos-gift-voucher',
	templateUrl: './gift-voucher.component.html',
	styleUrls: ['./gift-voucher.component.scss']
})
export class GiftVoucherComponent implements OnDestroy, OnInit {

	private selectedCustomerClubMember$: Observable<CustomerClubDto>;
	public selectedCustomerClubMember: CustomerClubDto = null;

	private readonly ngUnsubscribe = new Subject();

	pendingPayment$: Observable<Payment[]>;
	autoRequestedConfirmation: boolean = false;

	intEftReceipts: GetReceiptDto[];

	transNo$: Observable<number>
	transNo: number

	constructor(private printService: PrintingService, private modalService: NgbModal, private router: Router, private store: Store<AppState>, private fb: FormBuilder, private route: ActivatedRoute, private eftposService: EftposService) { }

	nPayments: number;
	giftFormP: FormGroup;

	alwaysOpenCashTill: string = "F";

	giftVoucherState: GiftVoucherState;

	balances$: Observable<number[]>;
	total: number;

	public sysStatus$: Observable<any>;
	sysStatus: any;

	modalButtons: PaymentModalButton[] = [
		new PaymentModalButton("Cash", "fa-money-bill-wave text-success", PaymentType.Cash),
		new PaymentModalButton("Eftpos", "fa-credit-card text-success", PaymentType.Eftpos)
	];


	ngOnInit() {

		//this.store.dispatch(giftVoucherAction.init());
		this.sysStatus$ = this.store.select(sysSelectors.selectSysConfig);
		this.sysStatus$.subscribe((sysconfig) => {
			this.sysStatus = sysconfig
			this.alwaysOpenCashTill = this.sysStatus.alwaysOpenCashTill;
		}
		);


		this.store.select(s => s.giftVoucher)
			//.pipe(takeUntil(this.ngUnsubscribe))
			.subscribe(state => {
				this.giftVoucherState = state;
			});

		this.store.dispatch(transActions.getTransactionNo());
		// Subscribe to the transaction number with proper error handling
		this.store.select(transSelectors.transNo)
			.pipe(
				tap(transNo => {
					this.transNo = transNo;
					console.log('Transaction number updated:', transNo);
				})
			)
			.subscribe();

		this.balances$ = this.store.select(giftVoucherSelector.balances);
		this.balances$.subscribe((balances) => {
			this.total = balances[0]
		}
		);


		this.selectedCustomerClubMember$ = this.store.select(customerClubSearchSelectors.selectedCustomerClubMember);
		this.selectedCustomerClubMember$
			.subscribe((s) => { this.selectedCustomerClubMember = s });

		//this.store.select(giftVoucherSelector.completed)
		//  //.pipe(takeUntil(this.ngUnsubscribe))
		//  .subscribe(completed => {
		//    if (completed) {
		//      this.TransactionCompleted();
		//    }
		//  })
	}

	ngOnDestroy(): void {
		this.ngUnsubscribe.next();
		this.ngUnsubscribe.complete();
	}

	readyToProcess(): boolean {
		// TODO: ensure pricing exceeds amount
		return this.giftVoucherState.payments.length > 0;
	}


	//show when transaction complete
	// TODO JORDAN print gift voucher code somewhere, also if the giftcard has the same number as one already registered, it will succeed
	// Add $0 gift card when cart window opens, update when transaction succeeds
	giftTransactionCompleted(transactionDto: TransactionDto): void {

		if (this.alwaysOpenCashTill === 'T') {
			this.store.dispatch(receiptActions.executeBatch({
				payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
			}));
		}
		else {
			if (transactionDto.payments.some(payment => payment.paymentType === 'Cash')) {
				this.store.dispatch(receiptActions.executeBatch({
					payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
				}));
			}
		}

		const saleDateTime = new Date();

		// Prepare the receiptTrans object
		let trans: ReceiptTransactionDto = {
			logs: transactionDto.translogs,
			pays: transactionDto.payments,
			saleDateTime: saleDateTime,
			transType: transTypeToUse,
		};

		Swal.fire({
			title: "Sale Completed",
			text: "The sale was successfully submitted.",
			showCancelButton: true,
			cancelButtonText: "Email Receipt",  // Set "Email Receipt" as the confirm button
			confirmButtonText: "Print Receipt",         // Set "Go Home" as the cancel button  
		}).then(async (result) => {

			// If the "Print Receipt" button is clicked
			if (result.value) {
				// This is a test for local printing
				await this.printService.printSolemateReceipt(
					"Gift Purchase",
					trans.logs,
					trans.pays,
					trans.transType,
					this.transNo.toString(),
					SolemateReceiptOptions.default(),
					trans
				);

				//this.store.dispatch(receiptActions.printSale({ payload: receiptTrans }));
			}
			// If the "Email" button is clicked
			else if (result.dismiss === Swal.DismissReason.cancel) {
				this.openEmailModal(trans).then(() => {
					// Open gift voucher issue after email is sent
					this.openGiftVoucherIssue();
				});
				return; // Exit early to prevent immediate opening of gift voucher issue
			}

			// If we get here, it means Print Receipt was chosen
			this.openGiftVoucherIssue();
		});
	}

	// Extract the gift voucher issue opening into a separate method
	private openGiftVoucherIssue(): void {
		const modalRef = this.modalService.open(GiftVoucherIssueComponent, { 
			windowClass: 'daily-modal-window',
			size: 'l',
			centered: true,
			backdrop: 'static',  // Prevent closing on backdrop click
			keyboard: false      // Prevent closing with escape key
		});

		this.store.dispatch(transActions.init());
		this.store.dispatch(paymentActions.init());
	}

	openEmailModal(receiptTrans: ReceiptTransactionDto): Promise<void> {
		return new Promise((resolve) => {
			const modalRef = this.modalService.open(EmailReceiptComponent, {
				size: 'lg',
				backdrop: 'static'
			});

			// Pass receiptTrans to the EmailReceiptComponent
			modalRef.componentInstance.receiptTrans = receiptTrans;

			// Check if a customer club member is selected and pass the email
			if (this.selectedCustomerClubMember && this.selectedCustomerClubMember.email) {
				modalRef.componentInstance.customerEmail = this.selectedCustomerClubMember.email;
			}

			modalRef.result.then(() => {
				console.log('Email receipt sent.');
				resolve();  // Resolve the promise once the modal is closed
			}).catch(() => {
				resolve();  // Resolve the promise if the modal is dismissed
			});
		});
	}

	backBtnClick() {
		this.router.navigateByUrl("gift/glance");
	}

	onProcess() {
		let modalRef = this.modalService.open(ProcessPaymentModalComponent, { size: 'xl', centered: true });
		modalRef.componentInstance.name = "Process Payment";
		modalRef.result.then((result) => {

			if (!this.readyToProcess()) {
				CreateErrorModal(this.modalService, true, "You need to make at least one payment in order to continue.");
				return;
			}

			console.log("Payment proceed: ");

			//make a record in translog and gift-voucher
			let submitGv: GiftVoucherPurchaseRequestDto = this.createGiftVoucher(this.giftVoucherState.giftVoucherCreation, this.giftVoucherState.payments);

			this.checkIntegratedEftpos(submitGv)

			// Send the entire transaction as constructed from state
			//this.store.dispatch(giftVoucherAction.submit({ payload: submitGv }));


		}).catch(res => console.log("Error occurred: ", res));

	}

	checkIntegratedEftpos(submitGv: GiftVoucherPurchaseRequestDto): void {

		console.log(this.transNo)
		let intEftPayment = new Payment;
		let intEft = false;
		let intAmount = 0;
		for (const payment of this.giftVoucherState.payments) {
			console.log(payment);
			if (payment.desc === "Integrated Eftpos") {
				intEftPayment = payment;
				intEft = true;
				intAmount = payment.amount;
				break;
			}
		}
		console.log("Please", intEft);
		if (intEft) {
			const modalRef = this.modalService.open(WaitingForEftposModalComponent, {
				size: 'md',
				centered: true,
				backdrop: 'static',
				keyboard: false
			});
			console.log(this.transNo)
			// Determine which integrated EFTPOS provider to use
			switch (this.sysStatus.integratedEFTProvider) {
				case "Linkly":
					modalRef.componentInstance.tenderAmount = intAmount;
					modalRef.componentInstance.totalAmount = this.total;
					modalRef.componentInstance.store = this.store;
					modalRef.componentInstance.discountAmt = 0; // TODO: calculate discount if needed
					modalRef.componentInstance.surchargeAmt = intAmount * 0; // TODO: adjust surcharge calculation if required
					modalRef.componentInstance.taxAmt = intAmount * 0; // TODO: adjust tax calculation based on config
					modalRef.componentInstance.transNo = this.transNo;

					const stockItem: StockItemDto = {
						barcode: "GiftVoucher" + this.total.toString(),
						price: this.total,
						styleDescription: "Gift Voucher",
						colourName: " ",
						size: this.total.toString()
					}

					const cartItem: CartItem = {
						id: Math.random().toString(36).substr(2, 9),
						quantity: 1,
						stockItem: stockItem,
						bestValue: 0,
						originalPrice: 0, //NOT USED
						lineNo: 0 //NOT USED
					}

					// Map the current cart to the format required by Linkly
					const mappedItems = mapCartToLinklyBasket([cartItem]);
					modalRef.componentInstance.items = mappedItems;
					modalRef.componentInstance.transType = "Purchase";
					break;

				case "Tyro":
					// TODO: Implement Tyro integration logic here if needed.
					break;

				default:
					console.log("Integrated EFTPOS not configured");
					return;
			}

			// Handle the result from the waiting-for-EFTPOS modal.
			modalRef.result.then((result: { paymentResult: Payment, surchargePayment: Payment }) => {
				if (result) {
					console.log("EFTPOS payment result:", result);
					this.eftposService.getReceipts(this.transNo, false)
						.subscribe((receipts: GetReceiptDto[]) => {
							this.intEftReceipts = receipts;
							console.log(receipts);
							this.store.dispatch(giftVoucherAction.submit({ payload: submitGv }));
							this.giftTransactionCompleted(submitGv.transaction);
							this.printService.printEftposReceipt(receipts, true);
						});
				} else {
					this.removePaymentOnDoubleClick(intEftPayment);
					this.store.dispatch(transActions.resetTransactionConfirmation());
					this.store.dispatch(transActions.getTransactionNo());

					console.log("EFTPOS payment failed or was cancelled");
				}
			}).catch(error => {
				console.error("Error in waiting-for-EFTPOS modal:", error);
			});

		}
		else {
			this.store.dispatch(giftVoucherAction.submit({ payload: submitGv }));
			this.giftTransactionCompleted(submitGv.transaction)

		}

	}

	//submit form value
	createGiftVoucher(voucher: GiftVoucherCreationDto, payments: Payment[]): GiftVoucherPurchaseRequestDto {
		// Gift voucher purchase request consists of two sub-dtos
		// 1. Transaction
		// 2. Voucher

		return {
			giftVoucher: {
				clientCode: this.selectedCustomerClubMember.clientCode,
				voucherFunds: voucher.voucherFunds,
				voucherNo: voucher.voucherNo
			},
			transaction: {
				transType: GIFTV_TRANSTYPE,

				translogs: [
					{
						clientCode: this.selectedCustomerClubMember.clientCode,
						styleCode: "GiftVIss",
						quantity: 1,
						sellingPrice: voucher.voucherFunds,
						gst: 0,
						nettSelling: 0,
						lineNo: 1,
						transNo: this.transNo
					} as TranslogDto
				],

				payments: payments.map(pay => {
					return {
						payAmount: pay.amount,
						paymentType: pay.type
					} as TranspayDto
				}),

				// No transrefs for a gift voucher issuance
				transReferences: []


			}
		}

	}

	//handle payment
	handlePayment(payment: any) {
		this.store.dispatch(giftVoucherAction.addPayment({ payment: { amount: payment.amount, type: payment.type, desc: payment.desc } as Payment }));
	}

	removePaymentOnDoubleClick(payment: Payment) {
		this.store.dispatch(giftVoucherAction.removePayment({ payment }))
	}
}
