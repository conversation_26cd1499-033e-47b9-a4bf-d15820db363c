import { Component, OnInit,Input } from '@angular/core';
import { DepartmentSalesDto } from 'src/app/pos-server.generated';
import { Observable, from, Subject } from 'rxjs';
import { Store } from '@ngrx/store';
import * as dailyActions from '../../reducers/daily/daily.actions'
import * as dailySelectors from '../../reducers/daily/daily.selectors'
import { AppState } from 'src/app/reducers';
import { toFinancialString } from 'src/app/utility/math-helpers';

@Component({
  selector: 'pos-department-sales',
  templateUrl: './department-sales.component.html',
  styleUrls: ['./department-sales.component.scss']
})
export class DepartmentSalesComponent implements OnInit {

  constructor(private store: Store<AppState>){}
  public departmentSales$: Observable<DepartmentSalesDto[]>;
  public departmentRefunds$: Observable<DepartmentSalesDto[]>;
  public currentView: 'sales' | 'refunds' = 'sales';

  ngOnInit(): void {
    this.departmentSales$ = this.store.select(dailySelectors.getDepartmentSales)
    this.departmentRefunds$ = this.store.select(dailySelectors.storeDepartmentRefunds)
    this.store.dispatch(dailyActions.getDepartmentSales({}))
    this.store.dispatch(dailyActions.getDepartmentRefunds({}))
  }
  
  toggleView(): void {
    this.currentView = this.currentView === 'sales' ? 'refunds' : 'sales';
  }
  
  toFinancialString(val) {
    if (val < 0) {
      return `-${toFinancialString(-1*val)}`
    }else {
      return toFinancialString(val)
    }
  }
}
