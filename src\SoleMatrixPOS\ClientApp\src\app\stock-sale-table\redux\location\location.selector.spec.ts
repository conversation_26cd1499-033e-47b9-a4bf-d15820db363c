import * as LocationSelector from './location.selector';
import { state } from '../../test-state';

describe('LocationSelector', () => {

  describe('getLocations', ()=>{

    it('should return the locations slice', () => {
      expect(LocationSelector.getLocations.projector(state)).toEqual(state.locations);
    });

  });

  describe('getImmutableLocations', ()=>{

    it('should return the locations slice', () => {
      expect(LocationSelector.getImmutableLocations.projector(state)).toEqual(state.immutableLocations);
    });

  });
  
});
