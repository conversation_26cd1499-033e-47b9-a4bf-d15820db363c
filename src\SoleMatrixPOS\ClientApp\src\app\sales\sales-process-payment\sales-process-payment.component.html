<div class="wrapper">
    <pos-nav-header pageName="Sales - Payment"></pos-nav-header>
    <div class="content-wrapper flex-grow-1">
        <div class="container-fluid">

            <div class="row text-align-center pb-3">

                <div class="column col-6">
                    <pos-payment-cart-table (removePayment)="removePaymentOnDoubleClick($event)" [transaction$]="transaction$"></pos-payment-cart-table>
                </div>
                <div class="column col-6">
                    <pos-payment-modal-button-group 
                        [transType]="transTypeToUse" 
                        [amountDue]="(transaction$ | async).amountDue" 
                        [change]="(transaction$ | async).change"
                        (paymentResult)="handlePayment($event)" 
                        [modalButtons]="modalButtons"
                        [isLaybyMode]="laybyActive"
                        [cartTotal]="cartTotal"
                        [laybyMinPolicyDeposit]="laybyMinDeposit">
                    </pos-payment-modal-button-group>
                    
                    <div *ngIf="laybyActive" class="card border-success mt-3">
                        <div class="card-header">
                            <h5 class="cart-title text-center">
                                Layby deposit total of {{isCustomDepositSet ? (customDepositAmount | currency) : (laybyMinDeposit | currency)}} is required
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="cart-text text-center">A layby deposit applies.</p>
                            <p class="cart-text text-center">The amount due has been adjusted to reflect this deposit.</p>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>
    <pos-sales-footer 
        [readyToProcess]="determineIfReadyToProcess()" 
        [laybyAllowed]="laybyModalAllowed"
        [transaction$]="transaction$" 
        [softCreditLimit]="softCreditLimit"
        [laybyActive]="laybyActive"
        (processPayment)="process()" 
        (newLayby)="newLayby($event)">
    </pos-sales-footer>
</div>
