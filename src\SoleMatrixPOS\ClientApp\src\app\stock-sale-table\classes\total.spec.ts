import { Total } from "./total";
import { Stock } from './stock';
import { Sale } from './sale';

describe("Total", ()=> {

    let stock: Stock[] = [
        new Stock("1", 1),
        new Stock("1", 1),
        new Stock("1", 1),
        new Stock("1", 1),
    ];

    let sales: Sale[] = [
        new Sale("1", 1),
        new Sale("1", 1),
        new Sale("1", 1),
        new Sale("1", 1),
    ];

    it('should calculate a stock total', ()=>{

        let total = new Total(stock, sales);

        expect(total.stock).toEqual(4);

    });

    it('should calculate a sales total', ()=>{

        let total = new Total(stock, sales);

        expect(total.sales).toEqual(4);
        
    });
})