using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using SoleMatrixPOS.Application.Client;
using SoleMatrixPOS.Application.Client.Queries;
using Microsoft.AspNetCore.Authentication.JwtBearer;

namespace SoleMatrixPOS.Controllers
{
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController]
    public class ClientAddController : ControllerBase
    {
        private readonly IMediator _mediator;

        public ClientAddController(IMediator mediator)
        {
            _mediator = mediator;
        }

		[HttpGet("barcode-exists/{barcode}")]
		public async Task<bool> CheckBarcodeExists(string barcode)
		{
			return await _mediator.Send(new ClientBarcodeExistsQuery(barcode));
		}

		[HttpPut]
        public async Task<CustomerClubDto> AddMember([FromBody] CustomerClubDto customerClubDto)
        { // not in use. just using update controller
            return await _mediator.Send(new ClientAddCommand(customerClubDto));
        }

    }
}
