import { createAction, props } from '@ngrx/store';
import { DailyDto, SalesByStaffQueryDto, SalesByHourQueryDto, MultiSaleDto, DepartmentSalesDto, DailyTotalsDto } from 'src/app/pos-server.generated';

export const getTodaysDaily = createAction(
  '[Daily] getTodaysDaily', 
  props<{ date?: Date }>()
);
export const storeTodaysDaily = createAction(
  '[Daily] storeTodaysDaily',
  props<{ payload: DailyDto }>()
);
export const getSalesByStaff = createAction(
  '[Daily] salesByStaff',
  props<{ date?: Date }>()
);
export const getRefundsByStaff = createAction(
  '[Daily] refundsByStaff',
  props<{ date?: Date }>()
);
export const getSalesByHour = createAction(
  '[Daily] salesByHour',
  props<{ date?: Date }>()
);
export const getRefundsByHour = createAction(
  '[Daily] refundsByHour',
  props<{ date?: Date }>()
);
export const storeSalesByStaffQuery = createAction(
  '[Daily] SalesByStaffQueryResponse',
  props<{ payload: SalesByStaffQueryDto[] }>()
);
export const storeRefundsByStaffQuery = createAction(
  '[Daily] RefundsByStaffQueryResponse',
  props<{ payload: SalesByStaffQueryDto[] }>()
);
export const storeSalesByHourQuery = createAction(
  '[Daily] SalesByHourQueryResponse',
  props<{ payload: SalesByHourQueryDto[] }>()
);
export const storeRefundsByHourQuery = createAction(
  '[Daily] RefundsByHourQueryResponse',
  props<{ payload: SalesByHourQueryDto[] }>()
);
export const getMultiSales = createAction(
  '[Daily] multiSales',
  props<{ date?: Date }>()
);
export const storeMultiSales = createAction(
  '[Daily] multiSalesResponse',
  props<{ payload: MultiSaleDto }>()
);
export const getDepartmentSales = createAction(
  '[Daily] departmentSales',
  props<{ date?: Date }>()
);
export const storeDepartmentSales = createAction(
  '[Daily] departmentSalesResponse',
  props<{ payload: DepartmentSalesDto[] }>()
);
export const getDepartmentRefunds = createAction(
  '[Daily] departmentRefunds',
  props<{ date?: Date }>()
);
export const storeDepartmentRefunds = createAction(
  '[Daily] departmentRefundsResponse',
  props<{ payload: DepartmentSalesDto[] }>()
);
export const init = createAction('[Daily] init');

// These existing range actions remain the same
export const getDailyRange = createAction(
  '[Daily] getDailyRange', 
  props<{ startDate?: Date, endDate?: Date }>()
);
export const storeDailyRange = createAction(
  '[Daily] storeDailyRange',
  props<{ payload: DailyDto[] }>()
);

export const getDailyRangeTotals = createAction(
  '[Daily] getDailyRangeTotals',
  props<{ startDate?: Date, endDate?: Date }>()
);
export const storeDailyRangeTotals = createAction(
  '[Daily] storeDailyRangeTotals',
  props<{ payload: DailyTotalsDto }>()
);
export const clearDailyData = createAction('[Daily] clearDailyData');
export const setRangeMode = createAction('[Daily] setRangeMode', props<{ isRangeMode: boolean }>());