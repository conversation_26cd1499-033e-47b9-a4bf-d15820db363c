using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.ReceiptPrinting;
using SoleMatrixPOS.Application.ReceiptPrinting.Commands;
using SoleMatrixPOS.Application.Transaction;


namespace SoleMatrixPOS.Controllers
{
	// TODO: delete all of this... this prints stuff on the backend which is not correct
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class ReceiptPrintingController : Controller
	{
		private readonly IMediator _mediator;

		public ReceiptPrintingController(IMediator mediator)
		{
			_mediator = mediator;
			AppContext.SetSwitch("System.Drawing.EnableUnixSupport", true);
		}
		// TODO: Right now all these commands to very similar things and are not very modular,
		// Not sure how to go about this... should make this all one big command and call seperate
		// sub-commands based on the transaction type?? 
		[Route("printSale")]
		[HttpPut]
		public async Task<int> PrintSales([FromBody] ReceiptTransactionDto dto)
		{
			await _mediator.Send(new CreateSalesReceiptCommand(dto,false));
			return 0; //TODO need to return some kind of success or failure
		}

		[Route("printReturn")]
		[HttpPut]
		public async Task<int> PrintReturn([FromBody] ReceiptTransactionDto dto)
		{
			await _mediator.Send(new CreateReturnsReceiptCommand(dto,false));
			return 0;
		}


		[Route("rePrintReceipt")]
		[HttpPut]
		public async Task<int> ReprintReceipt([FromBody] TransNoDto transNo)
		{

			// find out what the trans type then call the respective command?
			ReceiptTransactionDto dto = await _mediator.Send(new ReprintReceiptCommand(transNo, transNo.storeId, transNo.tillNo));
			if (transNo.transType == 1)
			{
				await _mediator.Send(new CreateSalesReceiptCommand(dto, true));

			}
			else if (transNo.transType == 2)
			{
				await _mediator.Send(new CreateReturnsReceiptCommand(dto, true));
			}

			// TODO : Create ReceiptCommand for exchange, new layby, new laybypayment
			// Transtypes 1,2 respectively.


			return 0;

		}

		[Route("printGiftVoucher")]
		[HttpPut]
		public async Task<int> PrintGiftVoucher([FromBody] ReceiptGiftVoucherDto dto)
		{
			await _mediator.Send(new CreateGiftVoucherReceiptCommand(dto, false));
			return 0;
		}

	}
}
