import { Component, Input, OnInit, OnDestroy, ElementRef, ViewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import { Payment, Transaction } from '../payment.service';
import { searchVoucher as searchVoucher, trackVoucherPayment, init, clearVoucherLookup } from "src/app/reducers/voucher-pay/voucher-pay.actions";
import * as giftCardPaySelectors from "src/app/reducers/voucher-pay/voucher-pay.selectors";
import { Observable, Subscription } from 'rxjs';
import { GiftVoucherResultDto } from 'src/app/pos-server.generated';
import { toTitleCase } from 'src/app/utility/strings';

@Component({
    selector: 'pos-gift-card-modal',
    templateUrl: './gift-card-modal.component.html',
    styleUrls: ['./gift-card-modal.component.scss']
})
export class GiftCardModalComponent implements OnInit, OnDestroy {

    @Input() type;
    @Input() amountDue: number;
    @Input() transaction: Transaction;

    get amount() { return this.form.get('Amount'); }
    get cardCode() { return this.form.get('CardCode'); }

    public isCreditNote: boolean = false;

    get voucherTypeDisplay(): string { return this.isCreditNote ? "credit note" : "gift voucher" }

    voucherCode: string = null;
    voucher: GiftVoucherResultDto = null;
    voucherSub: Subscription;
    errorMessage: string = null;
    errored$: Observable<boolean>;

    toTitleCase = toTitleCase;

    @ViewChild('cardCode', { static: false }) codeInput: ElementRef;

    constructor(
        public activeModal: NgbActiveModal,
        private store: Store<AppState>,
        private formBuilder: FormBuilder
    ) { }

    public form = this.formBuilder.group({
        Amount: [undefined, [
            Validators.required,
            Validators.min(0.01),
            Validators.pattern(/^\d*[.]{0,1}\d{0,2}$/)
        ]],
        CardCode: [undefined, [
            Validators.required
        ]]
    });

    ngOnInit() {

        this.voucherSub = this.store.select(giftCardPaySelectors.voucher).subscribe(
            (_voucher) => {
                console.log("Voucher updated:", _voucher);
                this.voucher = _voucher;

                // Clear error if voucher is loaded successfully
                if (_voucher) {
                    this.errorMessage = null;

                    // Show warning if voucher has limited or no funds
                    if (_voucher.voucherFunds <= 0) {
                        this.errorMessage = `This ${this.voucherTypeDisplay} has no remaining funds available.`;
                    }
                }
            }
        );

        this.errored$ = this.store.select(giftCardPaySelectors.errored);

        // this.codeInput.nativeElement.focus();

    }

    ngOnDestroy() {
        if (this.voucherSub) {
            this.voucherSub.unsubscribe();
        }

        // Clear the voucher from the store
        this.store.dispatch(clearVoucherLookup());

    }

    applyVoucherCode() {
        console.log("Foobar", this.isCreditNote);
        if (!this.cardCode.invalid) {
            console.log("Applying code: ", this.cardCode.value);
            this.store.dispatch(searchVoucher({
                code: this.cardCode.value,
                isCreditNote: this.isCreditNote
            }));
        }
    }

    dismiss(reason: string) {
        this.activeModal.dismiss(reason);
    }

    apply() {
        console.log(this.amount);
        if (this.form.valid) {
            // Validate the payment amount against available funds
            if (!this.voucher) {
                this.errorMessage = `Please enter a valid ${this.voucherTypeDisplay} code first.`;
                return;
            }

            const proposedAmount = +this.amount.value;

            const currentPayments = this.transaction && this.transaction.payments ?
                this.transaction.payments.reduce((sum, p) => sum + p.amount, 0) :
                0;

            if (proposedAmount + currentPayments > this.amountDue) {
                this.errorMessage = `This payment would exceed the amount due. Maximum payment allowed: ${(this.amountDue - currentPayments).toFixed(2)}`;
                return;
            }

            if (proposedAmount > this.voucher.voucherFunds) {
                this.errorMessage = `Amount exceeds available funds on ${this.voucherTypeDisplay}.`;
                return;
            }

            const payment = {
                type: this.type,
                amount: proposedAmount,
                context: {
                    giftVoucherNo: this.voucher.voucherNo
                }
            } as Payment;

            // Track this payment in the store
            console.log('Dispatching trackVoucherPayment with voucherType:', this.isCreditNote ? 'creditnote' : 'giftcard', ' (isCreditNote:', this.isCreditNote, ')');
            this.store.dispatch(trackVoucherPayment({
                payment: payment,
                transactionId: this.voucher.voucherNo,
                voucherType: this.isCreditNote ? 'creditnote' : 'giftcard'
            }));

            this.activeModal.close(payment);

        } else {
            this.form.markAllAsTouched();
        }
    }

    useRemainder() {
        if (this.voucher !== null) {
            const amountToUse = Math.min(this.amountDue, this.voucher.voucherFunds);
            const truncatedAmount = Math.floor(amountToUse * 100) / 100;
            this.amount.setValue(truncatedAmount.toFixed(2));
        }
    }

    public fieldValidate(control: AbstractControl): boolean {
        return control.invalid && (control.dirty || control.touched);
    }
}