<div class="modal-header">
    <h4 class="modal-title">Add Sundry Item</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="cancel()"></button>
</div>

<div class="modal-body">
    <form [formGroup]="sundryForm">
        <div class="mb-3">
            <label for="description" class="form-label">Sundry Description</label>
            <input type="text" minlength="3" maxlength="36" formControlName="description" class="form-control">
            <div *ngIf="sundryForm.get('description').invalid && sundryForm.get('description').touched" class="text-danger">
                <small *ngIf="sundryForm.get('description').errors?.minlength">Description must be at least 5 characters.</small>
                <small *ngIf="sundryForm.get('description').errors?.required">Description is required.</small>
            </div>
        </div>
        <div class="mb-3">
            <label for="price" class="form-label">Price</label>
            <input #priceInput type="number" step="0.01" min="0.01" formControlName="price"
                (input)="formatDecimals($event)" class="form-control">
            <div *ngIf="sundryForm.get('price').invalid && sundryForm.get('price').touched" class="text-danger">
                <small *ngIf="sundryForm.get('price').errors?.min">Price must be at least 0.01.</small>
                <small *ngIf="sundryForm.get('price').errors?.required">Price is required.</small>
            </div>
        </div>
    </form>
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="cancel()">Cancel</button>
    <button type="button" class="btn btn-primary" (click)="save()" [disabled]="!sundryForm.valid">
        Add Item
    </button>
</div>