<div class="row">
    <div id="sale-table-header" class="table-responsive">
        <table class="table table-borderless ">
            <thead>
                <tr>
                    <th scope="col">{{(cart$ | async).length}} Items</th>
                    <th scope="col">Colour</th>
                    <th scope="col">Size</th>
                    <th scope="col">Qty</th>
                    <th scope="col">Price</th>
                    <th scope="col">Total</th>
                </tr>
            </thead>
        </table>
    </div>
    <div id="sale-table-body" class="table-responsive"
        style="display: block; max-height: 300px; height: 300px; overflow-y: scroll;">

        <hr>
        <table class="table table-scroll ">
            <tbody>
                <tr *ngFor="let item of cart$ | async">
                    <td scope="col">{{item.stockItem.styleCode}}</td>
                    <td scope="col">{{item.stockItem.colourName}}</td>
                    <td scope="col">{{item.stockItem.size}}</td>
                    <td scope="col">{{item.quantity}}</td>
                    <td *ngIf="item.bestValue < item.stockItem.price" scope="col" class="text-warning">{{item.bestValue
                        | currency}}</td>
                    <td *ngIf="item.bestValue == item.stockItem.price" scope="col">{{item.bestValue | currency}}</td>
                    <td scope="col">{{item.quantity * item.bestValue | currency}}</td>
                </tr>
            </tbody>
            <tfoot>
                <tr>
                    <td scope="col">Total</td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col">
                        <span *ngIf="transaction$ | async">
                            {{cartTotal$ | async | currency}}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td scope="col">Rounding</td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col">
                        <span *ngIf="transaction$ | async">
                            {{(transaction$ | async).rounding | currency}}
                        </span>
                    </td>
                </tr>

            </tfoot>
        </table>
    </div>
    <div id="sale-table-footer" class="table-responsive">
        <table class="table table-borderless ">
            <tfoot>

                <tr>
                    <td colspan="5"></td>
                </tr>

                <!-- Unpaid Payments -->
                <ng-container *ngFor="let payment of (transaction$ | async).payments">
                    <tr *ngIf="!payment.paid" style="background-color: #EAF2F4A8;" (click)="paymentClick(payment)">
                        <td scope="col">{{ payment.type }}</td>
                        <td scope="col"></td>
                        <td scope="col"></td>
                        <td scope="col"></td>
                        <td scope="col">{{ payment.amount | currency }}</td>
                    </tr>
                </ng-container>

                <!-- Paid Payments -->
                <ng-container *ngFor="let payment of (transaction$ | async).payments">
                    <tr *ngIf="payment.paid" style="background-color: #C8E6C9;">
                        <td scope="col">{{ payment.type }}</td>
                        <td scope="col"></td>
                        <td scope="col"></td>
                        <td scope="col"></td>
                        <td scope="col">{{ payment.amount | currency }} Paid</td>
                    </tr>
                </ng-container>

                <tr style="background-color: #EAF2F4A8;">
                    <td colspan="2" scope="col">Amount Tendered</td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col">
                        <span *ngIf="transaction$ | async">
                            {{(transaction$ | async).amountTendered | currency}}
                            <!--Should be rounding not total- ask Craig what that is-->
                        </span>
                    </td>
                </tr>

                <tr>
                    <td colspan="5"></td>
                </tr>
                <tr *ngIf="(layby$ | async).active">

                </tr>
                <tr>
                    <td scope="col">Amount Due</td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td colspan="2" scope="col" style="justify-content: right;">
                        <span *ngIf="transaction$ | async">
                            <h1 *ngIf="(transaction$ | async).amountDue > 0" class="total text-danger">
                                {{(transaction$ | async).amountDue | currency}}
                            </h1>
                            <h1 *ngIf="(transaction$ | async).amountDue == 0" class="total text-success">
                                {{(transaction$ | async).amountDue | currency}}
                            </h1>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td scope="col">Change</td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col">
                        <span *ngIf="transaction$ | async">
                            <p *ngIf="(transaction$ | async).change > 0" class="text-success">
                                {{(transaction$ | async).change | currency}}
                            </p>
                            <p *ngIf="(transaction$ | async).change == 0">
                                {{(transaction$ | async).change | currency}}
                            </p>
                        </span>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>

</div>
