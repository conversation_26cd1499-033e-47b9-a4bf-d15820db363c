import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ReturnsProcessRefundComponent } from './returns-process-refund.component';

describe('ReturnsProcessRefundComponent', () => {
  let component: ReturnsProcessRefundComponent;
  let fixture: ComponentFixture<ReturnsProcessRefundComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ReturnsProcessRefundComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ReturnsProcessRefundComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
