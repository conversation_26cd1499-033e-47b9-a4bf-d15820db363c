import { Transfer } from './transfer.model';
import * as TransferType from './transfer.action';

const initialState: Transfer[] = [];

export function TransfersReducer(state: Transfer[] = initialState, action: any) {

    switch (action.type) {
        case TransferType.ADD_TRANSFER: {
            return [
                ...state, // Apends a transfer to the transfer list
                {
                    id: action.id,
                    from: action.from,
                    to: action.to,
                    name: action.name,
                    size: action.size,
                    qty: action.qty, 
                    highlighted: action.highlighted
                }
            ];
        }
        case TransferType.REMOVE_TRANSFER: { // Returns everything except the transfer matching this action's id
            return state.filter(transfer => action.id !== transfer.id); 
        }
        case TransferType.LOAD_TRANSFERS: {
            return action.transfers;
        }
        case TransferType.HIGHLIGHT_TRANSFER: { 

            let tempState = [...state];                                                // Copy the state
            let transfer = tempState.filter(transfer => action.id === transfer.id)[0]; // Find the transfer

            transfer.highlight = transfer.highlight == "on" ? "off" : "on";            // Turn it on or off
            state = tempState;                                                         // Replace the state
            
            return state;
        }
        default: {
            return state;
        }
    }
}
