﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<TypeScriptCompileBlocked>true</TypeScriptCompileBlocked>
		<TypeScriptToolsVersion>Latest</TypeScriptToolsVersion>
		<IsPackable>false</IsPackable>
		<SpaRoot>ClientApp\</SpaRoot>
		<DefaultItemExcludes>$(DefaultItemExcludes);$(SpaRoot)node_modules\**</DefaultItemExcludes>

		<!-- Set this to true if you enable server-side prerendering -->
		<BuildServerSideRenderer>false</BuildServerSideRenderer>
		<UserSecretsId>5e7cdfcf-bc48-4ea8-9992-723d86727824</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<PreserveCompilationContext>true</PreserveCompilationContext>
		<MvcRazorExcludeRefAssembliesFromPublish>false</MvcRazorExcludeRefAssembliesFromPublish>
		<GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="BarcodeLib" Version="3.1.4" />
		<PackageReference Include="FluentValidation.AspNetCore" Version="8.5.0-preview4" />
		<PackageReference Include="MediatR" Version="12.4.1" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.36" />
		<PackageReference Include="Microsoft.AspNetCore.DataProtection" Version="9.0.1" />
		<PackageReference Include="Microsoft.AspNetCore.DataProtection.Abstractions" Version="9.0.1" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.36" />
		<PackageReference Include="Microsoft.AspNetCore.SpaServices" Version="3.1.32" />
		<PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="6.0.35" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.36" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="6.0.36" />
		<PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="2.2.0" />
		<PackageReference Include="Microsoft.Extensions.Identity.Core" Version="6.6.0" />
		<PackageReference Include="iTextSharp" Version="5.5.13.1" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.7.9" />
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="2.2.3" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="NSwag.AspNetCore" Version="14.1.0" />
		<PackageReference Include="NSwag.Core" Version="14.1.0" />
		<PackageReference Include="NSwag.MSBuild" Version="14.1.0">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="PDFsharp-MigraDoc" Version="6.1.1" />
		<PackageReference Include="Postmark" Version="5.2.0" />
		<PackageReference Include="Select.HtmlToPdf.NetCore" Version="24.1.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="2.1.1" />
		<PackageReference Include="Serilog.Extensions.Hosting" Version="5.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="3.1.1" />
		<PackageReference Include="Serilog.Sinks.Seq" Version="4.0.0" />
	</ItemGroup>

	<!-- include libwkhtmltox runtimes -->
	<ItemGroup>
		<Content Include="libwkhtmltox.*">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<!-- Don't publish the SPA source files, but do show them in the project files list -->
		<Compile Remove="ClientApp\src\app\error-modal\error-confirm-modal\**" />
		<Compile Remove="ClientApp\src\app\reducers\house-keeping\readConfig\**" />
		<Compile Remove="ClientApp\src\app\reducers\house-keeping\updateConfig\**" />
		<Content Remove="$(SpaRoot)**" />
		<Content Remove="ClientApp\src\app\error-modal\error-confirm-modal\**" />
		<Content Remove="ClientApp\src\app\reducers\house-keeping\readConfig\**" />
		<Content Remove="ClientApp\src\app\reducers\house-keeping\updateConfig\**" />
		<EmbeddedResource Remove="ClientApp\src\app\error-modal\error-confirm-modal\**" />
		<EmbeddedResource Remove="ClientApp\src\app\reducers\house-keeping\readConfig\**" />
		<EmbeddedResource Remove="ClientApp\src\app\reducers\house-keeping\updateConfig\**" />
		<None Remove="$(SpaRoot)**" />
		<None Remove="ClientApp\src\app\error-modal\error-confirm-modal\**" />
		<None Remove="ClientApp\src\app\reducers\house-keeping\readConfig\**" />
		<None Remove="ClientApp\src\app\reducers\house-keeping\updateConfig\**" />
		<None Include="$(SpaRoot)**" Exclude="$(SpaRoot)node_modules\**" />
	</ItemGroup>

	<ItemGroup>
		<Content Remove="runtimeconfig.template.json" />
	</ItemGroup>

	<ItemGroup>
	  <None Remove="ClientApp\src\app\error-modal\error-confirm-modal\error-confirm-modal.component.html" />
	  <None Remove="ClientApp\src\app\error-modal\error-confirm-modal\error-confirm-modal.component.scss" />
	  <None Remove="ClientApp\src\app\error-modal\error-confirm-modal\error-confirm-modal.component.spec.ts" />
	  <None Remove="ClientApp\src\app\error-modal\error-confirm-modal\error-confirm-modal.component.ts" />
	  <None Remove="Email\GiftVoucherTemplate.html" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="certs\solemate-root.cer">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Email\GiftVoucherTemplate.html">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>


	<ItemGroup>
		<None Include="runtimeconfig.template.json" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\SoleMatrixPOS.Application\SoleMatrixPOS.Application.csproj" />
		<ProjectReference Include="..\SoleMatrixPOS.Dal\SoleMatrixPOS.Dal.csproj" />
		<ProjectReference Include="..\SoleMatrixPOS.Infrastructure\SoleMatrixPOS.Infrastructure.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Content Update="appsettings.local.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="appsettings.local.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="appsettings.Development.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="appsettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="appsettings.Test.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<Folder Include="ClientApp\src\app\printing\" />
		<Folder Include="ClientApp\src\app\reducers\NewFolder\" />
		<Folder Include="Controllers\NewFolder\" />
		<Folder Include="General\" />
	</ItemGroup>

	<ItemGroup>
	  <None Update="Email\ReceiptTemplate.html">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>
	<Target Name="LogConfiguration" AfterTargets="Build">
		<Message Text="Current Configuration: $(Configuration)" />
	</Target>

	<Target Name="NSwag" AfterTargets="Build">
		<Copy SourceFiles="@(Reference)" DestinationFolder="$(OutDir)References" />
		<Exec Command="$(NSwagExe_Net60) run nswag.json /variables:Configuration=$(Configuration)" StandardOutputImportance="High" StandardErrorImportance="High" />
		<RemoveDir Directories="$(OutDir)References" />
	</Target>
	<Target Name="NSwag-Hotfix" AfterTargets="NSwag">
		<Exec Command="py .\Scripts\nswag-fix.py -t .\ClientApp\src\app\pos-server.generated.ts"></Exec>
	</Target>


	<ProjectExtensions>
		<VisualStudio>
			<UserProperties />
		</VisualStudio>
	</ProjectExtensions>

</Project>
