.container-fluid {
    padding: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

h5 {
    text-align: center;
    margin-bottom: 2rem;
    font-weight: 500;
    color: #2c3e50;
}

.scan-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

label {
    font-size: 1rem;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

input {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    font-size: 1rem;
    width: 100%;
    max-width: 400px;
    transition: border-color 0.2s;

    &:focus {
        outline: none;
        border-color: #4299e1;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
    }
}

button {
    padding: 0.75rem 2rem;
    background-color: #4299e1;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
        background-color: #3182ce;
    }

    &:active {
        background-color: #2b6cb0;
    }
}

.barcodeValidMsg {
    color: #48bb78;
    font-weight: 500;
}

.barcodeInvalidMsg {
    color: #f56565;
    font-weight: 500;
}