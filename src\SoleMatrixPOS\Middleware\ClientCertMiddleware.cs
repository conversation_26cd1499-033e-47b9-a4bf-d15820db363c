using System;
using System.Net;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace SoleMatrixPOS.Middleware
{

    /// <summary>
    /// middleware to handle client certificates.
    /// we use middleware to support multiple scenarios - ssl terminated in kestrel (local dev) or ssl terminated by an azure appservice load balancer
    /// if a client cert is provided, it must be signed by our custom SoleMate root cert. The public key for this is shipped with the app.
    /// we allow request to proceed without a client cert - user will have to login manually.
    /// </summary>

    public class ClientCertMiddleware : IMiddleware
    {
        private readonly ILogger<ClientCertMiddleware> _log;

        public ClientCertMiddleware(ILogger<ClientCertMiddleware> log)
        {
            _log = log;
        }

        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            if (context.Connection.ClientCertificate != null)
            {
                _log.LogInformation("Got Client Cert {SubjectName}", context.Connection.ClientCertificate.SubjectName.Name);
                if (!ValidateCertificate(context.Connection.ClientCertificate))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.Forbidden;
                    await context.Response.WriteAsync("The provided client certificate was not from SoleMate");
                    return;
                }
            }
            await next(context);
        }

        /// <summary>
        /// validate the client certificate. If one is provided it must be from our root certificate
        /// </summary>
        /// <returns></returns>
        public bool ValidateCertificate(X509Certificate2 clientCert)
        {
            var rootCert = new X509Certificate2("certs/solemate-root.cer", "");

            var validationChain = new X509Chain(true);

            validationChain.ChainPolicy.RevocationMode = X509RevocationMode.NoCheck;
            validationChain.ChainPolicy.VerificationFlags =
                X509VerificationFlags.AllowUnknownCertificateAuthority;

            validationChain.ChainPolicy.VerificationTime = DateTime.Now;
            validationChain.ChainPolicy.UrlRetrievalTimeout = new TimeSpan(0, 0, 0);
            validationChain.ChainPolicy.ExtraStore.Add(rootCert);

            var valid = validationChain.Build(clientCert);

            // only trust client certs signed by our root cert
            valid = valid && validationChain.ChainElements[validationChain.ChainElements.Count - 1]
                        .Certificate.Thumbprint == rootCert.Thumbprint;

            return valid;
        }

    }
}