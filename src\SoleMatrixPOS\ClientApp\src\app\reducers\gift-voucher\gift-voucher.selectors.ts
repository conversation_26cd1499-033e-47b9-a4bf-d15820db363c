import { createSelector } from "@ngrx/store";
import { AppState } from "..";

//initialize the gift voucher appstate
export const select = (state: AppState) => state.giftVoucher;

// get gift voucher state
export const payments = createSelector(select, (s) => s.payments);
export const balances = createSelector(select, (s) => s.balances);
export const voucher = createSelector(select, (s) => s.giftVoucherCreation);
export const voucherResult = createSelector(select, (s) => s.giftVoucherResult);
export const isCreditNote = createSelector(select, (s) => s.isCreditNote);
export const completed = createSelector(select, (s) => s.completed);
