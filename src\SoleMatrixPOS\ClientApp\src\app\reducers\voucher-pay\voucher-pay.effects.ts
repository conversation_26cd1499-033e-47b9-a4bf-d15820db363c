import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { GiftVoucherClient } from "src/app/pos-server.generated";
import * as giftVoucherPaymentAction from './voucher-pay.actions';
import { catchError, delay, map, mergeMap, startWith, tap } from "rxjs/operators";
import { EMPTY } from "rxjs";


@Injectable()
export class VoucherPaymentEffects {
    constructor(private actions$: Actions, private client: GiftVoucherClient) { }

    //generate or get voucher number
    searchGiftVoucher$ = createEffect(() => this.actions$.pipe(
        ofType(giftVoucherPaymentAction.searchVoucher),
        tap((v) => console.log("Giftcard pay effect: ", v.code)),
        mergeMap((action) => this.client.searchGiftVoucher(action.isCreditNote, action.code)
            .pipe(
                map((response) => giftVoucherPaymentAction.searchVoucherResponse({ giftVoucher: response }),
                catchError(() => EMPTY)
                )
            )
        )
    ));
}
