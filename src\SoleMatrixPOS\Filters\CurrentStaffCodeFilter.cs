using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using SoleMatrixPOS.Application.Infrastructure;
using SoleMatrixPOS.Application.Store.Queries;
using SoleMatrixPOS.Identity.Interface;

namespace SoleMatrixPOS.Filters
{
    /// <summary>
    /// look for a staff code specified in a request header and write to a context object if found
    /// </summary>
    public class CurrentStaffCodeFilter: IAsyncAuthorizationFilter
    {
        public const string StaffCodeHeaderKey = "SolemateStaffCode";

        private readonly IMediator _mediator;
		private readonly RegisteredTillService _registeredTillService;

        public CurrentStaffCodeFilter(IMediator mediator, RegisteredTillService registeredTillService)
        {
            _mediator = mediator;
			_registeredTillService = registeredTillService;

		}

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            if (context.HttpContext.User.Identity.IsAuthenticated)
            {
                var staffCode = context.HttpContext.Request.Headers[StaffCodeHeaderKey];
                var staffCodeContext = context.HttpContext.RequestServices.GetService<StaffCodeContext>();

                if (!string.IsNullOrEmpty(staffCode) && staffCodeContext != null)
                {
					var tillContext = await _registeredTillService.GetContextualTillAsync();

					if (context.HttpContext.Request.Headers.ContainsKey(StaffCodeHeaderKey))
						staffCodeContext.StaffCode = staffCode;

					staffCodeContext.PINPadNo = tillContext.TillId;
					staffCodeContext.StoreDetailsDto = new StoreDetailsDto { StoreId = tillContext.StoreId };
					//staffCodeContext.StoreDetailsDto = await _mediator.Send(new GetStoreDetails(tillContext.StoreId), CancellationToken.None); // TODO get from identity
                }
            }
        }
    }
}
