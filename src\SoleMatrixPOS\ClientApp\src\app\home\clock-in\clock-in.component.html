<div class="card mx-auto" style="width: 30rem; margin-top: 10rem;" *ngIf="staff$ | async as staff">
	<div class="card-header">{{'login.ClockIn' | translate}} - {{ staff.staffDto.name }}</div>
	<div class="card-body">
		<p>{{'login.ClockInMessage' | translate}} {{ date | date :'shortTime' }}</p>
		<div class="text-center">
			<button type="button" class="btn btn-success" (click)="ClockIn()" [translate]="'core.buttons.OK'">Ok</button>
			&nbsp;&nbsp;
			<button type="button" class="btn btn-warning" (click)="Cancel()" [translate]="'core.buttons.Cancel'">Cancel</button>
		</div>
	</div>
</div>
