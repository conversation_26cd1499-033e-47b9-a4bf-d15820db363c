import { TestBed, inject } from '@angular/core/testing';

import { StockSaleTableService } from './stock-sale-table.service';
import { of } from 'rxjs';
import { Size } from '../classes/size';
import { Stock } from '../classes/stock';
import { Sale } from '../classes/sale';
//import { AppState } from '../classes/app.store';
import { AppState } from '../test-state';
import { Store } from '@ngrx/store';

type Trading = "T" | "F";
type Toggle = "on" | "off";

describe('StockSaleTableService', () => {

  let masterService: StockSaleTableService;
  let valueServiceSpy: jasmine.SpyObj<Store<AppState>>;

  const sizes = [
    new Size("1"),
    new Size("2"),
    new Size("3"),
    new Size("4")
  ];

  const locations = [
    {
      name: "Location 1",
      trading: "T" as Trading,
      rank: 1,
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 2",
      trading: "T" as Trading,
      rank: 1,
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 3",
      trading: "T" as Trading,
      rank: 1,
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
    {
      name: "Location 4",
      trading: "T" as Trading,
      rank: 1,
      stock: [
        new Stock("1", 1),
        new Stock("2", 2),
        new Stock("3", 3),
        new Stock("4", 4)
      ],
      sales: [
        new Sale("1", 1),
        new Sale("2", 2),
        new Sale("3", 3),
        new Sale("4", 4)
      ],
      toggle: "off" as Toggle
    },
  ]

  beforeEach(() => {

    const spy = jasmine.createSpyObj('Store', ['select']);

    TestBed.configureTestingModule({
      providers: [
        StockSaleTableService,
        { provide: Store, useValue: spy }
      ]
    })

    masterService = TestBed.get(StockSaleTableService);
    valueServiceSpy = TestBed.get(Store);
  });

  it('should retrieve main table', inject([StockSaleTableService], (service: StockSaleTableService) => {

    const mockObservableSizes = of(sizes);
    const mockObservableLocations = of(locations);

    const spySize = spyOn(service, 'getSize').and.returnValue(mockObservableSizes);
    const spyLocation = spyOn(service, 'getLocation').and.returnValue(mockObservableLocations);

    service.getSize().subscribe((data) => {
      expect(data).toEqual(sizes);
    });
    service.getLocation().subscribe((data) => {
      expect(data).toEqual(locations);
    });

  }));
});

