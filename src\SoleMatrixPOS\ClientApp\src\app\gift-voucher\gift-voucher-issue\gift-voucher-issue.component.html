<div class="modal-header">
    <h4 class="modal-title">{{voucherTypeTitle}}</h4>
</div>
<div class="modal-body">
    <p *ngIf="loading">Waiting for {{voucherTypeTitle}} creation...</p>
    <p *ngIf="!loading && voucher">{{voucherTypeTitle}} #{{voucher.voucherNo}} has been created with {{voucher.voucherFunds | currency}} funds.</p>
    <p *ngIf="!printed && !emailed && !loading && voucher" class="text-warning">
        <i class="fas fa-exclamation-triangle"></i> Please print or email the {{voucherTypeTitle}} before proceeding
    </p>
    <p *ngIf="printed && voucher" class="text-success">
        <i class="fas fa-check-circle"></i> {{voucherTypeTitle}} has been printed
    </p>
    <p *ngIf="emailed && voucher" class="text-success">
        <i class="fas fa-check-circle"></i> {{voucherTypeTitle}} has been emailed
    </p>
</div>
<div class="modal-footer">
    <button class="btn btn-primary" (click)="printVoucher()" [disabled]="loading">
        <i class="fas fa-print"></i> Print {{voucherTypeTitle}}
    </button>
    <button class="btn btn-info" (click)="emailVoucher()" [disabled]="loading">
        <i class="fas fa-envelope"></i> Email {{voucherTypeTitle}}
    </button>
    <button class="btn btn-secondary" (click)="goHome()" [disabled]="!printed && !emailed">
        <i class="fas fa-home"></i> Go home
    </button>
</div>