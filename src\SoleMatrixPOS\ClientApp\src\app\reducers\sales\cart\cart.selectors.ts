import { AppState } from "../../index";
import { createSelector } from "@ngrx/store";
import { CartItem } from "./cart.reducer";
import { CartState } from "./cart.reducer";

export const select = (state: AppState) => state.cart;
export const selectCartState = (state: AppState) => state.cart;
export const cart = createSelector(select, (s) => s.items);
export const noItems = createSelector(select, (s) => calculateNoItems(s.items));
export const total = createSelector(select, (s) => s.total);
export const reasons = createSelector(select, (s) => s.reasons);
export const isExchangeMode = createSelector(select, (s) => s.isExchangeMode);

export const getCart = createSelector(
	selectCartState,
	(cartState: CartState) => cartState // Return the cart state
);

function calculateNoItems(cartItems: CartItem[]): number {
	console.log("Working...");
	let count: number = 0;
	for (let item of cartItems) {
		count += item.quantity;
	}
	return count;
}
