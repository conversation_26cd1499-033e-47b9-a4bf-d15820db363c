<div class="table-responsive">
    <table class="table table-striped" style="margin: 0">
        <thead>
            <tr>
                <!-- Todo: reorder these to fit -->
                <th *ngIf="fieldsEnabled.style" scope="col">Style</th>
                <th *ngIf="fieldsEnabled.description" scope="col">Description</th>
                <th *ngIf="fieldsEnabled.colourName" scope="col">Colour</th>
                <th *ngIf="fieldsEnabled.colourCode" scope="col">Colour Code</th>
                <th *ngIf="fieldsEnabled.departmentCode" scope="col">Department</th>
                <th *ngIf="fieldsEnabled.makerCode" scope="col">Maker Code</th>
                <th *ngIf="fieldsEnabled.size" scope="col">Size</th>
                <th *ngIf="fieldsEnabled.qty" scope="col">Qty</th>
                <th *ngIf="fieldsEnabled.price" scope="col">Value</th>
                <th *ngIf="fieldsEnabled.totalPrice" scope="col">Total Price</th>
                <th scope="col"> </th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let item of tableItems; let i = index;">
                <td *ngIf="fieldsEnabled.style">{{item.stockItem.styleCode}}</td>
                <td *ngIf="fieldsEnabled.description">{{item.stockItem.styleDescription}}</td>
                <td *ngIf="fieldsEnabled.colourName">{{item.stockItem.colourName}}</td>
                <td *ngIf="fieldsEnabled.colourCode">{{item.stockItem.colourCode}}</td>
                <td *ngIf="fieldsEnabled.departmentCode">{{item.stockItem.departmentCode}}</td>
                <td *ngIf="fieldsEnabled.makerCode">{{item.stockItem.makerCode}}</td>
                <td *ngIf="fieldsEnabled.size">{{item.stockItem.size}}</td>
                <td *ngIf="fieldsEnabled.qty" style="width:100px;">
                    <input *ngIf="fieldsEnabled.qtySelectable" type="number" class="form-control" value="{{item.quantity}}"
                    (blur)="onTableEntryQuantityChanged(i, $event.target.value)"
                    (input)="onTableEntryQuantityChanged(i, $event.target.value)">
                    <div *ngIf="!fieldsEnabled.qtySelectable">
                        {{item.quantity}}
                    </div>
                </td>
                <td *ngIf="fieldsEnabled.price">{{item.stockItem.price == null ? "" : item.stockItem.price | currency}}</td>
                <td *ngIf="fieldsEnabled.totalPrice">{{item.stockItem.price == null ? "" : (item.stockItem.price * item.quantity) | currency}}</td>
                <td>
                    <span class="fas fa-trash fa-lg deleteButton" (click)="onTableEntryDeleteClicked(i)"></span>
                </td>
            </tr>
        </tbody>
    </table>
</div>