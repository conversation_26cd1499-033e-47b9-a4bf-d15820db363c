import { Component, OnInit, Output, EventEmitter } from "@angular/core";
import { Router } from "@angular/router";
import { Store } from "@ngrx/store";
import { Observable } from "rxjs";
import { AppState } from "src/app/reducers";
import * as itemCartSelector from "../../reducers/stocketake-entry/itemCart/itemCart.selectors";

@Component({
	selector: "pos-stocktake-footer",
	templateUrl: "./stocktake-footer.component.html",
	styleUrls: ["./stocktake-footer.component.scss"],
})
export class StocktakeFooterComponent implements OnInit {
	@Output() onNextClick: EventEmitter<void> = new EventEmitter();
	@Output() onDownload: EventEmitter<void> = new EventEmitter();

	totalUnits$: Observable<number>;

	constructor(private router: Router, private store: Store<AppState>) {}

	ngOnInit() {
		this.totalUnits$ = this.store.select(itemCartSelector.UnitTotal);
	}

	okToProcess() {
		this.onNextClick.emit();
	}

	backButtonClick() {
		this.router.navigateByUrl("/");
	}

	onDownloadStocktake() {
		this.onDownload.emit();
	}
}
