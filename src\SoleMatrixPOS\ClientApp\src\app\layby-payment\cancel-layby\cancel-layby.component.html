<div class="modal-header">
    <h4 class="modal-title">Cancel Layby</h4>
    <button type="button" class="close" aria-label="Close" (click)="dismissModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  
  <div class="modal-body">
    <div class="form-group">
      <label for="totalPaid">Total Paid Amount</label>
      <input
        type="text"
        id="totalPaid"
        class="form-control"
        [value]="totalPaid | currency"
        readonly
      />
    </div>
  
    <div class="form-group">
      <label for="refundAmount">Refund Amount</label>
      <input
        type="number"
        id="refundAmount"
        class="form-control"
        [value]="totalRefundedInModals"
        readonly
      />
    </div>
  
    <div class="form-group">
      <label for="refundMethod">Select Refund Method</label>
      <div class="d-flex justify-content-around py-3">
        <!-- Use Payment Modal Button component for Cash and EFTPOS -->
        <pos-payment-modal-button
          *ngFor="let button of modalButtons"
          [buttonInfo]="button"
          (openModal)="launchPaymentModal($event)">
        </pos-payment-modal-button>
      </div>
    </div>
  </div>
  
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="dismissModal()">Cancel</button>
    <button
      type="button"
      class="btn btn-success"
      [disabled]="!canProcessCancellation()"
      (click)="handleLaybyCancellation()"
    >
      Process Cancellation
    </button>
  </div>

  <div class="layby-history mt-4 p-3 border rounded">
    <h5 class="mb-3">Layby History</h5>
    
    <!-- Items purchased section -->
    <div class="mb-4">
      <h6 class="font-weight-bold">Items Purchased</h6>
      <div class="table-responsive">
        <table class="table table-sm">
          <thead class="thead-light">
            <tr>
              <th>Item</th>
              <th>Color</th>
              <th>Size</th>
              <th>Quantity</th>
              <th class="text-right">Price</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let line of laybyLines$ | async">
              <tr *ngIf="line.transType === 1 || line.transType === 5">
                <td>{{ getItemDescription(line) }}</td>
                <td>{{ line.colourCode }}</td>
                <td>{{ line.sizeCode }}</td>
                <td>{{ line.quantity }}</td>
                <td class="text-right">{{ line.extendedValue | currency }}</td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
    
    <!-- Payments made section -->
    <div class="mb-4">
      <h6 class="font-weight-bold">Payments</h6>
      <div class="table-responsive">
        <table class="table table-sm">
          <thead class="thead-light">
            <tr>
              <th>Date</th>
              <th>Type</th>
              <th>Payment Method</th>
              <th class="text-right">Amount</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let line of laybyLines$ | async">
              <tr *ngIf="line.transType === 2 || line.transType === 3">
                <td>{{ parseDate(line.styleCode) | date }}</td>
                <td>{{ line.transType === 2 ? 'Deposit' : 'Payment' }}</td>
                <td>{{ getPaymentMethod(line) }}</td>
                <td class="text-right">{{ -line.extendedValue | currency }}</td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
    
    <!-- Summary section -->
    <div class="row">
      <div class="col-md-6">
        <div class="card">
          <div class="card-body">
            <h6 class="card-title">Summary</h6>
            <div class="d-flex justify-content-between">
              <span>Total Item Value:</span>
              <span class="font-weight-bold">{{ getTotalItemValue() | currency }}</span>
            </div>
            <div class="d-flex justify-content-between">
              <span>Total Paid:</span>
              <span class="font-weight-bold">{{ totalPaid | currency }}</span>
            </div>
            <div class="d-flex justify-content-between mt-2">
              <span>Amount Remaining:</span>
              <span class="font-weight-bold text-danger">{{ amountDue | currency }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>