using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace SoleMatrixPOS.Middleware
{
    /// <summary>
    /// This middleware requires an authenticated Identity - or redirects to the  login screen
    /// Must be after the middleware that serves the login screen.
    /// </summary>
    public class AuthMiddleware: IMiddleware
    {
        public Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
			// TODO: readd once auth middleware is working!!!
			return next(context);
            if (!context?.User?.Identity?.IsAuthenticated == true)
            {
                context.Response.Redirect("/auth");
                return Task.CompletedTask;
            }
            else
            {
                return next(context);
            }
        }
    }
}
