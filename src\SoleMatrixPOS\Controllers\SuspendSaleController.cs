using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.SuspendSale;
using SoleMatrixPOS.Application.SuspendSale.Commands;
using SoleMatrixPOS.Application.SuspendSale.Queries;
using System.Threading.Tasks;
using System.Collections.Generic;
using SoleMatrixPOS.Application.ReceiptPrinting;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using SoleMatrixPOS.Application.Infrastructure;
using SoleMatrixPOS.Application.Client.Queries;
using SoleMatrixPOS.Dal.Interface.Models.SMPos;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class SuspendSaleController : ControllerBase
	{
		private readonly IMediator _mediator;
		private readonly StaffCodeContext _staffCodeContext;

		public SuspendSaleController(IMediator mediator, StaffCodeContext staffCodeContext)
		{
			_mediator = mediator;
			_staffCodeContext = staffCodeContext;
		}

		[HttpPut]
		public async Task<IActionResult> AddSuspendSale([FromBody] SuspendSaleDto suspendSaleDto)
		{
			var storeId = _staffCodeContext.StoreDetailsDto.StoreId;
			var tillId = _staffCodeContext.PINPadNo;
			await _mediator.Send(new AddSuspendSaleCommand(suspendSaleDto, storeId, tillId));
			return Ok();
		}

		[HttpPost("get-next-suspend-no")]
		public async Task<int> GetNextSuspendNo()
		{
			return await _mediator.Send(new GetSuspendNoQuery());
		}



		[HttpGet("headers")]
		public async Task<IEnumerable<SuspendSaleHdrDto>> GetSuspendSaleHeaders()
		{
			var storeId = _staffCodeContext.StoreDetailsDto.StoreId;
			// only return suspended headers for current store. ((possible to also pass down tillNo to restrict results to current till.. if suspendhdr adds tilldetails))
			// ITS FINE WE CAN JUST RETURN ALL TILLS SUSPENDED SALES HEREE..

			return await _mediator.Send(new GetSuspendSaleHeadersQuery(storeId));
		}

		[HttpGet("suspend-sale-lines/{suspendNo}")]
		public async Task<SuspendSaleLoadDto> GetSuspendSaleLines(int suspendNo)
		{
			var storeId = _staffCodeContext.StoreDetailsDto.StoreId;

			// We need to get the original header to get the client code
			SuspendSaleHdrDto suspension = await _mediator.Send(new GetSuspendSaleHdrQuery(suspendNo, storeId));

			// Start by getting the lines
			var lines = await _mediator.Send(new GetSuspendSaleLinesQuery(suspendNo));

			// If the suspension involved a client, we'll fetch that
			var client = suspension.ClientCode == null ? null : await _mediator.Send(new ClientQuery(suspension.ClientCode));

			return new SuspendSaleLoadDto()
			{
				Lines = lines,
				Client = client
			};
		}

		[HttpDelete("{suspendNo}")]
		public async Task<IActionResult> DeleteSuspendSale(int suspendNo)
		{
			await _mediator.Send(new DeleteSuspendSaleCommand(suspendNo));
			return NoContent();
		}

		[HttpPost("Reset-Status/{isActiveSale}")]
		public async Task<IActionResult> ResetSuspendSaleStatus(bool isActiveSale)
		{
			if (_staffCodeContext.StoreDetailsDto == null)
			{
				return Ok();
			}
			//true sets ACTIVE_SALE to "F", false sets EFT_ATTEMPTED to "F"
			var storeId = _staffCodeContext.StoreDetailsDto.StoreId;
			var tillId = _staffCodeContext.PINPadNo;

			await _mediator.Send(new ResetSuspendSaleStatusCommand(storeId, tillId, isActiveSale));
			return Ok();
		}

		[HttpGet("Recovery")]
		public async Task<(int SuspendNo, decimal EftPaid)> CheckForIncompleteSale()
		{
			//returns the recent saleNo for a suspend with active+eft_attempt
			var storeId = _staffCodeContext.StoreDetailsDto.StoreId;
			var pinPadId = _staffCodeContext.PINPadNo;
			return await _mediator.Send(new GetFailedSaleQuery(storeId, pinPadId));
			// suspendNo = 0 from dapper if no results exist. database should start from a non 0 value for suspend_no

		}

		[HttpPost("UpdateSuspendEftPaid")]
		public async Task<IActionResult> UpdateSuspendEftPaid([FromBody] float eft_amount)
		{
			var storeId = _staffCodeContext.StoreDetailsDto.StoreId;
			var pinPadId = _staffCodeContext.PINPadNo;
			await _mediator.Send(new UpdateSuspendEftPaidCommand(storeId, pinPadId, eft_amount));
			return Ok();
		}

		//[HttpPost("dummy")]
		//public async Task Dummy([FromBody] SuspendSaleHdrDto dto)
		//{

		//}
	}
}
