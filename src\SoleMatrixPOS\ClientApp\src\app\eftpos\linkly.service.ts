//import { Injectable } from '@angular/core';
//import { HttpClient, HttpHeaders } from '@angular/common/http';
//import { Observable, throwError } from 'rxjs';
//import { catchError, map, last } from 'rxjs/operators';
//import { v4 as uuidv4 } from 'uuid';
//import { AuthToken } from './eftpos-router';
//import { CartItem } from '../reducers/sales/cart/cart.reducer';

//export interface PurchaseAnalysisData {
//	OPR: string;
//	AMT: string;
//	PCM: string;
//}

//export interface LinklyBasketItem {
//	id: string;
//	sku: string;
//	qty: number;
//	amt: number;
//	tax: number;
//	dis: number;
//	name: string;
//}

//export interface LinklyBasket {
//	id: string;
//	amt: number;
//	tax: number;
//	dis: number;
//	sur: number;
//	items: LinklyBasketItem[];
//}

//export interface Notification {
//	Uri: string,
//	AuthorizationHeader: string
//}

//export interface LinklyPurchaseRequest {
//	Merchant: string;
//	TxnType: string;
//	AmtPurchase: number;
//	TxnRef: string;
//	CurrencyCode: string;
//	CutReceipt: string;
//	ReceiptAutoPrint: string;
//	Application: string;
//	PurchaseAnalysisData: PurchaseAnalysisData;
//	Basket: LinklyBasket;
//}


//export interface FullRequest {
//	Request: LinklyPurchaseRequest,
//	Notification: Notification
//}

//@Injectable({
//	providedIn: 'root'
//})
//export class LinklyService {
//	// TODO add env to appsettings (prod/dev...)
//	private authApiUrl = 'https://auth.sandbox.cloud.pceftpos.com/v1/';
//	private apiUrl = 'https://rest.pos.sandbox.cloud.pceftpos.com/v1/sessions/'

//	constructor(private http: HttpClient) { }

//	linklyPairPINPad(username: string, password: string, pairCode: string): Observable<any> {
//		const requestContent = {
//			Username: username,
//			Password: password,
//			PairCode: pairCode
//		};

//		const headers = new HttpHeaders({
//			'Content-Type': 'application/json'
//		});

//		return this.http.post<any>(this.authApiUrl + 'pairing/cloudpos', requestContent, { headers: headers })
//			.pipe(
//				map(response => response.secret),
//				catchError(this.handleError)
//			);
//	}

//	linklyGetAuthToken(secret: string, posId: string, posName: string = 'Solemate Software', posVersion: string = '1.0', posVendorId: string = "7183db99-2622-4e29-b0e7-a10be634a60b"): Observable<{ authToken: string, expiryTime: string }> {
//		const requestContent = {
//			Secret: secret,
//			PosName: posName,
//			PosVersion: posVersion,
//			PosId: posId,
//			PosVendorId: posVendorId
//		};

//		const headers = new HttpHeaders({
//			'Content-Type': 'application/json'
//		});

//		return this.http.post<any>(`${this.authApiUrl}${uuidv4()}/token/cloudpos`, requestContent, { headers: headers })
//			.pipe(
//				map(response => {
//					const authToken = response.authToken;
//					const expirySeconds = response.expirySeconds;

//					const expiryTimeRaw = new Date();
//					expiryTimeRaw.setSeconds(expiryTimeRaw.getSeconds() + expirySeconds);
//					const expiryTime = expiryTimeRaw.toISOString();
//					return { authToken, expiryTime };
//				}),
//				catchError(this.handleError)
//			);
//	}
//	// amtPurchase final price after tax and discount excluding surcharge, all amounts in cents
//	linklyProcessPurchase(amtPurchase: number, taxAmt: number, discountAmt: number, surchargeAmt: number, transNo: string, currencyCode: string = 'AUD', cutReceipt: string, receiptAutoPrint: string, operatorReference: string, hasBarCodeScanner: string, items: LinklyBasketItem[], authToken: string) {
//		const headers = new HttpHeaders({
//			'Content-Type': 'application/json'
//		});
//		const purchaseAnalysisData: PurchaseAnalysisData = {
//			OPR: operatorReference,
//			AMT: amtPurchase.toString(),
//			PCM: hasBarCodeScanner
//		};

//		const basket: LinklyBasket = {
//			id: transNo,
//			amt: amtPurchase,
//			tax: taxAmt,
//			dis: discountAmt,
//			sur:surchargeAmt,
//			items:items, 
//		}

//		const purchaseRequest: LinklyPurchaseRequest = {
//			Merchant: '00',
//			TxnType: 'P',
//			AmtPurchase: amtPurchase,
//			TxnRef: transNo,
//			CurrencyCode: currencyCode,
//			CutReceipt: cutReceipt,
//			ReceiptAutoPrint: receiptAutoPrint,
//			Application: '00',
//			PurchaseAnalysisData: purchaseAnalysisData,
//			Basket: basket
//		}

//		const notification: Notification = {
//			Uri: '',
//			AuthorizationHeader: `Bearer ${AuthToken}`
//		}

//		const fullRequest: FullRequest = {
//			Request: purchaseRequest,
//			Notification: notification
//		}

//		return this.http.post<any>(`${this.apiUrl}${uuidv4()}/purchase?async=true`, fullRequest, { headers: headers });
//	}

//	private handleError(error: any) {
//		console.error('An error occurred:', error);
//		return throwError(error);
//	}
//}

//export async function mapCartToLinklyBasket(cart$: Observable<CartItem[]>): Promise<LinklyBasketItem[]> {

//	const cart = await cart$.toPromise();

//	return cart.map(item => {
//		const stockItem = item.stockItem;

//		return {
//			id: stockItem.barcode, 
//			sku: stockItem.barcode,
//			qty: item.quantity,
//			amt: (stockItem.price || 0) * item.quantity,
//			tax: 0, //TODO choose amount in config?
//			dis: item.bestValue || 0,
//			name: `${stockItem.styleDescription || ''} ${stockItem.colourName || ''} ${stockItem.size || ''}`.trim()
//		};
//	});
//}
