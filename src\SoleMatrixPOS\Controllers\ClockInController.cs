using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Infrastructure;
using SoleMatrixPOS.Application.Staff.Commands;
using SoleMatrixPOS.Filters;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[RequireStaffCodeFilter]
	[ApiController]
	public class ClockInController : ControllerBase
	{

		private readonly IMediator _mediator;
		private readonly StaffCodeContext _staffCodeContext;
		private readonly StoreTimeContext _storeTimeContext;

		public ClockInController(IMediator mediator, StaffCodeContext staffCodeContext, StoreTimeContext storeTimeContext)
		{
			_mediator = mediator;
			_staffCodeContext = staffCodeContext;
			_storeTimeContext = storeTimeContext;
		}

		[Route("ClockIn")]
		public async Task<StaffClockInResponseDto> ClockIn(DateTime timeIn, CancellationToken ct)
		{
			return await _mediator.Send(new StaffClockIn(_storeTimeContext.StoreLocalTime, _staffCodeContext.StoreDetailsDto.StoreId));
		}


		[Route("ClockOut")]
		public async Task<ActionResult> ClockOut(DateTime timeOut, CancellationToken ct) // not using time from dto, using time created with timezone in request header
		{
			await _mediator.Send(new StaffClockOut(_storeTimeContext.StoreLocalTime, _staffCodeContext.StoreDetailsDto.StoreId, _staffCodeContext.PINPadNo));
			return Ok();
		}

		[HttpPost] // Use POST as it modifies data
		[Route("ClockOutAllForEndOfDay")]
		public async Task<ActionResult> ClockOutAllForEndOfDay(CancellationToken ct)
		{
			await _mediator.Send(new StaffClockOutAllForEndOfDay(), ct);
			return Ok();
		}
	}
}
