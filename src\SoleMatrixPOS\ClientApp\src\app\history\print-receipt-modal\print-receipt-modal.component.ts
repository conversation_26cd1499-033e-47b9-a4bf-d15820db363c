import { Component, OnInit, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { EmailReceiptComponent } from 'src/app/email-receipt/email-receipt.component';
import { TransNoDto, ReceiptTransactionDto, TranslogDto, Translog, TransactionClient, GetReceiptDto, Transpay, TranspayDto, TransrefDto, TransClientDetailsDto, LaybyClient, FileResponse, LaybylineDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import * as receiptActions from '../../reducers/receipt-printing/receipt.actions';
import * as laybySearchSelectors from '../../reducers/layby-search/layby-search.selectors';
import * as receiptSelector from '../../reducers/receipt-printing/receipt.selectors';
import { Transaction } from '../../payment/payment.service';
import { EftposService } from '../../eftpos/eftpos.service';
import { switchMap } from 'rxjs/operators';
import { ReceiptBatch, TextAction, FeedAction, BarcodeAction, CutAction, CutType } from '../../printing/printing-definitions';
import { PrintingService, SolemateReceiptOptions } from 'src/app/printing/printing.service';
import * as SysConfigSelectors from 'src/app/reducers/sys-config/sys-config.selectors';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import * as laybySearchActions from '../../reducers/layby-search/layby-search.actions';
@Component({
  selector: 'pos-print-receipt-modal',
  templateUrl: './print-receipt-modal.component.html',
  styleUrls: ['./print-receipt-modal.component.scss']
})
export class PrintReceiptModalComponent implements OnInit {
  public _transNo: number;
  public _transType: number;
  public _tillNo: string;
  public _storeId: string;
  public translogs: TranslogDto[] = [];
  public laybyCode: string;
  public laybyLines: LaybylineDto[] = [];
  public transpay: TranspayDto[] = [];
  public receipts: GetReceiptDto[];
  public laybyType: string;
  public printed: boolean;
  public refs: TransrefDto[] = [];
  public clientDetails: TransClientDetailsDto;
  public customerNameOnReceipt: string = 'F';
  private destroy$ = new Subject<void>();

  @Input()
  set transNo(transNo: number) {
    this._transNo = transNo;
  }

  @Input()
  set transType(transType: number) {
    this._transType = transType;
  }

  @Input()
  set tillNo(tillNo: string) {
    this._tillNo = tillNo;
  }

  @Input()
  set storeId(storeId: string) {
    this._storeId = storeId;
  }

  constructor(
    private store: Store<AppState>,
    public activeModal: NgbActiveModal,
    private modalService: NgbModal,
    private transactionClient: TransactionClient,
    private eftposService: EftposService,
    private printService: PrintingService,
    private laybyClient: LaybyClient
  ) { }

  ngOnInit() {
    this.store.select(receiptSelector.completed).subscribe(
      value => { if (value) this.close(); }
    );
    console.log("TransNo: " + this._transNo);
    console.log("TransType: " + this._transType);
    console.log("TillNo: " + this._tillNo);
    console.log("StoreId: " + this._storeId);

    // Subscribe to CustomerNameOnReceipt setting
    this.store.select(SysConfigSelectors.CustomerNameOnReceipt)
      .pipe(takeUntil(this.destroy$))
      .subscribe(setting => {
        this.customerNameOnReceipt = setting || 'F';
      });

    if (this._transType == 2) {
      console.log("Checking if return is a layby return" + this._transNo);
      this.laybyClient.getLaybyCodeByTransNo(this._transNo)
        .subscribe((code: string) => {
          this.laybyCode = code;
          console.log("Layby code:", this.laybyCode);
          
          if (this.laybyCode != null) {
            console.log("Layby code found, getting layby lines");
            this.laybyType = "Layby Return";
            this.store.dispatch(
              laybySearchActions.getLaybyLines({ laybyCode: this.laybyCode })
            );

            this.store
              .select(laybySearchSelectors.searchedLaybyLines)
              .subscribe((lines: LaybylineDto[]) => {
                this.laybyLines = lines;
                console.log("Layby lines:", this.laybyLines);
              });
          }
        }, err => {
          console.error("Error fetching layby code:", err);
        });
    }
    if (this._transType == 4) {
      this.laybyType = "Layby";
      console.log("Getting layby code for transNo:" + this._transNo);
      this.laybyClient.getLaybyCodeByTransNo(this._transNo)
        .subscribe((code: string) => {
          this.laybyCode = code;
          console.log("Layby code:", this.laybyCode);
        }, err => {
          console.error("Error fetching layby code:", err);
        });
    }      
    if (this._transType == 5) {
      this.laybyType = "Layby Payment";
      this.laybyClient.getLaybyCodeByTransNo(this._transNo)
        .subscribe(
          (code: string) => {
            this.laybyCode = code;
            console.log("Layby code:", this.laybyCode);
    
            // now that we have a valid code, fire the search
            this.store.dispatch(
              laybySearchActions.getLaybyLines({ laybyCode: this.laybyCode })
            );
    
            // and read the results
            this.store
              .select(laybySearchSelectors.searchedLaybyLines)
              .subscribe((lines: LaybylineDto[]) => {
                this.laybyLines = lines;
                console.log("Layby lines:", this.laybyLines);
              });
          },
          err => {
            console.error("Error fetching layby code:", err);
          }
        );
    }      
  }

  close() {
    this.store.dispatch(receiptActions.init());
    this.activeModal.close();
  }

  dismiss(reason: string) {
    this.activeModal.dismiss(reason);
  }

  print() {
    let dto: TransNoDto = {
      transNo: this._transNo,
      transType: this._transType,
      storeId: this._storeId,
      tillNo: this._tillNo
    };

    // call gettranslogs
    this.transactionClient.getTransLogByTransNo(this._transNo)
      .pipe(
        switchMap((logs: TranslogDto[]) => {
          this.translogs = logs;
          return this.transactionClient.getTransPayByTransNo(this._transNo);
        })
      )
      .pipe(
        switchMap((transPay: TranspayDto[]) => {
          this.transpay = transPay;
          return this.transactionClient.getTransClientDetailsByTransNo(this._transNo);
        })
      )
      .pipe(
        switchMap((clientDetails: TransClientDetailsDto) => {
          this.clientDetails = clientDetails;
          return this.transactionClient.getTransRefByTransNo(this._transNo);
        })
      )
      .pipe(
        switchMap((refs: TransrefDto[]) => {
          this.refs = refs || [];
          return this.eftposService.getReceipts(this._transNo, true);
        })
      )
      .subscribe({
        next: async (receipts: GetReceiptDto[]) => {
          if (receipts) {
            this.receipts = receipts;
          }
          await this.printReceipts();
        },
        error: (err) => {
          console.error('Error fetching data:', err);
          this.printReceipts();
        }
      });

    //this.store.dispatch(receiptActions.reprintReceipt({ payload: dto }));
  }

  async printReceipts() {
      this.printService.printEftposReceipt(this.receipts, true);

    let type = "";
    if (this._transType == 1) {
      type = "Sale";
    }
    else if (this._transType == 2) {
      type = "Return";
    }
    else if (this._transType == 4) {
      type = "Layby";
    }
    else if (this._transType == 5) {
      type = "Layby Payment";
    }
    else if (this._transType == 6) {
      type = "Gift Card";
    }
    else if (this._transType == 11) {
      type = "Customer Order";
    }
    else if (this._transType == 12) {
      type = "Quote";
    }

    let options = SolemateReceiptOptions.default()
    options.isReprint = true;
    let saleDateTime = new Date();
    console.log("Initial saleDateTime:", saleDateTime); // Log the initial value

    if (this.translogs && this.translogs.length > 0) {
      console.log("Translogs for reprinting!!!!:", this.translogs);
      const firstLog = this.translogs[0];
      console.log("First log:", firstLog); // Log the first log entry

      if (firstLog.transactionDate) {
        console.log("Transaction date exists:", firstLog.transactionDate);
        saleDateTime = new Date(firstLog.transactionDate);
        console.log("saleDateTime after parsing transactionDate:", saleDateTime);

        if (firstLog.transactionTime) {
          console.log("Transaction time exists:", firstLog.transactionTime);
          try {
            const timeParts = firstLog.transactionTime.match(/(\d+):(\d+):(\d+)\s*([AP]M)/i);
            console.log("Time parts:", timeParts); // Log the result of the regex match

            if (timeParts) {
              let hours = parseInt(timeParts[1]);
              const minutes = parseInt(timeParts[2]);
              const seconds = parseInt(timeParts[3]);
              const ampm = timeParts[4].toUpperCase();

              if (ampm === 'PM' && hours < 12) {
                hours += 12;
              } else if (ampm === 'AM' && hours === 12) {
                hours = 0;
              }

              saleDateTime.setHours(hours, minutes, seconds);
              console.log("saleDateTime after setting hours, minutes, seconds:", saleDateTime); // Log the updated saleDateTime
            }
          } catch (error) {
            console.error("Error parsing transaction time:", error);
          }
        } else {
          console.log("transactionTime does not exist in firstLog.");
        }
      } else {
        console.log("transactionDate does not exist in firstLog.");
      }
    } else {
      console.log("Translogs are empty or undefined.");
    }

    let remainingBalance = 0;
    let logsForPrinting: TranslogDto[] | LaybylineDto[] = this.translogs;
    let paysForPrinting: TranspayDto[] = this.transpay; // Use current transaction's pays by default
    let receiptDto: any; // Use 'any' for flexibility or define a specific interface

    if (this._transType == 4) { // Initial Layby
      logsForPrinting = this.translogs;
      paysForPrinting = this.transpay;
      // ... (balance calculation for initial layby) ...
      let totalCost = this.translogs
          .filter(log => log.quantity > 0 && log.sellingPrice > 0)
          .reduce((acc, log) => acc + (log.quantity * log.sellingPrice), 0);
      let totalPaid = this.transpay.reduce((acc, pay) => acc + pay.payAmount, 0);
      console.log("Total cost:", totalCost);
      console.log("Total paid:", totalPaid);
      remainingBalance = totalCost - totalPaid;

      // DTO for initial layby uses current transaction logs/pays
      receiptDto = {
        saleDateTime: saleDateTime,
        transType: this._transType,
        logs: this.translogs,
        pays: this.transpay,
        refs: this.refs
      };

    } else if (this._transType == 5 || this.laybyType == "Layby Return") { // Layby Payment Reprint
      // 1. Start with the full layby history
      logsForPrinting = this.laybyLines ? this.laybyLines.slice(0, -1) : [];
      paysForPrinting = this.transpay; // Current payment details only

      // 2. Calculate the outstanding amount BEFORE this payment (to match original receipt behaviour)
      let amountDueBeforePayment = 0;

      if (this.laybyLines && this.laybyLines.length > 0) {
        // Total value of items placed on layby (transType 1)
        const totalCost = this.laybyLines
          .filter(line => line.transType == 1)
          .reduce((acc, line) => acc + line.extendedValue, 0);

        // Total of ALL payments made so far (negative extendedValue, deposit/payment transTypes 2 & 3)
        const totalPaid = this.laybyLines
          .filter(line => line.extendedValue < 0 || line.transType == 2 || line.transType == 3)
          .reduce((acc, line) => acc + Math.abs(line.extendedValue), 0);

        // Current payment amount (taken from Transpay list so we don't need to rely on laybyLines)
        const currentPaymentTotal = this.transpay.reduce((acc, pay) => acc + pay.payAmount, 0);

        // Outstanding AFTER this payment (balance)
        const remainingAfterPayment = totalCost - totalPaid;

        // Outstanding BEFORE this payment
        amountDueBeforePayment = remainingAfterPayment + currentPaymentTotal;
      } else {
        console.warn("Cannot calculate amount due for Layby Payment reprint: laybyLines are empty or not loaded.");
      }

      // 3. Ensure a zero‑quantity log with the layby code exists (used for barcode & code look‑up)
      const hasZeroQtyLog = logsForPrinting.some(l => l.quantity === 0);
      if (!hasZeroQtyLog) {
        // Cast to TranslogDto shape – only required fields for printing service
        const balanceLog: any = {
          styleCode: this.laybyCode,
          colourCode: '',
          sizeCode: '',
          quantity: 0,
          sellingPrice: amountDueBeforePayment,
          transNo: this._transNo,
          lineNo: (logsForPrinting.length || 0) + 1
        };
        logsForPrinting.push(balanceLog as any);
      }

      // 4. Build DTO mirroring the original print payload
      receiptDto = {
        saleDateTime: saleDateTime,
        transType: this._transType,
        logs: logsForPrinting,
        pays: paysForPrinting,
        refs: this.refs
      };

      remainingBalance = amountDueBeforePayment; // Pass this as `amountDue` to print service
    } else { // Other transaction types
        logsForPrinting = this.translogs;
        paysForPrinting = this.transpay;
        receiptDto = {
            saleDateTime: saleDateTime,
            transType: this._transType,
            logs: this.translogs,
            pays: this.transpay,
            refs: this.refs
          };
    }

    // Log the data *before* calling the print service
    console.log("Data being sent to printService:", {
        laybyType: this.laybyType, // Only relevant for layby types
        logs: logsForPrinting,
        pays: paysForPrinting,
        transType: this._transType,
        transNo: this._transNo.toString(),
        options: options,
        receiptDto: receiptDto,
        remainingBalance: remainingBalance,
        change: 0,
        refs: this.refs,
        clientCode: this.customerNameOnReceipt === 'T' && this.clientDetails.clientCode ? this.clientDetails.clientCode : undefined,
        clientName: this.customerNameOnReceipt === 'T' && this.clientDetails.clientName ? this.clientDetails.clientName : undefined,
        laybyCode: this.laybyCode
    });

    try {
      // Use the determined logsForPrinting and remainingBalance for layby types
      if (this._transType == 4 || this._transType == 5) {
        await this.printService.printLaybyReceipt(
          this.laybyType,
          logsForPrinting,  // Full history for type 5
          paysForPrinting,  // Current payment details
          this._transType,
          this._transNo.toString(),
          options,
          receiptDto,       // DTO now contains full logs history for type 5
          remainingBalance, // Calculated remaining balance
          0,
          this.refs,
          this.customerNameOnReceipt === 'T' && this.clientDetails.clientCode ? this.clientDetails.clientCode : undefined,
          this.customerNameOnReceipt === 'T' && this.clientDetails.clientName ? this.clientDetails.clientName : undefined,
          this.laybyCode
        );
      } else if (this.laybyType === "Layby Return") {
        await this.printService.printLaybyReturnReceipt(
          this.laybyType,
          logsForPrinting,
          paysForPrinting,
          this._transType,
          this._transNo.toString(),
          options,
          receiptDto,
          0,
          0
        );
      } else if (this._transType == 12) {
        await this.printService.printQuoteReceipt(
          logsForPrinting,
          this._transType,
          this._transNo.toString(),
          options,
          receiptDto,
          this.customerNameOnReceipt === 'T' && this.clientDetails.clientName ? this.clientDetails.clientName : undefined,
          this.customerNameOnReceipt === 'T' && this.clientDetails.clientCode ? this.clientDetails.clientCode : undefined
        );
      } else { // Handle non-layby types
        await this.printService.printSolemateReceipt(
          type,
          logsForPrinting, // Original translogs for non-layby
          paysForPrinting, // Original transpay
          this._transType,
          this._transNo.toString(),
          options,
          receiptDto,
          0,
          this.refs,
          this.customerNameOnReceipt === 'T' && this.clientDetails.clientCode ? this.clientDetails.clientCode : undefined,
          this.customerNameOnReceipt === 'T' && this.clientDetails.clientName ? this.clientDetails.clientName : undefined
        );
      }
      this.printed = true;
      console.log("Receipt printed successfully");
    } catch (error) {
      console.error("Error printing receipt:", error);
    }
  }

  async printA4() {
    this.transactionClient.getTransLogByTransNo(this._transNo)
      .pipe(
        switchMap((logs: TranslogDto[]) => {
          this.translogs = logs;
          return this.transactionClient.getTransPayByTransNo(this._transNo);
        }),
        switchMap((transPay: TranspayDto[]) => {
          this.transpay = transPay;
          return this.transactionClient.getTransClientDetailsByTransNo(this._transNo);
        }),
        switchMap((clientDetails: TransClientDetailsDto) => {
          this.clientDetails = clientDetails;
          return this.transactionClient.getTransRefByTransNo(this._transNo);
        })
      )
      .subscribe({
        next: async () => {
          const receiptDto = await this.buildReceiptDto();
          try {
            await this.printService.printPdfReceipt(receiptDto);
            this.printed = true;
          } catch (error) {
            console.error("Error printing PDF receipt:", error);
          }
        },
        error: (err) => {
          console.error('Error fetching data for PDF receipt:', err);
        }
      });
  }

  private async buildReceiptDto(): Promise<any> {
    let saleDateTime = new Date();
    if (this.translogs && this.translogs.length > 0) {
      const firstLog = this.translogs[0];
      if (firstLog.transactionDate) {
        saleDateTime = new Date(firstLog.transactionDate);
        if (firstLog.transactionTime) {
          try {
            const timeParts = firstLog.transactionTime.match(/(\d+):(\d+):(\d+)\s*([AP]M)/i);
            if (timeParts) {
              let hours = parseInt(timeParts[1]);
              const minutes = parseInt(timeParts[2]);
              const seconds = parseInt(timeParts[3]);
              const ampm = timeParts[4].toUpperCase();
              if (ampm === 'PM' && hours < 12) {
                hours += 12;
              } else if (ampm === 'AM' && hours === 12) {
                hours = 0;
              }
              saleDateTime.setHours(hours, minutes, seconds);
            }
          } catch (error) {
            console.error("Error parsing transaction time:", error);
          }
        }
      }
    }

    let logsForPrinting: any[] = this.translogs;
    if (this._transType === 5 || this.laybyType === "Layby Return") {
      logsForPrinting = this.laybyLines ? this.laybyLines.slice(0, -1) : [];

      let amountDueBeforePayment = 0;
      if (this.laybyLines && this.laybyLines.length > 0) {
        const totalCost = this.laybyLines
          .filter(line => line.transType === 1)
          .reduce((acc, line) => acc + line.extendedValue, 0);
        const totalPaid = this.laybyLines
          .filter(line => line.extendedValue < 0 || line.transType === 2 || line.transType === 3)
          .reduce((acc, line) => acc + Math.abs(line.extendedValue), 0);
        const currentPaymentTotal = this.transpay.reduce((acc, pay) => acc + pay.payAmount, 0);
        const remainingAfterPayment = totalCost - totalPaid;
        amountDueBeforePayment = remainingAfterPayment + currentPaymentTotal;
      }

      const hasZeroQtyLog = logsForPrinting.some(l => l.quantity === 0);
      if (!hasZeroQtyLog && this.laybyCode) {
        const balanceLog: any = {
          styleCode: this.laybyCode,
          colourCode: '',
          sizeCode: '',
          quantity: 0,
          sellingPrice: amountDueBeforePayment,
          transNo: this._transNo,
          lineNo: (logsForPrinting.length || 0) + 1
        };
        logsForPrinting.push(balanceLog);
      }
    }

    const paysForDto = this.transpay.map(p => ({ ...p, transNo: this._transNo }));

    return {
      saleDateTime: saleDateTime,
      transType: this._transType,
      logs: logsForPrinting,
      pays: paysForDto,
      refs: this.refs
    };
  }

  // New method for emailing the receipt
  emailReceipt() {
    const modalRef = this.modalService.open(EmailReceiptComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    let dto: TransNoDto = {
      transNo: this._transNo,
      transType: this._transType,
      storeId: this._storeId,
      tillNo: this._tillNo
    };
    console.log(dto);

    // Pass the receiptTrans to the email component
    modalRef.componentInstance.receiptTrans = dto;

    modalRef.result.then(
      (email) => {
        console.log('Email receipt to:', email);
        this.close(); // Close both the email modal and the print modal
      },
      (reason) => {
        console.log('Email modal dismissed:', reason);
        this.close(); // Ensure both modals are closed even when dismissed
      }
    );
  }
}
