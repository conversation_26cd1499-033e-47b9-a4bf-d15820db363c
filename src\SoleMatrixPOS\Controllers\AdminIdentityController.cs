using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR; 
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Infrastructure;
using SoleMatrixPOS.Application.Transaction;
using SoleMatrixPOS.Application.Transaction.Commands;
using SoleMatrixPOS.Application.Transaction.Queries;
using SoleMatrixPOS.Domain.Identity.Models;
using SoleMatrixPOS.Identity;
using SoleMatrixPOS.Identity.Interface;
using SoleMatrixPOS.Identity.Models;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme, Roles = RoleDefinitions.ROLE_ADMIN)]
    [ApiController]
    public class AdminIdentityController : Controller
    {
        private readonly IMediator _mediator;
		private readonly UserManager<RegisteredTill> _tillManager;
		private readonly IAdminAuthService _adminAuthService;


		public AdminIdentityController(IMediator mediator, UserManager<RegisteredTill> tillManager, IAdminAuthService adminAuthService)
		{
			_tillManager = tillManager;
			_mediator = mediator;
			_adminAuthService = adminAuthService;
		}

		[HttpPost("Register")]
		public async Task<IActionResult> Register([FromBody] TillRegisterDto tillRegister)
		{
			// TODO: refactor, not high priority, but move the below code into adminauthservice

			var role = tillRegister.RoleName?.ToUpper();

			// Check role validity (if empty, pick till)
			if (role == "" || role == null) role = RoleDefinitions.ROLE_TILL;

			// If it's not a valid role
			else if (!RoleDefinitions.Definitions.Any((definition) => definition.RoleName.ToUpper() == role))
			{
				return NotFound(new
				{
					Message = $"Role <{tillRegister.RoleName.ToUpper()}> is not valid. Choose one of: <{string.Join(", ", RoleDefinitions.Definitions)}> (or provide no value for default role)"
				});
			}
			

			if (await _tillManager.FindByIdAsync(tillRegister.Id) != null)
			{
				return StatusCode(StatusCodes.Status400BadRequest, $"Till ID <{tillRegister.Id}> already registered");
			}

			RegisteredTill till = new RegisteredTill()
			{
				Id = tillRegister.Id,
				UserName = tillRegister.Id,
				StoreId = tillRegister.StoreId,
				TillId = tillRegister.TillId,
				Email = tillRegister.Email
			};

			await _tillManager.CreateAsync(till, tillRegister.Password);

			await _tillManager.AddToRoleAsync(till, role);

			// TODO: handle errors with creation
			// ...

			// Send password reset email
			var res = await _adminAuthService.ResetPasswordViaEmail(tillRegister.Id);

			return res.Succeeded ? Ok("Till registered and password reset requested successfully") : BadRequest($"Failed to create till <{tillRegister.Id}> as password wasn't reset. Reason: {res.Reason}");
		}

		[HttpPost("RequestReset")]
		public async Task<IActionResult> RequestReset([FromBody] PasswordResetRequestDto PasswordResetReqDto)
		{
			var res = await _adminAuthService.ResetPasswordViaEmail(PasswordResetReqDto.Id);

			return res.Succeeded ? Ok("Password reset requested successfully") : BadRequest("Failed to request password reset");

		}
	}
}
