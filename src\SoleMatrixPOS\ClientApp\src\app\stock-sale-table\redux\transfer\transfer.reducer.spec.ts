import { TransfersReducer } from './transfer.reducer';
import * as TransferSelector from './transfer.selector';
import { state } from '../../test-state';
import { Transfer } from './transfer.model';

describe('TransferReducer', () => {

  let initialState: Transfer[];

  beforeEach(()=>{
    initialState = TransferSelector.getTransfers.projector(state);
  });

  describe('TransferType.ADD_TRANSFER', ()=> {
    it('should add a transfer to the state', ()=> {

      const action = {
        type: "[TRANSFER] add", 
        id: 2,
        from: "Location 2",
        to: "Location 3",
        name: "BOUNCE",
        size: "1",
        qty: 1, 
        highlighted: "off"
      };

      const newState = TransfersReducer(initialState, action);

      expect(newState).toEqual([...initialState, {
        id: 2,
        from: "Location 2",
        to: "Location 3",
        name: "BOUNCE",
        size: "1",
        qty: 1, 
        highlighted: "off"
      }]);

    });
  })

  describe('TransferType.REMOVE_TRANSFER', ()=> {
    it('should remove a transfer from the state', ()=> {

      const action = {
        type: "[TRANSFER] remove", 
        id: 1
      };

      const newState = TransfersReducer(initialState, action);

      expect(newState).toEqual([initialState[1]]);

    });
  })

  describe('TransferType.LOAD_TRANSFERS', ()=> {
    it('should load the given transfers', ()=> {
      const action = {
        type: "[TRANSFER] load", 
        transfers: initialState
      };

      const newState = TransfersReducer([], action);

      expect(newState).toEqual(initialState);
    });
  })

  describe('TransferType.HIGHLIGHT_TRANSFER', ()=> {
    it('should toggle the highlight state between "on" and "off"', ()=> {
      const action = {
        type: "[TRANSFER] highlight", 
        id: 1
      };

      // Turn on transfer with id '1'
      const onState = TransfersReducer(initialState, action);
      expect(onState[0].highlight).toEqual("on");

      // Turn off transfer with id '1'
      const offState = TransfersReducer(initialState, action);
      expect(offState[0].highlight).toEqual("off");

    });
  })
  
});