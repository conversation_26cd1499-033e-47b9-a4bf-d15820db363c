import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { catchError } from 'rxjs/operators';


@Injectable({
  providedIn: 'root'
})
export class MysqlApiService {

  constructor() {};

  public getSizes(): Observable<JSON[]> {

    let result = [];

    result.push({SIZE: "1"});
    result.push({SIZE: "2"});
    result.push({SIZE: "3"});
    result.push({SIZE: "4"});


    return of(result);
  }

  public getLocations(): Observable<JSON[]> {

    let result = [];

    for(let i = 1; i < 5; i++){

      let location = {
        LOCATION_NAME: "LOCATION " + i,
        TRADING_LOCN: "T",
        RANKING_NO: i,
      }

      result.push(
        location
      )
    }
    
    return of(result);
  }

  public getStockSales(): Observable<JSON[]> {

    let result = [];

    for(let i = 1; i < 5; i++){

      for(let j = 1; j < 5; j++){
        

        let stockSale = {
          LOCATION_NAME: "LOCATION " + i,
          SSTOCK_ON_HAND: j,
          SYTD_SALES_QTY: j,
          SIZE_CODE: j + "",
          COLOUR_CODE: "BLK"
        }

        result.push(
          stockSale
        )
      }
    }
    
    return of(result);
  }

  public getLastReceiptedCost(): Observable<JSON[]> {

    let result = [];

    let cost = {
      LAST_RECEIPTED_COST: "25.00"
    }

    result.push(cost);
    
    return of(result);
  }
}