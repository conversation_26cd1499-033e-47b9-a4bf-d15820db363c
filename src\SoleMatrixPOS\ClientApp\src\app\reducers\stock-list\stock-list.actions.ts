import { createAction, props } from '@ngrx/store';
import { StockItemDto, StaffLoginResponseDto} from '../../pos-server.generated';
import {StockListItem, StockTransferType} from './stock-list.reducer';

export const init = createAction('[StockList] Init');
export const addItem = createAction('[StockList] AddItem', props<{ stockItem: StockItemDto, transferType: StockTransferType }>());
export const removeItem = createAction('[StockList] RemoveItem', props<{ item: StockListItem }>());

