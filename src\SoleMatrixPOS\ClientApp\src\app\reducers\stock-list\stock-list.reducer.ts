import { createReducer, on, Action, State } from '@ngrx/store';
import * as stockListActions from './stock-list.actions';
import { StockItemDto } from '../../pos-server.generated';

export class StockListState {
	items: StockListItem[];
}

export enum StockTransferType {
	Sale        = 1,
	Return      = 2,
	TransferOut = 3,
	TransferIn  = 4
}

export class ItemDiscount {
	type: string;
	percent: number;
	amount: number;
	staff: string;
	newPrice: number;
	qty: number;
}

export class StockListItem {
	stockItem: StockItemDto;
	discount: ItemDiscount;
	transferType: StockTransferType;
	qty: number;
}

export const initialState: StockListState = {
	items: []
} as StockListState;

export const stockList = createReducer(initialState,
	on(stockListActions.init, state => initialState),

	on(stockListActions.addItem, (state, action) => {
		return {...state,
			items: [...state.items, {
				stockItem: action.stockItem,
				discount: null,
				qty: 1,
				transferType: action.transferType
			} as StockListItem]
		};
	}),

	on(stockListActions.removeItem, (state, action) => {
		return {...state, items: state.items.filter(i => i !== action.item) };
	}),
);
