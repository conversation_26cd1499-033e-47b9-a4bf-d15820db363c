import { Injectable } from "@angular/core";
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree } from "@angular/router";
import { Store } from "@ngrx/store";
import { AppState } from "src/app/reducers";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { StaffLoginState } from "src/app/reducers/staff/staff.reducer";

@Injectable()
export class StaffStateGuard implements CanActivate {
    constructor (
        private store: Store<AppState>,
        private router: Router
    ){}

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean | UrlTree> {
        return this.store.select(s => s.staff.staffLoginState).pipe(map(staffLoginState => {

            if(staffLoginState === StaffLoginState.ClockIn ){
                return state.url === '/clock-in' ? true : this.router.parseUrl('/clock-in')
            }

            if(staffLoginState === StaffLoginState.DailyFloat){
                return state.url === '/daily-float-enter' ? true : this.router.parseUrl('/daily-float-enter') 
            }

            if(staffLoginState === StaffLoginState.Complete){
                return true
            }
            
            return this.router.parseUrl("/")
        }))
    }
}