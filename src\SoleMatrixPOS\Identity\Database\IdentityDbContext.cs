using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using SoleMatrixPOS.Domain.Identity.Models;
using SoleMatrixPOS.Identity.Models;

namespace SoleMatrixPOS.Domain.Identity.Database
{
	public class TillIdentityDbContext : IdentityDbContext<RegisteredTill, TillRole, string>
	{
		public TillIdentityDbContext(DbContextOptions<TillIdentityDbContext> options) : base(options) { }

		protected override void OnModelCreating(ModelBuilder builder)
		{
			base.OnModelCreating(builder);

			// Adapted from dashboard code
			// This tells entity framework to ignore the inherited fields that we don't use (email, phone, 2fac, lockout)
			builder.Entity<RegisteredTill>().Ignore(u => u.EmailConfirmed);
			builder.Entity<RegisteredTill>().Ignore(u => u.PhoneNumberConfirmed);
			builder.Entity<RegisteredTill>().Ignore(u => u.TwoFactorEnabled);
			builder.Entity<RegisteredTill>().Ignore(u => u.AccessFailedCount);
			builder.Entity<RegisteredTill>().Ignore(u => u.LockoutEnabled);

			// Extend here
		}
	}
}
