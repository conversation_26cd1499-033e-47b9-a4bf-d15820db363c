import { ActionReducerMap, createFeatureSelector } from '@ngrx/store';

import { Transfer } from './transfer/transfer.model';
import { Location } from './location/location.model';
import { Size } from '../classes/size';

import { TransfersReducer } from './transfer/transfer.reducer'
import { LocationReducer } from './location/location.reducer';
import { SizeReducer } from './size/size.reducer';
import { ImmutableLocationReducer } from './location/immutable-location.reducer';
import { Header } from '../classes/header.model';
import { HeaderReducer } from './header/header.reducers';
import { PageReducer } from './page/page.reducers';

/**
 * You can register new state slices here
 */
export interface AppState {
    transfers: Transfer[];
    locations: Location[];
    sizes: Size[];
    immutableLocations: Location[];
    headers: Header[];
    page: number;
}

/**
 * Include a reducer for handling actions 
 * targeting each state slice
 */
export const rootReducer: ActionReducerMap<AppState> = {
    transfers: TransfersReducer,
    locations: LocationReducer,
    sizes: SizeReducer,
    immutableLocations: ImmutableLocationReducer,
    headers: HeaderReducer,
    page: PageReducer
}

export const stockSaleTableState = createFeatureSelector<AppState>('stockSaleTable');