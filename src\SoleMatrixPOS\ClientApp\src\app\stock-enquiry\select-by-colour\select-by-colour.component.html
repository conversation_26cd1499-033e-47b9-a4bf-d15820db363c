<div class="color-selector-container">
    <div class="controls"> 
      <select class="modern-select " (change)="selectItem($event.target.value)" [value]="getSelectedValue()">
        <option *ngFor="let color of colors$ | async; trackBy: trackByColor"
          [value]="color.styleCode + '|' + color.colorCode + '|' + color.colorName">
          {{ color.colorName }}
        </option>
      </select>

      <div class="navigation-buttons" *ngIf="(colors$ | async)?.length > 1">
        <button class="nav-btn prev-btn" (click)="previousColor()" [disabled]="!canGoPrevious()">
          <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn next-btn" (click)="nextColor()" [disabled]="!canGoNext()">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>
</div>