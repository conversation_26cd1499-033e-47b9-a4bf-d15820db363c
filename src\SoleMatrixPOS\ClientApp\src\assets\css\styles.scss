/* Rebecca: We call Bootstrap via styles so we can reuse the variables in the bootstrap theme. */
@import "../../../node_modules/bootstrap/scss/bootstrap.scss";
@import "font-awesome-pro/css/fontawesome.min.css";
@import "font-awesome-pro/css/solid.min.css";
@import "font-awesome-pro/css/brands.min.css";

@import "../../../node_modules/primeng/resources/primeng.min.css"; // PrimeNG core styles
@import "../../../node_modules/primeng/resources/themes/nova-dark/theme.css";
@import "../../../node_modules/primeicons/primeicons.css";


/* Gradient Mixin */

html{
  font-size: 16px;
  min-height: 100vh;
}

body{
  @include gradient-directional($blue, $cyan, 225deg); 
}

.navbar{
  box-shadow: $box-shadow;
  margin-bottom: 1rem;
  font-family: $headings-font-family;
}

.navbar-brand img{
  height: 50px;
}

.navbar-text {
  font-family: $font-family-sans-serif;
  margin-right: 1rem;
}

.container-fluid{
  padding-left: $spacer;
  padding-right: $spacer;
  max-width: 1800px;
}

/* Not sure why bootstrap does this, but have to override it */

@media (max-width: 991.98px) {
  .navbar-expand-lg > .container, .navbar-expand-lg > .container-fluid {
    padding-right: $spacer;
    padding-left: $spacer;
}
}


.wrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.content-wrapper {
  background: $white;
  margin-top: -$spacer;
  padding: $spacer 0;
  overflow-y: auto;
}

.footer-wrapper{
  background-image: url('../content-wrapper.svg');
  background-repeat: repeat-x;
  background-position: left -1px;
  background-size: auto 30px;
  color: $white;
  padding: 2.5rem 0 2rem;
  
.btn-lg{
  height: 5rem;
}

}

h1, .h1, h5, .h5{
  font-family: $font-family-sans-serif;
}

h2, .h2{
  letter-spacing: 5px;
  text-transform: uppercase;
}

h4, .h4{
  text-transform: uppercase;
}

h6, .h6{
  letter-spacing: 1px;
  text-transform: uppercase;
}

.btn-lg{
box-shadow: $box-shadow-sm;
&.btn-default:hover{
  background: $white;
  color: $link-color;
  border-color: $white;
} 
h4{
  margin: 0 0 0 0;
  text-align: left;
}
}

.top-row {
  max-height:800px;
.btn-lg{
  height:100%;
}
.fa-stack{
  margin: 3rem 0;
}
}

.btn-hidden {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  h4{
    margin: 0 0 0 $spacer;
    text-transform: none;
  }
  &:not(:disabled):active .btn-default{
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0.2rem rgba(239, 239, 242, 0.5);
    color: $gray-500;
    background-color: darken($white, 10%);
    border-color: darken($white, 12.5%);
  }
  &:disabled{
    opacity: 0.65;
    .btn-default:active{
      box-shadow: none;
      color: $gray-500;
      background-color: $white;
      border-color: $white;
    }
  }
}


.btn-circle{
  border-radius: 100px;
  padding: 0;
  width: 5rem;
  height: 5rem;
  display: flex; /* How did we center align anything before flexbix? */
  justify-content: center;
  align-items: center;
}

.fa-shadow{
  color: $blue;
  position: relative;
  left: - ($spacer / 2);
  top:  - ($spacer / 2);
}

.color-gradient-green{
  background: $green;
  @include gradient-directional($green, $cyan, 45deg); 
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.color-gradient-pink{
  background: $pink;
  @include gradient-directional($cyan, $pink, 45deg); 
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.color-gradient-orange{
  background: $orange;
  @include gradient-directional($pink, $orange, 45deg); 
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.color-gradient-grey{
  background: $gray-400;
  @include gradient-directional($gray-400, $gray-200, 45deg); 
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-shadow {
text-shadow: $box-shadow-sm;
color: transparent;
}

.fa-vw{
  font-size: 4vw;
}

.total-container {
  display: flex;
  align-items: flex-end;
  border-bottom: 1px solid $border-color;
  h6{
    margin: 0.8rem 0;
  }
  h1{
    margin-left: $spacer * 3;
  }

}
.next-button{
  margin: 0 1.5rem;
}

label{
  @extend .h6;
  text-transform: none;
}

.btn-outline-default{
  color: $body-color;
  border-color: $gray-400;
  background: $gray-100;
  &:hover, &:not(:disabled):not(.disabled):active, &:not(:disabled):not(.disabled).active{
    color: $body-color;
    border-color: darken($gray-400, 5%);
    background: darken($gray-100, 5%);
  }
}

.form-control-special{
  color: $link-color;
  font-family: $headings-font-family;
  &:focus{
    color: $link-color;
  }
}

.table {
  thead th{
    @extend .h6;
    border-bottom: none;
  }
  .table-input{
    padding: 0.5rem 0.8rem;
    .form-control{
      padding: 0.5rem;
    }
  }
  td{
    vertical-align: middle;
    border-bottom: 1px solid $border-color;
    border-top:none;
  }
}

.search-highlight {
  background-color: #e83e8c33;
}

html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }
