<pos-nav-header [pageName]="'Create Layby'"></pos-nav-header>

<div class="container mt-2">
  <!-- Customer Details Form -->
  <div class="card mx-auto" style="margin-top: 0.5rem; margin-bottom: 0.5rem;">
    <div class="card-body">
      <h4 class="mb-3">Enter Customer Details</h4>

      <form [formGroup]="customerOrderForm" autocapitalize="on">
        <div class="row">
          <label class="col-6">
            <div class="mb-2">Club Number</div>
            <input type="text" class="form-control form-control-sm" formControlName="clubNumber" [readonly]="true">
          </label>
          <label class="col-6">
            <div class="mb-2">Search</div>
            <button class="form-control btn btn-outline-primary btn-sm" type="button" (click)="searchCustomer(content)">
              <i class="fas fa-search"></i>
            </button>
          </label>
        </div>

        <div class="row">
          <label class="col-2">
            <div class="mb-2">Title</div>
            <input type="text" class="form-control form-control-sm" formControlName="title">
          </label>
          <label class="col-5">
            <div class="mb-2">First Name <span class="text-danger">*</span></div>
            <input type="text" class="form-control form-control-sm" formControlName="firstName" required>
          </label>
          <label class="col-5">
            <div class="mb-2">Surname <span class="text-danger">*</span></div>
            <input type="text" class="form-control form-control-sm" formControlName="lastName" required>
          </label>
        </div>

        <div class="row">
          <label class="col-12">
            <div class="mb-2">Street</div>
            <input type="text" class="form-control form-control-sm" formControlName="street">
          </label>
        </div>

        <div class="row">
          <label class="col-6">
            <div class="mb-2">Suburb / Town</div>
            <input type="text" class="form-control form-control-sm" formControlName="suburb">
          </label>
          <label class="col-4">
            <div class="mb-2">State</div>
            <input type="text" class="form-control form-control-sm" formControlName="state">
          </label>
          <label class="col-2">
            <div class="mb-2">Postcode</div>
            <input type="text" class="form-control form-control-sm" formControlName="postcode">
          </label>
        </div>

        <div class="row">
          <label class="col-6">
            <div class="mb-2">Phone <span class="text-danger">*</span></div>
            <input type="text" class="form-control form-control-sm" formControlName="phone" required>
          </label>
          <label class="col-6">
            <div class="mb-2">Email <span class="text-danger">*</span></div>
            <input type="email" class="form-control form-control-sm" formControlName="email" required>
          </label>
        </div>
        <!-- Buttons for Create New Layby and Search Laybys -->
        <div class="row justify-content-center pt-2">
          <button type="button" 
                  class="btn btn-success btn-sm mr-2" 
                  [disabled]="!customerOrderForm.valid" 
                  (click)="createEntry()">
            Create New Layby
          </button>
          <button type="button" 
                  class="btn btn-info btn-sm" 
                  (click)="openSearchModal()">
            Search Laybys
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal for Customer Club Search -->
<ng-template #content let-modal>
    <div class="modal-header">
      <h4 class="modal-title">Customer Club Search</h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <pos-customer-club-container (onSelectedMember)="onMemberSelected($event)"></pos-customer-club-container>
    </div>
    <div class="modal-footer">
      <button class="btn btn-secondary btn-sm" (click)="modal.dismiss('Close click')">Close</button>
      <button class="btn btn-primary btn-sm" (click)="onUseSelected()">Use Selected</button>
    </div>
</ng-template>

<ng-template #confirmExistingMemberModal let-modal>
    <div class="modal-header">
      <h4 class="modal-title">Member Already Exists</h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>A member with the provided phone number already exists.</p>
      <p>Do you want to fill in their details?</p>
    </div>
    <div class="modal-footer">
      <button class="btn btn-secondary btn-sm" (click)="modal.dismiss('No')">No</button>
      <button class="btn btn-primary btn-sm" (click)="modal.close('Yes')">Yes</button>
    </div>
</ng-template>
