import { Injectable } from '@angular/core';
import { Size } from '../classes/size';

import { AppState } from '../redux/app.store';
//import { AppState, state } from '../test-state';
import { Store } from '@ngrx/store';
import { Location } from '../classes/location.model';
import { getImmutableLocations } from '../redux/location/location.selector';
import { getSizes } from '../redux/size/size.selector'
import { Observable, of } from 'rxjs';
import * as SizeSelector from '../redux/size/size.selector';
import * as LocationSelector from '../redux/location/location.selector';

@Injectable({
    providedIn: 'root'
})
export class StockSaleTableService {

    public sizes$: Observable<Size[]>;
    public locations$: Observable<Location[]>;

    constructor(private store: Store<AppState>) {
        this.sizes$ = this.getSize();
        this.locations$ = this.getLocation();
    }

    public getSize(): Observable<Size[]> {
        return this.store.select(getSizes);
    }

    public getLocation(): Observable<Location[]> {
        return this.store.select(getImmutableLocations);
    }

}