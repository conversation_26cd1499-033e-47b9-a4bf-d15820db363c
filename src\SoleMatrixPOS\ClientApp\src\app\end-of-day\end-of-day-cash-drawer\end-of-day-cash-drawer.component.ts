import { FormBuilder, Validators, AbstractControl } from '@angular/forms';
import { AppState } from 'src/app/reducers';
import { Store } from '@ngrx/store';
import { Component, OnInit } from '@angular/core';
import { Observable, combineLatest } from 'rxjs';
import { Router, ActivatedRoute } from '@angular/router';
import { map, startWith, first } from 'rxjs/operators';
import { EventEmitter, Output } from '@angular/core';
import * as endOfDayFloatSelectors from '../../reducers/end-of-day/end-of-day-float/end-of-day-float.selectors';
import * as endOfDayCashDrawerActions from '../../reducers/end-of-day/end-of-day-cash-drawer/end-of-day-cash-up.actions';
import * as endOfDayCashUpSelectors from '../../reducers/end-of-day/end-of-day-cash-drawer/end-of-day-cash-up.selectors';
import { FloatDto } from 'src/app/pos-server.generated';
import * as receiptActions from '../../reducers/receipt-printing/receipt.actions';
import { ReceiptBatch, OpenCashDrawerAction } from 'src/app/printing/printing-definitions';

@Component({
  selector: 'pos-end-of-day-cash-drawer',
  templateUrl: './end-of-day-cash-drawer.component.html',
  styleUrls: ['./end-of-day-cash-drawer.component.scss']
})
export class EndOfDayCashDrawerComponent implements OnInit {
  @Output() closeModal: EventEmitter<void> = new EventEmitter<void>();
  total: number;
  FloatTotal: number;
  floatData: FloatDto | null;
  combinedTotal: number;
  total$: Observable<number>;
  FloatTotal$: Observable<number>;

  constructor(
    private route: ActivatedRoute,
    private store: Store<AppState>,
    private formBuilder: FormBuilder,
    private router: Router
  ) { }

  public floatForm = this.formBuilder.group({
    Denomination100: [0, [Validators.min(0), Validators.required]],
    Denomination50: [0, [Validators.min(0), Validators.required]],
    Denomination20: [0, [Validators.min(0), Validators.required]],
    Denomination10: [0, [Validators.min(0), Validators.required]],
    Denomination5: [0, [Validators.min(0), Validators.required]],
    Denomination2: [0, [Validators.min(0), Validators.required]],
    Denomination1: [0, [Validators.min(0), Validators.required]],
    Denomination50c: [0, [Validators.min(0), Validators.required]],
    Denomination20c: [0, [Validators.min(0), Validators.required]],
    Denomination10c: [0, [Validators.min(0), Validators.required]],
    Denomination5c: [0, [Validators.min(0), Validators.required]]
  });

  ngOnInit() {
    this.store.select(endOfDayFloatSelectors.selectEndFloatData)
      .pipe(first()) // Fetch value once and complete
      .subscribe(floatData => {
        this.floatData = floatData;
      });

    // Load saved values from local storage if they exist
    this.loadFormValuesFromLocalStorage();

    this.total$ = this.floatForm.valueChanges.pipe(
      startWith(this.calculateTotal()),
      map(() => this.calculateTotal())
    );

    this.FloatTotal$ = this.store.select(endOfDayCashUpSelectors.selectCashDrawerTotal);

    this.FloatTotal$.subscribe(total => {
      this.FloatTotal = total;
    });
  }

  private loadFormValuesFromLocalStorage(): void {
    const savedValues = localStorage.getItem('endOfDayCashDrawerFormValues');
    if (savedValues) {
      try {
        const parsedValues = JSON.parse(savedValues);
        this.floatForm.patchValue(parsedValues);
        console.log('Loaded cash drawer form values from local storage');
      } catch (error) {
        console.error('Error loading cash drawer form values from local storage:', error);
        // Clear invalid data
        localStorage.removeItem('endOfDayCashDrawerFormValues');
      }
    }
  }

  private saveFormValuesToLocalStorage(): void {
    const formValues = this.floatForm.value;
    localStorage.setItem('endOfDayCashDrawerFormValues', JSON.stringify(formValues));
    console.log('Saved cash drawer form values to local storage');
  }

  calculateTotal(): number {
    return (
      this.floatForm.value.Denomination100 * 100 +
      this.floatForm.value.Denomination50 * 50 +
      this.floatForm.value.Denomination20 * 20 +
      this.floatForm.value.Denomination10 * 10 +
      this.floatForm.value.Denomination5 * 5 +
      this.floatForm.value.Denomination2 * 2 +
      this.floatForm.value.Denomination1 * 1 +
      this.floatForm.value.Denomination50c * 0.5 +
      this.floatForm.value.Denomination20c * 0.2 +
      this.floatForm.value.Denomination10c * 0.1 +
      this.floatForm.value.Denomination5c * 0.05
    );
  }

  public onSubmit(): void {
    // Save form values to local storage before proceeding
    this.saveFormValuesToLocalStorage();

    const total = this.calculateTotal();
    this.store.dispatch(endOfDayCashDrawerActions.saveCashDrawerTotal({ total }));
    if (this.floatData) {
      this.store.dispatch(endOfDayCashDrawerActions.saveEndOfDayFloat({ payload: this.floatData }));
    }
    this.router.navigate(['/end-of-day-cash-up']); // Replace with actual next page route
  }

  public fieldValidate(control: AbstractControl): boolean {
    // TODO handle errors
    return control.invalid;
  }

  public openTill(): void {
    this.store.dispatch(receiptActions.executeBatch({
      payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
    }));
  }

  Cancel(){
    console.log("Cancel called...");
  }
}
