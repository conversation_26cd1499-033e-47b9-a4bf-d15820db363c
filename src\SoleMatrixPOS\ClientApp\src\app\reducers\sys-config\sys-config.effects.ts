import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { HouseKeepingClient } from 'src/app/pos-server.generated';
import * as SysConfigActions from './sys-config.actions';

@Injectable()
export class SysConfigEffects {
	constructor(
		private actions$: Actions,
		private houseKeepingClient: HouseKeepingClient
	) { }

	// Effect to read system configuration
	readSysConfig$ = createEffect(() =>
		this.actions$.pipe(
			ofType(SysConfigActions.readSysConfig),
			mergeMap(() =>
				this.houseKeepingClient.getSysControl().pipe(
					map(sysConfig =>
						SysConfigActions.readSysConfigSuccess({ sysConfig })
					),
					catchError(error =>
						of(SysConfigActions.readSysConfigFailure({ error }))
					)
				)
			)
		)
	);

	// Effect to update system configuration
	updateSysConfig$ = createEffect(() =>
		this.actions$.pipe(
			ofType(SysConfigActions.updateSysConfig),
			mergeMap(action =>
				this.houseKeepingClient.updateSysControl(action.updateSys).pipe(
					map(() => SysConfigActions.updateSysConfigSuccess()),
					catchError(error =>
						of(SysConfigActions.updateSysConfigFailure({ error }))
					)
				)
			)
		)
	);

	// Effect to fetch updated configuration after successful update
	updateSysConfigSuccess$ = createEffect(() =>
		this.actions$.pipe(
			ofType(SysConfigActions.updateSysConfigSuccess),
			map(() => SysConfigActions.readSysConfig())
		)
	);
}
