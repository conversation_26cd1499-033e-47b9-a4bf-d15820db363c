import { Component, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import { Payment, PaymentType } from '../payment.service';
import { CustomerClubDto } from 'src/app/pos-server.generated';
import * as CustomerClubActions from 'src/app/reducers/customer-club/club-search/customer-club.actions';
import * as CustomerClubSearchSelectors  from 'src/app/reducers/customer-club/club-search/customer-club.selectors'
import * as SysConfigSelectors from 'src/app/reducers/sys-config/sys-config.selectors';
import { payments } from 'src/app/reducers/gift-voucher/gift-voucher.selectors';
import { CustomerClubModalComponent } from 'src/app/customer-club/customer-club-modal/customer-club-modal.component';

@Component({
  selector: 'pos-customer-points-modal',
  templateUrl: './customer-points-modal.component.html',
  styleUrls: ['./customer-points-modal.component.scss']
})
export class CustomerPointsModalComponent implements OnInit {
  @Input() amountDue: number;
  @Input() type;
  @Input() selectedCustomer: CustomerClubDto;
  
  public form: FormGroup;
  public maxPointsValue: number = 0;
  public pointsError: string = null;
  public pointsValuePerDollar: number = 0;

  constructor(
    public activeModal: NgbActiveModal,
    private store: Store<AppState>,
    private formBuilder: FormBuilder,
    private modalService: NgbModal
  ) {
    // Initialize form in constructor
    this.form = this.formBuilder.group({
      Points: [0, [
        Validators.required,
        Validators.min(1),
        Validators.pattern(/^[0-9]*$/) // Only whole numbers
      ]]
    });

    // Add valueChanges listener to ensure points are valid
    this.form.get('Points').valueChanges.subscribe(value => {
      // Ensure it's a whole number (no decimal points)
      if (value && !Number.isInteger(Number(value))) {
        this.form.get('Points').setValue(Math.floor(Number(value)));
      }
    });
  }

  get points() { return this.form.get('Points'); }
  get amount() { return this.form.get('Amount'); }

  private updateCustomerPoints() {
    if (!this.selectedCustomer) return;

    const customerPoints = this.selectedCustomer.clientPoints || 0;
    this.maxPointsValue = customerPoints / this.pointsValuePerDollar;
    
    // Update validation with new customer points
    this.points.setValidators([
      Validators.required,
      Validators.min(1),
      Validators.max(customerPoints),
      Validators.pattern(/^[0-9]*$/)
    ]);
    this.points.updateValueAndValidity();
  }

  ngOnInit() {
    // First subscribe to points value from system config
    this.store.select(SysConfigSelectors.DollarPerPoints).subscribe(value => {
      this.pointsValuePerDollar = value;
      // Update points calculation if we already have a customer
      if (this.selectedCustomer) {
        this.updateCustomerPoints();
      }
    });

    // Then subscribe to customer changes
    this.store.select(CustomerClubSearchSelectors.selectedCustomerClubMember).subscribe(value => {
      this.selectedCustomer = value;
      if (this.selectedCustomer && this.pointsValuePerDollar) {
        this.updateCustomerPoints();
      }
    });

    // If no customer is selected, open the modal
    if (!this.selectedCustomer) {
      const modalRef = this.modalService.open(CustomerClubModalComponent, { size: 'xl', centered: true });
      modalRef.componentInstance.name = 'CustomerClubModal';
      modalRef.result.then((result) => {
        if (result) {
          // The customer selection will be handled by the store subscription above
          console.log('Customer selected from modal');
        } else {
          this.activeModal.dismiss('No customer selected');
        }
      }).catch((reason) => {
        this.activeModal.dismiss('Modal closed');
      });
    }
  }

  dismiss(reason: string) {
    this.activeModal.dismiss(reason);
  }

  apply() {
    if (!this.selectedCustomer) {
      this.pointsError = 'No customer selected. Please select a customer to redeem points.';
      return;
    }
    const pointsToRedeem = +this.points.value;
    const amountValue = pointsToRedeem / this.pointsValuePerDollar;
  
    if(this.form.valid) {
      // Create a copy of the customer with updated points
      const updatedCustomer = {
        ...this.selectedCustomer,
        clientPoints: (this.selectedCustomer.clientPoints || 0) - pointsToRedeem
      };
      
      // Dispatch an action to update the customer in the store
      this.store.dispatch(CustomerClubActions.updateCustomerPoints({ 
        customerId: this.selectedCustomer.clientCode, 
        points: updatedCustomer.clientPoints 
      }));
      
      this.activeModal.close({ 
        type: PaymentType.CustomerPoints, 
        amount: +amountValue,
        pointsRedeemed: pointsToRedeem,
        customer: updatedCustomer
      } as Payment & { pointsRedeemed: number, customer: CustomerClubDto });
    } else {
      this.form.markAsPristine();
    }
  }

  useRemainder() {
    if (!this.selectedCustomer) {
      return;
    }
    
    const customerPoints = this.selectedCustomer.clientPoints || 0;
    const pointsNeededForFullAmount = Math.floor(this.amountDue * this.pointsValuePerDollar);
    
    // Use either all points or just enough for the transaction, whichever is less
    const pointsToUse = Math.min(customerPoints, pointsNeededForFullAmount);
    
    this.points.setValue(pointsToUse);
  }

  calculateDollarValue(points: number): number {
    return points / this.pointsValuePerDollar;
  }

  public fieldValidate(control: AbstractControl): boolean {
    return control.invalid && (control.dirty || control.touched);
  }
}