import { Injectable } from '@angular/core';
import { Actions, Effect, ofType, createEffect } from '@ngrx/effects';
import * as customerAddActions from './customer-add.actions';

import { catchError, map, mergeMap, tap, debounceTime } from 'rxjs/operators';
import { EMPTY, Observable, of } from 'rxjs';
import { ClientAddClient } from '../../../pos-server.generated';

@Injectable()
export class CustomerAddEffects {

	// addMember$ = createEffect(() => this.actions$.pipe(
	// 	ofType(customerAddActions.createMember),
	// 	//tap(()=> console.log("Calling customerAddActions.createMember")),
	// 	mergeMap((action) => this.clientAddClient.addMember(action.payload)
	// 		.pipe(
	// 			map(x => customerAddActions.createMemberSuccess()),
	// 			catchError(err => {
	// 				return of({
	// 					type: customerAddActions.createMemberError,
	//         			error: err.message as string
	// 				}) as Observable<any>;
	// 			})
	// 		)

	// 	)));

	addMember$ = createEffect(() => this.actions$.pipe(
		ofType(customerAddActions.createMember),
		//tap(()=> console.log("Calling customerAddActions.createMember")),
		mergeMap((action) => this.clientAddClient.addMember(action.payload)
			.pipe(
				map(x => customerAddActions.createMemberSuccess({ result: x })),
				catchError(err => {
					return of(customerAddActions.createMemberError({ error: err }))
				})
			)

		)));

	checkBarcode$ = createEffect(() =>
		this.actions$.pipe(
			ofType(customerAddActions.checkBarcode),
			debounceTime(300),
			mergeMap(action =>
				this.clientAddClient.checkBarcodeExists(action.barcode).pipe(
					map(exists => customerAddActions.checkBarcodeSuccess({ exists })),
					catchError(error => of(customerAddActions.checkBarcodeError({ error: error.message })))
				)
			)
		)
	);

	constructor(private actions$: Actions, private clientAddClient: ClientAddClient) { }

}

