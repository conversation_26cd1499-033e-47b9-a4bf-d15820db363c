import { Action } from '@ngrx/store';
import { Location } from './location.model';

export const LOAD_LOCATIONS_REQUESTED: string = "[LOCATION] loadReq";
export const SWAP_STOCK: string = "[LOCATION] swap";
export const LOAD_LOCATIONS: string = "[LOCATION] load";
export const UNDO_TRANSFER: string = "[LOCATION] undo";

export const TOGGLE_SALES: string = "[LOCATION] toggleSales";

export const HIGHLIGHT_TRANSFER: string = "[LOCATION] highlight";

export class SwapStockAction implements Action {
    readonly type = SWAP_STOCK;

    constructor(
        public id: number,
        public from: string,
        public to: string,
        public name: string,
        public size: string,
        public qty: number
    ){}
}

/**
 * A corresponding remove-transfer action for the above
 */
export class UndoTransferAction implements Action {
    readonly type = UNDO_TRANSFER;

    constructor(
        public id: number
    ){}
}

/**
 * Request action, this is the preferred action for loading data
 * as it is caught by an effect that ensures successive actions
 * are triggered in the correct order
 */
export class LoadLocationsRequested implements Action {
    readonly type = LOAD_LOCATIONS_REQUESTED;
}

/**
 * An array of LOCATIONs can be given to this action and the store
 * will replace the immutableLOCATIONs state slice with it
 */
export class LoadLocationsAction implements Action {
    readonly type = LOAD_LOCATIONS;

    constructor(
        public locations: Location[]
    ){}
}

/**
 * Used for displaying sales in the transfer table
 */
export class ToggleSalesAction implements Action {
    readonly type = TOGGLE_SALES; 
}

/**
 * Used for highlighting a transfer in the transfer table
 */
export class HighlightStockAction implements Action {
    readonly type = HIGHLIGHT_TRANSFER;

    constructor(
        public id: number,
        public from: string,
        public to: string,
        public name: string,
        public size: string,
        public qty: number
    ){}   
}

export type LOCATIONActionType = 
      LoadLocationsRequested
    | SwapStockAction
    | LoadLocationsAction
    | UndoTransferAction
    | ToggleSalesAction
    | HighlightStockAction;