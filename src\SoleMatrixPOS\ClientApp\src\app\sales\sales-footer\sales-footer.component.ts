import { Component, EventEmitter, Input, OnInit, Output, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil, take, tap } from 'rxjs/operators';
import { CustomerClubModalComponent } from 'src/app/customer-club/customer-club-modal/customer-club-modal.component';
import { Transaction } from 'src/app/payment/payment.service';
import { CustomerClubDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import { clearStaffLogin } from 'src/app/reducers/staff/staff.actions';
import { UrlHistoryService } from 'src/app/url-history.service';
import * as sysSelectors from '../../reducers/sys-config/sys-config.selectors';
import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';
import * as cartSelectors from '../../reducers/sales/cart/cart.selectors';
import * as laybySelectors from '../../reducers/layby/layby.selectors';
import * as orderActions from '../../reducers/order-item/order.actions';
import * as transSelectors from '../../reducers/transaction/transaction.selectors';
import * as SaleNoteActions from '../../reducers/sale-note/sale-note.actions';
import * as saleNoteSelectors from '../../reducers/sale-note/sale-note.selectors';

import { SaleLaybyModalComponent } from '../sale-layby-modal/sale-layby-modal.component';
import Swal from 'sweetalert2';
import { CheckCancelSaleModalComponent } from '../../eftpos/check-cancel-sale-modal/check-cancel-sale-modal.component';
import { CartItem } from '../../reducers/sales/cart/cart.reducer';
@Component({
  selector: 'pos-sales-footer',
  templateUrl: './sales-footer.component.html',
  styleUrls: ['./sales-footer.component.scss']
})
export class SalesFooterComponent implements OnInit, OnDestroy {
  // Add new input property
  @Input() softCreditLimit: string = 'F';
  @Input() laybyActive: boolean = false;
	@Input() laybyAllowed: boolean = true;
  @Input() saleComment: string = '';

  selectedCustomerClubMember$: any;

	transNo$: Observable<number>
	transNo: number

	sysStatus: any;
	sysStatus$: Observable<any>;

	cart: CartItem[];
	cart$: Observable<CartItem[]>;

	cartTotal: number;
	cartTotal$: Observable<number>;

  private destroy$ = new Subject<void>();

  constructor(private store: Store<AppState>, private router: Router, private modalService: NgbModal, private urlHistory: UrlHistoryService) { }

  selectedCustomerClubMember: CustomerClubDto = null;

  laybyActive$: Observable<boolean>;

  @Output() processPayment: EventEmitter<void> = new EventEmitter();
  @Output() newLayby: EventEmitter<CustomerClubDto> = new EventEmitter();
  @Output() saleCommentChange = new EventEmitter<string>();

  @Input() transaction$: Observable<Transaction>;
  @Input() readyToProcess: boolean;

	transaction: Transaction;

  isExchangeMode$: Observable<boolean>;

  // In SalesFooterComponent
  subscribeToState() {
    this.laybyActive$ = this.store.select(laybySelectors.active);

    // Subscribe to laybyActive$ and update the boolean property
    this.laybyActive$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(isActive => {
      this.laybyActive = isActive;
      console.log('Layby active status:', isActive); // Debug log
    });
    this.selectedCustomerClubMember$ = this.store.select(customerClubSearchSelectors.selectedCustomerClubMember);
    this.selectedCustomerClubMember$.subscribe((s) => { this.selectedCustomerClubMember = s, console.log(this.selectedCustomerClubMember) });

	  this.transaction$.subscribe(transaction => this.transaction = transaction);

	  this.cartTotal$ = this.store.select(cartSelectors.total);
	  this.cartTotal$.subscribe((t) => {
		  this.cartTotal = t;
	  });

	  this.cart$ = this.store.select(cartSelectors.cart);
	  this.cart$.subscribe(
		  (s) => this.cart = s
	  );

	  this.sysStatus$ = this.store.select(sysSelectors.selectSysConfig);
	  this.sysStatus$.subscribe((sysconfig) => {
		  this.sysStatus = sysconfig
	  });

	  this.store.select(transSelectors.transNo)
		  .pipe(
			  takeUntil(this.destroy$),
			  tap(transNo => {
				  this.transNo = transNo;
				  console.log('Transaction number updated:', transNo);
			  })
		  )
		  .subscribe();
  }

  ngOnInit() {
    this.subscribeToState();
    console.log("Soft Credit Limit: ", this.softCreditLimit);
    console.log("Layby Active: ", this.laybyActive);

    this.isExchangeMode$ = this.store.select(cartSelectors.isExchangeMode);

    // Check if we came from exchange-out
    this.isExchangeMode$.pipe(take(1)).subscribe(isExchangeMode => {
      console.log("Is Exchange Mode:", isExchangeMode);
	});

    this.store.select(saleNoteSelectors.selectSaleNote)
      .pipe(takeUntil(this.destroy$))
      .subscribe(note => {
        this.saleComment = note;
      });
  }

  ngOnDestroy() {
    // Cleanup in ngOnDestroy
    this.destroy$.next();
    this.destroy$.complete();
  }

  goToLoginPage() {
    this.store.dispatch(clearStaffLogin());
    // this.router.navigateByUrl('/staffLogin');
  }

  launchCustomerClubModal() {
    const modalRef = this.modalService.open(CustomerClubModalComponent, { size: 'xl', centered: true });
    modalRef.componentInstance.name = 'CustomerClubModal';
    modalRef.result.then((result) => {
      if (result) {
        console.log('result from modal:', result);
      }
    }).catch(error => {
      console.log("Error occurred: ", error);
    });
  }

  processBtnClick() {
    console.log("?");
    this.store.dispatch(orderActions.setDeposit({ deposit: 0 }));
    this.processPayment.emit();
  }

  launchLayby() {
    if (!this.laybyAllowed) {
      Swal.fire({
        title: 'Layby not allowed',
        text: 'Layby is not allowed for this customer',
        type: 'error'
      });
    }
    else {
      this.isExchangeMode$.pipe(take(1)).subscribe(isExchangeMode => {
        if (isExchangeMode) {
          return;
        }
  
        var laybyModal = this.modalService.open(SaleLaybyModalComponent, { size: 'xl', centered: true });
        laybyModal.result.then(
          result => {
            this.newLayby.emit(result);
          }
        ).catch(() => { });
      });
    }
  }

  backBtnClick() {
	  console.log("Navigating backwards...");
	  console.log(this.sysStatus);
	  if (this.transaction.successfulEft) {
		  var modalRef = this.modalService.open(CheckCancelSaleModalComponent, { size: 'xl', centered: true });
		  modalRef.componentInstance.transaction = this.transaction;
		  modalRef.componentInstance.intEftProvider = this.sysStatus.integratedEFTProvider;
		  modalRef.componentInstance.cartTotal = this.cartTotal;
		  modalRef.componentInstance.transNo = this.transNo;
		  modalRef.componentInstance.cart = this.cart;

		  modalRef.result.then(result => {
			  if (result == true) {
				  // Do something if needed
			  }
		  })
	  }
	  else { this.router.navigateByUrl(this.urlHistory.previousUrl); }
  }

  async launchCommentModal() {
    const { value: comment } = await Swal.fire({
      title: 'Sale Comment',
      input: 'textarea',
      text: 'Enter your comment',
      inputValue: this.saleComment || '',
      showCancelButton: true,
      inputValidator: (value) => {
        if ((value && value.length > 49)) {
          return 'Comment must be less than 50 characters';
        }
        return null;
      }
    });

    if (comment !== undefined && comment.length > 0) {
      this.saleComment = comment;
      this.saleCommentChange.emit(comment);
      this.store.dispatch(SaleNoteActions.setSaleNote({ note: comment }));
    }
  }

  removeComment() {
    this.saleComment = '';
    this.saleCommentChange.emit('');
    this.store.dispatch(SaleNoteActions.clearSaleNote());
  }

}
