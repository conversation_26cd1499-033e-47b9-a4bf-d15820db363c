using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Tokens;
using SoleMatrixPOS.Identity.Models;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace SoleMatrixPOS.Identity.Interface
{
	public class RegisteredTillService
	{
		private readonly IHttpContextAccessor _contextAccessor;
		private static readonly string bearerPrefix = "Bearer ";
		private readonly TokenValidationParameters _tokenValidationParameters;
		private string lastError;
		public RegisteredTillService(IHttpContextAccessor contextAccessor, TokenValidationParameters tokenValidationParameters)
		{
			_contextAccessor = contextAccessor;
			_tokenValidationParameters = tokenValidationParameters;
		}

		public string LastError
		{
			get
			{
				return lastError;
			}
		}

		public async Task<RegisteredTill> GetContextualTillAsync()
		{
			try
			{
				// Extract the token
				// TODO: confirm that this is "secure" or "best practice"

				if (_contextAccessor.HttpContext.Request.Headers.Authorization.Count <= 0)
					throw new Exception("No authorization header provided");

				string hdrAuth = _contextAccessor.HttpContext.Request.Headers.Authorization[0];

				if (!hdrAuth.StartsWith(bearerPrefix))
					throw new Exception("Bearer prefix not given or was given in incorrect format");

				var jwtToken = hdrAuth.Substring(bearerPrefix.Length);

				// Parse it and validate it
				var hdlJwt = new JwtSecurityTokenHandler();
				JwtSecurityToken token = hdlJwt.ReadJwtToken(jwtToken);

				var validation = await hdlJwt.ValidateTokenAsync(jwtToken, _tokenValidationParameters);
				if (!validation.IsValid) throw new Exception($"Token was invalid: {validation.Exception.Message}");

				// Get the claims, populate a registered till model and return it
				Dictionary<string, string> claimDict = token.Claims.ToDictionary((v) => v.Type, (v) => v.Value);

				return new RegisteredTill()
				{
					Id = claimDict[ClaimTypes.NameIdentifier],
					StoreId = claimDict["StoreId"],
					TillId = claimDict["TillId"]
				};

			} catch (Exception e)
			{
				lastError = e.Message;
				return null;
			}

		}
	}
}
