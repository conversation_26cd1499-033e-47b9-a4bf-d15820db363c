using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using PostmarkDotNet;
using PostmarkDotNet.Model;
using SoleMatrixPOS.Domain.Models;
using System;
using System.IO;
using System.Threading.Tasks;

namespace SoleMatrixPOS.Email
{
	/// <summary>
	/// The Postmark Email Service authenticates using a key found in the configuration.
	/// <br></br>
	/// This could be from <strong>appsettings</strong> or <strong>env variables</strong>.
	/// <br</br>
	/// Note that it also uses templates that are managed / created on the Postmark web portal.
	/// </summary>

	class PostmarkEmailServiceConfiguration
	{
		// Sender domain for all messages
		public string SenderDomain { get; set; }

		// API key for Postmark (obtained from web portal)
		public string ServerToken { get; set; }

		// Receipt email template ID
		public string ReceiptTemplateAlias { get; set; }

		// Password reset email template ID
		public string PasswordResetTemplateAlias { get; set; }

		public string OrganisationName { get; set; }

	}

	public class PostmarkEmailService : IEmailService
	{
		private readonly PostmarkEmailServiceConfiguration _config;
		private readonly PostmarkClient _postmarkClient;
		private readonly string publicBaseUrl;

		public PostmarkEmailService(IConfiguration configuration)
		{
			publicBaseUrl = configuration.GetValue<string>("PublicBaseUrl");
			var postmarkConfig = configuration.GetRequiredSection("Postmark");
			_config = new PostmarkEmailServiceConfiguration()
			{
				SenderDomain = postmarkConfig["SenderDomain"],
				ServerToken = postmarkConfig["ServerToken"],
				OrganisationName = postmarkConfig["OrganisationName"],
				ReceiptTemplateAlias = postmarkConfig["ReceiptTemplateAlias"],
				PasswordResetTemplateAlias = postmarkConfig["PasswordResetTemplateAlias"]
			};

			_postmarkClient = new PostmarkClient(_config.ServerToken);

		}

		public async Task<EmailResult> SendReceiptEmailAsync(string to, DateTime date, string transactionNo, byte[] pdfReceiptBytes, string title)
		{
			TemplatedPostmarkMessage msg = new TemplatedPostmarkMessage()
			{
				From = $"receipts@{_config.SenderDomain}",
				To = to,
				TemplateAlias = _config.ReceiptTemplateAlias,
				MessageStream = "outbound",
				TemplateModel = new
				{
					transactionNo,
					transactionDate = date.ToString("dd/MM/YYYY"),
					organisation_name = title
				}
			};

			msg.AddAttachment(pdfReceiptBytes, $"receipt-{transactionNo}.pdf");

			var res = await _postmarkClient.SendEmailWithTemplateAsync(msg);

			if (res.Status == PostmarkStatus.Success)
				return new EmailResult
				{
					Succeeded = true,
					Reason = EmailResultReason.Ok,
					Message = $"Receipt <{transactionNo}> successfully sent via email to recipient <{to}>"
				};

			else
				return new EmailResult
				{
					Succeeded = false,
					Reason = EmailResultReason.ProviderError,
					Message = res.Message
				};

		}

		public async Task<EmailResult> SendPasswordResetEmailAsync(string to, string id, string token, int expiryMinutes)
		{
			var unixExpiry = new DateTimeOffset(DateTime.UtcNow).Add(TimeSpan.FromMinutes(expiryMinutes)).ToUnixTimeSeconds();
			string resetUrl = $"{publicBaseUrl}/password-reset?id={id}&token={token}&exp={unixExpiry}";

			TemplatedPostmarkMessage msg = new TemplatedPostmarkMessage()
			{
				From = $"accounts@{_config.SenderDomain}",
				To = to,
				TemplateAlias = _config.PasswordResetTemplateAlias,
				MessageStream = "outbound",
				TemplateModel = new
				{
					reset_url = resetUrl,
					expiry_minutes = expiryMinutes.ToString()
				}
			};

			var res = await _postmarkClient.SendEmailWithTemplateAsync(msg);

			if (res.Status == PostmarkStatus.Success)
				return new EmailResult
				{
					Succeeded = true,
					Reason = EmailResultReason.Ok,
					Message = $"Password reset successfully sent via email to recipient <{to}>"
				};

			else
				return new EmailResult
				{
					Succeeded = false,
					Reason = EmailResultReason.ProviderError,
					Message = res.Message
				};

		}
	}
}
