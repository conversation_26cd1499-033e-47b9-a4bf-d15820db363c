import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CheckingIncompleteSaleModalComponent } from './checking-incomplete-sale-modal.component';

describe('CheckingIncompleteSaleModalComponent', () => {
  let component: CheckingIncompleteSaleModalComponent;
  let fixture: ComponentFixture<CheckingIncompleteSaleModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ CheckingIncompleteSaleModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CheckingIncompleteSaleModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
