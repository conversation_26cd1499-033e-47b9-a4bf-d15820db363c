using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using SoleMatrixPOS.Application.Infrastructure;

namespace SoleMatrixPOS.Filters
{
    public class RequireStoreTimeFilterAttribute : ActionFilterAttribute
    {
        public RequireStoreTimeFilterAttribute()
        {
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            var storeTimeContext = context.HttpContext.RequestServices.GetService<StoreTimeContext>();
            if (storeTimeContext?.StoreLocalTime == default)
            {
                context.Result = new BadRequestResult();
            }
            else
            {
                base.OnActionExecuting(context);
            }
        }
    }
} 
