// transaction.actions.ts

import { createAction, props } from '@ngrx/store';
import { TransactionDto, TransactionResponseDto, TranspayDto } from 'src/app/pos-server.generated';

// Existing Actions
export const init = createAction("[Transaction] Init");
export const submitTransaction = createAction(
  "[Transaction] Submit Transaction",
  props<{ payload: TransactionDto }>()
);
export const transactionConfirmation = createAction(
  "[Transaction] Transaction Submitted",
  props<{ payload: TransactionResponseDto }>()
);

export const resetTransactionConfirmation = createAction(
	"[Transaction] Reset Transaction Confirmation"
);

// New Actions for GetTransactionNo
export const getTransactionNo = createAction(
  "[Transaction] Get Transaction No"
);

export const getTransactionNoSuccess = createAction(
  "[Transaction] Get Transaction No Success",
  props<{ payload: number }>()
);

export const getTransactionNoFailure = createAction(
  "[Transaction] Get Transaction No Failure",
  props<{ error: any }>()
);

// New Failure Action for SubmitTransaction
export const submitTransactionFailure = createAction(
  "[Transaction] Submit Transaction Failure",
  props<{ error: any }>()
);

// New Actions for Get TransPay by Transaction No
export const getTransPayByTransNo = createAction(
  "[Transaction] Get TransPay By TransNo",
  props<{ transNo: number }>()
);

export const getTransPayByTransNoSuccess = createAction(
  "[Transaction] Get TransPay By TransNo Success",
  props<{ payload: TranspayDto[] }>()
);

export const getTransPayByTransNoFailure = createAction(
  "[Transaction] Get TransPay By TransNo Failure",
  props<{ error: any }>()
);

export const clearTransPayData = createAction(
  '[Transaction] Clear TransPay Data'
);
