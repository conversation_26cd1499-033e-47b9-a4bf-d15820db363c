using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using SoleMatrixPOS.Domain.Identity.Interface;
using SoleMatrixPOS.Domain.Identity.Models;
using SoleMatrixPOS.Identity.Models;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SoleMatrixPOS.Domain.Identity.Service
{
	public class TokenService : ITokenService
	{
		private readonly IConfiguration _configuration;
		private readonly HashAlgorithm _hasher;
		private readonly UserManager<RegisteredTill> _tillManager;
		public TokenService(IConfiguration configuration, UserManager<RegisteredTill> tillManager)
		{
			_configuration = configuration;
			_tillManager = tillManager;
			_hasher = HashAlgorithm.Create("SHA-256");
		}

		public TokenDto GenerateRefreshToken(RegisteredTill till)
		{
			var claims = new List<Claim>() { new Claim(ClaimTypes.NameIdentifier, till.Id) };
			var signingKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration.GetValue<string>("JWT:Key")));
			DateTime expiry = DateTime.UtcNow.AddDays(_configuration.GetValue<int>("Jwt:RefreshExpiryDays"));
			var token = new JwtSecurityToken(
				issuer: _configuration.GetValue<string>("Jwt:Issuer"),
				audience: _configuration.GetValue<string>("Jwt:Issuer"),
				claims: claims,
				expires: expiry,
				signingCredentials: new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256)
			);

			return new TokenDto(
				new JwtSecurityTokenHandler().WriteToken(token),
				expiry
			);
		}

		public TokenDto GenerateToken(RegisteredTill till, IList<Claim> claims)
		{
			// Add the username to the claims (creating new collection)
			List<Claim> claimsMutated = claims.ToList(); // O(n)

			claimsMutated.Add(new Claim(ClaimTypes.NameIdentifier, till.Id));

			if(till.TillId != null && till.StoreId != null)
			{
				claimsMutated.Add(new Claim("TillId", till.TillId));
				claimsMutated.Add(new Claim("StoreId", till.StoreId));
			}

			foreach(string role in _tillManager.GetRolesAsync(till).GetAwaiter().GetResult())
			{
				claimsMutated.Add(new Claim(ClaimTypes.Role, role));
			}

			var signingKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration.GetValue<string>("JWT:Key")));
			DateTime expiry = DateTime.UtcNow.AddMinutes(_configuration.GetValue<int>("Jwt:ExpiryMinutes"));
			var token = new JwtSecurityToken(
				issuer: _configuration.GetValue<string>("Jwt:Issuer"),
				audience: _configuration.GetValue<string>("Jwt:Issuer"),
				expires: expiry,
				claims: claimsMutated,
				signingCredentials: new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256)
			);

			return new TokenDto(
				new JwtSecurityTokenHandler().WriteToken(token),
				expiry
			);
		}

		public string HashRefreshToken(string payload)
		{
			string toHash = (payload + _configuration.GetValue<string>("Jwt:Salt"));
			byte[] hashBytes = _hasher.ComputeHash(Encoding.ASCII.GetBytes(toHash));
			return Convert.ToBase64String(hashBytes);
		}
	}
}
