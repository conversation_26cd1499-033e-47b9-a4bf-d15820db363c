.text {
    cursor: pointer;
}

.text:hover {
    background-color: rgb(125, 171, 211);
}

.editor {
    position: relative;
    
    input {
        width: 300px;  // Fixed width instead of 100%
        padding: 8px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        line-height: 1.5;
        
        &:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
    }
  
    .mt-2 {
        margin-top: 0.5rem;
    }
}

.table {
    table-layout: auto;  // Changed from fixed to auto
    
    td {
        word-wrap: break-word;
        min-width: auto;  // Changed from 200px to auto
    }
}