import { createReducer, on } from '@ngrx/store';
import { CustomerMateDto } from '../../pos-server.generated';
import * as AccountPaymentActions from './account-payment.actions';
import { CustomerPayment } from '../../account-payment/customer-modal/customer-modal.component';

export interface AccountPaymentState {
  customers: CustomerMateDto[];
  allCustomers: CustomerMateDto[];
  filteredCustomers: CustomerMateDto[];
  selectedCustomer: CustomerPayment | null;
  loading: boolean;
  isLoading: boolean;
  error: any;
  ctransInserted: boolean;
  searchParams?: {
    field: string;
    term: string;
  };
}

export const initialState: AccountPaymentState = {
  customers: [],
  allCustomers: [],
  filteredCustomers: [],
  selectedCustomer: null,
  loading: false,
  isLoading: false,
  error: null,
  ctransInserted: false,
  searchParams: {
    field: 'Phone',
    term: ''
  }
};

export const accountPaymentReducer = createReducer(
  initialState,
  
  on(AccountPaymentActions.init, state => ({
    ...state,
    loading: false,
    error: null
  })),

  on(AccountPaymentActions.searchCustomers, (state, { payload }) => ({
    ...state,
    loading: true,
    error: null,
    searchParams: {
      field: payload.searchField,
      term: payload.searchString
    }
  })),

  on(AccountPaymentActions.searchCustomersSuccess, (state, { customers }) => ({
    ...state,
    customers,
    filteredCustomers: customers,
    loading: false,
    error: null
  })),

  on(AccountPaymentActions.searchCustomersFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  on(AccountPaymentActions.storeAllCustomers, (state, { payload }) => ({
    ...state,
    allCustomers: payload
  })),

  on(AccountPaymentActions.storeSelectedCustomer, (state, { payload }) => ({
    ...state,
    selectedCustomer: payload
  })),

  on(AccountPaymentActions.insertCtransConfirmed, (state) => ({
    ...state,
    ctransInserted: true
  }))
);

export const dailyReducer = accountPaymentReducer;

// Selectors
export const selectCustomers = (state: AccountPaymentState) => state.customers;
export const selectLoading = (state: AccountPaymentState) => state.loading;
export const selectError = (state: AccountPaymentState) => state.error;
export const selectSearchParams = (state: AccountPaymentState) => state.searchParams;