import {Injectable} from '@angular/core';
import {Actions, createEffect, ofType} from '@ngrx/effects';
import * as laybyPaymentActions from './layby-payment.actions';
import {catchError, map, mergeMap, withLatestFrom, tap} from 'rxjs/operators';
import {EMPTY, of} from 'rxjs';
import {LaybyPaymentClient, LaybySearchClient, LaybyPaymentWithTransactionDto, MakeLaybyPaymentResultDto} from '../../pos-server.generated';
import {Store} from '@ngrx/store';
import {AppState} from '../index';
import * as laybyPaymentSelectors from './layby-payment.selectors';

@Injectable()
export class LaybyPaymentEffects {
	constructor(
		private store: Store<AppState>,
		private actions$: Actions,
		private laybySearchClient: LaybySearchClient,
		private laybyPaymentClient: LaybyPaymentClient) {
	}

	getLaybyLines$ = createEffect(() => this.actions$
	.pipe(
		ofType(laybyPaymentActions.setLayby),
		mergeMap((action) => this.laybySearchClient.getLaybyLines(action.layby.laybyCode)
			.pipe(
				map(lines => laybyPaymentActions.getLaybyLinesResponse({lines: lines}),
					catchError(() => EMPTY)
				)
			))));

			submitPayments$ = createEffect(() => this.actions$.pipe(
				ofType(laybyPaymentActions.submitPayments),
				map(action => action.payload as LaybyPaymentWithTransactionDto), // Ensure type if not already strictly typed
				mergeMap((payload: LaybyPaymentWithTransactionDto) => this.laybyPaymentClient.submitPayments(payload) // Assuming submitPayments takes LaybyPaymentWithTransactionDto
					.pipe(
						map((response: MakeLaybyPaymentResultDto) => // <--- API client returns this
							laybyPaymentActions.submitPaymentsSuccess({ payload: response }) // <--- DISPATCH SUCCESS WITH PAYLOAD
						),
						catchError(error => {
							console.error('Submit Layby Payments API Error:', error);
							const errorMessage = error.message || (error.error && error.error.message) || (error.error && error.error.detail) || 'Unknown error during layby payment submission.';
							return of(laybyPaymentActions.submitPaymentsFailure({ error: errorMessage })); // <--- DISPATCH FAILURE
						})
					)
				)
			));

	cancelLayby$ = createEffect(() => this.actions$
		.pipe(
			ofType(laybyPaymentActions.laybyRefund),
				mergeMap((action) => this.laybyPaymentClient.cancelLayby(action.payload)
					.pipe(
						map(() => laybyPaymentActions.submitRefundCompleted(),
							catchError(() => EMPTY)
						)
					))));
}

