import { Component, EventEmitter, OnInit, Output, Input } from '@angular/core';
import { Observable } from 'rxjs';
import { Store } from '@ngrx/store';
import { AppState } from '../../reducers';
import * as stockSearchSelectors from '../../reducers/stock-search/stock-search.selectors';
import * as stockSearchActions from '../../reducers/stock-search/stock-search.actions';
import { StockItemDto, StockSearchRequestDto } from '../../pos-server.generated';

@Component({
	selector: 'pos-stock-enquiry-search-container',
	templateUrl: './stock-enquiry-search-container.component.html',
	styleUrls: ['./stock-enquiry-search-container.component.scss']
})
export class StockEnquirySearchContainerComponent implements OnInit {
	@Input() initialSearchValue: string;
	styleItems$: Observable<StockItemDto[]>;
	searchOptions$: Observable<StockSearchRequestDto>;
	isLoading$: Observable<boolean>;

	@Output() selectedItemChanged = new EventEmitter<StockItemDto>();

	constructor(private store: Store<AppState>) {

	}

	ngOnInit() {
		this.styleItems$ = this.store.select(stockSearchSelectors.searchedItems);
		this.searchOptions$ = this.store.select(stockSearchSelectors.searchOptions);
		this.isLoading$ = this.store.select(stockSearchSelectors.isLoading);

		this.store.dispatch(stockSearchActions.clearSearchItems());

		if (this.initialSearchValue) {
			// Trigger initial search with the provided value
			this.onSearchChanged({
				searchString: this.initialSearchValue,
				first: 20,
				skip: 0
			} as StockSearchRequestDto);
		}
	}

	onSearchChanged(searchRequest: StockSearchRequestDto) {
		console.log('Stock Enquiry search request:', searchRequest);
		// Use the specific stock enquiry action
		this.store.dispatch(stockSearchActions.searchStockEnquiry({ searchParams: searchRequest }));
	}

	selectItem(item: StockItemDto) {
		this.selectedItemChanged.emit(item);
	}

	searchMore() {
		// Use the specific stock enquiry action for loading more items
		this.store.dispatch(stockSearchActions.searchStockEnquiryMore());
	}
}
