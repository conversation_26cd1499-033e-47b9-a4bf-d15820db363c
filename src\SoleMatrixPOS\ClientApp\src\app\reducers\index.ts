import {
	ActionReducer,
	ActionReducerMap,
	createFeatureSelector,
	createSelector,
	MetaReducer,
} from "@ngrx/store";

import { environment } from "../../environments/environment";
import * as fromStaff from "./staff/staff.reducer";
import * as fromStockSearch from "./stock-search/stock-search.reducer";
import * as fromLaybySearch from "./layby-search/layby-search.reducer";
import * as fromLaybyRefund from "./layby-refund/layby-refund.reducer";
import * as fromCustomerClubSearch from "./customer-club/club-search/customer-club.reducer";
import * as fromCustomerAdd from "./customer-club/customer-add/customer-add.reducer";
import * as fromCustomerUpdate from './customer-club/customer-update/customer-update.reducer';
import * as fromNoteSearch from "./customer-club/notes/notes.reducer";
import * as fromSendStock from "./send-stock/send-stock.reducer";
import * as fromReceiveItemScan from "./receive-stock/receive-item-scan/receive-item-scan.reducer";
import * as fromReceiveDocketScan from "./receive-stock/receive-docket-scan/receive-docket-scan.reducer";
import * as fromHistorySearch from "./customer-club/history/history.reducer";
import * as fromCart from "./sales/cart/cart.reducer";
import * as fromPayments from "./sales/payment/payment.reducer";
import * as fromTransaction from "./transaction/transaction.reducer";
import * as fromEndFloat from "./end-of-day/end-of-day-float/end-of-day-float.reducer";
import * as fromEndCashDrawer from "./end-of-day/end-of-day-cash-drawer/end-of-day-cash-up.reducer";
import * as fromEndOfDay from "./end-of-day/end-of-day-cash-up/end-of-day.reducer";
import * as fromTransRef from "./transref/transref.reducer";
import * as fromLayby from "./layby/layby.reducer";
import * as fromLaybyPayment from "./layby-payment/layby-payment.reducer";
import * as fromStockEnquiry from "./stock-enquiry/stock-enquiry.reducer";
import * as OpenTill from "./open-till/open-till.reducers";
import * as Daily from "./daily/daily.reducers";
import * as AccountPayment from "./account-payments/account-payment.reducers";
import * as fromHouseKeeping from "./house-keeping/mLogin.reducers";
import * as fromReceipt from "./house-keeping/updateReceipt/receipt.reducers";
import * as ReceiptPrinting from "./receipt-printing/receipt.reducers";
import * as fromStocktakeEntry from "./stocketake-entry/itemCart/itemCart.reducers";
import * as fromWriteTransaction from "./stocketake-entry/write-transaction/write-transact.reducers";
import * as fromDatFileRead from "./stocketake-entry/read-dat-file/read-dat-file.reducers";
import * as fromPrintInvalid from "./stocketake-entry/print/print-reducers";
import * as fromGiftVoucher from "./gift-voucher/gift-voucher.reducers";
import * as fromStoreInfo from "./store-info/store-info.reducers";
import * as fromCustomerOrder from "./order-item/order.reducers";
import * as fromOrderItemSearch from "./order-item-search/order-item-search.reducer";
import * as fromQuoteItemSearch from "./quote-item-search/quote-item-search.reducer";
import * as fromQuoteItem from "./quote-item/quote.reducers";
import * as fromSysConfig from "./sys-config/sys-config.reducers";
import * as suspendSaleSelectors from "./suspend-sale/suspend-sale.reducers";
import * as fromGiftVoucherPayment from "./voucher-pay/voucher-pay.reducers";
import * as fromPINAuth from './pin-auth/pin-auth.reducers';
import * as fromSaleNote from './sale-note/sale-note.reducer';

import { AppEffects } from "./app/app.effects";
import { StaffEffects } from "./staff/staff.effects";
import { StockSearchEffects } from "./stock-search/stock-search.effects";
import { CustomerClubEffects } from "./customer-club/club-search/customer-club.effects";
import { CustomerAddEffects } from "./customer-club/customer-add/customer-add.effects";
import { NotesEffects } from "./customer-club/notes/notes.effects";
import { SendStockEffects } from "./send-stock/send-stock.effects";
import { HistoryEffects } from "./customer-club/history/history.effects";
import { CartEffects } from "./sales/cart/cart.effects";
import { PaymentEffects } from "./sales/payment/payment.effects";
import { ReceiveDocketScanEffects } from "./receive-stock/receive-docket-scan/receive-docket-scan.effects";
import { ReceiveItemScanEffects } from "./receive-stock/receive-item-scan/receive-item-scan.effects";
import { TransactionEffects } from "./transaction/transaction.effects";
import { LaybyEffects } from "./layby/layby.effects";
import { LaybySearchEffects } from "./layby-search/layby-search.effects";
import { LaybyRefundEffects } from "./layby-refund/layby-refund.effects";
import { LaybyPaymentEffects } from "./layby-payment/layby-payment.effects";
import { StockEnquiryEffect } from "./stock-enquiry/stock-enquiry.effects";
import { OpenTillEffects } from "./open-till/open-till.effects";
import { DailyEffects } from "./daily/daily.effects";
import { AccountPaymentEffects } from "./account-payments/account-payment.effects";
import { ManagerEffects } from "./house-keeping/mLogin.effects";
import { ReceiptEffects } from "./house-keeping/updateReceipt/receipt.effects";
import { ReceiptPrintingEffects } from "./receipt-printing/receipt.effects";
import { StocktakeEntryEffect } from "./stocketake-entry/itemCart/itemCart.effects";
import { WriteTransactionEffect } from "./stocketake-entry/write-transaction/write-transact.effects";
import { ReadDatFileEffect } from "./stocketake-entry/read-dat-file/read-dat-file.effects";
import { PrintInvalidEffects } from "./stocketake-entry/print/print-effects";
import { GiftVoucherEffects } from "./gift-voucher/gift-voucher.effects";
import { StoreEffect } from "./store-info/store-info.effects";
import { EndOfDayEffects } from "./end-of-day/end-of-day-cash-up/end-of-day.effects";
import { TransrefEffects } from "./transref/transref.effects";
import { OrderEffects } from "./order-item/order.effects";
import { OrderSearchEffects } from "./order-item-search/order-item-search.effects";
import { QuoteEffects } from "./quote-item/quote.effects";
import { QuoteSearchEffects } from "./quote-item-search/quote-item-search.effects";
import { SysConfigEffects } from "./sys-config/sys-config.effects";
import { SuspendSaleEffects } from "./suspend-sale/suspend-sale.effects";
import { CustomerUpdateEffects } from './customer-club/customer-update/customer-update.effects';
import { VoucherPaymentEffects } from './voucher-pay/voucher-pay.effects';
import { PINAuthEffects } from './pin-auth/pin-auth.effects';

export interface AppState {
	staff: fromStaff.StaffState;
	suspendSale: suspendSaleSelectors.SuspendSaleState;
	stockSearch: fromStockSearch.StockSearchState;
	laybySearch: fromLaybySearch.LaybySearchState;
	customerOrder: fromCustomerOrder.OrderState;
	customerOrderSearch: fromOrderItemSearch.OrderSearchState;
	customerQuoteSearch: fromQuoteItemSearch.QuoteSearchState;
	laybyPayment: fromLaybyPayment.LaybyPaymentState;
	laybyRefund: fromLaybyRefund.LaybyRefundState;
	customerClubSearch: fromCustomerClubSearch.CustomerClubSearchState;
	customerAdd: fromCustomerAdd.CustomerClubAddMemberState;
	customerUpdate: fromCustomerUpdate.CustomerClubUpdateMemberState;
	noteSearch: fromNoteSearch.NoteSearchState;
	sendStock: fromSendStock.SendStockState;
	transaction: fromTransaction.TransactionState;
	endFloat: fromEndFloat.EndFloatState;
	endCashDrawer: fromEndCashDrawer.EndOfDayState;
	endOfDay: fromEndOfDay.EndOfDayState;
	transRef: fromTransRef.TransrefState;
	receiveDocketScan: fromReceiveDocketScan.ReceiveDocketScanState;
	receiveItemScan: fromReceiveItemScan.ReceiveItemScanState;
	historySearch: fromHistorySearch.HistorySearchState;
	cart: fromCart.CartState;
	payment: fromPayments.PaymentState;
	layby: fromLayby.LaybyState;
	stockEnquiry: fromStockEnquiry.StockEnquiryState;
	opentill: OpenTill.TransactionState;
	daily: Daily.DailyState;
	accountPayment: AccountPayment.AccountPaymentState;
	manager: fromHouseKeeping.ManagerState;
	receipt: fromReceipt.UpdateReceiptState;
	receiptPrinting: ReceiptPrinting.ReceiptState;
	stocktakeCart: fromStocktakeEntry.StockCartItemState;
	writeTransact: fromWriteTransaction.WriteTransactionState;
	readDatFile: fromDatFileRead.DatFileCartItemState;
	printInvalid: fromPrintInvalid.PrintInvalidState;
	giftVoucher: fromGiftVoucher.GiftVoucherState;
	store: fromStoreInfo.StoreState;
	quote: fromQuoteItem.QuoteState;
	sysConfig: fromSysConfig.SysConfigState;
	giftVoucherPayment: fromGiftVoucherPayment.GiftVoucherPaymentState;
	pinAuth: fromPINAuth.PINAuthState;
	saleNote: fromSaleNote.SaleNoteState;
}

export const reducers: ActionReducerMap<AppState> = {
	staff: fromStaff.reducer,
	suspendSale: suspendSaleSelectors.reducer,
	stockSearch: fromStockSearch.reducer,
	customerOrder: fromCustomerOrder.orderReducer,
	customerOrderSearch: fromOrderItemSearch.orderSearchReducer,
	laybySearch: fromLaybySearch.reducer,
	laybyPayment: fromLaybyPayment.reducer,
	laybyRefund: fromLaybyRefund.reducer,
	customerClubSearch: fromCustomerClubSearch.reducer,
	customerAdd: fromCustomerAdd.reducer,
	customerUpdate: fromCustomerUpdate.reducer,
	transRef: fromTransRef.transRefReducer,
	endFloat: fromEndFloat.endFloatReducer,
	endCashDrawer: fromEndCashDrawer.endOfDayReducer,
	noteSearch: fromNoteSearch.reducer,
	sendStock: fromSendStock.sendStockReducer,
	receiveDocketScan: fromReceiveDocketScan.receiveDocketScanReducer,
	receiveItemScan: fromReceiveItemScan.receiveItemScanReducer,
	transaction: fromTransaction.transactionReducer,
	endOfDay: fromEndOfDay.endOfDayReducer,
	historySearch: fromHistorySearch.reducer,
	cart: fromCart.reducer,
	payment: fromPayments.reducer,
	layby: fromLayby.laybyReducer,
	stockEnquiry: fromStockEnquiry.stockEnquiryReducer,
	opentill: OpenTill.transactionReducer,
	daily: Daily.dailyReducer,
	accountPayment: AccountPayment.dailyReducer,
	manager: fromHouseKeeping.managerReducer,
	receipt: fromReceipt.UpdateReceiptReducer,
	receiptPrinting: ReceiptPrinting.receiptReducer,
	stocktakeCart: fromStocktakeEntry.StocktakeReducer,
	writeTransact: fromWriteTransaction.TransactReducer,
	readDatFile: fromDatFileRead.DatFileItemReducer,
	printInvalid: fromPrintInvalid.printInvalidReducer,
	giftVoucher: fromGiftVoucher.GiftVoucherReducer,
	giftVoucherPayment: fromGiftVoucherPayment.GiftVoucherPaymentReducer,
	store: fromStoreInfo.StoreReducer,
	quote: fromQuoteItem.quoteReducer,
	customerQuoteSearch: fromQuoteItemSearch.quoteSearchReducer,
	sysConfig: fromSysConfig.sysConfigReducer,
	pinAuth: fromPINAuth.pinAuthReducer,
	saleNote: fromSaleNote.saleNoteReducer
};

export const metaReducers: MetaReducer<AppState>[] = !environment.production
	? []
	: [];

export const AllEffects = [
	AppEffects,
	StaffEffects,
	StockSearchEffects,
	StockEnquiryEffect,
	LaybySearchEffects,
	LaybyPaymentEffects,
	LaybyRefundEffects,
	CustomerClubEffects,
	CustomerAddEffects,
	NotesEffects,
	SendStockEffects,
	ReceiveDocketScanEffects,
	ReceiveItemScanEffects,
	HistoryEffects,
	CartEffects,
	LaybyEffects,
	EndOfDayEffects,
	TransrefEffects,
	PaymentEffects,
	TransactionEffects,
	StockEnquiryEffect,
	OpenTillEffects,
	DailyEffects,
	AccountPaymentEffects,
	ManagerEffects,
	ReceiptEffects,
	ReceiptPrintingEffects,
	StocktakeEntryEffect,
	WriteTransactionEffect,
	ReadDatFileEffect,
	PrintInvalidEffects,
	OrderEffects,
	GiftVoucherEffects,
	VoucherPaymentEffects,
	StoreEffect,
	OrderSearchEffects,
	QuoteEffects,
	QuoteSearchEffects,
	CustomerUpdateEffects,
	SysConfigEffects,
	SuspendSaleEffects,
	PINAuthEffects
];
