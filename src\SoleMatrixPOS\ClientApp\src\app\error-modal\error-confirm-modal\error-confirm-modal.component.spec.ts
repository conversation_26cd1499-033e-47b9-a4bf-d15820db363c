import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ErrorConfirmModalComponent } from './error-confirm-modal.component';

describe('ErrorConfirmModalComponent', () => {
  let component: ErrorConfirmModalComponent;
  let fixture: ComponentFixture<ErrorConfirmModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ErrorConfirmModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ErrorConfirmModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
