import { AppState } from '../../index';
import { createSelector } from '@ngrx/store';

export const select = (state: AppState) => state.noteSearch;
export const searchedNotes = createSelector(select, (s)=> s.notes);
export const searchOptions = createSelector(select, (s)=> s.options);
export const deleteResult = createSelector(select, (s)=> s.result);
export const addResult = createSelector(select, (s)=> s.result);