import {Component, OnInit} from '@angular/core'
import { Router } from '@angular/router';
import { AuthContextProvider } from '../core/context/auth.context';

@Component({
    selector: "app-login",
    templateUrl: "./login.component.html",
    styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
    
    constructor(private authContextProvider: AuthContextProvider, private router: Router){}


    ngOnInit(): void {
        this.authContextProvider.getAuthContext().subscribe(authContext => {
            if(authContext.isAuthenticated){
                this.router.navigateByUrl('/staff-login')
            }
        })
    }
}