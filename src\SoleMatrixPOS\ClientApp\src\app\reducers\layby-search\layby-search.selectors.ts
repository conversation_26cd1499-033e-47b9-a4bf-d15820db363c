import { AppState } from '../index';
import { createSelector } from '@ngrx/store';

export const select = (state: AppState) => state.laybySearch;

export const searchedLaybys = createSelector(select, (s) => s.results);
export const searchQuery = createSelector(select, (s) => s.query);
export const searchedItemsCount = createSelector(searchedLaybys, (s) => s.length);
export const isLoading = createSelector(select, (s) => s.isLoading);
export const selectedLayby = createSelector(select, (s) => s.selectedLayby);

// New selector for layby lines
export const searchedLaybyLines = createSelector(select, (s) => s.laybyLines);
