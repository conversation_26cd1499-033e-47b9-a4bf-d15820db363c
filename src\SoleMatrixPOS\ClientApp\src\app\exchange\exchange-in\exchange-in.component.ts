import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Actions } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { StockItemDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import { ReasonPromptResult, ReturnReasonPromptComponent } from 'src/app/returns/return-reason-prompt/return-reason-prompt.component';
import { UrlHistoryService } from 'src/app/url-history.service';

import * as cartActions from '../../reducers/sales/cart/cart.actions';
import * as cartSelectors from '../../reducers/sales/cart/cart.selectors';
import * as transActions from '../../reducers/transaction/transaction.actions';

@Component({
  selector: 'pos-exchange-in',
  templateUrl: './exchange-in.component.html',
  styleUrls: ['./exchange-in.component.scss']
})
export class ExchangeInComponent implements OnInit {
  savedReason: string;

  constructor(private router: Router, private store: Store<AppState>, private modalService: NgbModal, private actions$: Actions, private urlHistory: UrlHistoryService) { }

  ngOnInit() {
    this.store.dispatch(cartActions.init());
  }

  itemLookup(item: StockItemDto) {
    this.attemptAddCartItem(item);
  }

  attemptAddCartItem(item: StockItemDto) {
    if (this.savedReason == null) {

      var reasonModal = this.modalService.open(ReturnReasonPromptComponent, { size: 'xl', centered: true });

      reasonModal.result.then(
        (res: ReasonPromptResult) => {
          if (res.applyToAll) this.savedReason = res.reason;
          this.store.dispatch(cartActions.addReason({ barcode: item.barcode, reason: res.reason }));
          // Finally, add the item to the cart
          this.store.dispatch(cartActions.addItem({ stockItem: item }));
        }
      ).catch((reason) => {
        console.log("Return reason not obtained: " + reason);
        return;
      });
    }

    else {
      this.store.dispatch(cartActions.addReason({ barcode: item.barcode, reason: this.savedReason }));
      // Finally, add the item to the cart
      this.store.dispatch(cartActions.addItem({ stockItem: item }));
    }
  }

  attemptNext() {
    // Do some logic, tbd
    // Negate cart
    this.store.dispatch(cartActions.negateCart());

    this.router.navigateByUrl("exchange/out");
  }

}
