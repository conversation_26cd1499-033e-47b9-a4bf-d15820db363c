using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Transaction;
using SoleMatrixPOS.Application.Transaction.Commands;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class TransRefController : Controller
	{
		private readonly IMediator _mediator;

		public TransRefController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[HttpPut]
		public async Task<int> AddTransRef([FromBody] TransrefDto transrefDto)
		{
			return await _mediator.Send(new SubmitTransRefCommand(transrefDto)); //TODO need to return some kind of success or failure
		}

	}

}
