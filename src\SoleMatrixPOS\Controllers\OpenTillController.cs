using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.OpenTill;
using SoleMatrixPOS.Application.OpenTill.Commands;
namespace SoleMatrixPOS.Controllers

{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class OpenTillController : Controller
	{
		private readonly IMediator _mediator;

		public OpenTillController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[HttpPut]
		public async Task<IActionResult> AddTransaction([FromBody] OpenTillTransactionDto transactionDto)
		{
			await _mediator.Send(new CreateOpenTillTransactionCommand(transactionDto));
			return Ok(); //TODO need to return some kind of success or failure
		}

		[Route("PettyCash")]
		[HttpPut]
		public async Task<IActionResult> AddPettyCashTransaction([FromBody] PettyCashTransactionDto transactionDto)
		{
			await _mediator.Send(new CreatePettyCashTransactionCommand(transactionDto));
			return Ok(); //TODO need to return some kind of success or failure
		}

	}

}
