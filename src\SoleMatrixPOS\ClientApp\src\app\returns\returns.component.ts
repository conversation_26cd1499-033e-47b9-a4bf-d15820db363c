import { Component, ElementRef, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { StockTableDefaultFields, StockTableFieldsEnabled, TableItemFromDto } from '../generic-stock-table/stock-table-item/stock-table-item';
import { CartItemDto, StockItemDto, StockTableItemDto } from '../pos-server.generated';
import { StockSearchModalComponent } from '../stock-search/stock-search-modal/stock-search-modal.component';

import * as cartActions from '../reducers/sales/cart/cart.actions';
import * as cartSelectors from '../reducers/sales/cart/cart.selectors';
import * as transActions from '../reducers/transaction/transaction.actions';

import { Store } from '@ngrx/store';
import { AppState } from '../reducers';
import { Observable, Subscription } from 'rxjs';
import { timeStamp } from 'console';
import { Actions, ofType } from '@ngrx/effects';
import Swal, { SweetAlertResult } from 'sweetalert2';
import { ReasonPromptResult, ReturnReasonPromptComponent } from './return-reason-prompt/return-reason-prompt.component';
import { CartItem } from '../reducers/sales/cart/cart.reducer';
import { UrlHistoryService } from '../url-history.service';
import { Router } from '@angular/router';
import { CreateErrorModal } from '../error-modal/error-modal.component';
@Component({
  selector: 'pos-returns',
  templateUrl: './returns.component.html',
  styleUrls: ['./returns.component.scss']
})
export class ReturnsComponent implements OnInit {
  readyToProcess: boolean = false;
  
  constructor(private router: Router, private store: Store<AppState>, private modalService: NgbModal, private actions$: Actions, private urlHistory: UrlHistoryService) { }

  cart$: Observable<CartItem[]>;

  cart: CartItem[];

  reasons$: Observable<Map<string, string[]>>;

  reasons: Map<string, string[]>;

  savedReason: string = null;

  totalValueOfItems: number = 0;

  noItems: number = 0;

  @ViewChild('barcodeInput', {static: false}) barcodeInputChild: ElementRef;

  subscribeToState(){
    this.cart$ = this.store.select(cartSelectors.cart);
    this.reasons$ = this.store.select(cartSelectors.reasons);
    this.reasons$.subscribe(v => {this.reasons = v; console.log(v);});

    this.cart$.subscribe(cart => this.cart = cart);
    
    this.store.select(cartSelectors.noItems).subscribe(n => this.readyToProcess = (n < 0));

  }

  getReasons(item: StockItemDto){
    let v = this.reasons.get(item.barcode);
    return v == undefined ? [] : v;
  }

  itemLookup(item: StockItemDto){
    console.log("Returns says: " + item);
    this.attemptAddCartItem(item);
  }

  deleteItem(index: number){
  }

  onTableItemDeleteClicked(itemIndex: number){
    console.log("Value ", itemIndex, " to be deleted.");
    console.log("This item and all coresponding reasons should be deleted.");
    let sItem = this.cart[itemIndex].stockItem;
    this.store.dispatch(cartActions.removeItem({stockItem: sItem}))
    this.store.dispatch(cartActions.removeAllReasons({barcode: sItem.barcode}))
  }

  onTableReasonDeleteClicked(itemIndex: number, reasonIndex: number){
    console.log("Reason", reasonIndex, " of Value ", itemIndex, " to be deleted.");
    console.log("This reason should be deleted and its corresponding item should be reduced in quantity.");
    let cartItem = this.cart[itemIndex];
    this.store.dispatch(cartActions.setNumberOfItems({stockItem: cartItem.stockItem, quantity: cartItem.quantity - 1}))
    this.store.dispatch(cartActions.removeReason({barcode: cartItem.stockItem.barcode, reasonId: reasonIndex}));
  }

  attemptAddCartItem(item: StockItemDto){
    // Determine: reason already received or need to ask?
    if(this.savedReason == null){

      var reasonModal = this.modalService.open(ReturnReasonPromptComponent, { size: 'xl', centered: true });
    
      reasonModal.result.then(
        (res: ReasonPromptResult)=>{
          if(res.applyToAll) this.savedReason = res.reason;
          this.store.dispatch(cartActions.addReason({barcode: item.barcode, reason: res.reason}));
          // Use addReturnItem instead of addItem to handle negative quantities
          this.store.dispatch(cartActions.addReturnItem({stockItem: item}));
        }
      ).catch((reason)=>{
        console.log("Return reason not obtained: " + reason);
        return;
      });
    }

    else{
      this.store.dispatch(cartActions.addReason({barcode: item.barcode, reason: this.savedReason}));
      this.store.dispatch(cartActions.addReturnItem({stockItem: item}));
    }
  }
  
  initState(){
    this.store.dispatch(cartActions.init());
    this.store.dispatch(transActions.init());
  }

  ngOnInit() {
    if(this.urlHistory.previousUrl == '/home') this.initState();
    
    this.subscribeToState();
  }

  attemptNext(){
    if(this.readyToProcess){
      this.router.navigateByUrl("returns/refund");
    }

    else{
      CreateErrorModal(this.modalService, false, "You will need to process at least one item first.");
    }
  }

}



function calculateNoItems(stockTableItems: StockTableItemDto[]): number {
  let count: number = 0;
  for(let item of stockTableItems){
    if(item.stockItem.styleCode == "Reason") continue;
    count += item.quantity;
  }
  return count;
}

export function CartItemFromDto(dto: StockItemDto) : CartItem {
  return {
      stockItem: dto,
      quantity: 1,
      bestValue: 0
  } as CartItem;
}