import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { Store, select } from '@ngrx/store';
import { of, Observable, Subject, BehaviorSubject } from 'rxjs';
import { first, map, mergeMap, take, takeUntil, tap, withLatestFrom } from 'rxjs/operators';
import Swal from 'sweetalert2';
import * as staffActions from 'src/app/reducers/staff/staff.actions';
import * as laybyActions from 'src/app/reducers/layby/layby.actions';
import { Payment, PaymentType, Transaction } from 'src/app/payment/payment.service';
import * as transactionActions from 'src/app/reducers/transaction/transaction.actions';
import * as laybyRefundActions from 'src/app/reducers/layby-refund/layby-refund.actions';
import { PaymentModalButton } from 'src/app/payment/payment-modal-button/payment-modal-button.component';
import { AppState } from 'src/app/reducers';
import { CashModalComponent } from 'src/app/payment/cash-modal/cash-modal.component';
import { EftposModalComponent } from 'src/app/payment/eftpos-modal/eftpos-modal.component';
import * as receiptActions from 'src/app/reducers/receipt-printing/receipt.actions';
import {
  CancelLaybyDto,
  LaybySearchResultDto,
  MakeLaybyPaymentsDto,
  TransactionDto,
  TranslogDto,
  TranspayDto,
  LaybyPaymentDto,
  LaybyPaymentWithTransactionDto,
  LaybylineDto,
  EftposClient,
  GetReceiptDto,
  LaybyPaymentEmailRequest,
} from 'src/app/pos-server.generated';
import * as transActions from 'src/app/reducers/transaction/transaction.actions';
import { EmailReceiptComponent } from 'src/app/email-receipt/email-receipt.component';
import { ReceiptTransactionDto } from 'src/app/pos-server.generated';
import { CreateLaybyDto } from 'src/app/pos-server.generated';
import * as transSelectors from 'src/app/reducers/transaction/transaction.selectors';
import * as laybyPaymentActions from 'src/app/reducers/layby-payment/layby-payment.actions';
import * as laybySearchSelectors from 'src/app/reducers/layby-search/layby-search.selectors';
import * as sysSelectors from '../../reducers/sys-config/sys-config.selectors';
import { LaybyPayment } from 'src/app/reducers/layby-payment/layby-types';
import { WaitingForEftposModalComponent } from '../../payment/waiting-for-eftpos-modal/waiting-for-eftpos-modal.component';
import { EftposService, mapCartToLinklyBasket } from '../../eftpos/eftpos.service';
import {
  ReceiptBatch,
  TextAction,
  CutAction,
  CutType,
  FeedAction,
  BarcodeAction,
  OpenCashDrawerAction,
} from '../../printing/printing-definitions';
import { PrintingService, SolemateReceiptOptions } from 'src/app/printing/printing.service';
import { financialRound } from 'src/app/payment/payment.service';
import * as customerClubSearchActions from 'src/app/reducers/customer-club/club-search/customer-club.actions';
import * as customerClubUpdateActions from 'src/app/reducers/customer-club/customer-update/customer-update.actions';
import * as customerClubSearchSelectors from 'src/app/reducers/customer-club/club-search/customer-club.selectors';
import { PointsUpdatePayload } from 'src/app/reducers/customer-club/customer-update/customer-update.actions';
import { CustomerClubDto } from 'src/app/pos-server.generated';
import { GiftCardModalComponent } from 'src/app/payment/gift-card-modal/gift-card-modal.component';
import * as voucherPayActions from 'src/app/reducers/voucher-pay/voucher-pay.actions';
import * as voucherPaySelectors from 'src/app/reducers/voucher-pay/voucher-pay.selectors';

@Component({
  selector: 'pos-layby-payment-modal',
  templateUrl: './layby-payment-modal.component.html',
  styleUrls: ['./layby-payment-modal.component.scss'],
})
export class LaybyPaymentModalComponent implements OnInit, OnDestroy {
  @Input() clientCode: string | null = null;
  @Input() clientName: string | null = null;
  private destroy$ = new Subject<void>();
  private stateReset$ = new Subject<void>();
  modalButtonsRow1: PaymentModalButton[];
  modalButtonsRow2: PaymentModalButton[];

  sysStatus: any;
  public sysStatus$: Observable<any>;

  public intEftAmount = 0;
  alwaysOpenCashTill: string = 'F';
  intEftReceipts: GetReceiptDto[];

  selectedLayby$: Observable<LaybySearchResultDto | null>;
  selectedLaybyLines$: Observable<LaybylineDto[] | null>;

  // Variables to hold the TransPay data
  payAmount: number | null = null;
  paymentType: string | null = null;
  voucherNumber: string | null = null;
  totalRefundedInModals: number = 0; // Total amount refunded via payment modals
  transaction: Transaction; // Transaction to handle payments

  totalCashRefunded: number = 0;
  transNo: number | null = null;
  totalEftposRefunded: number = 0;
  totalGiftCardRefunded: number = 0;
  totalCreditNoteRefunded: number = 0;

  totalPaid: number = 0; // Total amount paid for the layby
  amountDue: number = 0;
  cancellationError: string | null = null; // Error message if the refund amount is invalid

  // Add new properties for the history component
  laybyLines$: Observable<LaybylineDto[]>;
  totalItemValue: number = 0;

  // Added properties for points calculation
  PointsPerDollar: number = 0;
  private selectedCustomerClubMember$: Observable<CustomerClubDto>;
  public selectedCustomerClubMember: CustomerClubDto = null;
  private customerClubMemberForPoints: CustomerClubDto | null = null; // To store fetched customer details
  newCustomerPointsTotal: number | null = null;
  pointsEarned: number = 0;

  constructor(
    public activeModal: NgbActiveModal,
    private store: Store<AppState>,
    private router: Router,
    private modalService: NgbModal,
    private eftposService: EftposService,
    private printService: PrintingService
  ) {
    this.selectedLayby$ = this.store.pipe(select(laybySearchSelectors.selectedLayby));
    this.selectedLaybyLines$ = this.store.pipe(select(laybySearchSelectors.searchedLaybyLines));
    this.selectedCustomerClubMember$ = this.store.pipe(select(customerClubSearchSelectors.selectedCustomerClubMember));

    // Create a separate observable for the template to use
    this.laybyLines$ = this.selectedLaybyLines$;
  }

  ngOnInit() {
    // Set up payment modal buttons for Cash and EFTPOS refunds.
    this.modalButtonsRow1 = [
      new PaymentModalButton('Cash', 'fas fa-money-bill-wave', PaymentType.Cash, false),
      new PaymentModalButton('EFTPOS', 'fas fa-credit-card', PaymentType.Eftpos, false),
    ];
    this.modalButtonsRow2 = [
      new PaymentModalButton('Gift Card', 'fa-gift', PaymentType.GiftCard, false),
      new PaymentModalButton('Credit Note', 'fa-sticky-note', PaymentType.CreditNote, false),
    ];

    this.store.dispatch(transActions.getTransactionNo());

    // Subscribe to the transaction number with proper error handling
    this.store
      .select(transSelectors.transNo)
      .pipe(
        takeUntil(this.destroy$),
        tap((transNo) => {
          this.transNo = transNo;
          console.log('Transaction number updated:', transNo);
        })
      )
      .subscribe();

    this.store
      .select(sysSelectors.PointsPerDollar)
      .pipe(takeUntil(this.destroy$))
      .subscribe((limit) => {
        this.PointsPerDollar = limit || 0;
      });

    this.sysStatus$ = this.store.select(sysSelectors.selectSysConfig);
    this.sysStatus$.subscribe((sysconfig) => {
      this.sysStatus = sysconfig;
      this.alwaysOpenCashTill = sysconfig.alwaysOpenCashTill || 'F';
    });

    // Fetch customer details if clientCode is provided
    if (this.clientCode) {
      this.store.dispatch(
        customerClubSearchActions.search({
          searchParams: {
            searchString: this.clientCode,
            customerClubSearchKeywordColumnDto: 3, // Search by client code
            customerClubSearchOrderByColumnDto: 3,
            customerClubOrderByDirectionEnumDto: 0,
            first: 1,
            skip: 0,
          },
        })
      );

      // Subscribe to the search results to select the first member
      this.store
        .select(customerClubSearchSelectors.searchedCustomerClubMembers)
        .pipe(
          takeUntil(this.destroy$),
          tap((searchResults) => {
            if (searchResults && searchResults.length > 0) {
              console.log('Customer club search results:', searchResults);
              // Select the first member from the search results
              this.store.dispatch(
                customerClubSearchActions.selectCustomerClubMember({
                  payload: searchResults[0],
                })
              );
            }
          })
        )
        .subscribe();

      // Subscribe to the selected customer club member
      this.selectedCustomerClubMember$.pipe(takeUntil(this.destroy$)).subscribe((member) => {
        if (member) {
          this.selectedCustomerClubMember = member;
          this.customerClubMemberForPoints = member;
          this.clientName = `${member.firstname || ''} ${member.surname || ''}`.trim();
          console.log('Customer Club Member found:', member);
        }
      });
    }

    // Subscribe to selectedLaybyLines$ to get the actual array of lines
    this.selectedLaybyLines$.pipe(takeUntil(this.destroy$)).subscribe((lines) => {
      if (lines) {
        console.log('Layby lines:', lines);

        // 1. Calculate the total value of items on layby.
        // Add the values for transtypes 1 and 5
        const stockPlaced = lines
          .filter((line) => line.transType === 1)
          .reduce((sum, line) => sum + (line.extendedValue || 0), 0);
        console.log('Stock placed on layby (transType 1):', stockPlaced);

        const additionalItems = lines
          .filter((line) => line.transType === 5)
          .reduce((sum, line) => sum + (line.extendedValue || 0), 0);
        console.log('Additional items added (transType 5):', additionalItems);

        // Subtract the values for transtypes 7 (items removed)
        const stockRemoved = lines
          .filter((line) => line.transType === 7)
          .reduce((sum, line) => sum + (line.extendedValue || 0), 0);
        console.log('Stock removed (transType 7):', stockRemoved);

        // Total value of items placed on layby:
        const totalItemsValue = stockPlaced + additionalItems - -1 * stockRemoved;
        console.log('Total items value (1 + 5 minus 7):', totalItemsValue);

        // 2. Calculate the total amount due (sum of all extended values)
        const amountDue = lines.reduce((sum, line) => sum + (line.extendedValue || 0), 0);
        console.log('Total amount due (sum of all extended values):', amountDue);

        // 3. Calculate the total paid as (total value of items - amount due)
        const totalPaid = totalItemsValue - amountDue;
        console.log('Total paid:', totalPaid);

        // Save these calculated values in your component
        this.totalPaid = parseFloat(totalPaid.toFixed(2));
        this.amountDue = parseFloat(amountDue.toFixed(2));

        // Initialize the transaction with the amount due
        this.transaction = new Transaction(this.amountDue);
        console.log('Transaction initialized with amount due:', this.amountDue);
      }
    });

    // Subscribe to stateReset$ to reset state when needed
    this.stateReset$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.resetState();
    });
  }

  dismissModal() {
    this.resetState(); // Clear state before closing
    this.destroy$.next();
    this.destroy$.complete();
    this.activeModal.dismiss('laybyCancelled');
  }

  validateCancellationAmount() {
    // Only show error if the amount is negative
    // Remove the check for exceeding amount due since we now allow that for cash payments
    if (this.totalRefundedInModals < 0) {
      this.cancellationError = 'Payment amount cannot be negative.';
    } else {
      this.cancellationError = null;
    }
  }

  canProcessCancellation(): boolean {
    // Allow processing if:
    // 1. The amount is positive
    // 2. Either the amount equals the amount due OR there's change from a cash payment
    return (
      this.totalRefundedInModals >= 0 && (this.totalRefundedInModals <= this.amountDue || this.transaction.change > 0)
    );
  }

  getAmountRemaining(): number {
    return Math.max(this.amountDue - this.totalRefundedInModals, 0);
  }

  handleLaybyCancellation() {
    if (this.canProcessCancellation()) {
      console.log('Payment amount', this.totalRefundedInModals);
      console.log('Change amount before processing:', this.transaction.change);

      this.checkIntegratedEftpos();
    } else {
      console.log('Layby cancellation failed: invalid payment amount');
    }
  }

  checkIntegratedEftpos(): void {
    if (this.intEftAmount != 0) {
      const modalRef = this.modalService.open(WaitingForEftposModalComponent, {
        size: 'md',
        centered: true,
        backdrop: 'static',
        keyboard: false,
      });

      console.log(this.transNo);
      // Determine which integrated EFTPOS provider to use
      switch (this.sysStatus.integratedEFTProvider) {
        case 'Linkly':
          console.log(this.intEftAmount);
          modalRef.componentInstance.tenderAmount = this.intEftAmount;
          modalRef.componentInstance.totalAmount = this.totalRefundedInModals;
          modalRef.componentInstance.store = this.store;
          modalRef.componentInstance.discountAmt = 0; // TODO: calculate discount if needed
          modalRef.componentInstance.surchargeAmt = this.intEftAmount * 0; // TODO: adjust surcharge calculation if required
          modalRef.componentInstance.taxAmt = this.intEftAmount * 0; // TODO: adjust tax calculation based on config
          modalRef.componentInstance.transNo = this.transNo;

          // Map the current cart to the format required by Linkly
          const mappedItems = mapCartToLinklyBasket([]); // TODO fix if needed
          modalRef.componentInstance.items = mappedItems;
          modalRef.componentInstance.transType = 'Purchase';
          break;

        case 'Tyro':
          // TODO: Implement Tyro integration logic here if needed.
          break;

        default:
          console.log('Integrated EFTPOS not configured');
          return;
      }
      // Handle the result from the waiting-for-EFTPOS modal.
      modalRef.result
        .then((result: { paymentResult: Payment; surchargePayment: Payment }) => {
          if (result) {
            console.log('EFTPOS payment result:', result);
            this.eftposService.getReceipts(this.transNo, false).subscribe((receipts: GetReceiptDto[]) => {
              this.intEftReceipts = receipts;
              this.laybyTransactionCompleted();
              this.printService.printEftposReceipt(receipts, true);
            });
          } else {
            // this.totalRefundedInModals -= this.intEftAmount;  TODO currently don't remove from transaction, breaks some stuff, will fix
            // this.totalEftposRefunded = 0;
            // this.intEftAmount = 0;
            this.store.dispatch(transActions.resetTransactionConfirmation());
            this.store.dispatch(transActions.getTransactionNo());

            console.log('EFTPOS payment failed or was cancelled');
          }
        })
        .catch((error) => {
          console.error('Error in waiting-for-EFTPOS modal:', error);
        });
    } else {
      this.laybyTransactionCompleted();
    }
  }

  laybyTransactionCompleted(): void {
    console.log('Final change amount:', this.transaction.change);

    this.selectedLayby$
      .pipe(
        take(1), // Take the first emission
        mergeMap((layby) => {
          if (!layby) {
            throw new Error('No layby selected');
          }

          // Get the transaction DTO
          return this.getLaybyPaymentDto().pipe(
            map((LaybyPaymentWithTransactionDto) => ({
              layby,
              LaybyPaymentWithTransactionDto,
            }))
          );
        }),
        mergeMap(({ layby, LaybyPaymentWithTransactionDto }) => {
          LaybyPaymentWithTransactionDto.transactionDto.translogs[0].styleCode = layby.laybyCode;

          this.store.dispatch(
            laybyPaymentActions.submitPayments({
              payload: LaybyPaymentWithTransactionDto,
            })
          );

          // Return the transaction DTO for further processing
          return of({
            transactionDto: LaybyPaymentWithTransactionDto.transactionDto,
            layby,
          });
        })
      )
      .subscribe(
        async ({ transactionDto, layby }) => {
          const saleDateTime = new Date();

          // Get all the layby lines to include in the receipt
          this.selectedLaybyLines$.pipe(take(1)).subscribe((laybyLines) => {
            // Create a more complete receipt transaction that includes all layby items
            const trans: ReceiptTransactionDto = {
              logs: laybyLines ? [...laybyLines, ...transactionDto.translogs] : transactionDto.translogs,
              pays: transactionDto.payments,
              saleDateTime: saleDateTime,
              transType: 5,
              //laybyCode: layby.laybyCode // Include layby code for reference
            };
            const receiptTrans: LaybyPaymentEmailRequest = {
              email: null,
              receiptDto: trans,
              receiptType: '5',
              logs: trans.logs,
              pays: trans.pays,
              transNo: this.transNo,
              amountDue: this.amountDue,
              change: this.transaction.change,
            };

            if (this.alwaysOpenCashTill === 'T') {
              this.store.dispatch(
                receiptActions.executeBatch({
                  payload: new ReceiptBatch().add_action(new OpenCashDrawerAction()),
                })
              );
            } else {
              if (trans.pays.some((payment) => payment.paymentType === 'Cash')) {
                this.store.dispatch(
                  receiptActions.executeBatch({
                    payload: new ReceiptBatch().add_action(new OpenCashDrawerAction()),
                  })
                );
              }
            }

            // Show confirmation message with change information if applicable
            let title = 'Payment Successful';
            let text = 'The layby payment was successfully processed.';

            // Add change information if there is change due
            if (this.transaction.change > 0) {
              title = `Change Due: ${this.transaction.change.toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })}`;
              text = 'The layby payment was successfully processed.';
            }

            Swal.fire({
              title: title,
              text: text,
              showCancelButton: true,
              cancelButtonText: 'Email Receipt',
              confirmButtonText: 'Print Receipt',
              preConfirm: async () => {
                const printPromise = this.printService.printLaybyReceipt(
                  'Layby Payment',
                  trans.logs,
                  trans.pays,
                  trans.transType,
                  this.transNo.toString(),
                  SolemateReceiptOptions.default(),
                  trans,
                  this.amountDue,
                  this.transaction.change,
                  undefined, // refs
                  this.clientCode,
                  this.clientName, // Pass client name
                  layby.laybyCode, // Pass layby code/number
                  undefined, // voucherBalances
                );

                const cooldownPromise = new Promise<void>((resolve) => {
                  let count = 3;
                  const btn = Swal.getConfirmButton() as HTMLButtonElement;
                  btn.textContent = `Print Reciept (${count})`;
                  const intervalId = setInterval(() => {
                    count--;
                    btn.textContent = `Print Receipt (${count})`;
                    if (count <= 0) {
                      clearInterval(intervalId);
                      btn.textContent = `Print Reciept`;
                      resolve();
                    }
                  }, 1000);
                });

                await Promise.all([printPromise, cooldownPromise]);

                return false;
              },
            }).then(async (result) => {
              if (result.dismiss === Swal.DismissReason.cancel) {
                this.openEmailModal(receiptTrans, this.pointsEarned, this.newCustomerPointsTotal).then(() => {
                  this.store.dispatch(staffActions.clearStaffLogin());
                  this.activeModal.dismiss('completed-emailed');
                });
              } else {
                this.store.dispatch(staffActions.clearStaffLogin());
                this.activeModal.dismiss('completed-closed');
              }
            });
          });
        },
        (error) => {
          // Handle error
          console.error('Cancellation failed:', error);
          Swal.fire({
            title: 'Error',
            text: 'Failed to process the layby cancellation.',
          });
        }
      );
  }

  openEmailModal(
    receiptTrans: LaybyPaymentEmailRequest,
    pointsEarned: number,
    newCustomerPointsTotal: number | null
  ): Promise<void> {
    return new Promise((resolve) => {
      const modalRef = this.modalService.open(EmailReceiptComponent, {
        size: 'lg',
        backdrop: 'static',
      });

      // Pass receiptTrans to the EmailReceiptComponent.
      modalRef.componentInstance.laybyReceipt = receiptTrans;
      modalRef.componentInstance.customerSelected = this.customerClubMemberForPoints;
      modalRef.componentInstance.pointsEarned = pointsEarned;
      modalRef.componentInstance.newCustomerPointsTotal = newCustomerPointsTotal;

      modalRef.result
        .then(() => {
          console.log('Email receipt sent.');
          resolve(); // Resolve the promise once the modal is closed.
        })
        .catch(() => {
          resolve(); // Resolve the promise if the modal is dismissed.
        });
    });
  }

  launchPaymentModal(type: PaymentType) {
    let modalRef;

    switch (type) {
      case PaymentType.GiftCard:
        modalRef = this.modalService.open(GiftCardModalComponent, {
          size: 'xl',
          centered: true,
        });
        modalRef.componentInstance.isCreditNote = false;
        break;
      case PaymentType.CreditNote:
        modalRef = this.modalService.open(GiftCardModalComponent, {
          size: 'xl',
          centered: true,
        });
        modalRef.componentInstance.isCreditNote = true;
        break;
      case PaymentType.Cash:
        modalRef = this.modalService.open(CashModalComponent, {
          size: 'xl',
          centered: true,
        });
        break;
      case PaymentType.Eftpos:
        this.store
          .select(sysSelectors.selectSysConfig)
          .pipe(
            first(), // Get the first emitted value and complete
            map((sysConfig) =>
              sysConfig && sysConfig.integratedEFTProvider !== 'None' ? sysConfig.integratedEFTProvider : null
            )
          )
          .subscribe((integrated) => {
            if (integrated && integrated != 'None') {
              modalRef = this.modalService.open(EftposModalComponent, {
                size: 'xl',
                centered: true,
              });
              modalRef.componentInstance.intEft = true;
            } else {
              modalRef = this.modalService.open(EftposModalComponent, {
                size: 'xl',
                centered: true,
              });
              modalRef.componentInstance.intEft = false;
            }
          });
        break;
    }

    if (modalRef) {
      modalRef.componentInstance.amountDue = this.getAmountRemaining();
      modalRef.componentInstance.transType = 'Purchase';
      modalRef.result
        .then((result: Payment) => {
          console.log(result);
          if (result) {
            // Add the payment to the transaction object first
            this.transaction.addPayment(result);

            if (type === PaymentType.Cash) {
              this.totalCashRefunded += result.amount;
              this.totalRefundedInModals += result.amount;

              // Calculate change if cash payment exceeds amount due
              if (result.amount > this.getAmountRemaining()) {
                console.log(`Cash payment exceeds amount due. Change: ${this.transaction.change}`);
              }
            } else if (type === PaymentType.Eftpos) {
              this.totalEftposRefunded += result.amount;
              this.totalRefundedInModals += result.amount;
              if (result.desc === 'Integrated Eftpos') {
                this.intEftAmount += result.amount;
              }
            } else if (type === PaymentType.GiftCard) {
              this.totalGiftCardRefunded += result.amount;
              this.totalRefundedInModals += result.amount;
            } else if (type === PaymentType.CreditNote) {
              this.totalCreditNoteRefunded += result.amount;
              this.totalRefundedInModals += result.amount;
            }

            // Optionally revalidate the cancellation amount after a payment modal completes.
            this.validateCancellationAmount();
            console.log('Transaction state:', this.transaction);
            console.log('Change amount:', this.transaction.change);
          }
        })
        .catch((error) => {
          console.log('Payment Cancelled:', error);
        });
    }
  }

  getLaybyCancellationTransaction(): Observable<TransactionDto> {
    return this.selectedLaybyLines$.pipe(
      take(1),
      withLatestFrom(this.store.select(voucherPaySelectors.getGiftCardTracking)),
      map(([lines, tracking]) => {
        const payments: TranspayDto[] = [];

        const voucherNumbers = Object.keys(tracking);
        // A simplification: picking the first available voucher number.
        // This may need to be revisited if multiple different vouchers are used.
        const aVoucherNumber = voucherNumbers.length > 0 ? voucherNumbers[0] : null;

        if (this.totalRefundedInModals > 0) {
          if (this.totalCashRefunded > 0) {
            // Adjust cash payment amount to subtract any change
            const adjustedCashAmount =
              this.transaction.change > 0 ? this.totalCashRefunded - this.transaction.change : this.totalCashRefunded;

            payments.push({
              paymentType: 'Cash',
              payAmount: adjustedCashAmount,
              voucherNumber: null,
            });
          }

          if (this.totalEftposRefunded > 0) {
            payments.push({
              paymentType: 'Eftpos',
              payAmount: this.totalEftposRefunded,
              voucherNumber: null,
            });
          }

          if (this.totalGiftCardRefunded > 0) {
            this.voucherNumber = aVoucherNumber;
            payments.push({
              context: {
                giftVoucherNo: this.voucherNumber,
              },
              paymentType: 'Gift Card',
              payAmount: this.totalGiftCardRefunded,
              voucherNumber: this.voucherNumber,
            });
          }

          if (this.totalCreditNoteRefunded > 0) {
            this.voucherNumber = aVoucherNumber;
            payments.push({
              context: {
                giftVoucherNo: this.voucherNumber,
              },
              paymentType: 'Credit Note',
              payAmount: this.totalCreditNoteRefunded,
              voucherNumber: this.voucherNumber,
            });
          }
        }

        // Create single translog entry for the refund
        const translog: TranslogDto = {
          styleCode: '', // Or any appropriate identifier for refunds
          colourCode: '', // Can be empty for refunds
          sizeCode: '', // Can be empty for refunds
          quantity: 0, // Single refund entry
          sellingPrice: this.totalRefundedInModals, // Use the actual amount due, not the tendered amount
          clientCode: this.clientCode,
          transNo: this.transNo, // Use first line's transNo or null
          lineNo: lines ? lines.length + 1 : 1,
        };

        return {
          transType: 5,
          translogs: [translog], // Single entry array
          payments: payments,
        };
      })
    );
  }

  getLaybyPaymentDto(): Observable<LaybyPaymentWithTransactionDto> {
    return this.selectedLayby$.pipe(
      take(1), // Take the first emission of selectedLayby$
      mergeMap((layby) => {
        // Prepare the payments array
        const payments: LaybyPaymentDto[] = [];

        if (this.totalRefundedInModals > 0) {
          if (this.totalCashRefunded > 0) {
            // Adjust cash payment amount to subtract any change
            const adjustedCashAmount =
              this.transaction.change > 0 ? this.totalCashRefunded - this.transaction.change : this.totalCashRefunded;

            payments.push({
              type: 'Cash',
              amount: adjustedCashAmount,
            });
          }

          if (this.totalEftposRefunded > 0) {
            payments.push({
              type: 'Eftpos',
              amount: this.totalEftposRefunded,
            });
          }

          if (this.totalGiftCardRefunded > 0) {
            payments.push({
              type: 'Gift Card',
              amount: this.totalGiftCardRefunded,
            });
          }

          if (this.totalCreditNoteRefunded > 0) {
            payments.push({
              type: 'Credit Note',
              amount: this.totalCreditNoteRefunded,
            });
          }
        }
        if (this.totalRefundedInModals === 0) {
          payments.push({
            type: 'Cash',
            amount: 0,
          });
        }

        // Create the payments DTO
        const paymentsDto: MakeLaybyPaymentsDto = {
          laybyCode: layby.laybyCode,
          payments: payments.length > 0 ? payments : null,
          transType: 3,
        };

        // Get the transaction DTO
        return this.getLaybyCancellationTransaction().pipe(
          map((transactionDto) => ({
            paymentsDto: paymentsDto,
            transactionDto: transactionDto, // Now this is a plain TransactionDto
          }))
        );
      })
    );
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.stateReset$.complete();
    this.store.dispatch(voucherPayActions.clearAllVoucherPayments());
    this.resetState(); // Ensure state is cleared when destroyed
  }

  private resetState() {
    this.totalCashRefunded = 0;
    this.totalEftposRefunded = 0;
    this.totalRefundedInModals = 0;
    this.totalGiftCardRefunded = 0;
    this.totalCreditNoteRefunded = 0;
    this.cancellationError = null;
    this.payAmount = null;
    this.paymentType = null;
    this.voucherNumber = null;
    this.pointsEarned = 0;
    this.newCustomerPointsTotal = null;
    this.customerClubMemberForPoints = null;
    this.selectedCustomerClubMember = null;
    this.clientName = null;
  }

  parseDate(dateString: string): Date {
    const parts = dateString.split('/');
    // Assuming "24" means "2024". You might need to adjust if the century is different.
    const day = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1; // JavaScript months are 0-based.
    const year = parseInt(parts[2], 10) + 2000;
    return new Date(year, month, day);
  }

  /**
   * Gets a descriptive name for the item
   * For true items (transType 1 and 5), return the style code
   * For payments and deposits, return a formatted date if available
   */
  getItemDescription(line: LaybylineDto): string {
    if (line.transType === 1 || line.transType === 5) {
      return line.styleCode || 'Unknown Item';
    }
    return '';
  }

  /**
   * Gets the payment method for a payment line
   * This is a placeholder since the actual payment method
   * might not be available in the layby lines
   */
  getPaymentMethod(line: LaybylineDto): string {
    // This is a placeholder - in a real implementation,
    // you might need to look up the payment method from transaction records
    if (line.transType === 2) {
      return 'Initial Deposit';
    } else if (line.transType === 3) {
      return 'Layby Payment';
    }
    return '';
  }

  /**
   * Calculates the total value of items on layby
   */
  getTotalItemValue(): number {
    let total = 0;

    // We'll calculate this on demand from the layby lines
    this.laybyLines$.pipe(take(1)).subscribe((lines) => {
      if (lines) {
        // Sum the values for transTypes 1 and 5 (items placed on layby)
        const stockPlaced = lines
          .filter((line) => line.transType === 1)
          .reduce((sum, line) => sum + (line.extendedValue || 0), 0);

        const additionalItems = lines
          .filter((line) => line.transType === 5)
          .reduce((sum, line) => sum + (line.extendedValue || 0), 0);

        // Subtract the values for transType 7 (items removed)
        const stockRemoved = lines
          .filter((line) => line.transType === 7)
          .reduce((sum, line) => sum + (line.extendedValue || 0), 0);

        // Total value of items placed on layby
        total = stockPlaced + additionalItems - -1 * stockRemoved;
      }
    });

    return total;
  }

}
