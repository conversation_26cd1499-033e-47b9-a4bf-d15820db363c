import { Stock } from "./stock";
import { Sale } from './sale';

export class Total {

    // We may want to assign location name for testing later
    sales: number;
    stock: number;

    constructor(
        stock: Stock[],
        sales: Sale[]
    ) {
        this.stock = this.sumStock(stock);
        this.sales = this.sumSales(sales);
    }
    private sumStock(stock: Stock[]): number {
        return stock.reduce((sum, next) => sum + next.qty, 0);
    }
    private sumSales(sale: Sale[]): number {
        return sale.reduce((sum, next) => sum + next.qty, 0);
    }
}

