using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR; 
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.ReceiptHelpers;
using SoleMatrixPOS.Application.ReceiptPrinting;
using SoleMatrixPOS.Application.ReceiptPrinting.Commands;
using SoleMatrixPOS.Application.Transaction;


namespace SoleMatrixPOS.Controllers
{
	// TODO: delete all of this... this prints stuff on the backend which is not correct
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class ReceiptHelpersController : Controller
	{
		private readonly IMediator _mediator;

		public ReceiptHelpersController(IMediator mediator)
		{
			_mediator = mediator;
		}
		
		[Route("GetEncodedReceiptLogo")]
		[HttpPost]
		public async Task<ReceiptLogoDto> GetEncodedReceiptLogo([FromBody] ReceiptLogoRequestDto request)
		{
			return await _mediator.Send(new GetEncodedReceiptLogoQuery(request));
		}

		[Route("GetReceiptTemplate")]
		[HttpGet]
		public async Task<ReceiptTemplateDto> GetReceiptTemplate()
		{
			return await _mediator.Send(new GetReceiptTemplateQuery());
		}
	}
}
