using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Infrastructure;
using SoleMatrixPOS.Application.Transaction;
using SoleMatrixPOS.Application.Transaction.Commands;
using SoleMatrixPOS.Application.Transaction.Queries;
using SoleMatrixPOS.Application.General;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController]
    public class TransactionController : Controller
    {
        private readonly IMediator _mediator;
		private readonly StaffCodeContext _staffCodeContext;


		public TransactionController(IMediator mediator, StaffCodeContext staffCodeContext)
		{
			_mediator = mediator;
			_staffCodeContext = staffCodeContext;
		}

		[HttpPut]
		public async Task<ActionResult<TransactionResponseDto>> AddTransaction([FromBody] TransactionDto transactionDto)
		{
			TransactionResult transResult = await _mediator.Send(new CreateTransactionCommand(transactionDto)); //TODO need to return some kind of success or failure
			return transResult.Succeeded ? Ok(transResult.TransactionResponse) : BadRequest(new ErrorResponseDto("Transaction failed", new ErrorResponseDto[] { new ErrorResponseDto(transResult.Message) } ));
		}

		[HttpGet("getTransactionNo")]
		public async Task<IActionResult> GetTransactionNo()
		{
			int transactionNo = await _mediator.Send(new GetTransactionNoQuery());
			return Ok(transactionNo);
		}

		// New endpoint for retrieving TransPay by TransNo
		[HttpGet("getTransPay/{transNo}")]
		public async Task<IEnumerable<TranspayDto>> GetTransPayByTransNo(int transNo)
		{
			return await _mediator.Send(new GetTransPayQuery(transNo));
		}

		[HttpGet("getTransLog/{transNo}")]
		public async Task<IEnumerable<TranslogDto>> GetTransLogByTransNo(int transNo)
		{
			return await _mediator.Send(new GetTransLogQuery(transNo, _staffCodeContext.StoreDetailsDto.StoreId, _staffCodeContext.PINPadNo));
		}

		[HttpGet("getTransClientDetails/{transNo}")]
		public async Task<TransClientDetailsDto> GetTransClientDetailsByTransNo(int transNo)
		{
			return await _mediator.Send(new GetTransClientDetailsQuery(transNo));
		}

		[HttpGet("getTransRef/{transNo}")]
		public async Task<IEnumerable<TransrefDto>> GetTransRefByTransNo(int transNo)
		{
			return await _mediator.Send(new GetTransRefQuery(transNo));
		}
	}
}
