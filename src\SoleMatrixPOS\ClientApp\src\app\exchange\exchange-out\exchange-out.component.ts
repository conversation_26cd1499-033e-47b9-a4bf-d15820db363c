import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Actions } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { combineLatest, Observable, Subject } from 'rxjs';
import { take, takeUntil, tap } from 'rxjs/operators';
import { ProcessPaymentModalComponent } from 'src/app/payment/process-payment-modal/process-payment-modal.component';
import { StockItemDto, TransactionDto, TranslogDto, TransrefDto, ReceiptTransactionDto, StaffLoginDto, CustomerClubDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import { CartItem, cartItemToTranslog } from 'src/app/reducers/sales/cart/cart.reducer';
import { UrlHistoryService } from 'src/app/url-history.service';
import Swal from 'sweetalert2';
import * as cartActions from '../../reducers/sales/cart/cart.actions';
import * as cartSelectors from '../../reducers/sales/cart/cart.selectors';
import * as transActions from '../../reducers/transaction/transaction.actions';
import * as transSelectors from '../../reducers/transaction/transaction.selectors'
import * as staffActions from '../../reducers/staff/staff.actions';
import { EmailReceiptComponent } from 'src/app/email-receipt/email-receipt.component';
import { PrintingService, SolemateReceiptOptions } from 'src/app/printing/printing.service';
import * as receiptActions from '../../reducers/receipt-printing/receipt.actions';
import * as sysSelectors from '../../reducers/sys-config/sys-config.selectors';
import * as staffSelectors from '../../reducers/staff/staff.selectors';
import { ReceiptBatch } from 'src/app/printing/printing-definitions';
import { OpenCashDrawerAction } from 'src/app/printing/printing-definitions';
import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';
import * as saleNoteSelectors from '../../reducers/sale-note/sale-note.selectors';

const EXCHANGE_TRANSTYPE = 3;

@Component({
  selector: 'pos-exchange-out',
  templateUrl: './exchange-out.component.html',
  styleUrls: ['./exchange-out.component.scss']
})
export class ExchangeOutComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private hasNavigatedAway = false;

  total: number;
  cart: CartItem[];
  reasons: Map<string, string[]>;
  transNo: number;
  staff$: Observable<StaffLoginDto>;
  staff: StaffLoginDto;
  alwaysOpenCashTill: string = 'F';
  transactionDto: TransactionDto;
  disableNext: boolean = false;
  nextButtonText: string = 'Proceed';
  private selectedCustomerClubMember$: Observable<CustomerClubDto>;
  public selectedCustomerClubMember: CustomerClubDto = null;

  customerNameOnReceipt: string = 'F';

  constructor(
    private router: Router,
    private store: Store<AppState>,
    private modalService: NgbModal,
    private actions$: Actions,
    private urlHistory: UrlHistoryService,
    private printService: PrintingService,
  ) { }

  ngOnInit() {
    // Set exchange mode when entering exchange-out component
    this.store.dispatch(cartActions.setExchangeMode({ isExchangeMode: true }));

    this.store.dispatch(transActions.getTransactionNo());
    this.store.select(transSelectors.transNo)
      .pipe(
        takeUntil(this.destroy$),
        tap(transNo => {
          this.transNo = transNo;
          console.log('Transaction number updated:', transNo);
        })
      )
      .subscribe();
    this.subscribeToState();

    // Get staff information
    this.staff$ = this.store.select(staffSelectors.selectStaffLoginDto);
    this.staff$.pipe(
      takeUntil(this.destroy$)
    ).subscribe((value) => (this.staff = value));

    // Get cash drawer setting
    this.store.select(sysSelectors.OpenCashTill)
      .pipe(takeUntil(this.destroy$))
      .subscribe(setting => {
        this.alwaysOpenCashTill = setting || 'F';
      });

    this.store.select(sysSelectors.CustomerNameOnReceipt)
      .pipe()
      .subscribe(limit => {
        this.customerNameOnReceipt = limit || 'F';
      });

    this.selectedCustomerClubMember$ = this.store.select(customerClubSearchSelectors.selectedCustomerClubMember);
    this.selectedCustomerClubMember$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(member => {
      this.selectedCustomerClubMember = member;
      console.log('Selected customer club member in exchange:', member);
    });
  }

  ngOnDestroy() {
    this.hasNavigatedAway = true;

    this.destroy$.next();
    this.destroy$.complete();
  }

  subscribeToState() {
    this.store.select(cartSelectors.cart)
      .pipe(takeUntil(this.destroy$))
      .subscribe(s => this.cart = s);

    this.store.select(cartSelectors.reasons)
      .pipe(takeUntil(this.destroy$))
      .subscribe(s => this.reasons = s);

      combineLatest([
        this.store.select(cartSelectors.total),
        this.store.select(cartSelectors.cart)
      ])
        .pipe(takeUntil(this.destroy$))
        .subscribe(([total, cart]) => {
          this.total = total;
          if (cart.length === 0) {
            this.disableNext = true;
            this.nextButtonText = 'No Items in cart';
          } else {
            this.disableNext = false;
            // Set the button text based on the total amount
            if (this.total > 0) {
              this.nextButtonText = 'Customer to pay difference';
            } else if (this.total < 0) {
              this.nextButtonText = 'Issue refund to customer';
            } else {
              this.nextButtonText = 'Complete even exchange';
            }
          }
        });

    this.store.select(transSelectors.completed)
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        if (value && !this.hasNavigatedAway) this.exchangeCompleted()
      });
  }

  exchangeCompleted() {
    const saleDateTime = new Date();

    // Create transaction for receipt
    this.transactionDto = this.createTransaction();

    // Calculate change amount (if applicable)
    let changeAmount = 0;

    // If there are payments, calculate the change
    if (this.transactionDto.payments && this.transactionDto.payments.length > 0) {
      // Sum up all payment amounts
      const totalPayments = this.transactionDto.payments.reduce(
        (sum, payment) => sum + payment.payAmount, 0
      );

      // Calculate change (only if total is positive and payments exceed total)
      if (this.total > 0 && totalPayments > this.total) {
        changeAmount = totalPayments - this.total;
      }
    }

    // Prepare the receiptTrans object for receipt printing
    let receiptTrans: ReceiptTransactionDto = {
      logs: this.transactionDto.translogs,
      pays: this.transactionDto.payments,
      saleDateTime: saleDateTime,
      transType: EXCHANGE_TRANSTYPE,
      refs: this.transactionDto.transReferences || []
    };

    // Open cash drawer if configured to always open
    if (this.alwaysOpenCashTill === 'T') {
      this.store.dispatch(receiptActions.executeBatch({
        payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
      }));
    }

    // Init the transaction reducer
    this.store.dispatch(transActions.init());

    // Show success message with receipt options
    Swal.fire({
      title: "Exchange Completed",
      type: "success",
      text: "The exchange was successfully submitted.",
      showCancelButton: true,
      cancelButtonText: "Email Receipt",
      confirmButtonText: "Print Receipt",
    }).then(async (result) => {
      // If the "Print Receipt" button is clicked
      if (result.value) {
        // Print receipt
        await this.printService.printSolemateReceipt(
          "Exchange",
          receiptTrans.logs,
          receiptTrans.pays,
          EXCHANGE_TRANSTYPE,
          receiptTrans.logs[0].transNo.toString(),
          SolemateReceiptOptions.default(),
          receiptTrans,
          0, // only gets here if exchange is even value, should be no change
          receiptTrans.refs,
          this.selectedCustomerClubMember && this.customerNameOnReceipt == 'T' ? this.selectedCustomerClubMember.clientCode : undefined,
          this.selectedCustomerClubMember && this.customerNameOnReceipt == 'T' ? this.selectedCustomerClubMember.firstname + ' ' + this.selectedCustomerClubMember.surname : undefined
        );

        // Only clear staff login if we haven't navigated away
        if (!this.hasNavigatedAway) {
          console.log("CLEARING STAFF LOGIN HERE 1");
          this.store.dispatch(staffActions.clearStaffLogin());
        }
      }
      // If the "Email" button is clicked
      else if (result.dismiss === Swal.DismissReason.cancel) {
        await this.openEmailModal(receiptTrans);

        // Only clear staff login if we haven't navigated away
        if (!this.hasNavigatedAway) {
          console.log("CLEARING STAFF LOGIN HERE 2");
          this.store.dispatch(staffActions.clearStaffLogin());
        }
      }

      this.finalizeTransaction();
    });
  }

  openEmailModal(receiptTrans: ReceiptTransactionDto): Promise<void> {
    return new Promise((resolve) => {
      const modalRef = this.modalService.open(EmailReceiptComponent, {
        size: 'lg',
        backdrop: 'static'
      });

      // Pass receiptTrans to the EmailReceiptComponent
      modalRef.componentInstance.receiptTrans = receiptTrans;

      if (this.selectedCustomerClubMember && this.selectedCustomerClubMember.email) {
        modalRef.componentInstance.customerEmail = this.selectedCustomerClubMember.email;
      }

      modalRef.result.then(() => {
        console.log('Email receipt sent.');
        resolve();  // Resolve the promise once the modal is closed
      }).catch(() => {
        resolve();  // Resolve the promise if the modal is dismissed
      });
    });
  }

  finalizeTransaction(): void {
    // Only clear staff login if we haven't navigated away
    if (!this.hasNavigatedAway) {
      console.log("CLEARING STAFF LOGIN HERE 3");
      this.store.dispatch(staffActions.clearStaffLogin());
    }
  }

  itemLookup(item: StockItemDto) {
    this.attemptAddCartItem(item);
  }

  attemptAddCartItem(item: StockItemDto) {
    this.store.dispatch(cartActions.addItem({ stockItem: item }));
  }

  attemptNext() {
    if (this.total > 0) {
      // Let sales-process-payment handle everything
      this.router.navigate(["sales/payment"], {
        state: {
          isExchangeMode: true
        }
      });
    }
    else if (this.total < 0) {
      // Handle negative total case - now we pass the absolute value
      this.router.navigate(["returns/refund"], {
        state: {
          isExchangeMode: true,
          absoluteTotal: Math.abs(this.total)
        }
      });
    }
    else {
      // Handle zero total exchange directly in this component
      this.processExchange();
    }
  }

  processExchange() {
    let modalRef = this.modalService.open(ProcessPaymentModalComponent, { size: 'xl', centered: true });
    modalRef.componentInstance.headerText = 'Ok to process?';
    modalRef.componentInstance.processButtonText = 'Process Exchange';

    modalRef.componentInstance.name = "Process Exchange";
    modalRef.result.then((result) => {
      // Build out the entire transaction
      let trans = this.createTransaction();
      console.log("Transaction is submitting...", trans);
      // Dispatch an action with the request
      this.store.dispatch(transActions.submitTransaction({ payload: trans }));

    }).catch(res => console.log("Error occurred: ", res));
  }

  createTransaction() {
    let lineNo: number = 1;
    let resTransrefs: TransrefDto[] = [];
    let resTranslogs: TranslogDto[] = [];

    let clientCode = (this.selectedCustomerClubMember && this.selectedCustomerClubMember.clientCode)
      ? this.selectedCustomerClubMember.clientCode
      : undefined;

    this.store.select(saleNoteSelectors.selectSaleNote)
      .pipe(take(1))
      .subscribe(note => {
        if (note) {
          resTransrefs.push({
            lineNo: 0,
            transReference: note,
            transNo: this.transNo
          } as TransrefDto);
        }
      });

    for (let i = 0; i < this.cart.length; i++) {
      let cartItem = this.cart[i];

      // Create log for item with client code
      resTranslogs.push(cartItemToTranslog(cartItem, lineNo, this.transNo, clientCode));
      lineNo++;

      // To use as key
      let stockItem: StockItemDto = cartItem.stockItem;

      // TODO: Change this to barcode
      let reasons = this.reasons.get(stockItem.barcode);
      if (reasons) {
        for (let reason of reasons) {
          //  Create translog for the reason
          // (this is a dodgy but necessary workaround for legacy)
          resTranslogs.push({
            styleCode: "Reason",
            lineNo: lineNo,
            clientCode
          } as TranslogDto);

          // Create transref for the reason
          resTransrefs.push({
            lineNo: lineNo,
            transReference: reason
          } as TransrefDto);
          lineNo++;
        }
      }

    }
    // Create the transaction DTO
    return {
      payments: [],
      translogs: resTranslogs,
      transReferences: resTransrefs,
      transType: EXCHANGE_TRANSTYPE
    } as TransactionDto;
  }
}
