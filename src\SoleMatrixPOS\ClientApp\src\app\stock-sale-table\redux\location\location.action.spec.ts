import * as LocationAction from './location.action';

describe('LocationAction', () => {
  it('should create SwapStockAction', () => {
    let action = new LocationAction.SwapStockAction(1, "Location 1", "Location 2", "BOUNCE", "1", 1);
    expect(action).toBeTruthy();
    expect(action.type).toBe("[LOCATION] swap");

  });
  it('should create LoadLocationsAction', () => {
    let action = new LocationAction.LoadLocationsAction([]);
    expect(action).toBeTruthy();
    expect(action.type).toBe("[LOCATION] load");
  });
  it('should create HighlightStockAction', () => {
    let action = new LocationAction.HighlightStockAction(1, "Location 1", "Location 2", "BOUNCE", "1", 1);
    expect(action).toBeTruthy();
    expect(action.type).toBe("[LOCATION] highlight");
  });
});