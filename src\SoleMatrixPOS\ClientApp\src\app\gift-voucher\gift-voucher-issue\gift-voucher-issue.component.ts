import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import * as staffActions from 'src/app/reducers/staff/staff.actions';
import { take } from 'rxjs/operators';
import { CreateErrorModal } from 'src/app/error-modal/error-modal.component';
import { CustomerClubDto, GiftVoucherCreationDto, GiftVoucherResultDto } from 'src/app/pos-server.generated';
import { PrintingService } from 'src/app/printing/printing.service';
import { AppState } from 'src/app/reducers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import * as customerClubSelectors from 'src/app/reducers/customer-club/club-search/customer-club.selectors';
import { EmailReceiptComponent } from 'src/app/email-receipt/email-receipt.component';

import * as voucherSelectors from 'src/app/reducers/gift-voucher/gift-voucher.selectors'

@Component({
    selector: 'pos-gift-voucher-issue',
    templateUrl: './gift-voucher-issue.component.html',
    styleUrls: ['./gift-voucher-issue.component.scss']
})
export class GiftVoucherIssueComponent implements OnInit {

    constructor(
      private router: Router, 
      private modalService: NgbModal, 
      private store: Store<AppState>, 
      private printService: PrintingService,
      public activeModal: NgbActiveModal
    ) { }
  
    printed: boolean = false;
    emailed: boolean = false; 
    voucher: GiftVoucherResultDto;
    loading: boolean = true;
    isCreditNote: boolean;
    customer: CustomerClubDto;

    get voucherTypeTitle() { return this.isCreditNote ? 'Credit Note' : 'Gift Voucher' }
  
    ngOnInit() {
      this.store.select(voucherSelectors.voucherResult).subscribe(
        v => {
          this.voucher = v
        }
      );
  
      this.store.select(voucherSelectors.completed).subscribe(
        completed => {
          this.loading = !completed
        }
      );

      this.store.select(voucherSelectors.isCreditNote).subscribe(
        isCreditNote => { 
          this.isCreditNote = isCreditNote;
        }
      )

      this.store.select(customerClubSelectors.selectedCustomerClubMember).subscribe(
        customer => {
          this.customer = customer;
        }
      )
    }
  
    printVoucher() {
      if (this.voucher) {
        this.printService.printVoucher(this.voucherTypeTitle, this.voucher.voucherNo, this.voucher.voucherFunds, this.customer.clientCode, this.customer.firstname + ' ' + this.customer.surname);
        this.printed = true;
      } else {
        CreateErrorModal(this.modalService, true, `${this.voucherTypeTitle} is in an invalid state. Please contact support`);
      }
    }
  
    emailVoucher() {
      if (this.voucher) {
        const modalRef = this.modalService.open(EmailReceiptComponent);
        
        modalRef.componentInstance.voucherNo = this.voucher.voucherNo;
        modalRef.componentInstance.voucherFunds = this.voucher.voucherFunds;
        modalRef.componentInstance.voucherTypeTitle = this.voucherTypeTitle;
        
        modalRef.result.then(
          (result) => {
            if (result) {
              console.log(`${this.voucherTypeTitle} email sent to:`, result);
              this.emailed = true;  // Set emailed to true on success
            }
          },
          (reason) => {
            console.log('Email modal dismissed');
          }
        );
      } else {
        CreateErrorModal(this.modalService, true, `${this.voucherTypeTitle} is in an invalid state. Please contact support`);
      }
    }
  
    goHome() {
      this.activeModal.close('home');
      this.store.dispatch(staffActions.clearStaffLogin());
    }
}