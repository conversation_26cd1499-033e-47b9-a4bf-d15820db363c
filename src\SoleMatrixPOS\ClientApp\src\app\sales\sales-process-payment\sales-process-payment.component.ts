import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { map, skip, take, takeUntil, tap, filter, concatMap, toArray } from 'rxjs/operators';
import * as staffActions from 'src/app/reducers/staff/staff.actions';
import { EmailReceiptComponent } from 'src/app/email-receipt/email-receipt.component';
import { BehaviorSubject, combineLatest, Observable, of, Subscription, from } from 'rxjs';
import { OpenCashDrawerAction } from "src/app/printing/printing-definitions";
import * as laybyPaymentActions from 'src/app/reducers/layby-payment/layby-payment.actions';
import * as customerClubUpdateActions from 'src/app/reducers/customer-club/customer-update/customer-update.actions';
import * as laybyPaymentSelectors from 'src/app/reducers/layby-payment/layby-payment.selectors';
import { CustomerClubModalComponent } from 'src/app/customer-club/customer-club-modal/customer-club-modal.component';
import { PaymentModalButton } from 'src/app/payment/payment-modal-button/payment-modal-button.component';
import { financialRound, Payment, PaymentType, Transaction } from 'src/app/payment/payment.service';
import { ProcessPaymentModalComponent } from 'src/app/payment/process-payment-modal/process-payment-modal.component';
import { CustomerClubDto, TransactionDto, TranslogDto, CreateLaybyDto, LaybyItemDto, CtransDto, ReceiptTransactionDto, EftposClient, GetReceiptDto, SuspendSaleClient, SuspendSaleDto, StaffLoginDto, TranspayDto, TransrefDto, GiftVoucherResultDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import { CartItem, cartItemToTranslog } from 'src/app/reducers/sales/cart/cart.reducer';
import Swal from 'sweetalert2';
import * as SysConfigSelectors from 'src/app/reducers/sys-config/sys-config.selectors';
import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';
import * as cartSelectors from '../../reducers/sales/cart/cart.selectors';
import * as transActions from '../../reducers/transaction/transaction.actions';
import * as transSelectors from '../../reducers/transaction/transaction.selectors';
import * as customerClubSearchActions from '../../reducers/customer-club/club-search/customer-club.actions';
import * as laybyActions from '../../reducers/layby/layby.actions';
import * as quoteActions from '../../reducers/quote-item/quote.actions';
import { Subject } from 'rxjs';
import * as quoteSelectors from '../../reducers/quote-item/quote.selectors';
import * as laybySelectors from '../../reducers/layby/layby.selectors';
import { CreateErrorModal } from 'src/app/error-modal/error-modal.component';
import * as paymentActions from '../../reducers/sales/payment/payment.actions';
import * as voucherActions from '../../reducers/voucher-pay/voucher-pay.actions';
import * as giftCardPaySelectors from "src/app/reducers/voucher-pay/voucher-pay.selectors";
import * as paymentSelector from '../../reducers/sales/payment/payment.selector';
import * as receiptActions from '../../reducers/receipt-printing/receipt.actions'
import * as orderActions from 'src/app/reducers/order-item/order.actions';
import * as orderSelectors from 'src/app/reducers/order-item/order.selectors';
import * as suspendSaleSelectors from '../../reducers/suspend-sale/suspend-sale.selectors';
import * as staffSelectors from '../../reducers/staff/staff.selectors';
import * as SuspendSaleActions from '../../reducers/suspend-sale/suspend-sale.actions';
import * as sysSelectors from '../../reducers/sys-config/sys-config.selectors';
import { EftposService, mapCartToLinklyBasket } from '../../eftpos/eftpos.service';
import { WaitingForEftposModalComponent } from '../../payment/waiting-for-eftpos-modal/waiting-for-eftpos-modal.component';
import { BarcodeAction, CutAction, CutType, FeedAction, ImageAction, ReceiptBatch, TextAction } from 'src/app/printing/printing-definitions';
import { PrintingService, SolemateReceiptOptions, VoucherBalanceInfo } from 'src/app/printing/printing.service';
import { PointsUpdatePayload } from 'src/app/reducers/customer-club/customer-update/customer-update.actions';
import { StaffLoginState, StaffState } from 'src/app/reducers/staff/staff.reducer';
import * as cartActions from '../../reducers/sales/cart/cart.actions';
import { UrlHistoryService } from 'src/app/url-history.service';
import * as saleNoteSelectors from '../../reducers/sale-note/sale-note.selectors';

const regularModalButtons: PaymentModalButton[] = [
	new PaymentModalButton("Cash", "fa-money-bill-wave text-success", PaymentType.Cash),
	new PaymentModalButton("Eftpos", "fa-credit-card text-success", PaymentType.Eftpos),
	new PaymentModalButton("Customer Points", "fa-coins text-success", PaymentType.CustomerPoints),
	new PaymentModalButton("Gift Card", "fa-gift text-info", PaymentType.GiftCard),
	new PaymentModalButton("Credit Note", "fa-sticky-note text-info", PaymentType.CreditNote)
];

const laybyModalButtons: PaymentModalButton[] = [
	new PaymentModalButton("Cash", "fa-money-bill-wave text-success", PaymentType.Cash),
	new PaymentModalButton("Eftpos", "fa-credit-card text-success", PaymentType.Eftpos),
];

@Component({
	selector: 'pos-sales-process-payment',
	templateUrl: './sales-process-payment.component.html',
	styleUrls: ['./sales-process-payment.component.scss']
})
export class SalesProcessPaymentComponent implements OnInit, OnDestroy {
	readonly SALE_TRANSTYPE = 1;
	readonly EXCHANGE_TRANSTYPE = 3;
	transTypeToUse = this.SALE_TRANSTYPE;

	sysStatus: any;
	public sysStatus$: Observable<any>;

	passedAmount: 0;

	transNo$: Observable<number>
	transNo: number

	laybyCode: string;
	private isExchangeMode$: Observable<boolean>;
	private isExchangeMode: boolean = false;

	staffMember$: Observable<StaffState>;
	staffLoginStateSub: Subscription
	intEftReceipts: GetReceiptDto[];

	staff$: Observable<StaffLoginDto>;
	laybyModalAllowed: boolean;
	staff: StaffLoginDto;

	tenderAmount: any;
	activeModal: any;
	private destroy$ = new Subject<void>();

	private selectedCustomerClubMember$: Observable<CustomerClubDto>;
	public selectedCustomerClubMember: CustomerClubDto = null;

	public cart$: Observable<CartItem[]>;
	public total$: Observable<number>;

	laybyActive$: Observable<boolean>;
	laybyActive: boolean;
	orderCode: string;

	modalButtons: PaymentModalButton[] = regularModalButtons;

	transaction: Transaction;
	private transactionDto: TransactionDto
	private transactionWithDeposit: TransactionDto
	private transactionSubject = new BehaviorSubject<Transaction>(new Transaction(0));
	public transaction$ = this.transactionSubject.asObservable();
	cart: CartItem[];
	alreadyAutoRequestedConfirmation: boolean = false;
	cartTotal: number;

	private eftPaidSubject = new BehaviorSubject<number>(0);
	public eftPaid$ = this.eftPaidSubject.asObservable();

	laybyDepositProp: number;
	laybyMinDeposit: number;

	readyToProcess: boolean = false;
	laybyDepositDue: number;
	laybyDepositDue$: Observable<number>;

	customerNameOnReceipt: string = 'F';

	reasons$: Observable<Map<string, string[]>>;
	reasons: Map<string, string[]>;

	cTrans$: Observable<CtransDto[]>
	cTrans: CtransDto[]

	depositPercent: number = 0;

	laybyDepositSub = this.store.select(SysConfigSelectors.selectLaybyDeposit).subscribe(
		(laybyDeposit) => {
			this.depositPercent = laybyDeposit; // Update depositPercent variable
		}
	);

	currentSuspendSaleNo: number | null = null;


	saleNote$: Observable<string>;

	saleComment: string = '';
	newCustomerPointsTotal: number;
	pointsEarned: number;

	getLaybyDepositProp() {
		return this.depositPercent / 100;
	}

	// Add new property for soft credit limit
	softCreditLimit: string = 'F';
	CustomerAccount: string = 'F';
	pointsOnAllSales: string = 'F';
	alwaysOpenCashTill: string = 'F';
	DollarPerPoints: number = 0;
	PointsPerDollar: number = 0;

	isCustomDepositSet: boolean = false;
	customDepositAmount: number = 0;

	private isComponentActive: boolean = false;

	constructor(
		private modalService: NgbModal,
		private router: Router,
		private store: Store<AppState>,
		private eftposService: EftposService,
		private printService: PrintingService,
		private suspendSaleClient: SuspendSaleClient,
		private urlHistory: UrlHistoryService
	) { }

	ngOnInit() {
		this.isComponentActive = true;
		let navigation = this.router.getCurrentNavigation();
		if (window.history.state && window.history.state.amount) {
			this.passedAmount = window.history.state.amount;
		}

		this.newCustomerPointsTotal = -1;
		// TODO make it so that a user can create a layby with an order active and show the correct amounts
		this.store.select(orderSelectors.selectUploadedOrderCode)
			.pipe(take(1))
			.subscribe(orderCode => {
				this.laybyModalAllowed = true; // Always allow layby
			});
		this.staff$ = this.store.select(staffSelectors.selectStaffLoginDto);
		this.staff$.subscribe((value) => (this.staff = value));

		// Check if we came from exchange-out and if it's exchange mode
		this.isExchangeMode$ = this.store.select(cartSelectors.isExchangeMode);
		this.isExchangeMode$.subscribe(mode => {
			this.isExchangeMode = mode;
		});
		console.log("Is Exchange Mode:", this.isExchangeMode);

		// If this is an exchange, set the transaction type
		if (this.isExchangeMode) {
			this.transTypeToUse = this.EXCHANGE_TRANSTYPE;
		} else {
			console.log("Not exchange mode, resetting to sale type (1)")
			this.transTypeToUse = this.SALE_TRANSTYPE;
		}

		// Initialise voucher reducer
		this.store.dispatch(voucherActions.init());

		// Init layby reducer
		this.store.dispatch(laybyActions.init());

		// Get layby deposit proportion
		this.laybyDepositProp = this.getLaybyDepositProp();

		this.store.dispatch(transActions.getTransactionNo());
		this.store.dispatch(laybyActions.getLaybyNumber());

		// Subscribe to the transaction number with proper error handling
		this.store.select(transSelectors.transNo)
			.pipe(
				takeUntil(this.destroy$),
				tap(transNo => {
					this.transNo = transNo;
					console.log('Transaction number updated:', transNo);
				})
			)
			.subscribe();

		this.sysStatus$ = this.store.select(sysSelectors.selectSysConfig);
		this.sysStatus$.subscribe((sysconfig) => {
			this.sysStatus = sysconfig
		}
		);

		console.log(this.modalButtons);
		this.selectedCustomerClubMember$ = this.store.select(customerClubSearchSelectors.selectedCustomerClubMember);
		this.selectedCustomerClubMember$.subscribe((s) => { this.selectedCustomerClubMember = s, console.log(this.selectedCustomerClubMember) });

		this.cart$ = this.store.select(cartSelectors.cart);
		this.cart$.subscribe(
			(s) => this.cart = s
		);
		this.cTrans$ = this.store.select(paymentSelector.ctrans)
		this.cTrans$.subscribe(
			(s) => this.cTrans = s)

		this.total$ = this.store.select(cartSelectors.total);
		this.total$.subscribe((t) => {
			this.cartTotal = t;

			let newTransaction = new Transaction(t);

			// Add already done eftpos payment if needed
			if (this.passedAmount) {
				const passedPayment = new Payment();
				passedPayment.amount = this.passedAmount;
				passedPayment.type = PaymentType.Eftpos;
				passedPayment.desc = `Eftpos`;
				passedPayment.paid = true;
				newTransaction.addPayment(passedPayment);
				newTransaction.acceptedEft(this.passedAmount);
			}

			// Emit the updated transaction:
			this.transactionSubject.next(newTransaction);
			this.transaction = newTransaction;

			this.laybyMinDeposit = financialRound(t * this.laybyDepositProp);
			console.log("Deposit: ", this.laybyMinDeposit);
			//console.log("new transaction", this.transaction);
			this.store.select(laybyPaymentSelectors.isLaybyOrderInProgress).pipe(take(1)).subscribe(active => {
				if (active) {
					// Set the local flag to true
					this.newLayby(this.selectedCustomerClubMember);
					// Reset the store flag to false
					this.store.dispatch(laybyPaymentActions.setLaybyOrderInProgress({ inProgress: false }));
				}
			});
		});
		//console.log('trans',this.transaction);
		//this.transaction$ = of(this.transaction);

		this.transaction$.subscribe(t => {
			console.log("Transaction has changed: ", t);
			this.laybyDepositDue = this.laybyMinDeposit - this.transaction.amountTendered;
		});

		this.transaction$.pipe(skip(1)).subscribe(t => {
			if (t.amountDue <= 0) {
				console.log("Let's check...");

				// Special handling for laybys where excess payment is applied to deposit (no change given)
				if (this.laybyActive && t.amountDue < 0 && t.change === 0) {
					console.log("Layby overpaid, excess applied to deposit. Skipping auto-process.");
					// Do not automatically call process() here to allow manual confirmation
					// The user will click the process button which will then call this.process()
					// We still set alreadyAutoRequestedConfirmation to true to prevent multiple modals if user navigates away and back
					this.alreadyAutoRequestedConfirmation = true; 
				} else {
					// If we haven't asked before
					if (!this.alreadyAutoRequestedConfirmation) {
						this.process();
						this.alreadyAutoRequestedConfirmation = true;
					}
				}
			}
		});

		this.laybyActive$ = this.store.select(laybySelectors.active);
		this.laybyActive$.pipe(
			takeUntil(this.destroy$)
		).subscribe(active => {
			this.laybyActive = active;
			if (active) {
				this.store.select(laybySelectors.laybyNumber)
					.pipe(
						takeUntil(this.destroy$),
						tap(laybyNumber => {
							this.laybyCode = laybyNumber;
							console.log('Layby Code: ', this.laybyCode);
						})
					)
					.subscribe();
				this.transaction.store = this.store;
				this.transaction.activateLayby(active);
				this.transTypeToUse = 4; // LAYBY
				this.modalButtons = laybyModalButtons;
			} else {
				this.transaction.activateLayby(active);
				// Only reset to SALE_TRANSTYPE if not in exchange mode
				if (!this.isExchangeMode) {
					this.transTypeToUse = this.SALE_TRANSTYPE;
				}
				this.modalButtons = regularModalButtons;
			}
		});

		// Add subscription to get current suspend sale number
		this.store.select(suspendSaleSelectors.selectCurrentSuspendSaleNo)
			.subscribe(suspendNo => {
				this.currentSuspendSaleNo = suspendNo;
				console.log('Current suspend sale number:', suspendNo);
			});

		// Add subscription to softCreditLimit
		this.store.select(sysSelectors.selectSoftCreditLimit)
			.pipe(takeUntil(this.destroy$))
			.subscribe(limit => {
				this.softCreditLimit = limit || 'F';
			});
		this.store.select(sysSelectors.CustomerAccount)
			.pipe(takeUntil(this.destroy$))
			.subscribe(limit => {
				this.CustomerAccount = limit || 'T';
				const index = regularModalButtons.findIndex(button => button.type === PaymentType.CustomerAccount);
				if (this.CustomerAccount === 'T' && index === -1) {
					regularModalButtons.splice(2, 0, new PaymentModalButton("Customer Account", 'fa-user-crown text-success', PaymentType.CustomerAccount));
				} else if (this.CustomerAccount !== 'T' && index !== -1) {
					regularModalButtons.splice(index, 1);
				}
			});
		this.store.select(sysSelectors.selectPointsOnAllSales)
			.pipe(takeUntil(this.destroy$))
			.subscribe(limit => {
				this.pointsOnAllSales = limit || 'F';
			});
		this.store.select(sysSelectors.OpenCashTill)
			.pipe(takeUntil(this.destroy$))
			.subscribe(limit => {
				this.alwaysOpenCashTill = limit || 'F';
			});
		this.store.select(sysSelectors.DollarPerPoints)
			.pipe(takeUntil(this.destroy$))
			.subscribe(limit => {
				this.DollarPerPoints = limit || 0;
			});
		this.store.select(sysSelectors.PointsPerDollar)
			.pipe(takeUntil(this.destroy$))
			.subscribe(limit => {
				this.PointsPerDollar = limit || 0;
			});

		this.store.select(sysSelectors.CustomerNameOnReceipt)
			.pipe(takeUntil(this.destroy$))
			.subscribe(limit => {
				this.customerNameOnReceipt = limit || 'F';
			});

		this.laybyDepositDue$ = this.transaction$.pipe(
			takeUntil(this.destroy$),
			map(transaction => this.laybyMinDeposit - transaction.amountTendered)
		);
		// Add this subscription
		this.reasons$ = this.store.select(cartSelectors.reasons);
		this.reasons$.subscribe(
			(s) => this.reasons = s
		);

		this.saleNote$ = this.store.select(saleNoteSelectors.selectSaleNote);
	}

	laybyStatusChanged(active: boolean) {
		if (active) {
			this.modalButtons = laybyModalButtons;
		}
		else {
			this.modalButtons = regularModalButtons;
		}
	}

	async checkIntegratedEftpos(): Promise<void> {
		// const transNo = this.transNo.toString();
		// update the ctrans
		// const updatedCtrans = this.cTrans
		let intEftPayments = new Array;
		let intEft = false;
		let intAmount = 0;
		for (let payment of this.transaction.payments) {
			if (payment.desc === "Integrated Eftpos" && payment.paid != true) {
				intEftPayments.push(payment);
				intEft = true;
				intAmount += payment.amount;
			}
		}

		// Delete suspend sale if exists
		if (this.currentSuspendSaleNo) {
			this.store.dispatch(SuspendSaleActions.deleteSuspendSale({
				suspendNo: this.currentSuspendSaleNo
			}));
		}
		console.log(this.sysStatus)

		if (intEft) {
			let first = true;
			// Create suspend sale in case something goes wrong
			this.suspendSale(intEft);
			let allSuccessful = true;
			for (const eftPayment of intEftPayments) {
				const modalRef = this.modalService.open(WaitingForEftposModalComponent, {
					size: 'md',
					centered: true,
					backdrop: 'static',
					keyboard: false
				});

				switch (this.sysStatus.integratedEFTProvider) {
					case "Linkly":
						modalRef.componentInstance.tenderAmount = eftPayment.amount;
						modalRef.componentInstance.totalAmount = this.cartTotal;
						modalRef.componentInstance.totalEftAmount = intAmount;
						modalRef.componentInstance.store = this.store;
						modalRef.componentInstance.discountAmt = 0; // TODO: calculate discount if needed
						modalRef.componentInstance.surchargeAmt = eftPayment.amount * 0; // TODO: adjust surcharge calculation if required
						modalRef.componentInstance.taxAmt = eftPayment.amount * 0; // TODO: adjust tax calculation based on config
						modalRef.componentInstance.transNo = this.transNo;
						modalRef.componentInstance.items = mapCartToLinklyBasket(this.cart);
						modalRef.componentInstance.transType = "Purchase";
						break;

					case "Tyro":
						modalRef.componentInstance.tenderAmount = intAmount;
						modalRef.componentInstance.transNo = this.transNo;
						modalRef.componentInstance.transType = "Purchase";
						break;

					default:
						console.log("Integrated EFTPOS not configured");
						return;
				}

				try {
					if (!first) { // Tyro issues without this
						await new Promise(resolve => setTimeout(resolve, 3000));
					}
					const result: any = await modalRef.result;
					console.log(result);
					if (!result) {
						// If no result, update state and exit the loop.
						this.transaction.removePayment(eftPayment);
						this.store.dispatch(transActions.resetTransactionConfirmation());
						// this.store.dispatch(transActions.getTransactionNo());
						this.suspendSaleClient.resetSuspendSaleStatus(false).subscribe();
						this.suspendSaleClient.updateSuspendEftPaid(this.transaction.successfulEft).subscribe();

						console.log("EFTPOS payment failed or was cancelled");
						allSuccessful = false;
						break;
					} else {
						console.log("EFTPOS payment result:", result);
						this.transaction.acceptedEft(eftPayment.amount)

						this.suspendSaleClient.updateSuspendEftPaid(this.transaction.successfulEft).subscribe();
						this.suspendSaleClient.resetSuspendSaleStatus(false).subscribe();

						eftPayment.paid = true;

						if (result.surchargePayment) {
							this.transaction.addPayment(result.surchargePayment);
						}

						if (result.despiteErrorPayment) {
							this.eftposService.logToBackend(`Payment manually accepted : $${result.despiteErrorPayment.amount}. TransNo ${this.transNo}.`)
							this.transaction.addPayment(result.despiteErrorPayment, this.sysStatus.multiAllowed && true);
						}

						first = false;

						this.eftposService.getReceipts(this.transNo, false)
							.subscribe((receipts: GetReceiptDto[]) => {
								this.intEftReceipts = receipts;
								if (receipts) {
									this.printService.printEftposReceipt([receipts[receipts.length - 1]], true); // Just print final one
								}
							});
					}
				} catch (error) {
					console.error("Error in waiting-for-EFTPOS modal:", error);
					this.suspendSaleClient.resetSuspendSaleStatus(false).subscribe();
					this.suspendSaleClient.updateSuspendEftPaid(this.transaction.successfulEft).subscribe();
					allSuccessful = false;
					break;
				}
			}

			// Only continue if every modal returned a result.
			if (allSuccessful) {
				this.suspendSaleClient.resetSuspendSaleStatus(true).subscribe();
				if (this.transTypeToUse === 4) {
					this.processLayby();
					this.processSale();
				} else {
					this.processSale();
				}

				this.saleTransactionCompleted();

			}
		}

		else {
			if (this.transTypeToUse == 4) {
				this.processLayby();
				this.processSale();
			}
			else this.processSale();
			this.saleTransactionCompleted()

		}

	}

	async executePrintAndFinalize(receiptTrans: ReceiptTransactionDto, voucherBalances?: VoucherBalanceInfo[]) {
		const logs = receiptTrans.logs;
		const pays = receiptTrans.pays;
		const refs = receiptTrans.refs;
		const change = this.transaction.change;
		const transType = receiptTrans.transType;
		const customerCode = this.selectedCustomerClubMember && this.customerNameOnReceipt == 'T' ? this.selectedCustomerClubMember.clientCode : undefined;
		const customerName = this.selectedCustomerClubMember && this.customerNameOnReceipt == 'T' ? `${this.selectedCustomerClubMember.firstname} ${this.selectedCustomerClubMember.surname}` : undefined;

		console.log("Printing receipt...", { logs, pays, transType, currentTransNo: this.transNo, options: SolemateReceiptOptions.default(), receiptTrans, change, refs, voucherBalances });

		if (this.laybyActive) {
			console.log("Printing layby receipt...", { total: this.cartTotal, tendered: this.transaction.amountTendered });
			const totalLaybyValue = this.cartTotal;
			const depositPaid = this.transaction.amountTendered;
			const remainingBalance = totalLaybyValue - depositPaid;
			console.log("Remaining balance:", remainingBalance);

			await this.printService.printLaybyReceipt(
				"Layby",
				logs,
				pays,
				4,
				this.transNo.toString(),
				SolemateReceiptOptions.default(),
				receiptTrans,
				remainingBalance,
				change,
				refs,
				customerCode,
				customerName,
				this.laybyCode,
				voucherBalances,
				this.newCustomerPointsTotal,
				this.pointsEarned
			);
			this.modalService.dismissAll();
		} else {
			// Find customer account payment details if they exist
			const customerAccountPayment = this.transaction.payments.find(p => p.type === PaymentType.CustomerAccount);
			const accountDetails = customerAccountPayment && customerAccountPayment.customerDetails
				? { code: customerAccountPayment.customerDetails.clientCode, name: customerAccountPayment.customerDetails.name }
				: null;

			await this.printService.printSolemateReceipt(
				this.isExchangeMode ? "Exchange" : "Sale",
				logs,
				pays,
				transType,
				this.transNo.toString(),
				SolemateReceiptOptions.default(),
				receiptTrans,
				change,
				refs,
				customerCode,
				customerName,
				voucherBalances,
				this.newCustomerPointsTotal,
				this.pointsEarned,
				accountDetails // Pass the extracted details
			);
		}
		this.store.dispatch(staffActions.clearStaffLogin());
		this.finalizeTransaction();
		this.store.dispatch(voucherActions.init());
	}

	saleTransactionCompleted(): void {
		// Store the transaction number locally before any potential state changes
		const currentTransNo = this.transNo;
		console.log("Current transaction number:", currentTransNo);

		// Delete suspend sale for all completed transactions
		this.deleteSuspendSale();

		// Init the transaction reducer
		const saleDateTime = new Date();

		const updatedCtrans = this.cTrans.map(c => ({
			...c,
			realdocNo: currentTransNo
		}));

		if (this.cTrans) {
			this.store.dispatch(paymentActions.updateCtrans({ payload: updatedCtrans }))
			console.log(`C trans - ${JSON.stringify(updatedCtrans)}`)
			this.store.dispatch(paymentActions.commitCtrans({ payload: updatedCtrans }))
		}

		this.calculateAndAssignPoints(this.cartTotal, this.cart.some(item => !!item.discountCode), this.transactionDto.payments);

		if (this.alwaysOpenCashTill === 'T') {
			this.store.dispatch(receiptActions.executeBatch({
				payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
			}));
		}
		else if (this.transactionDto.payments.some(payment => payment.paymentType === 'Cash')) {
			this.store.dispatch(receiptActions.executeBatch({
				payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
			}));
		}

		let transactionCopy = this.transactionWithDeposit ? {
			translogs: [...this.transactionDto.translogs],
			payments: [...this.transactionWithDeposit.payments],
			transReferences: this.transactionDto.transReferences ? [...this.transactionDto.transReferences] : [],
			transType: this.isExchangeMode ? this.EXCHANGE_TRANSTYPE : this.SALE_TRANSTYPE
		} : {
			translogs: [...this.transactionDto.translogs],
			payments: [...this.transactionDto.payments],
			transReferences: this.transactionDto.transReferences ? [...this.transactionDto.transReferences] : [],
			transType: this.isExchangeMode ? this.EXCHANGE_TRANSTYPE : this.SALE_TRANSTYPE
		};

		console.log("Transaction payments before printing:", transactionCopy.payments);

		let receiptTrans: ReceiptTransactionDto = {
			logs: transactionCopy.translogs,
			pays: transactionCopy.payments,
			saleDateTime: saleDateTime,
			transType: this.laybyActive ? 4 : (this.isExchangeMode ? this.EXCHANGE_TRANSTYPE : this.SALE_TRANSTYPE),
			refs: transactionCopy.transReferences
		};

		if (this.laybyActive) {
			const totalLaybyValue = this.cartTotal;
			const depositPaid = this.transaction.amountTendered;
			const remainingBalance = totalLaybyValue - depositPaid;
			const customerCode = this.selectedCustomerClubMember && this.customerNameOnReceipt == 'T' ? this.selectedCustomerClubMember.clientCode : undefined;
			const customerName = this.selectedCustomerClubMember && this.customerNameOnReceipt == 'T' ? `${this.selectedCustomerClubMember.firstname} ${this.selectedCustomerClubMember.surname}` : undefined;

			this.printService.printLaybyReceipt(
				"Layby",
				receiptTrans.logs,
				receiptTrans.pays,
				4,
				this.transNo.toString(),
				SolemateReceiptOptions.default(),
				receiptTrans,
				remainingBalance,
				this.transaction.change,
				receiptTrans.refs,
				customerCode,
				customerName,
				this.laybyCode,
				null,
				this.newCustomerPointsTotal,
				this.pointsEarned
			);
		}

		let title = "Sale Completed";
		let text = "The sale was successfully submitted.";
		function cashRound(value: number): number {
			const roundedValue = Math.round(value * 100) / 100;

			const dollars = Math.floor(roundedValue);
			const cents = Math.round((roundedValue - dollars) * 100);

			if (cents % 5 === 0) return roundedValue;

			const nearestLower = Math.floor(cents / 5) * 5;
			const nearestUpper = Math.ceil(cents / 5) * 5;

			const newCents = (cents % 5 < 3) ? nearestLower : nearestUpper;
			return dollars + (newCents / 100);
		}


		if (this.transaction.change > 0) {
			if (cashRound(this.transaction.change) >= 0.05) {
				title = `Change Due: ${cashRound(this.transaction.change).toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })}`;
			} else {
				title = `No Change Due`;
			}
		}

		Swal.fire({
			title: title,
			type: "success", // use 'type' instead of 'icon'
			text: text,
			showCancelButton: true,
			cancelButtonText: "Email Receipt",
			confirmButtonText: "Print Receipt",
			onOpen: () => {
				if (this.laybyActive) {
					const btn = Swal.getConfirmButton() as HTMLButtonElement;
					if (btn) {
						btn.disabled = true;
						let countdown = 3;
						btn.textContent = `Print Receipt (${countdown})`;

						const timer = setInterval(() => {
							countdown--;
							btn.textContent = `Print Receipt (${countdown})`;

							if (countdown <= 0) {
								clearInterval(timer);
								btn.disabled = false;
								btn.textContent = 'Print Receipt';
							}
						}, 1000);
					}
				}
			}
		}).then(async (result) => {
			if (result.value) {
				this.store.select(giftCardPaySelectors.getGiftCardTracking).pipe(take(1)).subscribe(trackingData => {
					console.log("Tracking data:", trackingData);
					const vouchersToLog: VoucherBalanceInfo[] = [];

					for (const voucherNo in trackingData) {
						if (trackingData.hasOwnProperty(voucherNo)) {
							const trackingInfo = trackingData[voucherNo];
							const totalUsedAmount = trackingInfo.payments.reduce((sum, payment) => sum + payment.amount, 0);
							const finalBalance = trackingInfo.originalAmount - totalUsedAmount;
							const voucherType = trackingInfo.payments[0].voucherType;

							vouchersToLog.push({
								type: voucherType,
								number: voucherNo,
								balance: finalBalance
							});
						}
					}

					if (vouchersToLog.length > 0) {
						console.log("Voucher Balances After Sale:", vouchersToLog);
						this.executePrintAndFinalize(receiptTrans, vouchersToLog);
					} else {
						console.log("No relevant voucher payments found");
						this.executePrintAndFinalize(receiptTrans);
					}
				});
			} else if (result.dismiss === Swal.DismissReason.cancel) {
				this.openEmailModal(receiptTrans).then(() => {
					this.store.dispatch(voucherActions.init());
					this.store.dispatch(staffActions.clearStaffLogin());
					this.finalizeTransaction();
				}).catch(() => {
					this.store.dispatch(voucherActions.init());
					this.store.dispatch(staffActions.clearStaffLogin());
					this.finalizeTransaction();
				});
			} else {
				this.store.dispatch(staffActions.clearStaffLogin());
				this.store.dispatch(voucherActions.init());
				this.finalizeTransaction();
			}
		});
	}

	openEmailModal(receiptTrans: ReceiptTransactionDto): Promise<void> {
		return new Promise((resolve) => {
			const modalRef = this.modalService.open(EmailReceiptComponent, {
				size: 'lg',
				backdrop: 'static'
			});

			const customerAccountPayment = this.transaction.payments.find(p => p.type === PaymentType.CustomerAccount);
			const accountDetails = customerAccountPayment && customerAccountPayment.customerDetails
			? { code: customerAccountPayment.customerDetails.clientCode, name: customerAccountPayment.customerDetails.name }
			: null;

			// Pass receiptTrans to the EmailReceiptComponent
			modalRef.componentInstance.accountDetails = accountDetails;
			modalRef.componentInstance.laybyCode = this.laybyCode;
			modalRef.componentInstance.receiptTrans = receiptTrans;
			modalRef.componentInstance.customerSelected = this.selectedCustomerClubMember;
			modalRef.componentInstance.pointsEarned = this.pointsEarned;
			modalRef.componentInstance.newCustomerPointsTotal = this.newCustomerPointsTotal;

			// Check if a customer club member is selected and pass the email
			if (this.selectedCustomerClubMember && this.selectedCustomerClubMember.email) {
				modalRef.componentInstance.customerEmail = this.selectedCustomerClubMember.email;
			}

			modalRef.result.then(() => {
				console.log('Email receipt sent.');
				resolve();  // Resolve the promise once the modal is closed
			}).catch(() => {
				resolve();  // Resolve the promise if the modal is dismissed
			});
		});
	}

	handlePayment(payment: Payment) {
		console.log("Attempting to add payment: ", payment);

		// Check if attempting to add a second Customer Account payment
		if (payment.type === PaymentType.CustomerAccount) {
			const existingAccountPayment = this.transaction.payments.find(p => p.type === PaymentType.CustomerAccount);
			if (existingAccountPayment) {
				Swal.fire({
					title: "Error",
					text: "Only one Customer Account payment is allowed per transaction. Please remove the existing Customer Account payment first.",
					type: "error"
				});
				return; // Stop processing this payment
			}
		}

		if (payment.type === PaymentType.Deposit && payment.desc === "Deposit Update") {
			this.isCustomDepositSet = true;
			this.customDepositAmount = payment.amount;

			this.store.dispatch(laybyActions.startLayby({
				minDeposit: payment.amount
			}));

			this.laybyMinDeposit = payment.amount; 

			this.transaction.updateTotal(this.laybyMinDeposit, true);
			console.log(`Transaction total updated to laybyMinDeposit: ${this.laybyMinDeposit}. Amount tendered: ${this.transaction.amountTendered}. New Amount Due: ${this.transaction.amountDue}`);

			return;
		}

		// Check if this is a cash payment that would exceed the amount due
		if (payment.type === PaymentType.Cash && this.laybyActive &&
			payment.amount > this.transaction.amountDue && this.transaction.amountDue > 0) {

			const extraAmount = financialRound(payment.amount - this.transaction.amountDue);

			Swal.fire({
				title: "Excess Payment",
				html: `The payment amount exceeds the set deposit amount by 
					   <strong>${extraAmount.toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })}</strong>.<br><br>
					   Would you like to provide change or apply the excess to the layby deposit?`,
				type: "question",
				showCancelButton: true,
				confirmButtonText: "Apply to Deposit ($" + payment.amount.toFixed(2) + ")",
				cancelButtonText: "Apply only ($" + (payment.amount - extraAmount).toFixed(2) + ")"
			}).then((result) => {
				if (result.value) {
					// Apply full amount to the deposit without giving change
					let success = this.transaction.addPayment(payment, this.sysStatus.multiAllowed == "T" && true, true);
					if (success) {
						// Workaround for payment service not setting change=0 and negative amountDue correctly for this case
						const finalTendered = this.transaction.amountTendered;
						const currentTotal = this.transaction.total; // This should be the laybyMinDeposit
						const desiredAmountDue = financialRound(currentTotal - finalTendered);

						if (this.laybyActive && payment.type === PaymentType.Cash && desiredAmountDue < 0) {
							console.log(`Layby overpayment by user choice. Original transaction state after addPayment: _change=${(this.transaction as any)['_change']}, _amountDue=${(this.transaction as any)['_amountDue']}, tendered=${finalTendered}, total=${currentTotal}. Public getters: change=${this.transaction.change}, amountDue=${this.transaction.amountDue}`);
							
							// Force the internal state to reflect no change and full overpayment applied
							(this.transaction as any)['_change'] = 0;
							(this.transaction as any)['_amountDue'] = desiredAmountDue; 
							
							console.log(`Forced internal state: _change=${(this.transaction as any)['_change']}, _amountDue=${(this.transaction as any)['_amountDue']}. Public getters now: change=${this.transaction.change}, amountDue=${this.transaction.amountDue}`);
						}

						this.store.dispatch(laybyActions.addToLaybyDeposit({ amount: payment.amount }));
						this.transactionSubject.next(this.transaction);
						this.readyToProcess = this.determineIfReadyToProcess();
					}
				} else if (result.dismiss !== Swal.DismissReason.backdrop &&
					result.dismiss !== Swal.DismissReason.esc &&
					result.dismiss !== Swal.DismissReason.close) {
					// Process normally (will give change)
					let success = this.transaction.addPayment(payment, this.sysStatus.multiAllowed == "T" && true);
					if (success) {
						this.store.dispatch(laybyActions.addToLaybyDeposit({ amount: payment.amount }));
						this.transactionSubject.next(this.transaction);
						this.readyToProcess = this.determineIfReadyToProcess();
					}
				}
			});
		} else {
			// Standard payment process for non-layby or non-excess payments
			let success = this.transaction.addPayment(payment, this.sysStatus.multiAllowed == "T" && true);

			if (success) {
				// Add to layby deposit if layby active
				if (this.laybyActive) {
					this.store.dispatch(laybyActions.addToLaybyDeposit({ amount: payment.amount }));
				}
				this.transactionSubject.next(this.transaction);
				// Payment completed, let's check if we've gone below 0
				// Don't do it if layby active
			} else {
				Swal.fire({
					title: "Error",
					text: "You have attempted a payment which exceeds the due amount."
				});
			}

			this.readyToProcess = this.determineIfReadyToProcess();
		}
	}

	determineIfReadyToProcess() {
		if (this.laybyActive) {
			// If layby is active and soft credit limit is enabled, allow partial payments
			if (this.softCreditLimit === 'T') {
				return this.transaction.amountTendered >= this.laybyMinDeposit;
			}
			// Otherwise require minimum deposit
			return this.transaction.amountTendered >= this.laybyMinDeposit;
		} else {
			// For non-layby transactions, always require full payment
			return this.transaction.amountDue <= 0;
		}
	}

	goHome() {
		this.store.dispatch(orderActions.clearDeposit());
		this.router.navigateByUrl('/home');
	}

	launchCustomerClubModal() {
		const modalRef = this.modalService.open(CustomerClubModalComponent, { size: 'xl', centered: true });
		modalRef.componentInstance.name = 'CustomerClubModal';
		modalRef.result.then((result) => {
			if (result) {
				console.log('result from modal:', result);
			}
		});
	}

	process() {
		// Complete one final check
		if (!this.determineIfReadyToProcess()) {
			// First check if this is a layby with soft credit limit enabled
			if (this.laybyActive && this.softCreditLimit === 'T' && this.transaction.amountTendered >= 0) {
				// Only show warning if component is still active
				if (this.isComponentActive) {
					Swal.fire({
						title: "Warning",
						text: `Set customer deposit is $${this.laybyMinDeposit.toFixed(2)}, but only $${this.transaction.amountTendered.toFixed(2)} has been paid. Do you want to continue anyway?`,
						showCancelButton: true,
						confirmButtonText: "Continue",
						cancelButtonText: "Cancel"
					}).then((result) => {
						if (result.value && this.isComponentActive) {
							this.proceedWithTransaction();
						}
					});
				}
			} else if (this.laybyActive && this.transaction.amountTendered < this.laybyMinDeposit) {
				// Show error for insufficient layby deposit when soft credit is disabled
				if (this.isComponentActive) {
					CreateErrorModal(
						this.modalService,
						true,
						"Please complete the required deposit payment."
					);
				}
			}
			return;
		}

		this.proceedWithTransaction();
	}

	private proceedWithTransaction() {
		console.log("This: ", this.cart);
		let modalRef = this.modalService.open(ProcessPaymentModalComponent, {
			size: 'xl',
			centered: true
		});
		modalRef.componentInstance.name = "Process Payment";
		modalRef.result.then((result) => {
			if (result === 'Process') {
				this.checkIntegratedEftpos();
			}
		}).catch(res => console.log("Error occurred: ", res));
	}

	processSale() {
		// Build out the entire transaction
		let trans = this.getSaleTransaction();
		console.log("Transaction is submitting...", trans);
		// Dispatch an action with the request
		this.transactionDto = trans

		this.store.dispatch(transActions.submitTransaction({ payload: trans }));
	}

	processLayby() {
		console.log("Layby is processing...")
		// Obtain a CreateLaybyDto
		let laybyDto: CreateLaybyDto = this.getLaybyTransaction();

		console.log("Attempting to send: ", laybyDto);

		// Dispatch
		this.store.dispatch(laybyActions.submitLayby({ payload: laybyDto }));
	}

	getLaybyTransaction(): CreateLaybyDto {
		// Get deposit paid
		let deposit = this.transaction.amountTendered - this.transaction.change;

		const actualUnitSellingPrice = this.cart.map(item =>
			item.bestValue !== null && item.bestValue !== undefined
				? item.bestValue
				: item.stockItem.price
		);
		let laybyItems: LaybyItemDto[] = []
		// Get each item and convert to LaybyItem
		for (let i = 0; i < this.cart.length; i++) {
			let cartItem = this.cart[i];
			laybyItems.push(
				{
					lineNo: (i + 1),
					styleCode: cartItem.stockItem.styleCode,
					colourCode: cartItem.stockItem.colourCode,
					sizeCode: cartItem.stockItem.size,
					quantity: cartItem.quantity,
					sellingPrice: actualUnitSellingPrice[i],
					extendedValue: actualUnitSellingPrice[i] * cartItem.quantity,
					transNo: this.transNo,
				} as LaybyItemDto
			);
		}

		return {
			clientId: this.selectedCustomerClubMember.clientCode,
			deposit: deposit,
			laybyItems: laybyItems,
			laybyCode: this.laybyCode
		} as CreateLaybyDto
	}

	getSaleTransaction(): TransactionDto {
		let resTranslogs: TranslogDto[] = [];
		let resTransrefs: TransrefDto[] = [];

		// Log customer details for Customer Account payments for verification
		this.transaction.payments.forEach(payment => {
			if (payment.type === PaymentType.CustomerAccount && payment.customerDetails) {
				console.log(
					`Processing Customer Account Payment - Code: ${payment.customerDetails.clientCode}, Name: ${payment.customerDetails.name}`
				);
			}
		});

		this.saleNote$.pipe(take(1)).subscribe(note => {
			if (note) {
				resTransrefs.push({
					lineNo: 0,
					transReference: note,
					transNo: this.transNo
				} as TransrefDto);
			}
		}); // RESERVE LINE NO 0 FOR SALE NOTE. Alternatively we could make it the final line number. ie get the max line number after (what is written below)and add 1.

		let lineNumber = 0;

		for (let i = 0; i < this.cart.length; i++) {
			let cartItem = this.cart[i];

			lineNumber++;

			// Check if the customer club member exists and has a clientCode
			let clientCode = (this.selectedCustomerClubMember && this.selectedCustomerClubMember.clientCode)
				? this.selectedCustomerClubMember.clientCode
				: undefined;

			resTranslogs.push(cartItemToTranslog(cartItem, lineNumber, this.transNo, clientCode));
			let reasons = this.reasons.get(cartItem.stockItem.barcode);
			if (reasons) {
				for (let reason of reasons) {
					lineNumber++;

					resTranslogs.push({
						styleCode: "Reason",
						lineNo: lineNumber
					} as TranslogDto);

					resTransrefs.push({
						lineNo: lineNumber, // Use the same line number as the translog of 'Reason'
						transReference: reason,
						transNo: this.transNo
					} as TransrefDto);
				}
			}
		}
		resTranslogs.forEach((translog) => {
			translog.transNo = this.transNo;
		});

		// Create the base transaction object
		let transaction: TransactionDto;
		if (this.transaction.toTranspayDtos().some(payment => payment.paymentType === 'Deposit')) {
			// If there's a deposit payment, create a transaction with all payments
			transaction = {
				payments: this.transaction.toTranspayDtos().filter(payment => payment.paymentType !== 'Deposit'),
				translogs: resTranslogs,
				transReferences: resTransrefs,
				transType: this.transTypeToUse
			};
			this.transactionWithDeposit = {
				payments: this.transaction.toTranspayDtos(),
				translogs: resTranslogs,
				transReferences: resTransrefs,
				transType: this.transTypeToUse
			};
			this.store.select(orderSelectors.selectUploadedOrderCode).pipe(take(1)).subscribe(orderCode => {
				if (orderCode) {
					// Dispatch the CompleteOrder action with the retrieved orderCode
					this.store.dispatch(orderActions.completeOrder({ orderNumber: orderCode }));
					this.store.dispatch(orderActions.clearUploadedOrderCode)
				} else {
					console.error("OrderCode not found in the store.");
				}
			});
		} else {
			// If no deposit payment, filter out deposit payments
			transaction = {
				payments: this.transaction.toTranspayDtos(),
				translogs: resTranslogs,
				transReferences: resTransrefs,
				transType: this.transTypeToUse
			};
			this.store.select(quoteSelectors.selectUploadedQuoteCode).pipe(take(1)).subscribe(quoteCode => {
				if (quoteCode) {
					console.log("dispatching complete quote")
					// Dispatch the CompleteQuote action with the retrieved quoteCode
					this.store.dispatch(quoteActions.completeQuote({ quoteCode: quoteCode }));
					this.store.dispatch(quoteActions.clearUploadedQuoteCode)
				} else {
					console.error("QuoteCode not found in the store.");
				}
			});
			this.store.select(orderSelectors.selectUploadedOrderCode).pipe(take(1)).subscribe(orderCode => {
				if (orderCode) {
					// Dispatch the CompleteOrder action with the retrieved orderCode
					this.store.dispatch(orderActions.completeOrder({ orderNumber: orderCode }));
					this.store.dispatch(orderActions.clearUploadedOrderCode)
				} else {
					console.error("OrderCode not found in the store.");
				}
			});
		}

		return transaction;
	}

	clickCount: number = 0;

	removePaymentOnDoubleClick(payment: Payment) {
		console.log("Received..!");
		this.transaction.removePayment(payment);
	}

	newLayby(member: CustomerClubDto) {
		this.store.dispatch(customerClubSearchActions.selectCustomerClubMember({ payload: member }));

		// Check for and store existing pre-paid order deposit
		const prePaidOrderDepositPayment = this.transaction.payments.find(
			p => p.desc === "Pre-paid Order Deposit" && p.type === PaymentType.Deposit
		);

		this.transaction.reset(); // Reset transaction for layby mode

		// If a pre-paid deposit existed, re-add it after the reset
		if (prePaidOrderDepositPayment) {
			console.log("Re-adding pre-paid order deposit after layby activation:", prePaidOrderDepositPayment);
			this.transaction.addPayment(prePaidOrderDepositPayment, false); // Add it back, don't check for multi-allowed here
		}

		// Recalculate laybyMinDeposit based on current cart total and depositPercent
		// This is the minimum total deposit required by store policy for the layby.
		this.laybyMinDeposit = financialRound(this.cartTotal * (this.depositPercent / 100));

		this.store.dispatch(laybyActions.startLayby({
			minDeposit: this.laybyMinDeposit 
		}));
		this.isCustomDepositSet = false;
		this.customDepositAmount = 0;
		// The transaction.amountDue will now be cartTotal - prePaidOrderDeposit (if any)
	}

	finalizeTransaction(): void {
		// Initialize all necessary reducers and clear state AFTER printing is complete
		this.store.dispatch(cartActions.init());
		this.store.dispatch(paymentActions.init());
		this.store.dispatch(transActions.init());

		// Delete suspend sale if it exists (one more check to ensure it's deleted)
		this.deleteSuspendSale();

		// Make sure logged out
		this.store.dispatch(staffActions.clearStaffLogin());


		// Clear suspend sale state in the store
		if (this.currentSuspendSaleNo) {
			this.currentSuspendSaleNo = null;
		}
	}

	suspendSale(intEft: boolean) {
		this.suspendSaleClient
			.getNextSuspendNo()
			.subscribe((suspendNo: number) => {
				this.currentSuspendSaleNo = suspendNo;
				const suspendSale: SuspendSaleDto = {
					header: {
						suspendNo: suspendNo,
						suspendType: 1,
						suspendDate: new Date(),
						suspendTime: new Date().toTimeString().split(" ")[0],
						staffCode: this.staff.code,
						total_Quantity: this.cart.reduce(
							(total, item) => total + item.quantity,
							0
						),
						total_Value: this.cart.reduce(
							(total, item) => total + (item.bestValue * item.quantity),
							0
						),
						active_Sale: "T",
						eft_Attempted: intEft ? "T" : "F",
						eft_Paid: this.transaction.successfulEft
					},
					lines: this.cart.map((item, index) => {
						const discountedPrice = item.bestValue || item.stockItem.price;
						return {
							suspendNo: suspendNo,
							lineNo: index + 1,
							barcode: item.stockItem.barcode,
							styleCode: item.stockItem.styleCode,
							styleDescription: item.stockItem.styleDescription,
							sizeCode: item.stockItem.size,
							colourCode: item.stockItem.colourCode,
							colourName: item.stockItem.colourName,
							quantity: item.quantity,
							recommendedSell: item.stockItem.rrp,
							discountCode: item.discountCode || '',
							discountPcent: item.discountPercent || 0,
							discountValue: item.stockItem.price - discountedPrice,
							sellingPrice: discountedPrice,
							extendedValue: item.quantity * discountedPrice,
						};
					}),
				};

				// Send the SuspendSaleDto payload to the backend
				this.suspendSaleClient.addSuspendSale(suspendSale).subscribe(
					() => {
						console.log("Sale suspended successfully");
						//this.activeModal.dismiss(); // Close the modal
					},
					(error) => {
						console.error("Error suspending sale", error);
					}
				);
			}
			)
	}

	/**
	 * Calculates and assigns loyalty points to a customer based on the sale amount
	 * @param saleAmount The total dollar amount of the sale
	 * @param hasDiscount Whether any item in the cart has a discount
	 * @param transpayDtos The list of TranspayDto objects used in the transaction
	 */
	calculateAndAssignPoints(saleAmount: number, hasDiscount: boolean, transpayDtos: TranspayDto[]): void {
		// Only proceed if there's a selected customer club member
		if (this.selectedCustomerClubMember) {
			// Get the client code from the selected customer
			const clientCode = this.selectedCustomerClubMember.clientCode;

			// Check if any payment type is "Customer Points"
			const customerPointsPayment = transpayDtos.find(payment => payment.paymentType === "Points");

			if (customerPointsPayment) {
				// Calculate the amount of points used
				const pointsUsed = customerPointsPayment.payAmount * this.DollarPerPoints;
				this.newCustomerPointsTotal = this.selectedCustomerClubMember.clientPoints;
				// Create payload for points deduction
				const pointsDeductionPayload: PointsUpdatePayload = {
					clientCode: clientCode,
					pointsToAdjust: -pointsUsed // Deduct points
				};

				// Dispatch the action to deduct points
				this.store.dispatch(customerClubUpdateActions.updatePoints({ payload: pointsDeductionPayload }));
			} else {
				// For regular purchases (not using points as payment)
				let dollarsSpentToEarnOnePoint = this.PointsPerDollar;
				// if dollarsSpentToEarnOnePoint is 0, we should not award any points
				if (dollarsSpentToEarnOnePoint === 0) {
					this.pointsEarned = 0;
					this.newCustomerPointsTotal = this.selectedCustomerClubMember.clientPoints;
					return;
				}

				// Calculate points on a per-item basis
				let totalPointsToAward = 0;

				// Iterate through each cart item
				this.cart.forEach(item => {
					// Skip points calculation if item has discount
					if (item.discountPercent > 0) {
						return; // Skip this item
					}

					// Calculate points for this item based on its total value
					const itemTotal = (item.bestValue || item.stockItem.price) * item.quantity;
					const itemPoints = Math.floor(itemTotal / dollarsSpentToEarnOnePoint);
					totalPointsToAward += itemPoints;
				});

				this.pointsEarned = totalPointsToAward;
				// Always set the new total points, even if no points were earned
				this.newCustomerPointsTotal = this.selectedCustomerClubMember.clientPoints + totalPointsToAward;

				if (totalPointsToAward > 0) {
					// Create payload for points update
					const pointsUpdatePayload: PointsUpdatePayload = {
						clientCode: clientCode,
						pointsToAdjust: totalPointsToAward
					};

					// Dispatch the action to update points
					this.store.dispatch(customerClubUpdateActions.updatePoints({ payload: pointsUpdatePayload }));
				}
			}
		}
	}

	deleteSuspendSale(): void {
		if (this.currentSuspendSaleNo) {
			console.log(`Deleting suspend sale #${this.currentSuspendSaleNo}`);
			this.store.dispatch(SuspendSaleActions.deleteSuspendSale({
				suspendNo: this.currentSuspendSaleNo
			}));

			// Reset the suspend sale client status
			this.suspendSaleClient.resetSuspendSaleStatus(false).subscribe();
			this.suspendSaleClient.resetSuspendSaleStatus(true).subscribe();
		}
	}

	ngOnDestroy() {
		this.isComponentActive = false;
		// Clear the uploaded quotes when the component is destroyed
		this.store.dispatch(quoteActions.clearUploadedQuoteCode());
		this.store.dispatch(orderActions.clearUploadedOrderCode());
		this.store.dispatch(orderActions.clearDeposit());
	}
}
