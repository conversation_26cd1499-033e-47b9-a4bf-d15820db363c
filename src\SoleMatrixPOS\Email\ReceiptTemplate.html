<!DOCTYPE html>
<html>
<head>
	<style>
		body {
			font-family: 'Arial', sans-serif;
			width: 100%;
			max-width: 600px;
			margin: 0 auto;
			padding: 20px;
			color: #333;
		}

		.title {
			text-align: center;
			font-size: 30px;
			margin-bottom: 10px;
		}

		.header {
			text-align: center;
			margin-bottom: 25px;
		}

		.transaction-title {
			font-size: 18px;
			margin-bottom: 15px;
			padding-bottom: 10px;
			border-bottom: 2px solid #eee;
		}

		.items-table {
			width: 100%;
			border-collapse: collapse;
			margin: 20px 0;
		}

			.items-table td {
				padding: 8px 0;
				border-bottom: 1px solid #eee;
			}

		.reasons-table {
			width: 100%;
			border-collapse: collapse;
			margin: 20px 0;
		}

			.reasons-table td {
				padding: 8px 0;
				border-bottom: 1px solid #eee;
			}

		.total-section {
			margin: 25px 0;
			padding: 15px 0;
			border-top: 2px solid #333;
			font-size: 16px;
		}

		.footer {
			text-align: center;
			font-size: 12px;
			color: #666;
			margin-top: 30px;
			padding-top: 20px;
			border-top: 1px solid #eee;
		}

		.text-right {
			text-align: right;
		}

		.notice {
			color: #666;
			margin: 20px 0;
			font-size: 14px;
		}

		.details-section {
			margin: 20px 0;
			padding: 15px 0;
			border-top: 1px solid #eee;
			text-align: center;
		}

		.contact-info {
			margin: 25px 0;
			line-height: 1.6;
			display: inline-block;
		}

		.reprint-notice {
			color: #cc0000;
			font-weight: bold;
			text-align: center;
			border: 2px solid #cc0000;
			padding: 5px;
			margin: 10px 0;
		}

		.customer {
			margin: 20px 0;
		}

		.customer-details {
			display: grid;
			grid-template-columns: max-content 1fr;
			column-gap: 1rem;
			row-gap: 0.25rem;
			padding: 0.5rem 0;
			border-top: 1px solid #eee;
			border-bottom: 1px solid #eee;
			font-size: 0.9rem;
		}

		.customer-details .label {
			font-weight: bold;
			color: #333;
		}

		.customer-details .value {
			color: #555;
		}
	</style>
</head>
<body>
	<div class="title">
		{{TITLE_LINES}}
	</div>
	<div class="header">
		{{REPRINT_NOTICE}}
		{{HEADER_LINES}}
	</div>

	<div class="customer">
		<div class="order-info">
			{{ORDER_CODE_SECTION}}
		</div>
		<div class="order-info">
			{{LAYBY_CODE_SECTION}}
		</div>
		{{CUSTOMER_DETAILS}}
	</div>

	<div class="transaction-title">
		{{TRANSACTION_TYPE}}<br>
		<small>{{TRANSACTION_DATE}}</small>
	</div>

	<table class="items-table">
		<tr>
			{{ITEM_HEADERS}}
		</tr>
		{{ITEMS}}
	</table>

	<div class="total-section">
		{{TOTAL_DETAILS}}
	</div>

	<!-- New placeholder for layby payment details -->
	<div class="layby-payments">
		{{LAYBY_ITEM_ROWS}}
	</div>

	<div class="voucher-section">
		{{VOUCHER_DETAILS}}
	</div>

	<div class="details-section">
		<div class="contact-info" style="text-align: center;">
			{{STAFF_NAME}}<br>
			{{FOOTER_LINES}}
		</div>
	</div>

	<div class="footer">
		{{QUOTE_DISCLAIMER}}
	</div>
	<div class="layby-message">
		{{LAYBY_MESSAGE}}
	</div>
</body>
</html>
