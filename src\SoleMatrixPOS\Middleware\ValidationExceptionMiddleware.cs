using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SoleMatrixPOS.Application.Exceptions;

namespace SoleMatrixPOS.Middleware
{
    public class ValidationExceptionMiddleware : IMiddleware
    {
        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            try
            {
                await next(context);

            }
            catch (DtoValidationException dtoValidationException)
            {
                context.Response.Clear();
                context.Response.StatusCode = StatusCodes.Status400BadRequest;

                string exceptionResponseBody = string.Empty;
                foreach (KeyValuePair<string,string[]> failure in dtoValidationException.Failures)
                {
                    foreach (string failureStr in failure.Value)
                    {
                        exceptionResponseBody += $"Type: {failure.Key} - Failure: {failureStr}\n";
                    }
                    
                }

                byte[] bytes = Encoding.ASCII.GetBytes(exceptionResponseBody);
                MemoryStream stream = new MemoryStream(bytes);

                await context.Response.WriteAsync(JsonConvert.SerializeObject(exceptionResponseBody));

            }
            
        }
    }
}
