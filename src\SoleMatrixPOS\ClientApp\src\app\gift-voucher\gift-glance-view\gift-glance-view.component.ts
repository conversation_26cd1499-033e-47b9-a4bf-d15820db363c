import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import * as giftVoucherAction from '../../reducers/gift-voucher/gift-voucher.actions';
import { FormGroup } from '@angular/forms';
import { CustomerClubModalComponent } from 'src/app/customer-club/customer-club-modal/customer-club-modal.component';


@Component({
  selector: 'pos-gift-glance-view',
  templateUrl: './gift-glance-view.component.html',
  styleUrls: ['./gift-glance-view.component.scss']
})
export class GiftGlanceViewComponent implements OnInit {

  constructor(private modalService: NgbModal, private router: Router, private store: Store<AppState>) { }

  ngOnInit() {

    this.store.dispatch(giftVoucherAction.init());

  }


}
