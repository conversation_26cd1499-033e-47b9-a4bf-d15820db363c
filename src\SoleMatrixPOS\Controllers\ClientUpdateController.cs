using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using SoleMatrixPOS.Application.Client;
using SoleMatrixPOS.Application.Client.Queries;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using SoleMatrixPOS.Application.Client.Commands;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class ClientUpdateController : ControllerBase
	{
		private readonly IMediator _mediator;

		public ClientUpdateController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[HttpPut]
		public async Task<CustomerClubDto> UpdateMember([FromBody] CustomerClubDto customerClubDto)
		{
			return await _mediator.Send(new ClientUpdateCommand(customerClubDto));
		}

		[HttpPut("points")]
		public async Task<ActionResult<int>> UpdatePoints([FromBody] ClientPointsUpdateDto pointsUpdateDto)
		{
			if (string.IsNullOrEmpty(pointsUpdateDto.ClientCode))
			{
				return BadRequest("Client code is required");
			}

			var updatedPoints = await _mediator.Send(
				new ClientUpdatePointsCommand(pointsUpdateDto.ClientCode, pointsUpdateDto.PointsToAdjust)
			);

			return Ok(updatedPoints);
		}
	}

	public class ClientPointsUpdateDto
	{
		public string ClientCode { get; set; }
		public int PointsToAdjust { get; set; }
	}
}
