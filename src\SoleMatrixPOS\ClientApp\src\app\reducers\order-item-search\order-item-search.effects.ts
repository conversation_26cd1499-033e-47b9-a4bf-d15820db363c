// src/app/reducers/order-item-search/order-item-search.effects.ts

import { Injectable } from '@angular/core';
import { Actions, ofType, createEffect } from '@ngrx/effects';
import * as orderSearchActions from './order-item-search.actions';
import { catchError, map, mergeMap, tap } from 'rxjs/operators';
import { EMPTY, of } from 'rxjs';
import { OrderSearchClient, CreateOrderDto } from '../../pos-server.generated';
import { HttpClient } from '@angular/common/http'; // Assuming you're using HttpClient

@Injectable()
export class OrderSearchEffects {
  matchedOrders$ = createEffect(() =>
    this.actions$.pipe(
      ofType(orderSearchActions.search),
      tap((action) => console.log('Dispatching orderSearchActions.search with:', action.searchParams)),
      mergeMap((action) =>
        this.orderSearchClient.get(action.searchParams).pipe(
          tap((foundOrders) => console.log('Received orders from API:', foundOrders)),
          map((foundOrders: CreateOrderDto[]) =>
            orderSearchActions.searchResponse({ payload: foundOrders })
          ),
          catchError((error) => {
            console.error('Error fetching orders:', error);
            return EMPTY;
          })
        )
      )
    )
  );

  // Effect for Cancelling Order
  // cancelOrder$ = createEffect(() =>
  //   this.actions$.pipe(
  //     ofType(orderSearchActions.cancelOrder),
  //     mergeMap((action) =>
  //       this.orderSearchClient.cancelOrder(action.orderCode).pipe(
  //         map(() => orderSearchActions.cancelOrderSuccess({ orderCode: action.orderCode })),
  //         catchError((error) => of(orderSearchActions.cancelOrderFailure({ error })))
  //       )
  //     )
  //   )
  // );

  constructor(
    private actions$: Actions,
    private orderSearchClient: OrderSearchClient,
    private http: HttpClient // If needed
  ) {}
}
