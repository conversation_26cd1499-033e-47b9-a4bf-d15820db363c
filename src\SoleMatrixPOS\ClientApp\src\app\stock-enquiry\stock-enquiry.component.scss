$stock-color-square: #c0c0c0;
$sale-color-square: #86cfda;
$sale-qty-color-square: #ff0000;

.stock-border-square {
    border: 1px solid $stock-color-square;
    text-align: center;
}

.sale-qty-color-square {
    color: #ff0000;
    font-weight: bold;
}

.sale-border-square {
    border: 1px solid $sale-color-square;
    text-align: center;
}

.title-border-square {
    text-align: center;
    border: 1px solid $sale-color-square;
}

.border-square {
    border: 1px solid #c0c0c0;
}

.border-square-total {
    border: 1px solid #c0c0c0;
    text-align: center;
}

td {
    height: 3em;
}

td:hover,
th:hover {
    cursor: default;
}

/* Sticky header styling */
.sticky-header {
    position: sticky;
    top: 0;
    background-color: #fff; /* Ensure background color is white */
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Optional shadow for visibility */
    border-bottom: 1px solid #ddd; /* Optional border for separation */
}

/* Border around content */
.border-wrapper {
    border: 1px solid #c0c0c0;
    padding: 15px;
    background-color: #fff;
    width: 100%;
    max-width: 100%;
    margin: 0;
}

/* Remove default margin and padding from the body */
body {
    margin: 0;
    padding: 0;
}

/* Remove margin and padding from navbar */
.navbar {
    margin: 0;
    padding: 0;
    border-bottom: none;
}

/* Remove margin from container to eliminate space */
.container-fluid {
    margin: 0;
    padding: 0;
    max-width: 100%;
}

/* Optional: if there's any margin added to .border-wrapper, remove it */
.border-wrapper {
    margin: 0;
    padding: 15px; /* Adjust if needed */
}

/* Main tables container */
.main-tables {
    margin: 0;
    width: 100%;
    
    .table-responsive {
        margin: 0;
        padding: 0;
    }
}