import { createAction, props } from '@ngrx/store';
import { StaffLoginDto, StaffLoginResponseDto, FloatDto, StaffClockInResponseDto } from 'src/app/pos-server.generated';


// dispatch to perform a login
export const staffLogin = createAction('[Staff] Login', props<{code: string}>());
// process the result from a login response
export const staffLoginResponse = createAction('[Staff] StaffLoginResponse', props<{payload: StaffLoginResponseDto }>());
// clear current login
export const clearStaffLogin = createAction('[Staff] ClearStaffLogin');
// raise when staff login process is complete and we can continue to main screen
export const staffLoginComplete = createAction('[Staff] StaffLoginComplete');


// user has clicked 'ok' to perform clockin
export const clockIn = createAction('[Staff] ClockIn');

// process server repsponse from a clock in request
export const clockInResponse = createAction('[Staff] ClockInResponse', props<{ payload: StaffClockInResponseDto }>());

// clock out
export const clockOut = createAction('[Staff] ClockOut');

// user has clicked 'ok' to perform daily float enter
export const dailyFloatEnter = createAction('[Staff] DailyFloatEnter', props<{payload: FloatDto }>());
export const dailyFloatEnterResponse = createAction('[Staff] DailyFloatEnterResponse');

export const dailyTotalEnterResponse =  createAction('[Staff] DailyTotalEnterResponse');

export const endOfDayClockOutAllStaff = createAction(
    '[Staff] End of Day - Clock Out All Staff'
);

export const endOfDayClockOutAllStaffSuccess = createAction(
    '[Staff] End of Day - Clock Out All Staff Success'
);

export const endOfDayClockOutAllStaffFailure = createAction(
    '[Staff] End of Day - Clock Out All Staff Failure',
    props<{ error: any }>()
);