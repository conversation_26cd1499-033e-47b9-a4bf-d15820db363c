import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CustomerClubDto } from 'src/app/pos-server.generated';
import { ClientSearchClient } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import { OnHoldSearchComponent } from './on-hold/on-hold-search/on-hold-search.component';
import * as customerClubSearchSelectors from '../reducers/customer-club/club-search/customer-club.selectors';
import * as customerClubSearchActions from '../reducers/customer-club/club-search/customer-club.actions';
import * as customerAddActions from '../reducers/customer-club/customer-add/customer-add.actions';
import * as customerAddSelectors from '../reducers/customer-club/customer-add/customer-add.selectors';
import * as customerClubUpdateActions from '../reducers/customer-club/customer-update/customer-update.actions';
import * as customerClubUpdateSelectors from '../reducers/customer-club/customer-update/customer-update.selectors';
import { CustomerOrdersSearchComponent } from './customer-orders/customer-orders-search/customer-orders-search.component';
import { Observable } from 'rxjs';
import { filter, switchMap, take } from 'rxjs/operators';
import { TemplateRef, ViewChild } from '@angular/core';
import { ClientSearchRequestDto, ClientSearchKeywordColumnDto, ClientSearchOrderByColumnDto, StockSearchOrderByDirectionEnumDto } from 'src/app/pos-server.generated';
import { CustomerQuotesSearchComponent } from './customer-quotes/customer-quotes-search/customer-quotes-search.component';
import * as orderActions from '../reducers/order-item/order.actions';
import * as cartActions from '../reducers/sales/cart/cart.actions';
import Swal from 'sweetalert2';

@Component({
  selector: 'pos-customer-specials',
  templateUrl: './customer-specials.component.html',
  styleUrls: ['./customer-specials.component.scss']
})
export class CustomerSpecialsComponenet implements OnInit {
  customerOrderForm: FormGroup;
  selectedClubMember: CustomerClubDto | null = null;
  handlingCustClub: boolean = false;
  mode: 'order' | 'quote' | 'hold' = 'order';
  isLoading: boolean = false;
  isCreateEntryContext: boolean = false;

  @ViewChild('content', { static: true }) searchModal: TemplateRef<any>;
  @ViewChild('confirmExistingMemberModal', { static: true }) confirmExistingMemberModal: TemplateRef<any>;

  constructor(
    private formBuilder: FormBuilder,
    private store: Store<AppState>,
    private router: Router,
    private modalService: NgbModal,
    private clientSearchClient: ClientSearchClient
  ) {}

  ngOnInit() {
    this.customerOrderForm = this.formBuilder.group({
      clubNumber: [''],
      title: [''],
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      street: [''],
      suburb: [''],
      state: [''],
      postcode: [''],
      phone: ['', Validators.pattern('^\\d{8,11}$')],
      email: ['', Validators.email]
    }, { 
      validators: this.phoneOrEmailRequired 
    });
    
    this.customerOrderForm.disable();

    this.store.dispatch(orderActions.setDeposit({ deposit: 0 }));
    this.store.dispatch(cartActions.init())

    this.store.select(customerClubSearchSelectors.selectedCustomerClubMember).subscribe(
      (selected) => {
        this.selectedClubMember = selected;
        if (selected != null) this.fillFormWithCustClubInfo(selected);
      }
    );
  }

  // Custom validator to ensure either phone or email is provided
  phoneOrEmailRequired(group: FormGroup): {[key: string]: any} | null {
    const phone = group.get('phone').value;
    const email = group.get('email').value;
    
    return (phone || email) ? null : { 'phoneOrEmailRequired': true };
  }

  // Open the Search Customer Orders modal
  openSearchOrdersModal() {
    this.store.dispatch(orderActions.setDeposit({ deposit: 0 }));
    this.store.dispatch(cartActions.init())
    const modalRef = this.modalService.open(CustomerOrdersSearchComponent, { centered: true, size: 'xl' });

    modalRef.result.then((result) => {
    }).catch((reason) => {
      console.log('Modal dismissed:', reason);
    });
  }

  // Existing methods
  searchCustomer() {
    this.isCreateEntryContext = false;
    this.modalService.open(this.searchModal, { centered: true, size: 'xl' });
  }

  onBack() {
    this.handlingCustClub = false;
  }

  onUseSelected() {
    if (this.selectedClubMember) {
      this.modalService.dismissAll();
      if (this.isCreateEntryContext) {
        this.navigateToEntryItem();
      }
    }
  }

  fillFormWithCustClubInfo(clubInfo: CustomerClubDto) {
    this.customerOrderForm.setValue({
      clubNumber: clubInfo.clientCode,
      title: clubInfo.title,
      firstName: clubInfo.firstname,
      lastName: clubInfo.surname,
      street: clubInfo.street,
      suburb: clubInfo.suburb,
      state: clubInfo.state,
      postcode: clubInfo.postcode,
      phone: clubInfo.telephone,
      email: clubInfo.email
    });
  }

  setMode(newMode: 'order' | 'quote' | 'hold') {
    this.mode = newMode;
  }

  getPageTitle(): string {
    switch (this.mode) {
      case 'order':
        return 'Customer Orders';
      case 'quote':
        return 'Customer Quotes';
      case 'hold':
        return 'On Hold';
      default:
        return 'Customer Specials';
    }
  }

  openSearchModal() {
    let component;
    switch (this.mode) {
      case 'order':
        component = CustomerOrdersSearchComponent;
        break;
      case 'quote':
        component = CustomerQuotesSearchComponent;
        break;
      case 'hold':
        component = OnHoldSearchComponent;
        break;
      default:
        return;
    }

    const modalRef = this.modalService.open(component, { centered: true, size: 'xl' });
    modalRef.result.then((result) => {
    }).catch((reason) => {
      console.log('Modal dismissed:', reason);
    });
  }

  updateMember() {
    if (!this.selectedClubMember) {
      console.error("No selected club member to update.");
      this.isLoading = false; // Stop loading on error
      return;
    }
  
    const updatedCustomer: CustomerClubDto = {
      clientCode: this.selectedClubMember.clientCode, // Ensure clientCode is included
      title: this.customerOrderForm.value.title,
      firstname: this.customerOrderForm.value.firstName,
      surname: this.customerOrderForm.value.lastName,
      street: this.customerOrderForm.value.street,
      suburb: this.customerOrderForm.value.suburb,
      state: this.customerOrderForm.value.state,
      postcode: this.customerOrderForm.value.postcode,
      telephone: this.customerOrderForm.value.phone,
      email: this.customerOrderForm.value.email,
      careof: this.selectedClubMember.careof, 
      clientPoints: this.selectedClubMember.clientPoints, 
      noMail: this.selectedClubMember.noMail 
    };
    console.log('Updating customer:', updatedCustomer);
    // Dispatch action to update customer
    this.store.dispatch(customerClubUpdateActions.updateMember({ payload: updatedCustomer }));
  
    // Wait for update confirmation before proceeding
    this.store.select(customerClubUpdateSelectors.customer)
      .pipe(
        filter(member => !!member), // Ensure the member is updated
        take(1)
      )
      .subscribe({
        next: (updatedMember) => {
          console.log('Customer updated successfully:', updatedMember);
          this.navigateToEntryItem();
        },
        error: (err) => {
          console.error('Error updating customer', err);
          this.isLoading = false; // Stop loading on error
        }
      });
  }

  // Update canProceed getter to use the new validation logic
  get canProceed(): boolean {
    return this.customerOrderForm.valid || 
           (!!this.customerOrderForm.get('clubNumber').value && 
            this.customerOrderForm.get('clubNumber').value !== '');
  }

  createEntry() {
    this.isLoading = true;
    
    // Check if club number exists or form is valid
    const clubNumber = this.customerOrderForm.get('clubNumber').value;
    const hasCustomerSelected = clubNumber && clubNumber !== '';
    
    if (!hasCustomerSelected) {
      // No customer selected, open the search modal
      this.isLoading = false;
      this.isCreateEntryContext = true;
      const modalRef = this.modalService.open(this.searchModal, { centered: true, size: 'xl' });
      return;
    }

    // If we have a customer selected, proceed with navigation
    this.navigateToEntryItem();
  }

  navigateToEntryItem() {
    switch (this.mode) {
      case 'order':
        this.router.navigate(['/customer-specials/order-item']);
        break;
      case 'quote':
        this.router.navigate(['/customer-specials/quote-item']);
        break;
      case 'hold':
        this.router.navigate(['/customer-specials/on-hold']);
        break;
    }
  }

  onMemberSelected(result: CustomerClubDto) {
    this.selectedClubMember = result;
    this.fillFormWithCustClubInfo(result);
    this.store.dispatch(customerClubSearchActions.selectCustomerClubMember({ payload: result }));
  }
}
