import { DailyDto, SalesByStaffQueryDto, SalesByHourQueryDto, MultiSaleDto, DepartmentSalesDto, DailyTotalsDto } from 'src/app/pos-server.generated';
import { createReducer, on, Action } from '@ngrx/store';
import * as dailyActions from './daily.actions'
 
export class DailyState {
    currDaily: DailyDto;
    isLoading: boolean;
    salesByStaff: SalesByStaffQueryDto[];
    refundsByStaff: SalesByStaffQueryDto[];
    salesByHour: SalesByHourQueryDto[];
    refundsByHour: SalesByHourQueryDto[];
    grossAmount: number;
    totalItems: number;
    totalCustomers: number;
    multiSales: MultiSaleDto;
    departmentSales: DepartmentSalesDto[];
    departmentRefunds: DepartmentSalesDto[];
    dailyRange: DailyDto[];
    isRangeMode: boolean;
    dailyRangeTotals: DailyTotalsDto;
    selectedDate?: Date; // Added to track the selected date
}

export const initialState: DailyState = {
    currDaily: null,
    isLoading: false,
    salesByStaff: null,
    refundsByStaff: null,
    salesByHour: null,
    refundsByHour: null,
    grossAmount: null,
    totalItems: null,
    isRangeMode: false,
    totalCustomers: null,
    multiSales: null,
    departmentSales: null,
    departmentRefunds: null,
    dailyRange: null,
    dailyRangeTotals: null,
    selectedDate: null // Initialize as null
}

export const dailyReducer = createReducer(
    initialState,
    on(dailyActions.init, (state) => (initialState)),
    on(dailyActions.storeTodaysDaily, (state, action) => ({...state, currDaily: action.payload, isLoading: false })),
    on(dailyActions.getTodaysDaily, (state, action) => ({...state, isLoading: true, selectedDate: action.date })),
    on(dailyActions.storeSalesByStaffQuery, (state, action) => ({...state, salesByStaff: action.payload, isLoading: false })),
    on(dailyActions.getSalesByStaff, (state, action) => ({...state, isLoading: true, selectedDate: action.date })),
    on(dailyActions.storeRefundsByStaffQuery, (state, action) => ({...state, refundsByStaff: action.payload, isLoading: false })),
    on(dailyActions.getRefundsByStaff, (state, action) => ({...state, isLoading: true, selectedDate: action.date })),
    on(dailyActions.storeSalesByHourQuery, (state, action) => ({...state,
        totalItems: getItems(action.payload),
        totalCustomers: getCusts(action.payload),
        grossAmount: getSales(action.payload),
        salesByHour: action.payload, isLoading: false })),
    on(dailyActions.getSalesByHour, (state, action) => ({...state, isLoading: true, selectedDate: action.date })),
    on(dailyActions.storeRefundsByHourQuery, (state, action) => ({...state, refundsByHour: action.payload, isLoading: false })),
    on(dailyActions.getRefundsByHour, (state, action) => ({...state, isLoading: true, selectedDate: action.date })),
    on(dailyActions.getMultiSales, (state, action) => ({...state, isLoading: true, selectedDate: action.date })),
    on(dailyActions.storeMultiSales, (state, action) => ({...state, multiSales: action.payload, isLoading: false })),
    on(dailyActions.getDepartmentSales, (state, action) => ({...state, isLoading: true, selectedDate: action.date })),
    on(dailyActions.storeDepartmentSales, (state, action) => ({...state, departmentSales: action.payload, isLoading: false })),
    on(dailyActions.storeDepartmentRefunds, (state, action) => ({...state, departmentRefunds: action.payload, isLoading: false })),
    on(dailyActions.getDepartmentRefunds, (state, action) => ({...state, isLoading: true, selectedDate: action.date })),
    // Range handlers remain the same
    on(dailyActions.getDailyRange, (state, action) => ({...state, isLoading: true })),
    on(dailyActions.storeDailyRange, (state, action) => ({...state, dailyRange: action.payload, isLoading: false })),
    on(dailyActions.getDailyRangeTotals, (state, action) => ({...state, isLoading: true })),
    on(dailyActions.storeDailyRangeTotals, (state, action) => ({...state, dailyRangeTotals: action.payload, isLoading: false })),
    on(dailyActions.clearDailyData, (state) => ({
        ...initialState,
        isRangeMode: state.isRangeMode // Preserve the range mode flag
    })),
    on(dailyActions.setRangeMode, (state, { isRangeMode }) => ({
        ...state,
        isRangeMode
    })),
);

function getSales(arr: SalesByHourQueryDto[]) {
    let sum = 0;
    arr.forEach(dto => {
        sum += dto.sales;
    });
    return sum;
}

function getItems(arr: SalesByHourQueryDto[]) {
    let sum = 0;
    arr.forEach(dto => {
        sum += dto.items;
    });
    return sum;
}

function getCusts(arr: SalesByHourQueryDto[]) {
    let sum = 0;
    arr.forEach(dto => {
        sum += dto.custs;
    });
    return sum;
}

export function reducer(state: DailyState | undefined, action: Action) {
    return dailyReducer(state, action);
}
