import { Component, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { PrintingService } from '../printing/printing.service';

@Component({
  selector: 'pos-select-printer-modal',
  templateUrl: './select-printer-modal.component.html',
  styleUrls: ['./select-printer-modal.component.scss']
})
export class SelectPrinterModalComponent implements OnInit {
  printers: string[] = [];
  selectedPrinter: string | null = null;
  printerWidth: number = 48;
  printerWidthOptions: number[] = [];
  dontShowAgain: boolean = false;
  testPrintStatusMessage: string | null = null;
  testPrintStatusType: 'success' | 'error' | null = null;

  constructor(public activeModal: NgbActiveModal, private printingService: PrintingService) {
    // Generate printer width options from 32 to 56 in increments of 8
    for (let width = 32; width <= 56; width += 8) {
      this.printerWidthOptions.push(width);
    }
  }

  ngOnInit() {
    // Fetch the list of printers from the service
    this.fetchPrinters();

    // Prepopulate with the last selected printer
    const lastSelectedPrinter = localStorage.getItem('selectedPrinter');
    if (lastSelectedPrinter) {
      this.selectedPrinter = lastSelectedPrinter;
    } else {
      this.selectedPrinter = null; // Explicitly set to null if nothing is in localStorage
    }

    // Load saved printer width from localStorage
    const savedWidth = localStorage.getItem('printerWidth');
    if (savedWidth) {
      this.printerWidth = parseInt(savedWidth, 10);
    }
  }

  fetchPrinters() {
    this.printingService.getPrinters().subscribe({
      next: (response: any) => {
        if (response && response.printers) {
          this.printers = response.printers;

          // Ensure the last selected printer is still available
          if (this.selectedPrinter && this.printers.indexOf(this.selectedPrinter) === -1) {
            this.selectedPrinter = null;
          }
        } else {
          this.printers = [];
        }
      },
      error: (error) => {
        console.error('Failed to fetch printers:', error);
        this.printers = [];
        // Optionally, set a status message for printer fetching errors
        this.testPrintStatusMessage = 'Failed to fetch printers.';
        this.testPrintStatusType = 'error';
      }
    });
  }

  onPrinterChange() {
    this.clearTestPrintStatus();
  }

  onPrinterWidthChange() {
    this.clearTestPrintStatus();
  }

  clearTestPrintStatus() {
    this.testPrintStatusMessage = null;
    this.testPrintStatusType = null;
  }

  async onTestPrint(): Promise<void> {
    this.clearTestPrintStatus();
    if (!this.selectedPrinter) {
      this.testPrintStatusMessage = 'Please select a printer from the dropdown to test.';
      this.testPrintStatusType = 'error';
      return;
    }
    try {
      await this.printingService.printTestReceipt(this.selectedPrinter, this.printerWidth);
      this.testPrintStatusMessage = `Test print sent to ${this.selectedPrinter}.`;
      this.testPrintStatusType = 'success';
    } catch (error) {
      console.error('Test print failed:', error);
      this.testPrintStatusMessage = 'Failed to send test print.';
      if (error && typeof error === 'object' && error !== null && 'message' in error) {
        this.testPrintStatusMessage += `\nError: ${(error as Error).message}`;
      } else if (typeof error === 'string') {
        this.testPrintStatusMessage += `\nError: ${error}`;
      }
      this.testPrintStatusType = 'error';
    }
  }
  
  save() {
    if (this.selectedPrinter) {
      localStorage.setItem('selectedPrinter', this.selectedPrinter);
    } else {
      localStorage.removeItem('selectedPrinter');
    }
    localStorage.setItem('printerWidth', this.printerWidth.toString());
    this.printingService.setPrinterWidth(this.printerWidth);
    this.activeModal.close({ printer: this.selectedPrinter, width: this.printerWidth });
  }

  continueWithoutPrinter() {
    this.activeModal.close('Continue click');
  }

  dismissModal(reason: string) {
    this.activeModal.dismiss(reason);
  }
} 
