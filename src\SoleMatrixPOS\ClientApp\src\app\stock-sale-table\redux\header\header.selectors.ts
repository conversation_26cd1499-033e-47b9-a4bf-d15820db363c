import { createSelector } from "@ngrx/store";
import { AppState } from "../app.store";


export const getState = (state: AppState) => state;
export const getHeaders = (state: AppState) => state.headers[state.page];

export const getHeadersLength = createSelector(getState, (state) => {
    if (state.headers.length != undefined) {
        return state.headers.length
    } else {
        return Number.MAX_SAFE_INTEGER
    }
});

export const getPage = createSelector(getState, (state) => {
    return state.page
})

export const getCost = createSelector(getState, (state) => {
    if (state.headers[state.page] != undefined) {
        return state.headers[state.page].lastReceiptedCost
    } else {
        return Number.MIN_SAFE_INTEGER
    }
})
