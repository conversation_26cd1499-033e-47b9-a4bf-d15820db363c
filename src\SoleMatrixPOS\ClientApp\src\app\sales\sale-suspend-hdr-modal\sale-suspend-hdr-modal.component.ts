import { Component, Input, OnInit, <PERSON><PERSON><PERSON>roy } from "@angular/core";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { Store } from "@ngrx/store";
import { AppState } from "src/app/reducers";
import { SuspendSaleHdrDto } from "src/app/pos-server.generated"; // Adjust this import to match the location of the DTO
import { Observable, Subscription } from "rxjs";
import { HttpClient } from "@angular/common/http";
import * as SuspendSaleActions from "src/app/reducers/suspend-sale/suspend-sale.actions";
import * as suspendSaleSelectors from "src/app/reducers/suspend-sale/suspend-sale.selectors";

@Component({
	selector: "pos-sale-suspend-hdr-modal",
	templateUrl: "./sale-suspend-hdr-modal.component.html",
	styleUrls: ["./sale-suspend-hdr-modal.component.scss"],
})
export class SaleSuspendHdrModalComponent implements OnInit, OnDestroy {
	@Input() suspendSaleItems: SuspendSaleHdrDto[] = []; // Array to hold the suspend sale headers
	private subscription: Subscription;

	constructor(
		public activeModal: NgbActiveModal,
		private modalService: NgbModal,
		private http: HttpClient, // HTTP Client for making requests to backend
		private store: Store<AppState> // Store for dispatching actions
	) { }

	ngOnInit(): void {
		// Subscribe to the store to get updates
		this.subscription = this.store
			.select(suspendSaleSelectors.selectSuspendSaleHeaders)
			.subscribe((headers) => {
				if (headers) {
					this.suspendSaleItems = headers;
					if (headers.length === 0) {
						this.close();
					}
				}
			});
	}

	ngOnDestroy(): void {
		if (this.subscription) {
			this.subscription.unsubscribe();
		}
	}

	fetchSuspendSaleHeaders(): void {
		this.http.get<SuspendSaleHdrDto[]>("api/suspendsale/headers").subscribe(
			(data) => {
				this.suspendSaleItems = data; // Assign fetched data to the component property
			},
			(error) => {
				console.error("Error fetching suspend sale headers", error);
			}
		);
	}

	onSuspendSaleClick(suspendSale: SuspendSaleHdrDto): void {
		console.log("Suspend Sale clicked:", suspendSale);
		this.store.dispatch(SuspendSaleActions.selectSuspendSale({ suspendNo: suspendSale.suspendNo }));
		this.activeModal.close(); // Close the modal after dispatching
	}

	// Method to handle delete item click
	// onTableItemDeleteClicked(index: number): void {
	// 	const suspendNo = this.suspendSaleItems[index].suspendNo;
	// 	// Perform deletion logic, maybe call backend API for deletion
	// 	console.log("Delete suspend sale with Suspend No:", suspendNo);
	// 	// After deleting, remove from the local list
	// 	this.suspendSaleItems.splice(index, 1);
	// }
	onTableItemDeleteClicked(index: number): void {
		const suspendNo = this.suspendSaleItems[index].suspendNo;
		console.log("Delete suspend sale with Suspend No:", suspendNo);
		this.store.dispatch(
			SuspendSaleActions.deleteSuspendSale({ suspendNo })
		);
	}


	// Close the modal
	close(): void {
		this.activeModal.dismiss();
		//this.modalService.dismissAll();
	}
}
