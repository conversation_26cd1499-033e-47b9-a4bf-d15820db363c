import {Action, createReducer, on} from '@ngrx/store';
import * as laybyPaymentActions from './layby-payment.actions';
import {LaybylineDto, LaybySearchQueryDto, LaybySearchResultDto, MakeLaybyPaymentResultDto, SortDirection, StockItemDto, StockSearchFields, StockSearchRequestDto} from '../../pos-server.generated';
import {LaybyItem, LaybyPayment, LaybyPaymentType} from './layby-types'
import { Payment } from 'src/app/payment/payment.service';

export class LaybyPaymentState {
	layby: LaybySearchResultDto;
	isLoading: boolean;
	items: LaybyItem[];
	completedPayments: LaybyPayment[];
	pendingPayments: Payment[];
	balances: Array<number>;
	lastPaymentResult: MakeLaybyPaymentResultDto | null;
	laybyOrderInProgress: boolean;
	completed: boolean;
}

export const initialState: LaybyPaymentState = {
	layby: null,
	isLoading: false,	
	items: [],
	completedPayments: [],
	pendingPayments: [],
	balances: [null, null, null],
	laybyOrderInProgress: false,
	completed: false,
	lastPaymentResult: null

} as LaybyPaymentState;

export const laybyPaymentReducer = createReducer(initialState,
	on(laybyPaymentActions.init, (state, action) => ({...initialState})),
	on(laybyPaymentActions.setLayby, (state, action) => ({...state, isLoading: true, layby: action.layby})),
	on(laybyPaymentActions.getLaybyLinesResponse, (state, action) => {
		console.log("Got lines!");
		const [items, payments] = mapAndSeparate(action.lines);
		const balancesValues = calculateTotals(items, payments, state.pendingPayments);
		return {...state, isLoading: false, items: items, completedPayments: payments, balances: balancesValues};
	}),
	on(laybyPaymentActions.addPendingPayment, (state, action) => {
		let newPayments = addToPendingPayments(state.pendingPayments, action.payment);
		let balancesValues = calculateTotals(state.items, state.completedPayments, newPayments);
		return {...state, pendingPayments: newPayments, balances: balancesValues};
	}),
	on(laybyPaymentActions.laybyRefund, (state, action) => ({...state, isLoading: true})),
	on(laybyPaymentActions.submitRefundCompleted, (state, action) => ({...state, completed: true, isLoading: false})),
	on(laybyPaymentActions.submitPayments, (state) => ({
        ...state,
        isLoading: true,
        completed: false,
        lastPaymentResult: null,
        error: null
    })),
    on(laybyPaymentActions.submitPaymentsSuccess, (state, { payload }) => ({ // <--- HANDLE SUCCESS
        ...state,
        isLoading: false,
        completed: true,
        lastPaymentResult: payload, // <--- STORE THE RESULT
        error: null,
        pendingPayments: [] // Clear pending payments on successful submission
    })),
    on(laybyPaymentActions.submitPaymentsFailure, (state, { error }) => ({ // <--- HANDLE FAILURE
        ...state,
        isLoading: false,
        completed: false, // Or true if "completed" means the attempt is done
        error: error,
        lastPaymentResult: null
    })),
	on(laybyPaymentActions.submitPaymentsCompleted, (state, action) => ({...state, completed: true, isLoading: false})),
	on(laybyPaymentActions.setLaybyOrderInProgress, (state, action) => {
		return { ...state, laybyOrderInProgress: action.inProgress }
	})
);

function addToPendingPayments(existing: Payment[], newP: Payment): Payment[] {
	let newPayments = [];
	let foundDuplicate = false;
	for(let p of existing){
		if(p.type == newP.type){
			console.log(newP)
			foundDuplicate = true;
			newPayments.push({...p, amount: p.amount + newP.amount} as Payment);
		}

		else{
			newPayments.push({...p} as Payment);
		}
	}

	if(!foundDuplicate) newPayments.push(newP);

	return newPayments;
}

export function reducer(state: LaybyPaymentState | undefined, action: Action) {
	return laybyPaymentReducer(state, action);
}

// Enum consts that represent the transtypes in the database
export enum LaybyLineTypes{
	item = 1,
	deposit = 2,
	payment = 3,
	refund = 9
}

/*
	Maps layby lines to 
*/
function mapAndSeparate(lines: LaybylineDto[]): [LaybyItem[], LaybyPayment[]] {
	let items: LaybyItem[] = [];
	let payments: LaybyPayment[] = [];
	for(let line of lines){
		console.log(line.transType + "in reducer");
		switch(line.transType){
			case(LaybyLineTypes.item): {
				items.push(lineToItem(line));
				break;
			}

			case(LaybyLineTypes.payment):
			case(LaybyLineTypes.deposit): 
			case(LaybyLineTypes.refund):{
				payments.push(lineToPayment(line));
				break;
			}

			default: {
				console.warn(`An unrecognised layby transaction type, '${line.transType}', was found. This will not be handled.`)
			}

		}
	}

	return [items, payments];

}

/*
	Maps a line to an item
*/
function lineToItem(line: LaybylineDto): LaybyItem{
	return {
		style: line.styleCode,
		colour: line.colourCode,
		size: line.sizeCode,
		quantity: line.quantity,
		price: line.sellingPrice,
		value: line.extendedValue
	} as LaybyItem;
}

/*
	Maps a line to a payment
*/
function lineToPayment(line: LaybylineDto): LaybyPayment{
	let paymentType: LaybyPaymentType = null;
	switch(line.transType){
		case(2): {
			paymentType = LaybyPaymentType.deposit;
			break;
		}

		case(3): {
			paymentType = LaybyPaymentType.payment;
			break;
		}

		case(9): {
			paymentType = LaybyPaymentType.refund;
			break;
		}

		default: {
			throw Error(`Payment of type '${line.transType}' is not a recognised payment type.`)
		}
	}
	return {
		date: line.styleCode,
		type: paymentType,
		amount: Math.abs(line.extendedValue) // Un-negate this payment
	} as LaybyPayment;
}

function calculateTotals(items: LaybyItem[], madePayments: LaybyPayment[], pendingPayments: Payment[]): [number, number, number] {
	let sale: number = 0, paid: number = 0, due: number = 0;
	
	for(let item of items){
		sale += item.price * item.quantity;
	}

	for(let payment of madePayments){
		paid += payment.amount;
	}

	for(let payment of pendingPayments){
		paid += payment.amount;
	}

	due = sale - paid;
	return [sale, paid, due];

}

