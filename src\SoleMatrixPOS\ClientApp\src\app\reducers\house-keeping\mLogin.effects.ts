import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { EMPTY, of } from "rxjs";
import { catchError, map, mergeMap } from "rxjs/operators";
import { HouseKeepingClient } from "src/app/pos-server.generated";
import * as houseActions from './mLogin.actions';

@Injectable()
export class ManagerEffects {

    date: Date;

    constructor(
        private action$: Actions,
        private managerClient: HouseKeepingClient,
    ) {
        //this.date = new Date();
        //this.date.setMonth(this.date.getMonth() + 1);
    }

    managerLogin$ = createEffect(() => this.action$.pipe(
        ofType(houseActions.mLogin),
        mergeMap((action) => this.managerClient.login(action.password)
            .pipe(
                map(loginResponse => houseActions.mLoginResponse({ payload: loginResponse }),
                    catchError(() => EMPTY)
                )
            )
        )
    ))

}
