<div class="card mx-auto" style="margin-top: 1rem; margin-bottom: 1rem;">
    <div class="card-body">
        <div class="container">
            <div *ngIf="!showMemberDetails">
                <pos-customer-club-container (isEditing)="isEditing($event)"
                    (onSelectedMember)="onMemberSelected($event)"
                    (onMemberDoubleClick)="onUseSelected()"></pos-customer-club-container>
                <hr>

                <div *ngIf="!editing" class="row mt-5">
                    <div class="col-6">
                    </div>
                    <div class="col-6 text-right">
                        <div>
                            <button class="btn btn-outline-default" type="button" (click)="onUseSelected()"
                                [translate]="'customer-club.buttons.Use'">
                                <i class="fas fa-lg fa-fw fa-check-circle text-success mr-2"></i>
                                Use Selected
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div *ngIf="showMemberDetails">
                <pos-customer-club-member [member]="member" (onBack)="onBack()">
                </pos-customer-club-member>
            </div>
        </div>
    </div>
</div>