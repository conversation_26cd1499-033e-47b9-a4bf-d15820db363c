import { Component, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
    selector: 'app-discount-confirm-modal',
    template: `
        <div class="modal-header">
            <h4 class="modal-title">Confirm Discount</h4>
            <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss()"></button>
        </div>
        <div class="modal-body">
            <p>Are you sure you want to apply this discount and reason to all items?</p>
            <p class="mb-0"><strong>Type:</strong> {{discountType === 'percent' ? 'Percentage' : 'Amount'}}</p>
            <p class="mb-0"><strong>Value:</strong> {{discountType === 'percent' ? value + '%' : ('$' + value)}}</p>
            <p class="mb-0"><strong>Reason:</strong> {{reason}}</p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Cancel</button>
            <button type="button" class="btn btn-primary" (click)="modal.close(true)">Confirm</button>
        </div>
    `
})
export class DiscountConfirmModalComponent {
    @Input() discountType: 'percent' | 'amount';
    @Input() value: number;
    @Input() reason: string;

    constructor(public modal: NgbActiveModal) { }
} 