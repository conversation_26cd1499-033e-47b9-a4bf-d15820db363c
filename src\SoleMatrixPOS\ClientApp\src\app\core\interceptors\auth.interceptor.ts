import { Injectable } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpEvent,
  HttpHandler,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import jwtDecode from 'jwt-decode';
import { map, tap, flatMap, finalize, share, take, catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { AuthService, RefreshData } from '../services/auth.service';
import { TokenStore } from '../store/token.store';

export interface ITokenPayload {
  nbf: number;
  exp: number;
  role: string[];
  loginId: string;
  tillId: string;
  storeId: string;
}

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(
    private authService: AuthService,
    private tokenStore: TokenStore,
    private router: Router
  ) {}

  private refreshData$: Observable<RefreshData> | null = null;

  authMiddleware(req: HttpRequest<unknown>): Observable<HttpRequest<unknown>> {
    const accessToken = localStorage.getItem('access_token');
    const refreshToken = localStorage.getItem('refresh_token');

    if (!accessToken) {
      return of(req);
    }

    const decodedAccessToken = jwtDecode<ITokenPayload>(accessToken);

    //If accessToken is not expired then attack accessToken to req
    if ((decodedAccessToken.exp - 5) * 1000 >= Date.now()) {
      return of(
        req.clone({ setHeaders: { Authorization: `Bearer ${accessToken}` } }),
      );
    }

    if (!refreshToken) {
      this.authService.logout();
      throw new Error('refresh token missing');
    }

    const decodedRefreshToken = jwtDecode<ITokenPayload>(refreshToken);

    if ((decodedRefreshToken.exp - 5) * 1000 <= Date.now()) {
      this.authService.logout();
      throw new Error('refresh token expired');
    }

    if (!this.refreshData$) {
      this.refreshData$ = this.authService.refresh({ refreshToken }).pipe(
        tap((refreshData) => {
          this.tokenStore.setAccessToken(refreshData.accessToken);
          this.tokenStore.setRefreshToken(refreshData.refreshToken);
        }),
        finalize(() => (this.refreshData$ = null)),
        catchError(error => {
          if(error instanceof HttpErrorResponse && error.status === 403){
            this.authService.logout()
            return throwError(error)
          } 
          return throwError(error)
        }),
        share(),  
      );
    }

    return this.refreshData$.pipe(
      take(1),
      map((refreshData) =>
        req.clone({
          setHeaders: {
            Authorization: `Bearer ${refreshData.accessToken}`,
          },
        }),
      ),
    );
  }

  intercept(
    req: HttpRequest<unknown>,
    next: HttpHandler,
  ): Observable<HttpEvent<unknown>> {
    return this.authMiddleware(req).pipe(flatMap((r) => next.handle(r).pipe(
      catchError(error => {
        if(error instanceof HttpErrorResponse && error.status === 401) {
          this.authService.logout()
          return throwError(error)
        } 
        return throwError(error)
      })
    )));
  }
}