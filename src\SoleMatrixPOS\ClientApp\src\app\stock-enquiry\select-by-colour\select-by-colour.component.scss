.color-selector-container {
    margin-bottom: 1.5rem;
}

.color-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 0.5rem 0.8rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    min-height: 30px;
}

.color-info {
    margin-bottom: 1rem;
}

.color-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.color-details {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;

    .style-code,
    .color-code {
        background: #e9ecef;
        color: #6c757d;
        padding: 0.25rem 0.75rem;
        border-radius: 6px;
        font-weight: 500;
    }
}

.controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modern-select {
    flex: 1;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    background: white;
    color: #495057;
    cursor: pointer;
    min-height: 48px;

    &:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    option {
        padding: 0.5rem;
        font-size: 1rem;
    }
}

.navigation-buttons {
    display: flex;
    gap: 0.5rem;
}

.nav-btn {
    background: #007bff;
    border: none;
    border-radius: 6px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    cursor: pointer;

    &:disabled {
        background: #6c757d;
        cursor: not-allowed;
    }

    &:not(:disabled):active {
        background: #0056b3;
    }
}

// Responsive design
@media (max-width: 768px) {
    .color-card {
        padding: 1rem 1.5rem;
        min-height: 70px;
    }

    .color-name {
        font-size: 1.1rem;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
    }

    .navigation-buttons {
        justify-content: center;
    }
}