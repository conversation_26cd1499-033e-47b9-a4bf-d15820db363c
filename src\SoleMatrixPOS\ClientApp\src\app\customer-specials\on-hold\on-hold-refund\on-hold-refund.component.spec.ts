import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { OnHoldRefundComponent } from './on-hold-refund.component';

describe('OnHoldRefundComponent', () => {
  let component: OnHoldRefundComponent;
  let fixture: ComponentFixture<OnHoldRefundComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ OnHoldRefundComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(OnHoldRefundComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
