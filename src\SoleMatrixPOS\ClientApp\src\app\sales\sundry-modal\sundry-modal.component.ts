import { Component, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { AppState } from '../../reducers';
import * as cartActions from '../../reducers/sales/cart/cart.actions';
import { Observable } from 'rxjs';
import * as cartSelectors from "../../reducers/sales/cart/cart.selectors";


@Component({
    selector: 'pos-sundry-modal',
    templateUrl: './sundry-modal.component.html'
})
export class SundryModalComponent implements OnInit {
    sundryForm: FormGroup;

    constructor(
        private activeModal: NgbActiveModal,
        private fb: FormBuilder,
        private store: Store<AppState>
    ) { }

    
	reasons$: Observable<Map<string, string[]>>;

	reasons: Map<string, string[]>;

    ngOnInit() {
        this.sundryForm = this.fb.group({
            price: [0, [
                Validators.required, 
                Validators.min(0.01),
                Validators.pattern(/^\d*\.?\d{0,2}$/)
            ]],
            description: ['', [
                Validators.required, 
                Validators.minLength(5), 
                Validators.maxLength(36)
            ]]
        });		
        this.reasons$ = this.store.select(cartSelectors.reasons);
		this.reasons$.subscribe(v => {this.reasons = v});
    }

    save() {
        console.log("Save button clicked");
        if (this.sundryForm.valid) {
            console.log("Form is valid");
            const price = parseFloat(this.sundryForm.get('price').value).toFixed(2);
            const description = this.sundryForm.get('description').value;
            
            console.log(`Dispatching addSundryItemWithReason: price=${price}, description=${description}`);
            
            this.store.dispatch(cartActions.addSundryItemWithReason({ 
                price: +price,
                description: description 
            }));
            
            this.activeModal.close();
        } else {
            console.log("Form is invalid:", this.sundryForm.errors);
            Object.keys(this.sundryForm.controls).forEach(key => {
                const control = this.sundryForm.get(key);
                if (control.invalid) {
                    console.log(`${key} errors:`, control.errors);
                }
            });
        }
    }

    cancel() {
        this.activeModal.dismiss();
    }

    formatDecimals(event: Event) {
        const input = event.target as HTMLInputElement;
        let value = input.value;
        
        if (value.includes('.')) {
            const [whole, decimal] = value.split('.');
            if (decimal && decimal.length > 2) {
                input.value = `${whole}.${decimal.substring(0, 2)}`;
                this.sundryForm.get('price').setValue(parseFloat(input.value));
            }
        }
    }
}
