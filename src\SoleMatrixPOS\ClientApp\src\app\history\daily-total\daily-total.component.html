<div class="content-wrapper pt-10">
    <div class="m-2">
        <div class="d-flex justify-content-between ml-2 mb-2">
            <div>
                <h2>Daily Totals for {{getDateRangeDescription()}}</h2>
                <div class="date-selector">
                    <div class="d-flex align-items-center">
                        <label class="mr-3">Select Date Range:</label>
                        <div class="calendar-container">
                            <p-calendar #calendar appendTo="document.body" [(ngModel)]="selectedDates"
                                selectionMode="range" dateFormat="dd/mm/yy" (onSelect)="onDateSelect()"
                                placeholder="Select Date Range" [style]="{'width': '280px'}"
                                [inputStyle]="{'width': '100%'}" [readonlyInput]="true" [autoZIndex]="true"
                                [baseZIndex]="9999" [showOnFocus]="false">
                            </p-calendar>
                            <button class="calendar-toggle-btn" (click)="toggleCalendar($event)">
                                <i class="pi pi-calendar"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <button class="text-secondary close-btn" (click)="close()">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>

        <div *ngIf="loading" class="text-center my-3">
            <mat-spinner diameter="50" class="m-auto"></mat-spinner>
        </div>

        <!-- Single Day View -->
        <div class="d-flex" *ngIf="!(isRangeMode$ | async) && !loading">
            <table class="table table-primary table-striped ml-2 mr-2 table-hover table-daily-font w-25">
                <tr *ngIf="dailyTotal$ | async as daily">
                    <th scope="col-1">Cash Total</th>
                    <td>{{toFinancialString(daily?.cashTotal || 0)}}</td>
                </tr>
                <tr *ngIf="dailyTotal$ | async as daily">
                    <th scope="col-1">EFT Total</th>
                    <td>{{toFinancialString(daily?.eftTotal || 0)}}</td>
                </tr>
                <tr *ngIf="dailyTotal$ | async as daily">
                    <th scope="col-1">Petty Cash</th>
                    <td>{{toFinancialString(daily?.pettyCash || 0)}}</td>
                </tr>
                <tr *ngIf="dailyTotal$ | async as daily">
                    <th scope="col-1">Total</th>
                    <td>{{toFinancialString(getTotal(daily))}}</td>
                </tr>
            </table>
            <div class="w-75 pr-2 pl-2">
                <pos-sales-by-table></pos-sales-by-table>
                <pos-sales-by-hour-table></pos-sales-by-hour-table>
                <pos-department-sales></pos-department-sales>
                <table class="table table-primary table-striped ml-2 mr-2 table-hover table-summary-font">
                    <thead>
                        <tr>
                            <th scope="col-1">Gross Sales</th>
                            <th scope="col-1">Items</th>
                            <th scope="col-2">Custs</th>
                            <th scope="col-1">Multi Sales</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>{{toFinancialString((grossSales$ | async) || 0)}}</td>
                            <td>
                                <div class="ml-5">{{(totalItems$ | async) || 0}}</div>
                            </td>
                            <td>
                                <div class="ml-5">{{(totalCusts$ | async) || 0}}</div>
                            </td>
                            <td>
                                <div class="ml-5">{{(multiSales$ | async)?.multiSales || 0}}</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Range View -->
        <div class="d-flex" *ngIf="(isRangeMode$ | async) && !loading">
            <table class="table table-primary table-striped ml-2 mr-2 table-hover table-daily-font w-25">
                <tr *ngIf="dailyRangeTotals$ | async as totals">
                    <th scope="col-1">Cash Total</th>
                    <td>{{toFinancialString(totals?.cashTotal || 0)}}</td>
                </tr>
                <tr *ngIf="dailyRangeTotals$ | async as totals">
                    <th scope="col-1">EFT Total</th>
                    <td>{{toFinancialString(totals?.eftTotal || 0)}}</td>
                </tr>
                <tr *ngIf="dailyRangeTotals$ | async as totals">
                    <th scope="col-1">Petty Cash</th>
                    <td>{{toFinancialString(totals?.pettyCash || 0)}}</td>
                </tr>
                <tr *ngIf="dailyRangeTotals$ | async as totals">
                    <th scope="col-1">Total</th>
                    <td>{{toFinancialString((totals?.cashTotal || 0) + (totals?.eftTotal || 0))}}</td>
                </tr>
            </table>
            <div class="w-75 pr-2 pl-2">
                <h3>Daily Range Summary</h3>
                <!-- Table showing daily range data -->
                <table class="table table-primary table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Cash Total</th>
                            <th>EFT Total</th>
                            <th>Petty Cash</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let day of (dailyRange$ | async)">
                            <td>{{transformDate(day.transactionDate)}}</td>
                            <td>{{toFinancialString(day.cashTotal || 0)}}</td>
                            <td>{{toFinancialString(day.eftTotal || 0)}}</td>
                            <td>{{toFinancialString(day.pettyCash || 0)}}</td>
                            <td>{{toFinancialString((day.cashTotal || 0) + (day.eftTotal || 0))}}</td>
                        </tr>
                    </tbody>
                </table>

                <h3>Range Totals</h3>
                <table class="table table-primary table-striped ml-2 mr-2 table-hover table-summary-font">
                    <thead>
                        <tr>
                            <th scope="col-1">Total Sales</th>
                            <th scope="col-1">Total Cash</th>
                            <th scope="col-2">Total EFT</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngIf="dailyRangeTotals$ | async as totals">
                            <td>{{toFinancialString((totals?.cashTotal || 0) + (totals?.eftTotal || 0))}}</td>
                            <td>{{toFinancialString(totals?.cashTotal || 0)}}</td>
                            <td>{{toFinancialString(totals?.eftTotal || 0)}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>