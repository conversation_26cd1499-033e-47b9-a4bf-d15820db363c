// transaction.reducer.ts

import { createReducer, on } from '@ngrx/store';
import * as transActions from "./transaction.actions";
import { GiftVoucherResultDto, TranspayDto } from 'src/app/pos-server.generated';

export interface TransactionState {
  transactionCompleted: boolean;
  transNo: number | null;
  transCreditNote: GiftVoucherResultDto,
  transPayData: TranspayDto[] | null;  // Add a new field to store transPayData
  loading: boolean;
  error: string | null;
}

export const initialState: TransactionState = {
  transactionCompleted: false,
  transNo: null,
  transCreditNote: null,
  transPayData: null,  // Initialize with null
  loading: false,
  error: null,
};

export const transactionReducer = createReducer(
  initialState,
  on(transActions.init, () => initialState),
  // Handle submitTransaction (start loading)
  on(transActions.submitTransaction, (state) => ({
    ...state,
    loading: true,
    error: null,
  })),
  // Handle transactionConfirmation
  on(transActions.transactionConfirmation, (state, action) => ({
    ...state,
    transactionCompleted: true,
    transNo: action.payload.transactionNumber,
    transCreditNote: action.payload.creditNote,
    loading: false,
    error: null,
  })),
  // Handle submitTransactionFailure
  on(transActions.submitTransactionFailure, (state, action) => ({
    ...state,
    loading: false,
    error: action.error,
  })),
  // Handle getTransactionNo (start loading)
  on(transActions.getTransactionNo, (state) => ({
    ...state,
    loading: true,
    error: null,
  })),
  // Handle getTransactionNoSuccess
  on(transActions.getTransactionNoSuccess, (state, action) => ({
    ...state,
    transNo: action.payload,
    loading: false,
    error: null,
  })),
  // Handle getTransactionNoFailure
  on(transActions.getTransactionNoFailure, (state, action) => ({
    ...state,
    loading: false,
    error: action.error,
  })),
  // Handle getTransPayByTransNo (start loading)
  on(transActions.getTransPayByTransNo, (state) => ({
    ...state,
    loading: true,
    error: null,
  })),
  // Handle getTransPayByTransNoSuccess
  on(transActions.getTransPayByTransNoSuccess, (state, action) => ({
    ...state,
    transPayData: action.payload,  // Store the retrieved data
    loading: false,
    error: null,
  })),
  // Handle getTransPayByTransNoFailure
  on(transActions.getTransPayByTransNoFailure, (state, action) => ({
    ...state,
    loading: false,
    error: action.error,
  })),
  on(transActions.clearTransPayData, (state) => ({
    ...state,
    transPayData: null
  })),
	on(transActions.resetTransactionConfirmation, (state) => ({
		...state,
		transactionCompleted: false,
	}))
);
