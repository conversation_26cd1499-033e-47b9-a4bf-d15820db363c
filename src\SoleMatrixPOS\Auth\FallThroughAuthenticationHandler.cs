using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Security.Claims;
using System.Text.Encodings.Web;
using System.Threading.Tasks;

namespace SoleMatrixPOS.Auth
{
	public class FallThroughAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
	{
		public FallThroughAuthenticationHandler(IOptionsMonitor<AuthenticationSchemeOptions> options, ILoggerFactory logger, UrlEncoder encoder, ISystemClock clock) : base(options, logger, encoder, clock)
		{
		}

		protected override Task<AuthenticateResult> HandleAuthenticateAsync()
		{
			// Create a dummy claims identity
			var identity = new ClaimsIdentity(Array.Empty<Claim>(), Scheme.Name);
			var principal = new ClaimsPrincipal(identity);

			// Create the authentication ticket
			var ticket = new AuthenticationTicket(principal, Scheme.Name);

			// Always return success
			return Task.FromResult(AuthenticateResult.Success(ticket));
		}
	}
}
