<!-- <div class="card mx-auto" style="margin-top: 1rem; margin-bottom: 1rem;">
    <div class="card-body">
        <div class="container">
            <form></form>
        </div>
    </div>
</div> -->
<div *ngIf="handlingCustClub">
    <pos-customer-club-container 
        (onSelectedMember)="onMemberSelected($event)"
        (onMemberDoubleClick)="onUseSelected()">
    </pos-customer-club-container>
    <div class="row pb-4">
        <div class="col-6 text-center">
            <button class="btn btn-outline-default" type="button" (click)="onBack()"
                    [translate]="'customer-club.buttons.BackLayby'">
                    <i class="fas fa-lg fa-fw fa-times-circle text-danger mr-2"></i>
                    Go Back to Layby
            </button>
        </div>
        
        <div class="col-6 text-center">
            <button class="btn btn-outline-default" type="button" (click)="onUseSelected()"
                    [translate]="'customer-club.buttons.Use'">
                    <i class="fas fa-lg fa-fw fa-check-circle text-success mr-2"></i>
                    Use Selected
            </button>
        </div>
    </div>
    
</div>
<div class="container" *ngIf="!handlingCustClub">
    <h1>Apply for Layby</h1>
    <form [formGroup]="laybyApplicationForm" autocapitalize="on">
        <div class="card mx-auto" style="margin-top: 1rem; margin-bottom: 1rem;">
            <div class="card-body">
                <div class="container">
                    <div class="row">
                        <label class="col-6">
                            <div class="mb-2">Club Number</div>
                            <input type="text" class="form-control ng-untouched ng-pristine ng-valid" [readonly]="true"
                                formControlName="txtClubNo">
                        </label>
                        <label class="col-6">
                            <div class="mb-2">Controls</div>
                            <div class="row">
                                <div class="container col">
                                    <button class="form-control" (click)="handlingCustClub = true">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            
                        </label>
                    </div>
                    <div class="row">
                        <label class="col-2">
                            <div class="mb-2">Title</div>
                            <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                                formControlName="txtTitle">
                        </label>
                        <label class="col-5">
                            <div class="mb-2">First Name</div>
                            <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                                formControlName="txtFirstName">
                        </label>
                        <label class="col-5">
                            <div class="mb-2">Surname</div>
                            <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                                formControlName="txtLastName">
                        </label>
                    </div>

                    <div class="row">
                        <label class="col-12">
                            <div class="mb-2">Street</div>
                            <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                                formControlName="txtStreet">
                        </label>                        
                    </div>

                    <div class="row">
                        <label class="col-6">
                            <div class="mb-2">Suburb / Town</div>
                            <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                                formControlName="txtSuburb">
                        </label>
                        <label class="col-4">
                            <div class="mb-2">State</div>
                            <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                                formControlName="txtState">
                        </label>
                        <label class="col-2">
                            <div class="mb-2">Postcode</div>
                            <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                                formControlName="txtPostcode">
                        </label>
                    </div>

                    <div class="row">
                        <label class="col-6">
                            <div class="mb-2">Phone</div>
                            <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                                formControlName="txtPhone">
                        </label>
                        <label class="col-6">
                            <div class="mb-2">Email</div>
                            <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                                formControlName="txtEmail">
                        </label>
                    </div>
                    
                    <div class="row justify-content-center pt-2">
                        <button *ngIf="laybyActive$ | async" type="button" class="btn btn-danger" (click)="cancelLayby()">Cancel Layby</button>
                        <button *ngIf="!(laybyActive$ | async)" type="button" class="btn btn-success" (click)="submitLayby()">Create Layby</button>
                    </div>
                    

                </div>
            </div>
        </div>
    </form>
</div>
