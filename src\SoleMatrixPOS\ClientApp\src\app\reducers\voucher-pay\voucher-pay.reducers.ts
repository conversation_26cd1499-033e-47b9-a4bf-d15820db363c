import { Action, createReducer, on } from "@ngrx/store";
import * as giftVoucherPaymentAction from "./voucher-pay.actions";
import { GiftVoucherResultDto } from "src/app/pos-server.generated";
import { Payment } from "src/app/payment/payment.service";

// Interface to track payments per gift card
interface GiftCardTracking {
  [giftCardCode: string]: {
    originalAmount: number;  // Original amount on the card
    payments: {
      transactionId: string;
      amount: number;
      voucherType: 'giftcard' | 'creditnote'; // Add voucher type
    }[];
  };
}

export class GiftVoucherPaymentState {
    giftVoucher: GiftVoucherResultDto;
    // Add gift card tracking
    giftCardTracking: GiftCardTracking;
    giftCardErrored: boolean;
}

export const initialState: GiftVoucherPaymentState = {
    giftVoucher: null,
    giftCardTracking: {},
    giftCardErrored: false
} as GiftVoucherPaymentState;

export const GiftVoucherPaymentReducer = createReducer(initialState,
    on(giftVoucherPaymentAction.init, (state) => initialState),
    
    on(giftVoucherPaymentAction.searchVoucherResponse, (state, payload) => {
        if (payload.giftVoucher === null) {
            // Issue with gift voucher! Didn't exist or redeemed
            return {...state, giftVoucher: null, giftCardErrored: true}
        }

        // POSTCOND: gift voucher is valid as we received from backend
        const giftCardCode = payload.giftVoucher.voucherNo;
        const tracking = state.giftCardTracking[giftCardCode];
        
        // Calculate used amount if this card is already being tracked
        let usedAmount = 0;
        if (tracking) {
            usedAmount = tracking.payments.reduce((sum, payment) => sum + payment.amount, 0);
        }
        
        // Create a modified copy of the gift voucher with adjusted funds
        const adjustedGiftVoucher = {
            ...payload.giftVoucher,
            voucherFunds: payload.giftVoucher.voucherFunds - usedAmount
        };
        
        // Update tracking if not already present
        let updatedTracking = { ...state.giftCardTracking };
        if (!tracking) {
            updatedTracking[giftCardCode] = {
                originalAmount: payload.giftVoucher.voucherFunds,
                payments: []
            };
        }
        
        return {
            ...state, 
            giftVoucher: adjustedGiftVoucher,
            giftCardTracking: updatedTracking,
            giftCardErrored: false
        };
    }),

    on(giftVoucherPaymentAction.clearVoucherLookup, state => {
        return {...state, giftVoucher: null}
    }),
    
    // Handle tracking gift card payments
    on(giftVoucherPaymentAction.trackVoucherPayment, (state, { payment, transactionId, voucherType }) => {
        const giftCardCode = payment.context && payment.context.giftVoucherNo ? payment.context.giftVoucherNo : null;
        
        if (!giftCardCode) {
            return state;
        }
        
        // Clone the tracking state
        const updatedTracking = { ...state.giftCardTracking };
        
        // Initialize tracking for this gift card if it doesn't exist
        if (!updatedTracking[giftCardCode]) {
            updatedTracking[giftCardCode] = {
                originalAmount: state.giftVoucher ? state.giftVoucher.voucherFunds : 0,
                payments: []
            };
        }
        
        // Add the payment to the tracking
        updatedTracking[giftCardCode] = {
            ...updatedTracking[giftCardCode],
            payments: [
                ...updatedTracking[giftCardCode].payments,
                { transactionId, amount: payment.amount, voucherType }
            ]
        };
        
        // Update voucher funds if this is the currently selected voucher
        let updatedVoucher = state.giftVoucher;
        if (state.giftVoucher && state.giftVoucher.voucherNo === giftCardCode) {
            const totalUsed = updatedTracking[giftCardCode].payments.reduce(
                (sum, payment) => sum + payment.amount, 0
            );
            
            updatedVoucher = {
                ...state.giftVoucher,
                voucherFunds: updatedTracking[giftCardCode].originalAmount - totalUsed
            };
        }
        
        return {
            ...state,
            giftVoucher: updatedVoucher,
            giftCardTracking: updatedTracking
        };
    }),
    
    // Handle removing gift card payments
    on(giftVoucherPaymentAction.removeVoucherPayment, (state, { payment, transactionId, voucherType }) => {
        const giftCardCode = payment.context && payment.context.giftVoucherNo ? payment.context.giftVoucherNo : null;
        
        if (!giftCardCode || !state.giftCardTracking[giftCardCode]) {
            return state;
        }
        
        // Clone the tracking state
        const updatedTracking = { ...state.giftCardTracking };
        const cardTracking = { ...updatedTracking[giftCardCode] };
        
        // Remove the payment from tracking
        const updatedPayments = cardTracking.payments.filter(
            p => !(p.transactionId === transactionId && p.amount === payment.amount && p.voucherType === voucherType)
        );
        
        // If no payment was removed (e.g., wrong voucherType), return state to avoid unnecessary updates
        if (updatedPayments.length === cardTracking.payments.length) {
            console.warn('Attempted to remove voucher payment, but no matching payment found for:', { transactionId, amount: payment.amount, voucherType });
            return state;
        }
        
        updatedTracking[giftCardCode] = {
            ...cardTracking,
            payments: updatedPayments
        };
        
        // Update voucher funds if this is the currently selected voucher
        let updatedVoucher = state.giftVoucher;
        if (state.giftVoucher && state.giftVoucher.voucherNo === giftCardCode) {
            const totalUsed = updatedPayments.reduce(
                (sum, payment) => sum + payment.amount, 0
            );
            
            updatedVoucher = {
                ...state.giftVoucher,
                voucherFunds: updatedTracking[giftCardCode].originalAmount - totalUsed
            };
        }
        
        return {
            ...state,
            giftVoucher: updatedVoucher,
            giftCardTracking: updatedTracking
        };
    }),
    
    on(giftVoucherPaymentAction.clearAllVoucherPayments, (state) => ({
        ...state,
        giftVoucher: null,
        giftCardTracking: {}
    })),
    
    // Reset all payments for a transaction
    on(giftVoucherPaymentAction.resetTransactionPayments, (state, { transactionId }) => {
        // Clone the tracking state
        const updatedTracking = { ...state.giftCardTracking };
        
        // Remove all payments for this transaction from all gift cards
        Object.keys(updatedTracking).forEach(giftCardCode => {
            const cardTracking = updatedTracking[giftCardCode];
            const updatedPayments = cardTracking.payments.filter(
                p => p.transactionId !== transactionId
            );
            
            updatedTracking[giftCardCode] = {
                ...cardTracking,
                payments: updatedPayments
            };
        });
        
        // Update voucher funds if there's a currently selected voucher
        let updatedVoucher = state.giftVoucher;
        if (state.giftVoucher) {
            const giftCardCode = state.giftVoucher.voucherNo;
            if (updatedTracking[giftCardCode]) {
                const totalUsed = updatedTracking[giftCardCode].payments.reduce(
                    (sum, payment) => sum + payment.amount, 0
                );
                
                updatedVoucher = {
                    ...state.giftVoucher,
                    voucherFunds: updatedTracking[giftCardCode].originalAmount - totalUsed
                };
            }
        }
        
        return {
            ...state,
            giftVoucher: updatedVoucher,
            giftCardTracking: updatedTracking
        };
    })
    
);



export function reducers(state: GiftVoucherPaymentState | undefined, action: Action) {
    return GiftVoucherPaymentReducer(state, action);
}