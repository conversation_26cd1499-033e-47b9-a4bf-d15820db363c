<div class="d-flex flex-column vh-100 overflow-hidden">
    <pos-nav-header pageName="History"></pos-nav-header>

    <div class="content-wrapper flex-grow-1 pt-10 overflow-auto" #scrollContainer (scroll)="onScroll($event)">
        <div class="container-fluid d-flex flex-column">
            <div class="row text-align-center mt-2 mb-4 d-flex">
                <h3 class="mt-3 mb-0 mr-2 ml-4 mr-4 text-secondary">
                    Search</h3> <!-- TODO Add translation -->
                <div class="col-2 pr-0">

                    <select class="form-control form-control-special" [(ngModel)]="field" (change)="search()">
                        <option value="Date" selected>DATE</option>
                        <option value="Time">TIME</option>
                        <option value="Type">TYPE</option>
                        <option value="Docket">DOCKET</option>
                        <option value="Style">STYLE</option>
                        <option value="Colour">COLOUR</option>
                        <option value="Size">SIZE</option>
                        <option value="Qty">QTY</option>
                        <option value="Price">PRICE</option>
                        <option value="Client">CLIENT</option>
                    </select>
                </div>
                <div class="col-3 pl-0">
                    <input *ngIf="field !== 'Date'" type="text" class="form-control" [(ngModel)]="term"
                        (keyup)="search()" />

                    <p-calendar appendTo="body" *ngIf="field === 'Date'" [(ngModel)]="selectedDates" selectionMode="range"
                        dateFormat="dd/mm/yy" (onSelect)="search()" placeholder="Select Date Range"
                        [style]="{'width': '100%', 'height': '87%'}" [inputStyle]="{'width': '100%', 'height': '87%'}">
                    </p-calendar>
                </div>
                <!--(keyup)='keyUp.next($event)'-->
                <!-- <input type="text" class="form-control" [(ngModel)]="term" (keyup)="search()"  *ngIf = "!showDailyTotal" /> -->
                <button type="button" class="btn btn-primary" (click)="openDailyModal()">
                    DAILY TOTAL
                </button>

            </div>

            <div class="row justify-content-center" *ngIf="loading">
                <mat-spinner diameter="50"></mat-spinner>
            </div>

            <div class="row m-2" [class.d-none]="loading">
                <div class="table-responsive w-100">
                    <table class="table table-striped ml-2 mr-2 table-hover">
                        <thead>
                            <tr>
                                <th scope="col-1">Date</th>
                                <th scope="col-1">Time</th>
                                <th scope="col-2">Type</th>
                                <th scope="col-1">Docket</th>
                                <th scope="col-2">Style</th>
                                <th scope="col-2">Colour</th>
                                <th scope="col-1">Size</th>
                                <th scope="col-1">Qty</th>
                                <th scope="col-2">Price</th>
                                <th scope="col-2">Staff</th>
                                <th scope="col-2">Client</th>
                                <th scope="col-1">Store</th>
                                <th scope="col-1"> </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngIf="(transactionHistory$ | async)?.length === 0">
                                <td colspan="13" class="text-center py-4">
                                    <h5 class="text-secondary mb-0">No results found</h5>
                                </td>
                            </tr>
                            <tr *ngFor="let transaction of transactionHistory$|async; let i = index">
                                <td>
                                    <ngb-highlight [result]="transaction.transactionDate | date:'d/MM/yyyy'"
                                        [highlightClass]="'search-highlight'">
                                    </ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="transaction.transactionTime"
                                        [highlightClass]="'search-highlight'">
                                    </ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="transaction.transName" [highlightClass]="'search-highlight'">
                                    </ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="transaction.transNo" [highlightClass]="'search-highlight'">
                                    </ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="transaction.styleCode" [highlightClass]="'search-highlight'">
                                    </ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="transaction.colourCode" [highlightClass]="'search-highlight'">
                                    </ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="transaction.sizeCode" [highlightClass]="'search-highlight'">
                                    </ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="transaction.quantity" [highlightClass]="'search-highlight'">
                                    </ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="transaction.sellingPrice | currency"
                                        [highlightClass]="'search-highlight'">
                                    </ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="transaction.staffName" [highlightClass]="'search-highlight'">
                                    </ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="transaction.firstname + ' ' + transaction.surname | removeNull"
                                        [highlightClass]="'search-highlight'">
                                    </ngb-highlight>
                                </td>
                                <td>
                                    <ngb-highlight [result]="transaction.storeId" [highlightClass]="'search-highlight'">
                                    </ngb-highlight>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-secondary"
                                        *ngIf="transaction.transType !== '7' && transaction.transType !== '8' && transaction.transType !== '17' && transaction.transType !== '10'"
                                        (click)="openReceiptModal(transaction.transNo, transaction.transType, transaction.tillNo, transaction.storeId)">
                                        RECEIPT
                                    </button>

                                    <!-- UNCOMMENT BELOW FOR RETURNS BUTTON IN HISTORY -->

                                    <!-- <button *ngIf="(transaction.transType == 1 || transaction.transType == 3) && transaction.quantity > 0" 
                                        type="button" class="btn btn-warning mr-2" 
                                        (click)="openReturnModal(transaction.transNo, transaction.transType, transaction.tillNo, transaction.storeId)">
                                        RETURN
                                    </button> -->
                                </td>
                                <!-- <td><i style="cursor: pointer;" class="fa fa-ellipsis-v" [matMenuTriggerFor]="menu"></i>
                                    <mat-menu #menu="matMenu">
                                        <button mat-menu-item>
                                            <span>Print Receipt</span>
                                        </button> 
                                        <button mat-menu-item (click)="launchCustomerModal(transaction.clientCode)">
                                            <span>Client Details</span>
                                        </button>
                                        <button mat-menu-item>
                                            <span>Account Reference</span>
                                        </button>
                                        <button mat-menu-item>
                                            <span>Print on A4</span>
                                        </button>
                                        <button mat-menu-item>
                                            <span>Email Invoice</span>
                                        </button>
                                    </mat-menu>
                                <td> -->
                            </tr>
                        </tbody>
                    </table>
                    <div *ngIf="isLoadingMore" class="text-center py-2" style="width: 100%;">
                        <mat-spinner diameter="30" style="margin: 0 auto;"></mat-spinner>
                    </div>
                    <div *ngIf="noMoreResultsToLoad" class="text-center py-4">
                        <h5 class="text-secondary mb-0">No more results</h5>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="footer-wrapper mt-auto">
        <div class="container-fluid">
            <div class="row flex-row-reverse">
                <!-- <div class="col-auto next-button">
                    <button type="button" class="btn-hidden">
                        <div class="btn btn-lg btn-circle btn-default">
                            <i class="fas fa-caret-right fa-4x color-gradient-green ml-2"></i>
                        </div>
                        <h4 class="text-light">View Item</h4>
                    </button> 
                </div> -->
            </div>
        </div>
    </div>
</div>

