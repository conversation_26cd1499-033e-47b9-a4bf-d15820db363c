import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { map, mergeMap, catchError, tap } from 'rxjs/operators';
import * as transRefActions from './transref.actions';
import { TransRefClient } from 'src/app/pos-server.generated';
import { of } from 'rxjs';

@Injectable()
export class TransrefEffects {
  constructor(
    private actions$: Actions,
    private client: TransRefClient
  ) {}

  submitTransRef$ = createEffect(() => this.actions$.pipe(
	ofType(transRefActions.submitTransref),
	mergeMap(
		(action) => {
			console.log("Yes");
			return this.client.addTransRef(
				action.payload
			).pipe(
				map(
					response => {
						console.log("Yes");
						return transRefActions.transRefConfirmation({
							message: response.toString()
						});
					}
				)
			)
		}
	)
  ));
}
