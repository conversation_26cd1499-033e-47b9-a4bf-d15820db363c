
import * as PageActions from './page.actions'

const initialState: number = 0;

export function PageReducer(state: number = initialState, action: any) {
    switch (action.type) {
        case PageActions.NEXT_PAGE_REQUESTED: {
            return state;
        }
        case PageActions.PreviousPageRequested: {
            return state;
        }
        case PageActions.NEXT_PAGE: {
            if (state + 1 > action.max) {
                state = 0
            } else {
                state++
            }
            return state;
        }
        case PageActions.PREVIOUS_PAGE: {
            if (state - 1 < 0) {
                state = action.max
            } else {
                state--
            }
            return state;
        }
        case PageActions.SET_PAGE: {
            state = action.page
            return state;
        }
        default:
            return state;
    }
}