import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { OnHoldDepositModalComponent } from './on-hold-deposit-modal.component';

describe('DepositModalComponent', () => {
  let component: OnHoldDepositModalComponent;
  let fixture: ComponentFixture<OnHoldDepositModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ OnHoldDepositModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(OnHoldDepositModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
