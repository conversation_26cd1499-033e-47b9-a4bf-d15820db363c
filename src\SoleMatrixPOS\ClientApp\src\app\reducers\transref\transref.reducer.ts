import { createReducer, on } from '@ngrx/store';
import * as transRefActions from './transref.actions';

export interface TransrefState {
  transRefCompleted: boolean;
  message: string | null;
}

export const initialState: TransrefState = {
  transRefCompleted: false,
  message: null
};

export const transRefReducer = createReducer(
  initialState,
  on(transRefActions.init, (state) => initialState),
  on(transRefActions.submitTransref, (state, action) => {console.log(action); return {...state}}),

  on(transRefActions.transRefConfirmation, (state, action) => {
    return { ...state, endOfDayCompleted: true, message: action.message };
  }),

  on(transRefActions.transRefError, (state, action) => {
    // Assuming action.error is a string representing an error message
    return { ...state, endOfDayCompleted: false, message: action.error };
  })
);
