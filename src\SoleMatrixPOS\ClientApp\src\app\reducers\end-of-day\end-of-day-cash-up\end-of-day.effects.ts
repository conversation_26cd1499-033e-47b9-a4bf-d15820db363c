import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { map, mergeMap, catchError, tap } from 'rxjs/operators';
import * as endOfDayActions from './end-of-day.actions';
import { EndOfDayClient } from 'src/app/pos-server.generated';
import { of } from 'rxjs';

@Injectable()
export class EndOfDayEffects {
  constructor(
    private actions$: Actions,
    private client: EndOfDayClient
  ) {}

//   submitEndOfDay$ = createEffect(() => this.actions$.pipe(
//     ofType(endOfDayActions.submitEndOfDay),
//     mergeMap(
// 	  (action) => {
// 		this.client.addTransaction(action.payload).pipe(
//       map(message => {
//         // Handle the response message from the server
//         return endOfDayActions.endOfDayConfirmation({ message });
//       }),
//       catchError(error => of(endOfDayActions.endOfDayError({ error })))
//     ))
//   ));

  submitEndOfDay$ = createEffect(() => this.actions$.pipe(
	ofType(endOfDayActions.submitEndOfDay),
	mergeMap(
		(action) => {
			console.log("Yes");
			return this.client.addTransaction(
				action.payload
			).pipe(
				map(
					response => {
						console.log("Yes");
						return endOfDayActions.endOfDayConfirmation({
							message: response
						});
					}
				)
			)
		}
	)
  ));
}
