import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { StockItemDto } from '../../pos-server.generated';

@Component({
	selector: 'pos-stock-search-modal',
	templateUrl: './stock-search-modal.component.html',
	styleUrls: ['./stock-search-modal.component.scss']
})
export class StockSearchModalComponent implements OnInit {

	@Input() name: string;
	@Input() initialSearchValue: string;
	constructor(public activeModal: NgbActiveModal) { }

	ngOnInit() {
	}

	selectItem(item: StockItemDto) {
		this.activeModal.close(item);
	}

	dismiss(reason: string) {
		this.activeModal.dismiss(reason);
	}
}
