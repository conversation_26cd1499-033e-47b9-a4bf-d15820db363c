import { createSelector } from "@ngrx/store";
import { AppState } from "../..";
import { StockCartItem } from "./itemCart.reducers";

export const select = (state: AppState) => state.stocktakeCart;

export const cartItem = createSelector(select, (s) => s.items);
export const noItems = createSelector(select, (s) => calculateNoItems(s.items));
export const UnitTotal = createSelector(select, (s) => s.unitTotal);
export const reasons = createSelector(select, (s) => s.reasons);

function calculateNoItems(cartItem: StockCartItem[]): number {
	console.log("Working...");
	let count: number = 0;
	for (let item of cartItem) {
		count += item.quantity;
	}
	return count;
}

export const tableLoading = createSelector(select, (s) => s.tableLoading);
