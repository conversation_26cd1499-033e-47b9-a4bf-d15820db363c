<!-- Main Table -->
<div class="fixed-container">
    <table class="table table-striped table-hover table-scroll">
        <!-- Header Table component-->
        <thead>
            <tr>
                <th scope="col">
                    Location
                </th>
                <!-- Display the sizes -->
                <ng-container *ngFor="let size of mainTableService.sizes$ | async">
                    <th scope="col">
                        {{size.size}}
                    </th>
                </ng-container>

            </tr>
        </thead>
        <!-- Body Table -->
        <tbody class="body-half-screen">
            <!-- ng-container is a very useful tag for implementing logic without affecting the heirarchy -->
            <ng-container *ngFor="let location of mainTableService.locations$ | async">
                <!-- Might be a good idea to move stuff with these ngif directives to new components -->
                <ng-container *ngIf="location.trading == 'T'">
                    <tr>
                        <th scope="row">
                            {{location.name}}
                        </th>
                        <ng-container *ngFor="let size of mainTableService.sizes$ | async">
                            <ng-container *ngFor="let stock of location.stock">
                                <ng-container *ngIf="stock.size == size.size">
                                    <ng-container>
                                        <td #elem [id]="stock.size">
                                            {{stock.qty | removeZeros}}
                                        </td>
                                    </ng-container>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                    </tr>
                    <tr>
                        <th scope="row">
                            SALES
                        </th>

                        <ng-container *ngFor="let size of mainTableService.sizes$ | async">
                            <ng-container *ngFor="let sale of location.sales">
                                <ng-container *ngIf="sale.size == size.size">
                                    <ng-container>
                                        <td>
                                            {{sale.qty | removeZeros}}
                                        </td>
                                    </ng-container>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                    </tr>
                </ng-container>
            </ng-container>
            <br>
            <!-- ng-container is calculating Total Stock of all stores in each size -->
            <tr>
                <!-- Heading Row-->
                <th scope="row">
                    TOTAL STOCK
                </th>
                <!-- Calculating Part -->
                <ng-container *ngFor="let totalStock of totalStocks$ | async">
                    <td>
                        {{ totalStock | removeZeros }}
                    </td>
                </ng-container>
            </tr>

            <tr>
                <!-- Heading Row-->
                <th scope="row">
                    TOTAL SALES
                </th>
                <!-- Calculating Part -->
                <ng-container *ngFor="let totalSale of totalSales$ | async">
                    <td>
                        {{ totalSale | removeZeros }}
                    </td>
                </ng-container>
            </tr>
        </tbody>
    </table>
</div>