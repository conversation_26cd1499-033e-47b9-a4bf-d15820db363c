import { Action, createReducer, on } from "@ngrx/store";
import { StoreDetailsDto } from "src/app/pos-server.generated";
import * as StoreAction from "./store-info.actions"


export class StoreState {
    isLoading: boolean;
    storeInfo: StoreDetailsDto;
}


export const initialState: StoreState = {
    isLoading: false,
    storeInfo: null
}

export const StoreReducer = createReducer(initialState,

    on(StoreAction.init, () => ({ ...initialState })),

    on(StoreAction.getStoreInfo, (state, action) => ({ ...state, isLoading: true })),

    on(StoreAction.getStoreInfoResponse, (state, action) => ({ ...state, isLoading: false, storeInfo: action.payload }))

)


export function reducers(state: StoreState | undefined, action: Action) {
    return StoreReducer(state, action);
}