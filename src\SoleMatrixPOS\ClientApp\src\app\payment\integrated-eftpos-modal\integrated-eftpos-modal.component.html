<div class="container">
	<div class="container-fluid p-5">
		<div class="row align-items-center">
			<div class="col-1">
				<i class="fas fa-credit-card fa-2x text-success align-items-center"></i>
			</div>
			<div class="col-1">
				<h2 class="text-secondary align-items-center">EFTPOS</h2>
			</div>
			<div class="col-10">
				<button type="button" class="btn btn-circle float-right" (click)="dismiss('Cross click')">
					<i class="fas fa-times fa-2x"></i>
				</button>
			</div>
		</div>
		<hr style="height: 2px;">

		<form [formGroup]="form">
			<label for="amount">Tendered Amount $</label>
			<div class="form-group">
				<div class="input-group">
					<input autofocus name="amount" id="amount" type="text"
						   class="form-control form-control-lg"
						   [ngClass]="{'is-invalid': form.get('Amount')?.invalid && form.get('Amount')?.touched}"
						   formControlName="Amount">

					<div class="input-group-append">
						<button id="remainder" type="button" class="btn btn-outline-default" (click)="useRemainder()">
							Use Remainder
						</button>
					</div>

					<div class="invalid-feedback" *ngIf="form.get('Amount')?.invalid && form.get('Amount')?.touched">
						<div *ngIf="form.get('Amount')?.errors?.min">
							Amount must be greater than or equal to 0.01
						</div>
						<div *ngIf="form.get('Amount')?.errors?.max">
							Amount must be less than or equal to 219.95
						</div>
						<div *ngIf="form.get('Amount')?.errors?.required">
							Amount required
						</div>
						<div *ngIf="form.get('Amount')?.errors?.pattern">
							Amount must be a number
						</div>
					</div>
				</div>
			</div>

			<div class="row justify-content-center p-4">
				<div>
					<button class="btn btn-outline-success" type="button" (click)="processPayment()">
						Process Payment
					</button>
				</div>
			</div>
		</form>
	</div>
</div>
