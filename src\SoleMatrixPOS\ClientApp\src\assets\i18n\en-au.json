{"core": {"buttons": {"OK": "OK", "Cancel": "Cancel", "Submit": "Submit", "Skip": "<PERSON><PERSON>", "Apply": "Apply", "Edit": "Edit"}}, "home-header": {"buttons": {"SelectPrinter": "Select Receipt Printer", "Support": "Support", "EndOfDay": "End Of Day", "ClockOut": "Clock Out", "Logout": "Log Out", "SwitchUser": "Switch User"}}, "end-of-day-float": {"EndFloat": "End Float", "EndFloatValidationMessage": "Please provide a valid {{denomination}} value", "Total": "Total", "buttons": {"Cancel": "Cancel", "Submit": "Next"}}, "end-of-day-cash-drawer": {"CashDrawer": "Cash Drawer", "EndFloatValidationMessage": "Please provide a valid {{denomination}} value", "Total": "Total", "buttons": {"Cancel": "Cancel", "Submit": "Next"}}, "end-of-day-cash-up": {"CashUp": "Cash Up", "EndFloatValidationMessage": "Please provide a valid {{denomination}} value", "Total": "Total", "buttons": {"Cancel": "Cancel", "Submit": "Submit"}}, "home": {"buttons": {"Sales": "Sales", "Returns": "Returns", "Exchange": "Exchange", "AccountPayment": "Account Payment", "Layby": "Layby Payment", "SendStock": "Send Stock", "ReceiveStock": "Receive Stock", "CustomerClub": "Customer Club", "GiftVoucher": "Gift Voucher", "CustomerSpecials": "Customer Specials", "History": "History", "StocktakeEntry": "Stocktake Entry", "StockEnquiry": "Stock Enquiry", "Housekeeping": "Housekeeping", "OpenTill": "Open Till"}, "StaffNoAccess": "You do not have access to anything. Please contact your administrator."}, "login": {"StaffCode": "Staff Code", "Login": "<PERSON><PERSON>", "ClockIn": "Clock In", "ClockOut": "Clock Out", "ClockInMessage": "You will be clocked in at", "ClockOutMessage": "You will be clocked out at", "DailyFloat": "Daily Float", "DailyFloatValidationMessage": "Please provide a valid {{denomination}} value", "Total": "Total"}, "stock-search": {"columns": {"StyleCode": "STYLE", "StyleDescription": "DESCRIPTION", "MakerCode": "MAKER", "DepartmentCode": "DEPARTMENT", "Color": "COLOR", "Size": "SIZE", "StylePrice": "STYLE", "Barcode": "BARCODE"}}, "layby-search": {"columns": {"PhoneNumber": "PHONE", "Email": "EMAIL", "Surname": "SURNAME", "Firstname": "FIRST NAME", "Street": "STREET", "LaybyCode": "LAYBY CODE"}}, "payment": {"Eftpos": {"types": {"DirectDebit": "Direct Debit", "Mastercard": "Mastercard", "Visa": "Visa", "Amex": "Amex", "ZipPay": "Zip Pay", "AfterPay": "After-Pay"}}, "Process": {"OkToProcessPayment": "Ok to Process Payment?", "GoBack": "Go Back", "ProcessPayment": "Process Payment"}, "modal": {"text": {"MaxEftposWarning": "Maximum EFTPOS tender cannot exceed "}, "labels": {"GiftVoucherCode": "Gift Voucher Code", "TenderedAmount": "Tendered Amount $$$", "CustomerReference": "Customer Reference", "Type": "Type"}, "buttons": {"UseRemainder": "Use Remainder", "ApplyAmount": "Apply Amount"}, "validation": {"Eftpos": {"TypeRequired": "Type Required", "AmountMin": "Amount must be greater than or equal to 0.01", "AmountMax": " Amount must be less than or equal to ", "AmountRequired": "Amount Required", "AmountPattern": "Amount must be a valid number (no more than 2 decimal places)"}, "Cheque": {"CharacterLength": "Reference must be less than or equal to 50 characters long", "CustomerReferenceRequired": "Customer Reference Required"}}}}, "customer-club": {"ClubNumber": "Club Number", "Points": "Points", "Title": "Title", "FirstName": "First Name", "LastName": "Last Name", "CareOf": "Care of", "Street": "Street", "Suburb": "Suburb", "State": "State", "Country": "Country", "Postcode": "Postcode", "Email": "Email", "Mobile": "Mobile", "Phone": "Phone", "NoMail": "No mail please", "BarNo": "Bar No.", "Discount": "Discount", "Issued": "Issued", "Expires": "Expires", "Created": "Created", "Altered": "Altered", "FirstSale": "FirstSale", "LastSale": "LastSale", "StaffDetails": "Staff Details", "VIP": "VIP", "buttons": {"Details": "Details", "History": "History", "Notes": "Notes", "Save": "Save Changes", "Use": "Use Selected", "Deselect": "Deselect", "Show": "Show", "Hide": "<PERSON>de", "Back": "Go Back to Member Search", "BackLayby": "Go Back to Layby"}}, "note-modal": {"NewNote": "New Note", "Note": "Note", "Date": "Date", "Time": "Time", "SaveChanges": "Save Changes"}, "customer-club-error": {"PointsValidationMessage": "Invalid Points", "NameValidationMessage": "Invalid Name", "StreetValidationMessage": "Invalid Street", "SuburbValidationMessage": "Invalid Suburub", "StateValidationMessage": "Invalid State", "CountryValidationMessage": "Invalid Country", "PostcodeValidationMessage": "Invalid Postcode. Must be 4 digits long", "EmailValidationMessage": "<PERSON><PERSON><PERSON>", "PhoneValidationMessage": "Invalid Phone number. Must be between 8 and and 11 digits long including spaces"}, "suspend-sale-modal": {"OkToSuspendSale": "Do you wish to Suspend this Sale?", "ContinueToSuspendSale": "Suspend Sale"}}