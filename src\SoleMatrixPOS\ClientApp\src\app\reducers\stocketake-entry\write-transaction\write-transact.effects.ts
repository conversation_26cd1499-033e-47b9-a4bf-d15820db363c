import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { EMPTY } from "rxjs";
import { catchError, map, mergeMap } from "rxjs/operators";
import { StocktakeEntryClient } from "src/app/pos-server.generated";
import * as transactAction from '../write-transaction/write-transact.actions'



@Injectable()
export class WriteTransactionEffect {

    constructor(
        private actions$: Actions,
        private stocktakeEntryClient: StocktakeEntryClient
    ) { }


    writeTransaction$ = createEffect(() => this.actions$.pipe(
        ofType(transactAction.submitTrans),
        mergeMap((action) => this.stocktakeEntryClient.logTransRecord(action.payload)
            .pipe(
                map((response) => {
                    console.log(response)
                    return transactAction.submitTransConfirmation();
                },catchError(() => EMPTY))
            )
        )
    ));
}

