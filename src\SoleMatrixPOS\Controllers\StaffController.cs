using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Infrastructure;
using SoleMatrixPOS.Application.Staff;
using SoleMatrixPOS.Application.Staff.Commands;
using SoleMatrixPOS.Identity.Interface;

namespace SoleMatrixPOS.Controllers
{
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController]
    public class StaffController : ControllerBase
    {

		private readonly IMediator _mediator;
		private readonly StaffCodeContext _staffCodeContext;
		private readonly RegisteredTillService _tillService;
		public StaffController(RegisteredTillService tillService, IMediator mediator, StaffCodeContext staffCodeContext)
		{
			_mediator = mediator;
			_staffCodeContext = staffCodeContext;
			_tillService = tillService;
		}

		[Route("Login")]
		public async Task<ActionResult<StaffLoginResponseDto>> Login(string code, CancellationToken ct)
		{
			if (code == null || code.Length < 0 || code.Length > 8) return Ok(new { resultCode = 10 });
			return await _mediator.Send(new StaffLogin(code, (await _tillService.GetContextualTillAsync()).StoreId, (await _tillService.GetContextualTillAsync()).TillId));

		}
	}
}
