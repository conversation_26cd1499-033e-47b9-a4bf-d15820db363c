import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ElementRef } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, map, takeWhile, take, finalize } from 'rxjs/operators';
import { CustomerClubDto, SalesByStaffQueryDto, DailyDto, HistoryDto, HistorySearchFields, HistorySearchRequestDto, SortDirection } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import * as dailySelectors from '../reducers/daily/daily.selectors';
import * as historySelectors from '../reducers/customer-club/history/history.selector';
import * as historyActions from '../reducers/customer-club/history/history.actions';
import * as dailyActions from '../reducers/daily/daily.actions';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CustomerClubMemberComponent } from '../customer-club/customer-club-member/customer-club-member.component';
import { DailyTotalComponent } from './daily-total/daily-total.component';
import { PrintReceiptModalComponent } from './print-receipt-modal/print-receipt-modal.component';
import Swal from 'sweetalert2';
import { HistoryRefundComponent } from './history-refund/history-refund.component';
@Component({
  selector: 'pos-history',
  templateUrl: './history.component.html',
  styleUrls: ['./history.component.scss']
})
export class HistoryComponent implements OnInit {

  field = 'Date';
  public term: string;
  public selectedDates: Date[];
  public transactionHistory$: Observable<HistoryDto[]>;
  public dailyTotal$: Observable<DailyDto>;
  public salesByStaff$: Observable<SalesByStaffQueryDto>;
  public showDailyTotal = false;
  public loading = false;

  private searchTerm$ = new Subject<string>();
  private _member: CustomerClubDto;

  private alive = true;

  @Output()
  searchChanged = new EventEmitter<HistorySearchRequestDto>();

  @Input() options: HistorySearchRequestDto;
  @Input() set member(member: CustomerClubDto) {
    this._member = member;
  }

  // Add new properties for infinite scroll
  public isLoadingMore = false;
  public noMoreResultsToLoad = false;
  public previousTransactionCount = 0;
  public currentSkip = 0;
  @ViewChild('scrollContainer', { static: false }) scrollContainer: ElementRef;

  constructor(private store: Store<AppState>, private modalService: NgbModal) { }

  ngOnInit() {
    const savedField = localStorage.getItem('history_search_field');
    if (savedField) {
      this.field = savedField;
    }
    // Set today's date as the default selected date
    this.selectedDates = [new Date(), null];
  
    // Init state
    this.store.dispatch(historyActions.init());
    this.store.dispatch(dailyActions.init());
  
    this.transactionHistory$ = this.store.select(historySelectors.searchedHistory);
    this.dailyTotal$ = this.store.select(dailySelectors.storeTodaysDaily);
    
    this.searchTerm$.pipe(
      takeWhile(() => this.alive),
      debounceTime(500),
      distinctUntilChanged(),
      map(() => this.searchChanged.emit({
        searchString: this.term,
        first: 25,
        clientNumber: "",
        sortDirection: SortDirection.DESC,
        searchByField: HistorySearchFields[this.field],
        orderByField: HistorySearchFields[this.field],
      })),
    ).subscribe((s) => this.doSearch({
      searchString: this.term,
      first: 25,
      clientNumber: "",
      sortDirection: SortDirection.DESC,
      searchByField: HistorySearchFields[this.field],
      orderByField: HistorySearchFields[this.field],
    }));
    
    // Trigger the initial search with today's date
    this.search();

    // Add subscription to handle loading states
    this.transactionHistory$.pipe(
      takeWhile(() => this.alive)
    ).subscribe(transactions => {
      if (this.isLoadingMore) {
        if (transactions.length === this.previousTransactionCount) {
          console.log("Load more returned no results. Preventing further loads.");
          this.noMoreResultsToLoad = true;
        }
        this.isLoadingMore = false;
      }
    });
  }
  

  search() {
    localStorage.setItem('history_search_field', this.field);
    this.loading = true;
    // Reset pagination state when starting a new search
    this.currentSkip = 0;
    this.isLoadingMore = false;
    this.noMoreResultsToLoad = false;
    this.previousTransactionCount = 0;

    if (this.field === 'Date' && this.selectedDates) {
      console.log(this.selectedDates);
      if (this.selectedDates[1] === null) {
        // Only one date is selected, so perform a search for this date
        const selectedDate = this.selectedDates[0];
        const formattedDate = this.formatDate(selectedDate);
        this.searchTerm$.next(formattedDate);
      } else if (this.selectedDates[1] !== null) {
        const formattedStartDate = this.formatDate(this.selectedDates[0]);
        const formattedEndDate = this.formatDate(this.selectedDates[1]);
        const dateRange = `${formattedStartDate} - ${formattedEndDate}`;
        this.searchTerm$.next(dateRange);
      }
    } else {
      // For other fields, perform a search as usual
      this.searchTerm$.next(this.term);
    }
  }

  // Helper function to parse time string to Date object
  private parseTimeString(timeStr: string): Date {
    const today = new Date();
    const isPM = timeStr.toLowerCase().includes('pm');
    const timeParts = timeStr.replace(/\s*(AM|PM)\s*$/i, '').split(':');
    
    let hours = parseInt(timeParts[0]);
    const minutes = parseInt(timeParts[1]);
    const seconds = parseInt(timeParts[2]);
    
    // Convert to 24-hour format if PM
    if (isPM && hours !== 12) {
      hours += 12;
    }
    // Convert 12 AM to 0 hours
    if (!isPM && hours === 12) {
      hours = 0;
    }
    
    return new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes, seconds);
  }

  // Helper function to create a full datetime from date and time
  private createDateTime(date: Date | string | null, timeStr: string | null): Date {
    // Use current date if date is null, otherwise parse the date string
    const baseDate = date ? new Date(date) : new Date();
    // Use 00:00:00 if time is null
    const timeDate = timeStr ? this.parseTimeString(timeStr) : new Date(0, 0, 0, 0, 0, 0);
    
    // Combine date and time
    return new Date(
      baseDate.getFullYear(),
      baseDate.getMonth(),
      baseDate.getDate(),
      timeDate.getHours(),
      timeDate.getMinutes(),
      timeDate.getSeconds()
    );
  }

  doSearch(options: HistorySearchRequestDto, loadMore = false) {
    if (this.field === 'Date' && this.selectedDates) {
      if (this.selectedDates[1] === null) {
        // Perform the search with the selected date
        options.searchString = this.formatDate(this.selectedDates[0]);
      } else if (this.selectedDates[1] !== null) {
        const formattedStartDate = this.formatDate(this.selectedDates[0]);
        const formattedEndDate = this.formatDate(this.selectedDates[1]);
        options.searchString = `${formattedStartDate} - ${formattedEndDate}`;
      }
    }

    this.store.dispatch(historyActions.search({ searchParams: options, loadMore }));

    // If searching by time or date, we need to handle the sorting after getting results
    if (this.field === 'Time' || this.field === 'Date') {
      this.transactionHistory$ = this.transactionHistory$.pipe(
        map(transactions => {
          return transactions.sort((a, b) => {
            // Create full datetime objects for comparison
            const dateTimeA = this.createDateTime(a.transactionDate, a.transactionTime);
            const dateTimeB = this.createDateTime(b.transactionDate, b.transactionTime);
            
            return options.sortDirection === SortDirection.DESC ? 
              dateTimeB.getTime() - dateTimeA.getTime() : 
              dateTimeA.getTime() - dateTimeB.getTime();
          });
        })
      );
    }

    // Subscribe to the search results to know when loading is complete
    this.transactionHistory$.pipe(
      take(1),
      finalize(() => this.loading = false)
    ).subscribe();
  }

  // Helper function to format the date
  private formatDate(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are zero-based
    // TODO - Confirm this works still
    const year = date.getFullYear();
    return `${year}-${month}-${day}`;
  }

  showDaily() {
    // Get data
    this.showDailyTotal = true;
  }

  hideDaily() {
    this.showDailyTotal = false;
  }

  openDailyModal() {
    const modalRef = this.modalService.open(DailyTotalComponent, { 
      windowClass: 'daily-modal-window', 
      size: 'xl', 
      centered: true 
    });
    modalRef.componentInstance.name = 'DailyTotalModal';
    
    // Pass the currently selected dates to the modal
    modalRef.componentInstance.selectedDates = [...this.selectedDates];
    
    modalRef.result.then((result) => {
      if (result) {
        console.log('result from modal:', result);
      }
    }).catch((reason) => console.log(reason));
  }

  openReceiptModal(transNo, transType, tillNo, storeId) {
    console.log('Opening receipt modal with:', { transNo, transType, tillNo, storeId });
    const modalRef = this.modalService.open(PrintReceiptModalComponent, { size: 'm', centered: true });
    modalRef.componentInstance.name = 'PrintReceiptModal';
    modalRef.componentInstance.transNo = transNo;
    modalRef.componentInstance.transType = transType;
    modalRef.componentInstance.tillNo = tillNo;
    modalRef.componentInstance.storeId = storeId;
    modalRef.result.then((result) => {
      if (result) {
        console.log('result from modal:', result);
      }
    }).catch((reason) => console.log(reason));
  }

  launchCustomerModal(custId: string) {
    let modalRef = this.modalService.open(CustomerClubMemberComponent, { size: 'xl', centered: true });
    modalRef.componentInstance.name = 'CustomerClubMemberModal';
    modalRef.componentInstance.member = {
      clientCode: custId,
      title: "",
      firstname: "",
      surname: "",
      careof: "",
      street: "",
      suburb: "",
      state: "",
      postcode: "",
      email: "",
      telephone: "",
      clientPoints: 0,
    };

    console.log("Loading member details:", modalRef.componentInstance.loadMemberWithId(custId));
    modalRef.result.then((result) => {
      if (result) {
        console.log('result from modal:', result);
      }
    });
  }

  openReturnModal(transNo, transType, tillNo, storeId) {
    Swal.fire({
      title: 'Return Transaction',
      text: `Are you sure you want to return transaction #${transNo}?`,
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, return it!'
    }).then((result) => {
      if (result) {
        const modalRef = this.modalService.open(HistoryRefundComponent, { 
          backdrop: 'static',
          keyboard: false
        });
        
        modalRef.componentInstance.transNo = transNo;
        modalRef.componentInstance.transType = transType;
        modalRef.componentInstance.tillNo = tillNo;
        modalRef.componentInstance.storeId = storeId;

        modalRef.result.catch((reason) => {
          console.log('Modal dismissed:', reason);
          // Handle any errors here
        });
      }
    });
  }

  // Add loadMore function
  loadMore() {
    if (this.loading || this.isLoadingMore || this.noMoreResultsToLoad) return;

    this.isLoadingMore = true;
    this.currentSkip += 25;

    // Get the current search string based on dates or term
    let searchString = this.term;
    if (this.field === 'Date' && this.selectedDates) {
      if (this.selectedDates[1] === null) {
        searchString = this.formatDate(this.selectedDates[0]);
      } else {
        const formattedStartDate = this.formatDate(this.selectedDates[0]);
        const formattedEndDate = this.formatDate(this.selectedDates[1]);
        searchString = `${formattedStartDate} - ${formattedEndDate}`;
      }
    }

    let options: HistorySearchRequestDto = {
      searchString: searchString,
      first: 25,
      clientNumber: "",
      sortDirection: SortDirection.DESC,
      searchByField: HistorySearchFields[this.field],
      orderByField: HistorySearchFields[this.field],
      skip: this.currentSkip
    };

    // Store the current count before loading more
    this.transactionHistory$.pipe(take(1)).subscribe(transactions => {
      this.previousTransactionCount = transactions.length;
    });

    this.store.dispatch(historyActions.search({ searchParams: options, loadMore: true }));
  }

  // Add scroll event handler
  onScroll(event: any) {
    // Get the scroll container element
    const container = this.scrollContainer.nativeElement;
    
    // Calculate scroll metrics
    const scrollTop = container.scrollTop;
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;
    
    // Calculate how close to the bottom we are
    const scrollPosition = scrollTop + clientHeight;
    const threshold = 100; // Load more when within 100px of the bottom
    const distanceToBottom = scrollHeight - scrollPosition;
    
    // Check if we're near the bottom
    if (distanceToBottom <= threshold && !this.isLoadingMore && !this.loading && !this.noMoreResultsToLoad) {
      console.log('Loading more results...');
      this.loadMore();
    }
  }
}
