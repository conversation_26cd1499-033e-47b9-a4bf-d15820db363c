import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { GetSystemStatusDto, SysControlUpdateDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import * as SysConfigActions from '../../reducers/sys-config/sys-config.actions';
import * as SysConfigSelectors from '../../reducers/sys-config/sys-config.selectors';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { LinklyPINPairingModalComponent } from '../pinpairing-modal/linkly-pinpairing-modal.component';
import { tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { on } from 'process';
import { TyroSettingsModalComponent } from '../pinpairing-modal/tyro-settings-modal/tyro-settings-modal.component';

@Component({
  selector: 'pos-config',
  templateUrl: './config.component.html',
  styleUrls: ['./config.component.scss']
})
export class ConfigComponent implements OnInit {
	eftProviders: string[] = ['None', 'Linkly', 'Tyro'];
	selectedEFTProvider: string;
	sysConfig: GetSystemStatusDto;
	managerSys$: Observable<GetSystemStatusDto>;
	updateStatus$: Observable<string>;
	isOtherOptionsVisible: boolean = false; 
	constructor(private store: Store<AppState>, private modalService: NgbModal, private router: Router) { }

  ngOnInit() {
    // Dispatch action to fetch configuration
    this.store.dispatch(SysConfigActions.readSysConfig());
  
    this.managerSys$ = this.store.select(SysConfigSelectors.selectSysConfig).pipe(
      tap(result => {
		  if (result) {
			  this.sysConfig = result;
			  this.selectedEFTProvider = result.integratedEFTProvider;
			  console.log(result);
        }
      })
    );

    this.updateStatus$ = this.store.select(SysConfigSelectors.selectUpdateStatus);
  }

  toggleOtherOptions() {
    this.isOtherOptionsVisible = !this.isOtherOptionsVisible;
  }

  validateInputDigit(e){
    var code = e.keyCode;
    var allowedKeys = [2];
    if (allowedKeys.indexOf(code) !== -1) {
      return;
    }
    e.target.value = e.target.value.replace(
       /^([1-9]\/|[2-9])$/g, '0$1' // 3 > 03/
     )
    //.replace(
    //   /^(0[1-9]|1[0-2])$/g, '$1' // 11 > 11/
    // ).replace(
    //   /^([0-1])$/g, '0$1' // 1 > 01
    // ).replace(
    //   /^([0]+)+$/g, '00' // 0/ > 0 and 0 > 00
    // ).replace(
    //   /^(-[0-9]+)|('')+$/g, '00' // 0/ > if negative or blank > 00 
    // )
  }
  
  saveConfig(data) {
    console.log(data);

    const sysControlUpdateDto: SysControlUpdateDto = {
        client_Database: data.client_Database, //TODO
        demo_Q1: data.demo_Q1, //TODO
        demo_Q2: data.demo_Q2, //TODO
        demo_Q3: data.demo_Q3, //TODO
        demo_Q4: data.demo_Q4, //TODO
        suspend_Sale: data.suspend_Sale, //TODO
        suspend_Rtn: data.suspend_Rtn, //TODO
        suspend_Tfr: data.suspend_Tfr, //TODO
        suspend_Laypay: data.suspend_Laypay, //TODO
        tender_Centrev: data.tender_Centrev, //TODO
        points_Per_Dollar: data.points_Per_Dollar,
        non_Coloursize: data.non_Coloursize, //TODO
        customer_Accounts: data.customer_Account,
        auto_Disc: data.auto_Disc, //TODO
        integrated_Eft_Provider: data.integrated_Eft_Provider,
        order_Deposit: data.order_Deposit,
        onHold_Deposit: data.on_Hold_Deposit,
        layby_Deposit: data.layby_Deposit,
        always_Open_Cash_Drawer: data.always_Open_Cash_Drawer,
        customer_Order_No_Stock_Movement: data.customer_Order_No_Stock_Movement, //TODO
        open_Layby: data.open_Layby, //TODO
        point_On_All_Sales: data.point_On_All_Sales,
        price_Prompt: data.price_Prompt, //TODO
        no_Rounding: data.no_Rounding, //TODO
        layby_Number_Of_Weeks: data.layby_Number_Of_Weeks,
        block_Exchange_Button: data.block_Exchange_Button, //TODO
        enter_Order_No_And_No_Deposit: data.enter_Order_No_And_No_Deposit, //TODO
        header_Image: data.header_Image, //TODO
        footer_Image: data.footer_Image, //TODO
        consolidate_C_Tran: data.consolidate_C_Tran, //TODO
        customer_Name_On_Receipt: data.customer_Name_On_Receipt, //TODO
        customer_Account_Print_On_A4: data.customer_Account_Print_On_A4, //TODO
        demographic_Question_Ask: data.demographic_Question_Ask, //TODO
        customer_Club_Mandatory: data.customer_Club_Mandatory, //TODO
        customer_Club_Birthday: data.customer_Club_Birthday, //TODO
        basic_Cash_Up: data.basic_Cash_Up, //TODO
        soft_Credit_Limit: data.soft_Credit_Limit,
		dollar_Per_Points: data.dollar_Per_Points,
		integrated_Eft_Receipt: this.sysConfig.integrated_Eft_Receipt,
		integrated_Eft_Surcharge: this.sysConfig.integrated_Eft_Surcharge
    };

    console.log("Updated DTO:", sysControlUpdateDto);
    this.store.dispatch(SysConfigActions.updateSysConfig({ updateSys: sysControlUpdateDto }));
    this.router.navigate(['/home']);
}


	openPairing() {
		// Open pairing modal
		if (this.selectedEFTProvider == "Linkly") {
			const modalRef = this.modalService.open(LinklyPINPairingModalComponent);
		}
		else if (this.selectedEFTProvider == "Tyro") {
			const modalRef = this.modalService.open(TyroSettingsModalComponent);
		}
	}
}
