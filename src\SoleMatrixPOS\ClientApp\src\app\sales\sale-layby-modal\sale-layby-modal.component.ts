import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { Store } from '@ngrx/store';
import { CustomerClubDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import { Router } from '@angular/router';

import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';

import * as customerClubSearchActions from '../../reducers/customer-club/club-search/customer-club.actions';

import * as customerAddActions from '../../reducers/customer-club/customer-add/customer-add.actions'

import * as customerAddSelectors from '../../reducers/customer-club/customer-add/customer-add.selectors'
import { timeStamp } from 'console';
import { MemberAddStatus } from 'src/app/reducers/customer-club/customer-add/customer-add.reducer';
import { CreateErrorModal } from 'src/app/error-modal/error-modal.component';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';

import * as laybyActions from '../../reducers/layby/layby.actions';
import * as laybySelectors from '../../reducers/layby/layby.selectors';
import { Observable } from 'rxjs';
import { take } from 'rxjs/operators';
import { CustomerClubModalComponent } from '../../customer-club/customer-club-modal/customer-club-modal.component';

@Component({
  selector: 'pos-sale-layby-modal',
  templateUrl: './sale-layby-modal.component.html',
  styleUrls: ['./sale-layby-modal.component.scss']
})
export class SaleLaybyModalComponent implements OnInit {
  addedCustomer: CustomerClubDto;
  laybyActive$: Observable<boolean>;

  constructor(private store: Store<AppState>, private formBuilder: FormBuilder, private modalService: NgbModal, private activeModal: NgbActiveModal, private router: Router) { }

  selectedClubMember: CustomerClubDto;

  handlingCustClub: boolean = false;

  public laybyApplicationForm = this.formBuilder.group({
    txtClubNo: [],
    txtTitle: [],
    txtFirstName: [],
    txtLastName: [],
    txtStreet: [],
    txtSuburb: [],
    txtState: [],
    txtPostcode: [],
    txtPhone: [],
    txtEmail: []
  });

  ngOnInit() {
    this.initState();
    this.subscribeToState();
    this.laybyApplicationForm.disable();
  }

  initState() {
    this.store.dispatch(customerAddActions.init());
  }

  subscribeToState() {
    this.store.select(customerClubSearchSelectors.selectedCustomerClubMember).subscribe(
      (selected) => {
        console.log("Customer logged: ", selected);
        this.selectedClubMember = selected;
        if(selected != null) this.fillFormWithCustClubInfo(selected);
      }
    )

    this.store.select(customerAddSelectors.customer).subscribe(
      cust => this.addedCustomer = cust
    );

    this.store.select(customerAddSelectors.status).subscribe(
      s => this.handleAddStatus(s)
    );

    this.laybyActive$ = this.store.select(laybySelectors.active);

  }

  handleAddStatus(status: MemberAddStatus): void {
    switch(status){
      case MemberAddStatus.Success: {
        // Set the added to the actual
        this.selectedClubMember = this.addedCustomer;

        console.info("Customer successfully added!")

        // Register layby
        this.registerLayby();
        return;
      }

      case MemberAddStatus.Error:{
        // Show error modal
        CreateErrorModal(this.modalService, true, "Failed to create new member through layby. Please try again later, or contact support.");
        return;
      }

      default: {
        break;
      }
       
    }
  }

  registerLayby() {
    // Return a modal result
    this.activeModal.close(this.selectedClubMember);
  }

  cancelLayby() {
    this.store.dispatch(laybyActions.init());
    this.activeModal.dismiss("Cancelled.");
    this.router.navigateByUrl('/sales');
  }

  fillFormWithCustClubInfo(clubInfo: CustomerClubDto) {
    this.laybyApplicationForm.patchValue({
      txtClubNo: clubInfo.clientCode,
      txtTitle: clubInfo.title,
      txtFirstName: clubInfo.firstname,
      txtLastName: clubInfo.surname,
      txtStreet: clubInfo.street,
      txtSuburb: clubInfo.suburb,
      txtState: clubInfo.state,
      txtPostcode: clubInfo.postcode,
      txtPhone: clubInfo.telephone,
      txtEmail: clubInfo.email
    });
  }

  onMemberSelected(result: CustomerClubDto){
    this.selectedClubMember = result;
  }

  onBack(){
    this.handlingCustClub = false;
  }

  onUseSelected(){
    this.store.dispatch(customerClubSearchActions.selectCustomerClubMember({payload: this.selectedClubMember})); 
    this.handlingCustClub = false;
  }

  submitLayby() {
    if (!this.selectedClubMember) {
      CreateErrorModal(this.modalService, true, "Please select a customer first.");
      return;
    }
    this.registerLayby();
  }

  private updateLaybyForm(clubInfo: CustomerClubDto) {
    this.laybyApplicationForm.patchValue({
      txtClubNo: clubInfo.clientCode,
      txtTitle: clubInfo.title,
      txtFirstName: clubInfo.firstname,
      txtLastName: clubInfo.surname,
      txtStreet: clubInfo.street,
      txtSuburb: clubInfo.suburb,
      txtState: clubInfo.state,
      txtPostcode: clubInfo.postcode,
      txtPhone: clubInfo.telephone,
      txtEmail: clubInfo.email
    });
  }

}
