using System;
using System.Threading.Tasks;
using MediatR; 
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Daily;
using SoleMatrixPOS.Application.Daily.Commands;
using SoleMatrixPOS.Application.Float;
using SoleMatrixPOS.Application.Float.Commands;
using SoleMatrixPOS.Application.Float.Queries;
using SoleMatrixPOS.Application.Infrastructure;
using SoleMatrixPOS.Filters;

namespace SoleMatrixPOS.Controllers
{
    [Route("api/[controller]")]
    [RequireStaffCodeFilter]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController]
    public class DailyFloatController : ControllerBase
    {
        private readonly IMediator _mediator;
		private readonly StaffCodeContext _staffCodeContext;
		private readonly StoreTimeContext _storeTimeContext;

		public DailyFloatController(IMediator mediator, StaffCodeContext staffCodeContext, StoreTimeContext storeTimeContext)
		{
			_mediator = mediator;
			_staffCodeContext = staffCodeContext;
			_storeTimeContext = storeTimeContext;
		}

		[HttpPut]
		public async Task<IActionResult> EnterFloat([FromBody] FloatDto floatDto)
		{
			floatDto.TillNo = _staffCodeContext.PINPadNo;
			floatDto.StoreId = _staffCodeContext.StoreDetailsDto.StoreId;
			floatDto.TransactionDate = _storeTimeContext.StoreLocalTime;

			// Create daily object as well
			DailyDto dailyDto = new DailyDto
			{
				TransactionDate = _storeTimeContext.StoreLocalTime.Date,
				StoreId = floatDto.StoreId,
				TillNo = floatDto.TillNo,
				AmexTotal = 0,
				BcardTotal = 0,
				VisaTotal = 0,
				McardTotal = 0,
				CashTotal = 0,
				DinersTotal = 0,
				EftTotal = 0,
				ChqTotal = 0
			};

			// Send the commands
			await _mediator.Send(new CreateInitialDailyRecordCommand(dailyDto));
			await _mediator.Send(new EnterFloatCommand(floatDto));

			return Ok();
		}


        [HttpGet]
        public async Task<string> FloatReport(string storeId, string PINPadNo, DateTime transactionDate, FloatType floatType)
        {
			// TODO implement float report. and remove hardcoding details inside the floatreportQuery ie. 'EVANS SHOES' '256 Hargrave St' 'VIC 3041' etc
            return await _mediator.Send(new FloatReportQuery(_staffCodeContext.StoreDetailsDto.StoreId, _staffCodeContext.PINPadNo, _storeTimeContext.StoreLocalTime, floatType));
        }
    }
}
