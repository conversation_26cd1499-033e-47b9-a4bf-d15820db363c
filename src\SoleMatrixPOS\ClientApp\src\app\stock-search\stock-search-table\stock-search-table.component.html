<div class="fixed-container">
	<table class="table table-striped table-hover table-scroll">
		<thead>
		<tr>
			<th scope="col" sortable="style">Style</th>
			<th scope="col" sortable="description">Description</th>
			<th scope="col" sortable="maker">Maker</th>
			<th scope="col" sortable="dept">Dept</th>
			<th scope="col" sortable="colour">Colour</th>
			<th scope="col" sortable="size">Size</th>
			<th scope="col" sortable="Price">Price</th>
		</tr>
		</thead>
		<tbody class="body-half-screen"
			   infinite-scroll
			   [infiniteScrollDistance]="scrollDistance"
			   [infiniteScrollThrottle]="throttle"
			   [scrollWindow]="false"
			   (scrolled)="onScrollDown()">
		<tr *ngFor="let item of items; let i = index" (click)="selectItem(item)">
			<td><ngb-highlight [result]="item.styleCode" [term]="options.searchString" [highlightClass]="'search-highlight'"></ngb-highlight></td>
			<td><ngb-highlight [result]="item.styleDescription" [term]="options.searchString" [highlightClass]="'search-highlight'"></ngb-highlight></td>
			<td><ngb-highlight [result]="item.makerCode" [term]="options.searchString" [highlightClass]="'search-highlight'"></ngb-highlight></td>
			<td><ngb-highlight [result]="item.departmentCode" [term]="options.searchString" [highlightClass]="'search-highlight'"></ngb-highlight></td>
			<td><ngb-highlight [result]="item.colourName" [term]="options.searchString" [highlightClass]="'search-highlight'"></ngb-highlight></td>
			<td><ngb-highlight [result]="item.size" [term]="options.searchString" [highlightClass]="'search-highlight'"></ngb-highlight></td>
			<td><ngb-highlight [result]="item.price | currency" [term]="options.searchString" [highlightClass]="'search-highlight'"></ngb-highlight></td>
		</tr>
		</tbody>
	</table>
</div>
<div class="spinner-row"><div *ngIf="isLoading" class="lds-hourglass"><div></div></div></div>
