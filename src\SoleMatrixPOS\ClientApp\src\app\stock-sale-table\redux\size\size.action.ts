import { Action } from '@ngrx/store';
import { Size } from './size.model';

export const LOAD_SIZES_REQUESTED: string = "[SIZE] loadReq";

export const LOAD_SIZES: string = "[SIZE] load";

/**
 * Request action, this is the preferred action for loading data
 * as it is caught by an effect that ensures successive actions
 * are triggered in the correct order
 */
export class LoadSizesRequested implements Action {
    readonly type = LOAD_SIZES_REQUESTED;
}

/**
 * An array of sites can be given to this action and the store
 * will replace the sizes state slice with it
 */
export class LoadSizesAction implements Action {
    readonly type = LOAD_SIZES;

    constructor(
        public sizes: Size[]
    ){}
}

export type SizeActionType = 
LoadSizesRequested | 
LoadSizesAction;