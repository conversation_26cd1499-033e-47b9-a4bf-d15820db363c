<pos-nav-header [pageName]="getPageTitle()"></pos-nav-header>

<div class="container-fluid mt-2">
  <!-- Customer Details Form -->
  <div class="card form-wrapper">
    <div class="d-flex tab-group">
      <button type="button" class="flex-fill py-3 px-4 border-0 tab-button" [class.active-tab]="mode === 'order'"
        [class.inactive-tab]="mode !== 'order'" (click)="setMode('order')">
        <i class="fas fa-shopping-cart"></i> Orders
      </button>
      <button type="button" class="flex-fill py-3 px-4 border-0 tab-button" [class.active-tab]="mode === 'quote'"
        [class.inactive-tab]="mode !== 'quote'" (click)="setMode('quote')">
        <i class="fas fa-file-alt"></i> Quotes
      </button>
      <button type="button" class="flex-fill py-3 px-4 border-0 tab-button" [class.active-tab]="mode === 'hold'"
        [class.inactive-tab]="mode !== 'hold'" (click)="setMode('hold')">
        <i class="fas fa-clock"></i> On Hold
      </button>
    </div>

    <div class="search-section py-3 px-4 bg-light border-bottom">
      <div class="text-center">
        <button type="button" class="btn btn-info btn-lg" [disabled]="isLoading" (click)="openSearchModal()">
          <i class="fas fa-search me-2"></i>
          Search {{ mode === 'order' ? 'Orders' : mode === 'quote' ? 'Quotes' : 'Holds' }}
        </button>
      </div>
    </div>

    <div class="card-body">
      <h4 class="mb-4 section-title">Customer Details</h4>

      <form [formGroup]="customerOrderForm" autocapitalize="on">
        <div class="row mb-4">
          <div class="col-md-6">
            <label class="form-label">Club Number</label>
            <input type="text" class="form-control" formControlName="clubNumber" [readonly]="true">
          </div>
          <div class="col-md-6">
            <label class="form-label">Search Customer</label>
            <button class="form-control btn btn-outline-primary search-button" type="button" (click)="searchCustomer()">
              <i class="fas fa-search"></i> Find Customer
            </button>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-2">
            <label class="form-label">Title</label>
            <input type="text" class="form-control" formControlName="title">
          </div>
          <div class="col-md-5">
            <label class="form-label">First Name</label>
            <input type="text" class="form-control" formControlName="firstName" required>
          </div>
          <div class="col-md-5">
            <label class="form-label">Surname</label>
            <input type="text" class="form-control" formControlName="lastName" required>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-12">
            <label class="form-label">Street</label>
            <input type="text" class="form-control" formControlName="street">
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-6">
            <label class="form-label">Suburb / Town</label>
            <input type="text" class="form-control" formControlName="suburb">
          </div>
          <div class="col-md-4">
            <label class="form-label">State</label>
            <input type="text" class="form-control" formControlName="state">
          </div>
          <div class="col-md-2">
            <label class="form-label">Postcode</label>
            <input type="text" class="form-control" formControlName="postcode">
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-6">
            <label class="form-label">Phone</label>
            <input type="text" class="form-control" formControlName="phone">
          </div>
          <div class="col-md-6">
            <label class="form-label">Email</label>
            <input type="email" class="form-control" formControlName="email">
          </div>
        </div>

        <!-- Centered Buttons -->
        <div class="button-group text-center mt-4">
          <button type="button" class="btn btn-success" (click)="createEntry()">
            <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2" role="status"
              aria-hidden="true"></span>
            <span *ngIf="!isLoading">Create New {{ mode === 'order' ? 'Order' : mode === 'quote' ? 'Quote' : 'Hold'
              }}</span>
            <span *ngIf="isLoading">Processing...</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal Templates -->
<ng-template #content let-modal>
  <div class="modal-header">
    <h4 class="modal-title">Customer Club Search</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <pos-customer-club-container (onSelectedMember)="onMemberSelected($event)" (onMemberDoubleClick)="onUseSelected()">
    </pos-customer-club-container>
    <div class="small text-muted text-end mt-2">
      <i class="fas fa-info-circle"></i> Double click member to select and proceed
    </div>
  </div>
</ng-template>

<ng-template #confirmExistingMemberModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">Member Already Exists</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <p>A member with the provided phone number already exists.</p>
    <p>Do you want to fill in their details?</p>
  </div>
  <div class="modal-footer">
    <button class="btn btn-secondary" (click)="modal.dismiss('No')">No</button>
    <button class="btn btn-primary" (click)="modal.close('Yes')">Yes</button>
  </div>
</ng-template>