import { Component, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { GetLast, GetSystemStatusDto, StaffLoginDto, SuspendSaleClient, SuspendTransToCartDto } from 'src/app/pos-server.generated';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import * as staffSelectors from '../../reducers/staff/staff.selectors';
import { StaffAccess } from '../../reducers/staff/staff.selectors';
import { UrlHistoryService } from 'src/app/url-history.service';
import * as customerClubSearchActions from '../../reducers/customer-club/club-search/customer-club.actions';
import * as StoreAction from '../../reducers/store-info/store-info.actions';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { CustomerSpecialsComponenet } from 'src/app/customer-specials/customer-specials.component';
import * as  SysConfigSelectors from '../../reducers/sys-config/sys-config.selectors';
import * as SysConfigAction from '../../reducers/sys-config/sys-config.actions';
import { readPINAuth } from '../../reducers/pin-auth/pin-auth.actions';
import * as cartActions from '../../reducers/sales/cart/cart.actions';
import * as  SysSelector from '../../reducers/house-keeping/mLogin.selectors';
import * as houseActions from '../../reducers/house-keeping/mLogin.actions';
import { DownloadPrintServiceModalComponent } from '../../printing/download-print-service-modal/download-print-service-modal.component';
import { PrintingService } from '../../printing/printing.service';
import * as orderActions from '../../reducers/order-item/order.actions';
import { EftposService } from '../../eftpos/eftpos.service';
import * as transActions from '../../reducers/transaction/transaction.actions';
import { tap } from 'rxjs/operators';
import { TyroIClientService } from '../../eftpos/tyro-iclient.service';
import * as stockSearchActions from '../../reducers/stock-search/stock-search.actions';
import * as customerClubActions from '../../reducers/customer-club/club-search/customer-club.actions';
import * as accountPaymentActions from '../../reducers/account-payments/account-payment.actions';
import * as endOfDayFloatActions from '../../reducers/end-of-day/end-of-day-float/end-of-day-float.actions';
import * as historyActions from '../../reducers/customer-club/history/history.actions';
import * as endOfDayCashUpActions from '../../reducers/end-of-day/end-of-day-cash-drawer/end-of-day-cash-up.actions';
import { OpenTillComponent } from 'src/app/open-till/open-till.component';
import { PasswordEntryComponent } from 'src/app/house-keeping/password-entry/password-entry.component';
import { CheckingIncompleteSaleModalComponent } from '../../eftpos/checking-incomplete-sale-modal/checking-incomplete-sale-modal.component';
import * as saleNoteActions from '../../reducers/sale-note/sale-note.actions';
import { SelectPrinterModalComponent } from '../../select-printer-modal/select-printer-modal.component';

@Component({
	selector: 'pos-home',
	templateUrl: './home.component.html',
	styleUrls: ['./home.component.scss'],
	host: { class: 'wrapper' }
})
export class HomeComponent implements OnInit {

  constructor(
    private store: Store<AppState>,
    private urlHistory: UrlHistoryService,
    private modalService: NgbModal,  // Inject NgbModal
	  private router: Router,
	  private printService: PrintingService,
	  private eftposService: EftposService,
	  private suspendSaleClient: SuspendSaleClient,
	  private tyroIClientService: TyroIClientService

	) { }

	staff$: Observable<StaffLoginDto>;
	staffAccess$: Observable<StaffAccess>;
	sysStatus: any;

	managerSys$: any;
	selectedEFTProvider;

	ngOnInit() {
		this.store.dispatch(cartActions.init());
		this.store.dispatch(houseActions.init());
		this.store.dispatch(SysConfigAction.readSysConfig());
		this.store.dispatch(stockSearchActions.clearSearchItems());
		this.store.dispatch(customerClubActions.init());
		//this.store.dispatch(PinAuthAction.readPINAuth({ storeId: 'BA'})); 
		this.store.dispatch(orderActions.setDeposit({ deposit: 0 }));
		this.store.dispatch(readPINAuth());
		this.store.dispatch(accountPaymentActions.init());
		this.store.dispatch(endOfDayFloatActions.init());
		this.store.dispatch(endOfDayCashUpActions.init());
		this.store.dispatch(saleNoteActions.init());
		this.store.dispatch(historyActions.init());
		
		this.staff$ = this.store.select(staffSelectors.selectStaffLoginDto);
		this.staffAccess$ = this.store.select(staffSelectors.staffAccess);
		this.managerSys$ = this.store.select(SysConfigSelectors.selectSysConfig).pipe(
			tap(result => {
				if (result) {
					this.selectedEFTProvider = result.integratedEFTProvider;
					if (this.selectedEFTProvider == 'Tyro') {
						this.tyroIClientService.getClient()
					}

					// checks if an eftpos transaction was started and not completed
					this.getLastTrans();
				}
			})
		);
		this.managerSys$.subscribe();

		this.store.dispatch(customerClubSearchActions.init());

		this.checkLocalService().then(isRunning => {
			if (!isRunning) {
				this.showDownloadModal();
			}
		});

	}

	private checkPrinterSelectionAndProceed(action: () => void): void {
		const selectedPrinter = localStorage.getItem('selectedPrinter');

		if (selectedPrinter) {
			action();
		} else {
			const modalRef = this.modalService.open(SelectPrinterModalComponent, {
				centered: true,
				backdrop: 'static'
			});
			modalRef.result.then(
				(result) => { // User saved selection or modal was closed via save
					action();
				},
				(reason) => { // User dismissed modal (e.g., cancel, cross, escape)
					action();
				}
			);
		}
	}

	onClickSales(): void {
		this.checkPrinterSelectionAndProceed(() => this.router.navigate(['/sales']));
	}

	onClickReturns(): void {
		this.checkPrinterSelectionAndProceed(() => this.router.navigate(['/returns']));
	}

	onClickExchange(): void {
		this.checkPrinterSelectionAndProceed(() => this.router.navigate(['/exchange/in']));
	}

	onClickOpenTill(): void {
		this.checkPrinterSelectionAndProceed(() => this.openTillModal());
	}

	onClickAccountPayment(): void {
		this.checkPrinterSelectionAndProceed(() => this.router.navigate(['/account-payment']));
	}

	onClickLaybyPayment(): void {
		this.checkPrinterSelectionAndProceed(() => this.router.navigate(['/layby/payment']));
	}

	onClickCustomerSpecials(): void {
		this.checkPrinterSelectionAndProceed(() => this.openCustomerSpecialsModal());
	}

	onClickGiftVoucher(): void {
		this.checkPrinterSelectionAndProceed(() => this.router.navigate(['/gift/glance']));
	}

	navigateToSendStock(): void {

	}

	openCustomerSpecialsModal() {
		this.router.navigate(['customer-specials']);
	}

	private checkLocalService(): Promise<boolean> {
		return new Promise<boolean>((resolve) => {
			this.printService.checkService().subscribe({
				next: (res) => {
					this.printService.triggerPrinterRefresh();
					resolve(true);
				},
				error: (err) => {
					window.location.href = 'stylematrix://';
					resolve(false);
				}
			});
		});
	}

	public getLastTrans() {
		this.modalService.dismissAll(); // Would open a bunch of times for some reason
		// First, check for incomplete (suspended) sale lines
		this.suspendSaleClient.checkForIncompleteSale().subscribe({
			next: (suspend) => {
				let suspendNo = suspend.item1;
				let eftPaid = suspend.item2;
				if (suspendNo !== 0) {
					if (this.selectedEFTProvider != "None") {
						let modalRef = this.modalService.open(CheckingIncompleteSaleModalComponent, {
							size: 'md',
							centered: true,
							backdrop: 'static',
							keyboard: false
						});
						this.eftposService.getLastTrans(this.selectedEFTProvider).subscribe({
							next: (result: any) => {
								if (result) {
									// const { receipts, success, amount } = result;
									// Always print the integrated receipt
									this.printService.printEftposReceipt([result.item1], true);

									if (result.item2) {
										// Open sale using the amount and the suspend number if successful
										this.openSale(result.item3 + eftPaid, suspendNo, modalRef);
									} else {
										// If the last payment was unsuccessful but there were previous payments that were successful on that cart
										if (eftPaid) {
											this.openSale(eftPaid, suspendNo, modalRef);
										} else {
											// Reset suspend sale status if no transaction was successful
											this.suspendSaleClient.resetSuspendSaleStatus(true).subscribe();

										}
									}
								}
								else if (eftPaid) {
									this.openSale(eftPaid, suspendNo, modalRef);
								}
							},
							error: (error) => {
								console.error("Error fetching transaction:", error);
								if (eftPaid) {
									this.openSale(eftPaid, suspendNo, modalRef);
								}
								else {
									modalRef.dismiss();
									this.suspendSaleClient.resetSuspendSaleStatus(true).subscribe();
								}
							}
						});
					}
				}
			},
			error: (error) => {
				console.error("Error checking for incomplete sale:", error);
				this.modalService.dismissAll();
			}
		});
	}

	public openSale(amount?: number, suspendNo?: number, modalRef?: any): void {
		if (modalRef) { modalRef.dismiss(); }
		// Navigate to the sales page and pass the amount and suspendLines in the navigation state
		this.router.navigate(['/sales'], {
			state: { amount, suspendNo }
		});
	}

	openTillModal() {
		this.modalService.open(OpenTillComponent, {
		  centered: true,
		  backdrop: 'static',
		  size: 'lg'
		});
	  }

	openPasswordEntryModal() {
	this.modalService.open(PasswordEntryComponent, {
		centered: true,
		backdrop: 'static',
		size: 'lg'
	});
	}

	private showDownloadModal() {

		const modalRef = this.modalService.open(DownloadPrintServiceModalComponent, { size: 'xl', centered: true });
		modalRef.componentInstance.retryCallback = () => this.checkLocalService();
	}
}
