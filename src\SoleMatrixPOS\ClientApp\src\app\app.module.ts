import { InjectionToken, NgModule } from "@angular/core";

import { StoreModule } from "@ngrx/store";
import {
	OrderSearchClient,
	OrderClient,
	ReceiptUpdateDto,
	HistoryClient,
	HouseKeepingClient,
	LaybyClient,
	LaybyPaymentClient,
	LaybySearchClient,
	OpenTillClient,
	StockEnquiryClient,
	StockSearchClient,
	TransactionClient,
	ReceiptPrintingClient,
	StocktakeEntryClient,
	GiftVoucherClient,
	HomeClient,
	EndOfDayClient,
	TransRefClient,
	LaybyRefundClient,
	EftposClient,
	QuoteClient,
	QuoteSearchClient,
	SuspendSaleClient,
	ClientUpdateClient,
	ClientSubClient,
	ClientBarcodeSearchClient,
} from "./pos-server.generated";
import { InfiniteScrollModule } from "ngx-infinite-scroll";
// import { MatMenuModule } from  '@angular/material/menu';
import { MatProgressSpinnerModule } from "@angular/material/progress-spinner";

import { BrowserModule } from "@angular/platform-browser";

import { AppRoutingModule } from "./app-routing.module";
import { AppComponent } from "./app.component";
import { CalendarModule } from "primeng/calendar";
import { HomeComponent } from "./home/<USER>/home.component";
import { HomeHeaderComponent } from "./home/<USER>/home-header.component";
import { NotFoundComponent } from "./not-found/not-found.component";
import { ClockInComponent } from "./home/<USER>/clock-in.component";
import { TestComponent } from "./test/test.component";

import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { reducers, metaReducers, AllEffects } from "./reducers";
import { EffectsModule } from "@ngrx/effects";
import {
	StaffClient,
	DailyFloatClient,
	ClientSearchClient,
	ClockInClient,
	ClientAddClient,
	NoteClient,
	SendStockClient,
	ReceiveStockClient,
	ReceiptHelpersClient
} from "./pos-server.generated";
import {
	HttpClientModule,
	HTTP_INTERCEPTORS,
	HttpClient,
} from "@angular/common/http";
import { StaffLoginComponent } from "./home/<USER>/staff-login.component";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { FloatRecordEnterComponent } from "./home/<USER>/float-record-enter.component";
import { StaffIdInterceptor } from "./core/interceptors/staffId.interceptor";
import { ClockOutComponent } from "./home/<USER>/clock-out.component";
import { CustomerClubComponent } from "./customer-club/customer-club.component";
import { StockSearchHeaderComponent } from "./stock-search/stock-search-header/stock-search-header.component";
import { StockSearchTableComponent } from "./stock-search/stock-search-table/stock-search-table.component";
import { StockSearchContainerComponent } from "./stock-search/stock-search-container/stock-search-container.component";
import { StockSearchModalComponent } from "./stock-search/stock-search-modal/stock-search-modal.component";
import { StoreDevtoolsModule } from "@ngrx/store-devtools";
import { environment } from "../environments/environment";
import { TranslateModule, TranslateLoader } from "@ngx-translate/core";
import { TranslateHttpLoader } from "@ngx-translate/http-loader";

// Stock Sale Table
import { StockSaleTableModule } from "./stock-sale-table/stock-sale-table.module";
import { TransferModule } from "./transfer-table/transfer.module";
import { TransferComponent } from "./transfer-table/transfer.component";
import { CustomerClubMemberComponent } from "./customer-club/customer-club-member/customer-club-member.component";
import { CustomerClubContainerComponent } from "./customer-club/customer-club-container/customer-club-container.component";
import { CustomerClubSearchComponent } from "./customer-club/customer-club-search/customer-club-search.component";
import { ScrollingModule } from "@angular/cdk/scrolling";
import { CustomerClubNotesComponent } from "./customer-club/customer-club-notes/customer-club-notes.component";
import { CustomerClubHistoryComponent } from "./customer-club/customer-club-history/customer-club-history.component";
import { CustomerClubMemberFormComponent } from "./customer-club/customer-club-member-form/customer-club-member-form.component";
import { NoteModalComponent } from "./customer-club/note-modal/note-modal.component";
import { SendStockComponent } from "./pos-transactions/send-stock/send-stock.component";
import { NavHeaderComponent } from "./pos-transactions/shared/nav-header/nav-header.component";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { CustomerClubModalComponent } from "./customer-club/customer-club-modal/customer-club-modal.component";
import { HistoryComponent } from "./history/history.component";
import { RemoveNullPipe } from "./core/pipes/remove-null.pipe";
import { PaymentComponent } from "./payment/payment.component";
import { GiftCardModalComponent } from "./payment/gift-card-modal/gift-card-modal.component";
import { ZipPayModalComponent } from "./payment/zip-pay-modal/zip-pay-modal.component";
import { AfterPayModalComponent } from "./payment/after-pay-modal/after-pay-modal.component";
import { CashModalComponent } from "./payment/cash-modal/cash-modal.component";
import { ChequeModalComponent } from "./payment/cheque-modal/cheque-modal.component";
import { CustomerAccountModalComponent } from "./payment/customer-account-modal/customer-account-modal.component";
import { EftposModalComponent } from "./payment/eftpos-modal/eftpos-modal.component";
import { ProcessPaymentModalComponent } from "./payment/process-payment-modal/process-payment-modal.component";
import { ReceiveStockComponent } from "./pos-transactions/receive-stock/receive-stock.component";
import { ReceiveDocketScanComponent } from "./pos-transactions/receive-stock/receive-docket-scan/receive-docket-scan.component";
import { ReceiveItemScanComponent } from "./pos-transactions/receive-stock/receive-item-scan/receive-item-scan.component";
import { GenericStockTableComponent } from "./generic-stock-table/generic-stock-table.component";
import { StockTableItemComponent } from "./generic-stock-table/stock-table-item/stock-table-item.component";
import { ReturnsComponent } from "./returns/returns.component";
import { ReturnReasonPromptComponent } from "./returns/return-reason-prompt/return-reason-prompt.component";
import { ReturnsFooterComponent } from "./returns/returns-footer/returns-footer.component";
import { PaymentCartTableComponent } from "./payment/payment-cart-table/payment-cart-table.component";
import { PaymentModalButtonGroupComponent } from "./payment/payment-modal-button-group/payment-modal-button-group.component";
import { PaymentFooterComponent } from "./payment/payment-footer/payment-footer.component";
import { PaymentModalButtonComponent } from "./payment/payment-modal-button/payment-modal-button.component";
import { ReturnsProcessRefundComponent } from "./returns/returns-process-refund/returns-process-refund.component";
import { SalesComponent } from "./sales/sales.component";
import { SalesFooterComponent } from "./sales/sales-footer/sales-footer.component";
import { SalesProcessPaymentComponent } from "./sales/sales-process-payment/sales-process-payment.component";
import { ExchangeInComponent } from "./exchange/exchange-in/exchange-in.component";
import { ExchangeOutComponent } from "./exchange/exchange-out/exchange-out.component";
import { ItemLookupComponent } from "./item-lookup/item-lookup.component";
import { ItemNoSizeLookupComponent } from "./item-nosize-lookup/item-nosize-lookup.component";
import { TransactionTableComponent } from "./transaction-table/transaction-table.component";
import { TransactionFooterComponent } from "./transaction-footer/transaction-footer.component";
import { SaleLaybyModalComponent } from "./sales/sale-layby-modal/sale-layby-modal.component";
import { ErrorModalComponent } from "./error-modal/error-modal.component";
import { LaybyPaymentComponent } from "./layby-payment/layby-payment.component";
import { StockEnquiryComponent } from "./stock-enquiry/stock-enquiry.component";
import { MainTableComponent } from "./stock-enquiry//main-table/main-table.component";
import { TotalAmountComponent } from "./stock-enquiry/total-amount/total-amount.component";
import { RemoveZerosPipe } from "./stock-enquiry/remove-zeros.pipe";
import { SelectByColourComponent } from "./stock-enquiry/select-by-colour/select-by-colour.component";
import { TransactionLineAdjustModalComponent } from "./transaction-line-adjust-modal/transaction-line-adjust-modal.component";
import { DailyClient, AccountPaymentClient } from "./pos-server.generated";
import { OpenTillComponent } from "./open-till/open-till.component";
import { DailyTotalComponent } from "./history/daily-total/daily-total.component";
import { SkipComponent } from './skip/skip.component';
import { TyroPINPairingModalComponent } from './house-keeping/pinpairing-modal/tyro-pinpairing-modal/tyro-pinpairing-modal.component';
import { EmailReceiptComponent } from './email-receipt/email-receipt.component';
import { CustomerSpecialsComponenet } from './customer-specials/customer-specials.component';
import { OrderItemComponent } from './customer-specials/customer-orders/order-item/order-item.component';
import { DepositModalComponent } from './customer-specials/customer-orders/deposit-modal/deposit-modal.component';
import { CustomerOrdersSearchComponent } from './customer-specials/customer-orders/customer-orders-search/customer-orders-search.component';
import { SalesByTableComponent } from "./history/sales-by-staff-table/sales-by-table.component";
import { SalesByHourTableComponent } from "./history/sales-by-hour-table/sales-by-hour-table.component";
import { DepartmentSalesComponent } from "./history/department-sales/department-sales.component";
import { AccountPaymentComponent } from "./account-payment/account-payment.component";
import { CustomerModalComponent } from "./account-payment/customer-modal/customer-modal.component";
import { AccountProcessPaymentComponent } from "./account-payment/account-process-payment/account-process-payment.component";
import { HouseKeepingComponent } from "./house-keeping/house-keeping.component";
import { PasswordEntryComponent } from "./house-keeping/password-entry/password-entry.component";
import { ReceiptComponent } from "./house-keeping/receipt/receipt.component";
import { PrintReceiptModalComponent } from "./history/print-receipt-modal/print-receipt-modal.component";
import { ConfigComponent } from "./house-keeping/config/config.component";
import { TwoDigitInputDirective } from "./house-keeping/config/two-digit-input.directive";
import { StocktakeEntryComponent } from "./stocktake-entry/stocktake-entry.component";
import { StockCartTableComponent } from "./stocktake-entry/stock-cart-table/stock-cart-table.component";
import { StocktakeFooterComponent } from "./stocktake-entry/stocktake-footer/stocktake-footer.component";
import { ConfirmModalComponent } from "./confirm-modal/confirm-modal.component";
import { ReadFileModalComponent } from "./stocktake-entry/read-file-modal/read-file-modal.component";
import { GiftVoucherComponent } from "./gift-voucher/gift-voucher.component";
import { FormModalComponent } from "./gift-voucher/gift-glance-view/form-modal/form-modal.component";
import { GiftFooterComponent } from "./gift-voucher/gift-footer/gift-footer.component";
import { GiftGlanceViewComponent } from "./gift-voucher/gift-glance-view/gift-glance-view.component";
import { GiftPaymentTableComponent } from "./gift-voucher/gift-payment-table/gift-payment-table.component";
import { CommonModule } from "@angular/common";
import { FooterComponent } from "./pos-transactions/send-stock/footer/footer.component";
import { EndOfDayFloatComponent } from "./end-of-day/end-of-day-float/end-of-day-float.component";
import { EndOfDayCashDrawerComponent } from "./end-of-day/end-of-day-cash-drawer/end-of-day-cash-drawer.component";
import { EndOfDayCashUpComponent } from "./end-of-day/end-of-day-cash-up/end-of-day-cash-up.component";
import { EndOfDayVarianceComponent } from "./end-of-day/end-of-day-variance/end-of-day-variance.component";
import { LinklyPINPairingModalComponent } from "./house-keeping/pinpairing-modal/linkly-pinpairing-modal.component";
import { IntegratedEftposModalComponent } from "./payment/integrated-eftpos-modal/integrated-eftpos-modal.component";
import { WaitingForEftposModalComponent } from "./payment/waiting-for-eftpos-modal/waiting-for-eftpos-modal.component";
import { OrderRefundComponent } from "./customer-specials/customer-orders/order-refund/order-refund.component";
import { DownloadPrintServiceModalComponent } from "./printing/download-print-service-modal/download-print-service-modal.component";
import { CustomerQuotesSearchComponent } from "./customer-specials/customer-quotes/customer-quotes-search/customer-quotes-search.component";
import { CustomerQuoteComponent } from "./customer-specials/customer-quotes/customer-quote/customer-quote.component";
import { LoginComponent } from "./login/login.component";
import { LoginFormComponent } from "./login/login-form/login-form.component";
import { DiscountConfirmModalComponent } from "./discount-confirm-modal/discount-confirm-modal.component";
import { OnHoldSearchComponent } from "./customer-specials/on-hold/on-hold-search/on-hold-search.component";
import { PlaceOnHoldComponent } from "./customer-specials/on-hold/place-on-hold/place-on-hold.component";
import { OnHoldRefundComponent } from "./customer-specials/on-hold/on-hold-refund/on-hold-refund.component";
import { OnHoldDepositModalComponent } from "./customer-specials/on-hold/on-hold-deposit-modal/on-hold-deposit-modal.component";
import { LaybySearchComponent } from './layby-payment/layby-search/layby-search.component';
import { SaleSuspendHdrModalComponent } from "./sales/sale-suspend-hdr-modal/sale-suspend-hdr-modal.component";
import { SaleSuspendModalComponent } from "./sales/sale-suspend-modal/sale-suspend-modal.component";
import { ConfirmationModalComponent } from "./shared/components/confirmation-modal/confirmation-modal.component";
import { PrintReturnModalComponent } from "./shared/components/print-return-modal/print-return-modal.component";
import { HistoryRefundComponent } from "./history/history-refund/history-refund.component";
import { StockEnquirySearchModalComponent } from "./stock-enquiry-search/stock-enquiry-search-modal/stock-enquiry-search-modal.component";
import { LaybyZeroRefundConfirmationComponent } from "./layby-payment/cancel-layby/layby-zero-refund-confirmation/layby-zero-refund-confirmation.component";
import { LocalTimeInterceptor } from "./core/interceptors/local-time.interceptor";

//auth
import { TokenStore } from "./core/store/token.store";
import { AuthService } from "./core/services/auth.service";
import { AuthInterceptor } from "./core/interceptors/auth.interceptor";
import { AuthContextProvider } from "./core/context/auth.context";
import { AuthGuard } from "./core/guards/auth.guard";
import { StaffStateGuard } from "./core/guards/staff-state.guard";
import { PasswordResetComponent } from "./password-reset/password-reset.component";
import { PasswordResetFormComponent } from "./password-reset/password-reset-form/password-reset-form.component";
import { PasswordResetSuccessComponent } from "./password-reset-success/password-reset-success.component";


import { CancelLaybyComponent } from './layby-payment/cancel-layby/cancel-layby.component';
import { LaybyPaymentModalComponent } from './layby-payment/layby-payment-modal/layby-payment-modal.component';
import { SundryModalComponent } from './sales/sundry-modal/sundry-modal.component';
import { PrintCacheService } from "./printing/print-cache.service";
import { GiftVoucherIssueComponent } from './gift-voucher/gift-voucher-issue/gift-voucher-issue.component';
import { CustomerPointsModalComponent } from './payment/customer-points-modal/customer-points-modal.component';
import { CreditNoteIssuanceModalComponent } from './payment/credit-note-issuance-modal/credit-note-issuance-modal.component';
import { StockEnquirySearchTableComponent } from "./stock-enquiry-search/stock-enquiry-search-table/stock-enquiry-search-table.component";
import { StockEnquirySearchHeaderComponent } from "./stock-enquiry-search/stock-enquiry-search-header/stock-enquiry-search-header.component";
import { StockEnquirySearchContainerComponent } from "./stock-enquiry-search/stock-enquiry-search-container/stock-enquiry-search-container.component";
import { GiftGlanceFooterComponent } from './gift-voucher/gift-glance-view/gift-glance-footer/gift-glance-footer.component';
import { SupportComponent } from "./support/support.component";
import { SupportListComponent } from "./support/support-list/support-list.component";
import { SupportListItemComponent } from "./support/support-list-item/support-list-item.component";
import { TyroSettingsModalComponent } from './house-keeping/pinpairing-modal/tyro-settings-modal/tyro-settings-modal.component';
import { CheckCancelSaleModalComponent } from './eftpos/check-cancel-sale-modal/check-cancel-sale-modal.component';
import { CheckingIncompleteSaleModalComponent } from './eftpos/checking-incomplete-sale-modal/checking-incomplete-sale-modal.component';
import { ErrorConfirmModalComponent } from './error-modal/error-confirm-modal/error-confirm-modal.component';
import { ConfirmErrorModalComponent } from "./error-modal/confirm-error-modal.component";
import { SelectPrinterModalComponent } from './select-printer-modal/select-printer-modal.component';

// AoT requires an exported function for factories
export function HttpLoaderFactory(http: HttpClient) {
	return new TranslateHttpLoader(http);
}

export const ROOT_REDUCER = new InjectionToken<any>("Root Reducer");

@NgModule({
	declarations: [
		DepositModalComponent,
		SaleSuspendHdrModalComponent,
		SaleSuspendModalComponent,
		HistoryRefundComponent,
		AppComponent,
		HomeComponent,
		HomeHeaderComponent,
		NotFoundComponent,
		TestComponent,
		StaffLoginComponent,
		ClockInComponent,
		CustomerOrdersSearchComponent,
		FloatRecordEnterComponent,
		ClockOutComponent,
		StockSearchHeaderComponent,
		StockSearchTableComponent,
		StockSearchContainerComponent,
		StockSearchModalComponent,
		StockEnquirySearchHeaderComponent,
		StockEnquirySearchTableComponent,
		StockEnquirySearchContainerComponent,
		StockEnquirySearchModalComponent,
		SendStockComponent,
		CustomerClubComponent,
		NoteModalComponent,
		CustomerClubMemberComponent,
		CustomerClubContainerComponent,
		CustomerClubSearchComponent,
		CustomerClubNotesComponent,
		CustomerClubHistoryComponent,
		CustomerClubMemberFormComponent,
		CustomerClubModalComponent,
		NavHeaderComponent,
		HistoryComponent,
		RemoveNullPipe,
		PaymentComponent,
		AfterPayModalComponent,
		CashModalComponent,
		ChequeModalComponent,
		CustomerAccountModalComponent,
		EftposModalComponent,
		GiftCardModalComponent,
		ZipPayModalComponent,
		ProcessPaymentModalComponent,
		ReceiveStockComponent,
		ReceiveDocketScanComponent,
		ReceiveItemScanComponent,
		GenericStockTableComponent,
		StockTableItemComponent,
		ReturnsComponent,
		ReturnReasonPromptComponent,
		ReturnsFooterComponent,
		PaymentCartTableComponent,
		PaymentModalButtonGroupComponent,
		PaymentFooterComponent,
		PaymentModalButtonComponent,
		ReturnsProcessRefundComponent,
		SalesComponent,
		SalesFooterComponent,
		SalesProcessPaymentComponent,
		ExchangeInComponent,
		ExchangeOutComponent,
		ItemLookupComponent,
		ItemNoSizeLookupComponent,
		TransactionTableComponent,
		TransactionFooterComponent,
		SaleLaybyModalComponent,
		ErrorModalComponent,
		LaybyPaymentComponent,
		StockEnquiryComponent,
		MainTableComponent,
		TotalAmountComponent,
		RemoveZerosPipe,
		SelectByColourComponent,
		OpenTillComponent,
		DailyTotalComponent,
		SalesByTableComponent,
		SalesByHourTableComponent,
		DepartmentSalesComponent,
		AccountPaymentComponent,
		CustomerModalComponent,
		AccountProcessPaymentComponent,
		HouseKeepingComponent,
		PasswordEntryComponent,
		ReceiptComponent,
		PrintReceiptModalComponent,
		ConfigComponent,
		TwoDigitInputDirective,
		StocktakeEntryComponent,
		StockCartTableComponent,
		StocktakeFooterComponent,
		ConfirmModalComponent,
		ReadFileModalComponent,
		GiftVoucherComponent,
		FormModalComponent,
		GiftFooterComponent,
		GiftGlanceViewComponent,
		GiftPaymentTableComponent,
		FooterComponent,
		EndOfDayFloatComponent,
		EndOfDayCashDrawerComponent,
		EndOfDayCashUpComponent,
		EndOfDayVarianceComponent,
		EmailReceiptComponent,
		SkipComponent,
		CustomerSpecialsComponenet,
		OrderItemComponent,
		DepositModalComponent,
		CustomerOrdersSearchComponent,
		LinklyPINPairingModalComponent,
		IntegratedEftposModalComponent,
		WaitingForEftposModalComponent,
		OrderRefundComponent,
		DownloadPrintServiceModalComponent,
		CustomerQuotesSearchComponent,
		CustomerQuoteComponent,
		TransactionLineAdjustModalComponent,
		DiscountConfirmModalComponent,
		OnHoldSearchComponent,
		PlaceOnHoldComponent,
		OnHoldRefundComponent,
		OnHoldDepositModalComponent,
		LaybySearchComponent,
		LoginComponent,
		LoginFormComponent,
		CancelLaybyComponent,
		LaybyPaymentModalComponent,
		TyroPINPairingModalComponent,
		PasswordResetComponent,
		PasswordResetFormComponent,
		PasswordResetSuccessComponent,
		SundryModalComponent,
		GiftVoucherIssueComponent,
		CustomerPointsModalComponent,
		ConfirmationModalComponent,
		PrintReturnModalComponent,
		CreditNoteIssuanceModalComponent,
		StockEnquirySearchModalComponent,
		LaybyZeroRefundConfirmationComponent,
		SupportComponent,
		SupportListComponent,
		SupportListItemComponent,
		GiftGlanceFooterComponent,
		TyroSettingsModalComponent,
		CheckCancelSaleModalComponent,
		CheckingIncompleteSaleModalComponent,
		ErrorConfirmModalComponent,
		ConfirmErrorModalComponent,
		SelectPrinterModalComponent
	],
	imports: [
		BrowserModule,
		AppRoutingModule,
		NgbModule,
		HttpClientModule,
		FormsModule,
		CommonModule,
		ReactiveFormsModule,
		CalendarModule,
		InfiniteScrollModule,
		// MatMenuModule,
		MatProgressSpinnerModule,
		StoreModule.forRoot(ROOT_REDUCER),
		// StoreModule.forRoot(reducers, {
		// 	metaReducers,
		// 	runtimeChecks: {
		// 		strictStateImmutability: true,
		// 		strictActionImmutability: true,
		// 	},
		// }),
		EffectsModule.forRoot(AllEffects),
		TranslateModule.forRoot({
			loader: {
				provide: TranslateLoader,
				useFactory: HttpLoaderFactory,
				deps: [HttpClient],
			},
		}),
		StoreDevtoolsModule.instrument({
			maxAge: 25, // Retain 25 states
			logOnly: environment.production, // readonly in prod
		}),
		StockSaleTableModule,
		TransferModule,
		BrowserAnimationsModule,
	],
	providers: [
		{ provide: ROOT_REDUCER, useValue: reducers },
		StaffClient,
		DailyFloatClient,
		OrderClient,
		QuoteClient,
		ClockInClient,
		StockSearchClient,
		ClientSubClient,
		ClientUpdateClient,
		LaybySearchClient,
		LaybyRefundClient,
		StockEnquiryClient,
		LaybyPaymentClient,
		ClientSearchClient,
		ClientBarcodeSearchClient,
		TransRefClient,
		ClientAddClient,
		NoteClient,
		SendStockClient,
		ReceiveStockClient,
		EndOfDayClient,
		LaybyClient,
		HistoryClient,
		TransactionClient,
		OpenTillClient,
		DailyClient,
		ReceiptPrintingClient,
		AccountPaymentClient,
		EftposClient,
		QuoteSearchClient,
		SuspendSaleClient,
		HouseKeepingClient,
		ReceiptHelpersClient,
		StocktakeEntryClient,
		GiftVoucherClient,
		HomeClient,
		OrderSearchClient,
		TokenStore,
		AuthGuard,
		StaffStateGuard,
		AuthService,
		PrintCacheService,
		AuthContextProvider,
		{
			provide: HTTP_INTERCEPTORS,
			useClass: StaffIdInterceptor,
			multi: true,
		},
		{
			provide: HTTP_INTERCEPTORS,
			useClass: AuthInterceptor,
			multi: true,
		},
		{
			provide: HTTP_INTERCEPTORS,
			useClass: LocalTimeInterceptor,
			multi: true
		}
	],
	bootstrap: [AppComponent],
	entryComponents: [
		CreditNoteIssuanceModalComponent,
		StockSearchModalComponent,
		DepositModalComponent,
		SelectPrinterModalComponent,
		NoteModalComponent,
		PasswordEntryComponent,
		ReturnReasonPromptComponent,
		CustomerOrdersSearchComponent,
		CustomerClubModalComponent,
		GiftVoucherIssueComponent,
		ClockOutComponent,
		OpenTillComponent,
		CustomerClubMemberComponent,
		CustomerPointsModalComponent,
		AfterPayModalComponent,
		OnHoldDepositModalComponent,
		OnHoldRefundComponent,
		CashModalComponent,
		ChequeModalComponent,
		TransactionTableComponent,
		CancelLaybyComponent,
		CustomerAccountModalComponent,
		EftposModalComponent,
		GiftCardModalComponent,
		ZipPayModalComponent,
		ProcessPaymentModalComponent,
		LaybySearchComponent,
		ErrorModalComponent,
		SaleLaybyModalComponent,
		DailyTotalComponent,
		CustomerModalComponent,
		AccountProcessPaymentComponent,
		PrintReceiptModalComponent,
		ReadFileModalComponent,
		ConfirmModalComponent,
		EndOfDayFloatComponent,
		EndOfDayVarianceComponent,
		EmailReceiptComponent,
		TransactionLineAdjustModalComponent,
		LinklyPINPairingModalComponent,
		IntegratedEftposModalComponent,
		WaitingForEftposModalComponent,
		OrderRefundComponent,
		OnHoldSearchComponent,
		DownloadPrintServiceModalComponent,
		TransactionLineAdjustModalComponent,
		CustomerQuotesSearchComponent,
		SaleSuspendHdrModalComponent,
		SaleSuspendModalComponent,
		LaybyPaymentModalComponent,
		TyroPINPairingModalComponent,
		PasswordResetComponent,
		PasswordResetFormComponent,
		PasswordResetSuccessComponent,
		DiscountConfirmModalComponent,
		SundryModalComponent,
		ConfirmationModalComponent,
		PrintReturnModalComponent,
		StockEnquirySearchModalComponent,
		ItemNoSizeLookupComponent,
		StockEnquirySearchHeaderComponent,
		StockEnquirySearchTableComponent,
		StockEnquirySearchContainerComponent,
		LaybyZeroRefundConfirmationComponent,
		TyroSettingsModalComponent,
		HistoryRefundComponent,
		CheckCancelSaleModalComponent,
		CheckingIncompleteSaleModalComponent,
		ConfirmErrorModalComponent
	],
})
export class AppModule {
	constructor() {
		AppModule.overrideDateToJSON();
	}

	static overrideDateToJSON() {
		Date.prototype.toJSON = function (key) {
			const tz = this.getTimezoneOffset();
			const sign = tz > 0 ? "-" : "+";
			const tzHours = Math.floor(Math.abs(tz) / 60);
			const tzMinutes = Math.abs(tz) % 60;
			const tzOffset = sign + tzHours + ":" + tzMinutes;

			const hours = this.getHours().toString().padStart(2, "0");
			const minutes = this.getMinutes().toString().padStart(2, "0");
			const seconds = this.getSeconds().toString().padStart(2, "0");
			const timePart = `T${hours}:${minutes}:${seconds}${tzOffset}`;

			const month = (this.getMonth() + 1).toString().padStart(2, "0");
			const day = this.getDate().toString().padStart(2, "0");
			const datePart = `${this.getFullYear()}-${month}-${day}`;

			return `${datePart}${timePart}`;
		};
	}
}
