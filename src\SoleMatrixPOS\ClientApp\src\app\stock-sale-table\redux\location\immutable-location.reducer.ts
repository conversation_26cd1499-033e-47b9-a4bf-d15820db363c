import { Location } from './location.model';
import * as ImmutableLocationType from './immutable-location.action';

const initialState: Location[] = [];

export function ImmutableLocationReducer(state: Location[] = initialState, action: any) {
    switch (action.type) {
        case ImmutableLocationType.LOAD_IMMUTABLE_LOCATIONS: {
            state = action.locations
            return state;
        }
        default: {
            return state;
        }
    }
}
