import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { GiftVoucherClient } from "src/app/pos-server.generated";
import * as giftVoucherAction from './gift-voucher.actions';
import { catchError, delay, map, mergeMap, startWith, tap } from "rxjs/operators";
import { EMPTY } from "rxjs";


@Injectable()
export class GiftVoucherEffects {
    constructor(private actions$: Actions, private client: GiftVoucherClient) { }

    //generate or get voucher number
    // getVoucherNo$ = createEffect(() => this.actions$.pipe(
    //     ofType(giftVoucherAction.getVoucherNo),
    //     tap(() => console.log("gift-voucher number")),

    //     mergeMap(() => this.client.getVoucherNo()
    //         .pipe(
    //             map((response) => giftVoucherAction.getVoucherNoResponse({ voucherNo: response }),
    //             catchError(() => EMPTY)
    //             )
    //         )
    //     )

    // ));



    //Create Gift voucher
    purchaseGiftVoucher$ = createEffect(() => this.actions$.pipe(
        ofType(giftVoucherAction.submit),
        tap(() => console.log("submit gift-voucher")),

        mergeMap((action) => this.client.purchaseGiftVoucher(action.payload)
            .pipe(
                map((response) => giftVoucherAction.submitCompleted({payload: response}),
                catchError(() => EMPTY)
                )
            )
        )
    ));




}
