import { Injectable } from '@angular/core';
import { Actions, Effect, ofType, createEffect } from '@ngrx/effects';
import * as customerClubSearchActions from './customer-club.actions';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import Swal from 'sweetalert2';

import { catchError, map, mergeMap, tap, switchMap, debounceTime } from 'rxjs/operators';
import { EMPTY } from 'rxjs';
import { ClientSearchClient, ClientBarcodeSearchClient, ClientSubClient } from '../../../pos-server.generated';

@Injectable()
export class CustomerClubEffects {

	// Api version
	matchedMembers$ = createEffect(() => this.actions$.pipe(
		ofType(customerClubSearchActions.search),
		tap(() => console.log("Calling customerClubSearchActions.search")),
		switchMap((action) => this.clientSearchClient.get(action.searchParams)
			.pipe(
				map(foundMembers => customerClubSearchActions.searchResponse({ payload: foundMembers })),
				catchError(() => EMPTY)
			))
	));

	barcodeSearch$ = createEffect(() => this.actions$.pipe(
		ofType(customerClubSearchActions.searchBarcode),
		tap(() => console.log("Calling customerClubSearchActions.searchBarcode")),
		mergeMap((action) => this.clientBarcodeSearchClient.get({ barcode: action.barcode })
			.pipe(
				map(response => {
					if (response.clientCode !== null && response.clientCode !== undefined) {
						if (response.isVIP === 1) {
							console.log('VIP FOUND');
							Swal.fire({
								title: 'VIP Customer',
								text: `An active VIP has been scanned and is eligible for discounts\nRecommended Discount: ${Math.round(response.privDiscount)}%`,
								type: 'success',
								timer: 8000
							});
						}
						else {
							Swal.fire({
								title: 'Not VIP',
								text: 'Customer has been scanned but VIP status is not active',
								type: 'warning',
								timer: 8000
							});

						}
						this.modalService.dismissAll();
						return customerClubSearchActions.selectCustomerClubMember({ payload: response });
					}
					return { type: '[CustomerClubSearch] No Action' };
				}),
				catchError(() => EMPTY)
			))
	));

	searchSuburbs$ = createEffect(() => this.actions$.pipe(
		ofType(customerClubSearchActions.searchSuburbs),
		debounceTime(300),
		switchMap((action) => this.clientSubClient.searchSuburbs(action.search)
			.pipe(
				map(suburbs => customerClubSearchActions.searchSuburbsResponse({ suburbs })),
				catchError(() => EMPTY)
			))
	));

	constructor(
		private actions$: Actions,
		private clientSearchClient: ClientSearchClient,
		private clientBarcodeSearchClient: ClientBarcodeSearchClient,
		private modalService: NgbModal,
		private clientSubClient: ClientSubClient
	) { }
}

