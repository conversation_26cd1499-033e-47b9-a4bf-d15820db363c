import { createAction, props } from '@ngrx/store';
import { NoteSearchRequestDto, NoteDto } from '../../../pos-server.generated';

export const search = createAction('[NotesSearch] Search', props<{searchParams: NoteSearchRequestDto}>());
export const searchResponse = createAction('[NotesSearch] Response', props<{payload: NoteDto[] }>());

export const createNote = createAction('[Note] CreateNote', props<{payload: NoteDto }>());
export const createNoteResponse = createAction('[Note] CreateNoteResponse');

export const deleteNote = createAction('[Note] DeleteNote', props<{payload: NoteDto }>());
export const deleteNoteResponse = createAction('[Note] DeleteNoteResponse');