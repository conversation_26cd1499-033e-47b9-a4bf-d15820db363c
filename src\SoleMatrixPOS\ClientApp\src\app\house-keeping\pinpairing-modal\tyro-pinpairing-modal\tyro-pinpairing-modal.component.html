<div class="content-wrapper pt-10">
	<div class="m-2 d-flex justify-content-between">
		<form [formGroup]="pairingForm" (ngSubmit)="submitPINPairCode()" class="d-flex flex-column justify-content-center align-items-center w-100">

			<div class="form-group w-75">
				<label for="mid">Tyro Merchant ID</label>
				<input id="mid"
					   formControlName="mid"
					   type="text"
					   class="form-control"
					   [class.is-invalid]="pairingForm.get('mid').invalid && pairingForm.get('mid').touched" />
				<small *ngIf="pairingForm.get('mid').invalid && pairingForm.get('mid').touched" class="text-danger">
					Merchant ID is required.
				</small>
			</div>

			<div class="form-group w-75">
				<label for="tid">Tyro Terminal ID</label>
				<input id="tid"
					   formControlName="tid"
					   type="text"
					   class="form-control"
					   [class.is-invalid]="pairingForm.get('tid').invalid && pairingForm.get('tid').touched" />
				<small *ngIf="pairingForm.get('tid').invalid && pairingForm.get('tid').touched" class="text-danger">
					Terminal ID is required.
				</small>
			</div>

			<button type="submit" class="btn btn-success mt-3" [disabled]="pairingForm.invalid">
				Pair PIN Pad
			</button>

			<button class="text-secondary float-right mt-2" type="button" (click)="dismiss('Cross Click')">X</button>

		</form>
	</div>
</div>
