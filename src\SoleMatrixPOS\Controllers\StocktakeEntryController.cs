using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.StocktakeEntry;
using SoleMatrixPOS.Application.StocktakeEntry.Queries;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class StocktakeEntryController : ControllerBase
	{
		private readonly IMediator _mediator;

		public StocktakeEntryController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[Route("GetStockTake")]
		[HttpPost]
		public async Task<StockItemCartDto> StockTakeCartItem([FromBody] StocktakeEntryRequestDto item, CancellationToken ct)
		{
			return await _mediator.Send(new StocktakeEntryQuery(item), ct);
		}


		[Route("StockTakeEntry")]
		[HttpPut]
		public async Task<IActionResult> LogTransRecord([FromBody] TransactRequestDto item)
		{
			await _mediator.Send(new WriteStocktakeEntryQuery(item));
			return Ok();
		}


		[Route("DownloadStocktake")]
		[HttpPost]
		public async Task<IEnumerable<ReadDatFileDto>> ReadDatFile([FromBody] string filename)
		{
			return await _mediator.Send(new ReadDatFileQuery(filename));
		}

		[Route("BarcodeValidation")]
		[HttpPost]
		public async Task<BarcodeResultDto> ValidateBarcode([FromBody] BarcodeRequestDto dto)
		{
			return await _mediator.Send(new ValidateBarcodeQuery(dto));
		}


		[Route("printInvalidCode")]
		[HttpPut]
		public async Task<int> PrintInvalidCode([FromBody] PrintInvalidDto dto)
		{
			await _mediator.Send(new PrintInvalidBarcodeCommand(dto));
			return 0;
		}



	}
}
