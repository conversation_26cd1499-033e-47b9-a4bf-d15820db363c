import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder, Validators } from '@angular/forms';
import { Payment } from '../payment.service';
import { WaitingForEftposModalComponent } from '../waiting-for-eftpos-modal/waiting-for-eftpos-modal.component';
import { mapCartToLinklyBasket } from '../../eftpos/eftpos.service';
import { Store } from '@ngrx/store';
import { AppState } from '../../reducers';
import { Observable, Subscription } from 'rxjs';
import { LaybyState } from '../../reducers/layby/layby.reducer';
import { CartItem } from '../../reducers/sales/cart/cart.reducer';
import * as cartSelectors from '../../reducers/sales/cart/cart.selectors'
import * as sysSelectors from '../../reducers/sys-config/sys-config.selectors'


@Component({
	selector: 'pos-integrated-eftpos-modal',
	templateUrl: './integrated-eftpos-modal.component.html',
	styleUrls: ['./integrated-eftpos-modal.component.scss']
})
export class IntegratedEftposModalComponent implements OnInit {

	@Input() amountDue: number;
	@Input() pinPadId: string;

	get amount() { return this.form.get('Amount'); }
	private subscriptions: Subscription = new Subscription(); 

	constructor(
		public activeModal: NgbActiveModal,
		private formBuilder: FormBuilder,
		private modalService: NgbModal,
		private store: Store<AppState>
	) { }

	public form;
	public sysStatus: any;
	public cartTotal: number;
	public cart: CartItem[];
	cart$: Observable<CartItem[]>;
	cartTotal$: Observable<number>;
	layby$: Observable<LaybyState>;
	sysStatus$: Observable<any>;

	dismiss(reason: string){
		console.log("Dismissed for reason:", reason);
	}

	subscribeToState() {
		this.cart$ = this.store.select(cartSelectors.cart);
		this.cartTotal$ = this.store.select(cartSelectors.total);
		this.layby$ = this.store.select(s => s.layby);
		this.sysStatus$ = this.store.select(sysSelectors.selectSysConfig);


		this.subscriptions.add(
			this.cart$.subscribe(cart => {
				console.log(cart);
				this.cart = cart;
			})
		);

		this.subscriptions.add(
			this.cartTotal$.subscribe(total => {
				console.log('Cart total:', total);
				this.cartTotal = total;
			})
		);

		this.subscriptions.add(
			this.layby$.subscribe(layby => {
				console.log('Layby state:', layby);
			})
		);

		this.subscriptions.add(
			this.sysStatus$.subscribe(sysStatus => {
				console.log('sysStatus state:', sysStatus);
				this.sysStatus = sysStatus;
			})
		);
	}

	ngOnInit() {
		this.form = this.formBuilder.group({
			Amount: [undefined, [
				Validators.required,
				Validators.min(0.01),
				Validators.max(this.amountDue),
				Validators.pattern(/^\d*[.]{0,1}\d{0,2}$/)
			]]
		});
		this.subscribeToState();
	}

	ngOnDestroy() {
		if (this.subscriptions) {
			this.subscriptions.unsubscribe();
		}
	}

	useRemainder() {
		this.amount.setValue(this.amountDue);
	}

	processPayment() {
		if (this.form.valid) {
			// LOGIC IS BAD, THIS HAS TO BE DONE AFTER HITTING PROCEED
				//// Open the waiting-for-payment modal
				//const modalRef = this.modalService.open(WaitingForEftposModalComponent, { size: 'md', centered: true });

				//switch (this.sysStatus.integratedEFTProvider) {
				//	case "Linkly":
				//		modalRef.componentInstance.tenderAmount = this.form.get('Amount').value;
				//		modalRef.componentInstance.totalAmount = this.cartTotal;
				//		modalRef.componentInstance.store = this.store;
				//		modalRef.componentInstance.discountAmt = 0; // TODO calculate if needed
				//		modalRef.componentInstance.surchargeAmt = this.form.get('Amount').value * 0; // TODO choose amount in config?
				//		modalRef.componentInstance.taxAmt = this.form.get('Amount').value * 0.1; // TODO choose amount in config?
				//		modalRef.componentInstance.transNo = 0; // TODO find where this is generated

				//		let mappedItems = mapCartToLinklyBasket(this.cart);
				//		modalRef.componentInstance.items = mappedItems;
				//		modalRef.componentInstance.pinPadId = this.pinPadId;
				//		break;

				//	case "Tyro":
				//		// TODO Tyro
				//		break;

				//	default:
				//		console.log("Integrated EFTPOS not configured");
				//		this.activeModal.close();
				//		break;
				//}

				//// Handle the result (success or failure)
				//modalRef.result.then((result: Payment) => {
				//	if (result) {
				//		console.log(result);
				//		this.activeModal.close(result);
				//	} else {
				//		console.log("Payment failed");
				//	}
				//});
			this.activeModal.close({ type: "Eftpos", desc: "Integrated Eftpos", amount: +this.amount.value } as Payment);
		} else {
			this.form.markAsPristine();
		}
	}

}
