<div class="container">
    <div class="container-fluid p-5 align-items-center">
        <div class="row justify-content-center">
            <h1 class="text-secondary align-items-center" [translate]="'suspend-sale-modal.OkToSuspendSale'"></h1>
        </div>
        <div class="row justify-content-center">
            <div class="row">
                <button class="m-2 btn btn-outline-default" type="button" (click)="backButtonClick()" style="display: inline;">
                    <i class="fas fa-lg fa-fw fa-times-circle text-gray mr-2" style="display: inline;"></i>
                    <p style="display: inline;">Exit Sale</p>
                </button>
                <button class="m-2 btn btn-outline-default" type="button" (click)="process()" style="display: inline;">
                    <i class="fas fa-lg fa-fw fa-check-circle text-success mr-2" style="display: inline;"></i>
                    <p [translate]="'suspend-sale-modal.ContinueToSuspendSale'" style="display: inline;"></p>
                </button>
            </div>
        </div>

    </div>
</div>