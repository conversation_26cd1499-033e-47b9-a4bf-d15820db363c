import { createAction, props } from '@ngrx/store';
import { CancelLaybyDto } from 'src/app/pos-server.generated';

export const cancelLayby = createAction('[Layby Payment] Cancel Layby', props<{ payload: CancelLaybyDto }>());
export const laybyCancelledSuccess = createAction('[Layby Payment] Layby Cancelled Success');
export const laybyCancelledFailure = createAction(
  '[Layby Payment] Layby Cancelled Failure',
  props<{ error: string }>()  )
