import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ReceiveItemScanComponent } from './receive-item-scan.component';

describe('ReceiveItemScanComponent', () => {
  let component: ReceiveItemScanComponent;
  let fixture: ComponentFixture<ReceiveItemScanComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ReceiveItemScanComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ReceiveItemScanComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
