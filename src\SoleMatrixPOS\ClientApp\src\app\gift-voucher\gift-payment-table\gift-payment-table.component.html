<div class="row">
    <div id="sale-table-header" class="table-responsive">
        <table class="table table-borderless ">
            <thead>
                <tr>
                    <th scope="col">Gift Voucher No</th>
                    <th scope="col">Gift Voucher No</th>
                    <th scope="col"></th>
                    <th scope="col">Item</th>
                    <th scope="col">Qty</th>
                    <th scope="col">Price</th>
                </tr>
            </thead>
        </table>
    </div>
    <div id="sale-table-body" class="table-responsive"
        style="display: block; max-height: 300px; height: 300px; overflow-y: scroll;">

        <hr>
        <table class="table table-scroll ">
            <tbody>
                <tr>
                    <td scope="col">{{ (voucher$ | async).voucherNo || 'Generated After Issuance'}}</td>
                    <td scope="col"></td>
                    <td scope="col">Gift Voucher Issue</td>
                    <td scope="col">1</td>
                    <td scope="col">{{(voucher$ | async).voucherFunds | currency}}</td>
                </tr>
            </tbody>
            <tfoot>
                <tr class="font-weight-bold">
                    <td scope="col">Total</td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col">
                        <span>
                            {{(balances$ | async)[0] | currency}}
                        </span>
                    </td>
                </tr>


            </tfoot>
        </table>
    </div>
    <div id="sale-table-footer" class="table-responsive">
        <table class="table table-borderless ">
            <tfoot>

                <tr>
                    <td colspan="5"></td>
                </tr>

                <tr style="background-color: #dbffcca8;" *ngFor="let payment of payments$ | async" 
                (click)="paymentClick(payment)"
                >
                    <td scope="col">{{payment.type}}</td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col">
                        {{payment.amount | currency}}
                    </td>
                </tr>

                <tr>
                    <td colspan="5"></td>
                </tr>

                <tr style="background-color: #EAF2F4A8;">
                    <td colspan="2" scope="col">Amount Tendered</td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col">
                        <span>
                            {{(balances$ | async)[1] | currency}}
                            <!--Should be rounding not total- ask Craig what that is-->
                        </span>
                    </td>
                </tr>

                <tr>
                    <td colspan="5"></td>
                </tr>

                <tr class="font-weight-bold" style="background-color: #9dd6e4a8;">
                    <td colspan="2" scope="col">Balance Due</td>
                    <td scope="col"></td>
                    <td scope="col"></td>
                    <td scope="col">
                        <span>
                            {{(balances$ | async)[2] | currency}}
                        </span>

                    </td>
                </tr>
            </tfoot>
        </table>
    </div>

</div>