import { Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class UrlHistoryService {

  private _urlHistory: string[] = [];

  constructor(
    private router: Router
  ) {
    router.events
      .pipe(filter(e => e instanceof NavigationEnd))
      .subscribe((e: NavigationEnd) => {
        this._urlHistory.push(e.url);
      });
  }

  public get urlHistory(): string[] {
    return this._urlHistory;
  }

  public get lastUrl(): string {
    return this._urlHistory[this.urlHistory.length - 1];
  }

  public get previousUrl(): string {
    return this._urlHistory[this.urlHistory.length - 2];
  }

}
