import { Component, EventEmitter, Input, OnInit, Output, OnDestroy } from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { Store } from "@ngrx/store";
import { Observable } from "rxjs";
import { CustomerClubModalComponent } from "src/app/customer-club/customer-club-modal/customer-club-modal.component";
import {
	CustomerClubDto,
	SuspendSaleClient,
	SuspendSaleDto,
} from "src/app/pos-server.generated";
import { AppState } from "src/app/reducers";
import { CartState, CartItem } from "src/app/reducers/sales/cart/cart.reducer";
import { clearStaffLogin } from "src/app/reducers/staff/staff.actions";
import * as customerClubSearchSelectors from "../reducers/customer-club/club-search/customer-club.selectors";
import * as cartSelectors from "../reducers/sales/cart/cart.selectors";
import { SaleSuspendModalComponent } from "../sales/sale-suspend-modal/sale-suspend-modal.component";
import { Transaction } from "src/app/payment/payment.service";
import { CreateErrorModal } from "src/app/error-modal/error-modal.component";
import { take } from "rxjs/operators";
import { tap, catchError } from "rxjs/operators";
import { throwError } from "rxjs";
import { SuspendSaleLineDto } from "src/app/pos-server.generated";
import { SaleSuspendHdrModalComponent } from "../sales/sale-suspend-hdr-modal/sale-suspend-hdr-modal.component";
import { SundryModalComponent } from '../sales/sundry-modal/sundry-modal.component';
import * as suspendSaleSelectors from '../reducers/suspend-sale/suspend-sale.selectors';
import * as customerClubSearchActions from '../reducers/customer-club/club-search/customer-club.actions';
import * as saleNoteSelectors from '../reducers/sale-note/sale-note.selectors';
import * as SaleNoteActions from '../reducers/sale-note/sale-note.actions';
import Swal from 'sweetalert2';

@Component({
	selector: "pos-transaction-footer",
	templateUrl: "./transaction-footer.component.html",
	styleUrls: ["./transaction-footer.component.scss"],
})
export class TransactionFooterComponent implements OnInit {
	@Input() prevUrl: string;
	@Input() nextButtonText: string = "Payment";
	cart: CartState;
	@Input() showCustomerClub: boolean;
	@Input() disableNext: boolean = false;
	@Input() canEditCart: boolean = true;
	@Input() showSundries: boolean = false;
	@Input() goToSuspendSale: boolean = false;

	@Output() onNextClick: EventEmitter<void> = new EventEmitter();

	selectedCustomerClubMember$: any;

	constructor(
		private store: Store<AppState>,
		private router: Router,
		private modalService: NgbModal,
		private suspendSaleClient: SuspendSaleClient
	) { }

	selectedCustomerClubMember: CustomerClubDto = null;

	noItems$: Observable<number>;
	totalValue$: Observable<number>;
	public transaction: Transaction;
	suspendSaleItems$: Observable<SuspendSaleDto[]>;
	suspendNo: number;
	readyToProcess: boolean = false;
	saleComment: string = '';

	subscribeToState() {
		this.selectedCustomerClubMember$ = this.store.select(
			customerClubSearchSelectors.selectedCustomerClubMember
		);
		this.selectedCustomerClubMember$.subscribe((s) => {
			this.selectedCustomerClubMember = s;
		});
		this.totalValue$ = this.store.select(cartSelectors.total);
		// Subscribe to the cart state to ensure it is initialized
		this.store
			.select(cartSelectors.getCart)
			.subscribe((cart: CartState) => {
				this.cart = cart; // Assign the cart from the store
				//this.checkForSuspendedSales(); // Check for suspended sales after cart is loaded
			});
		this.store.select(suspendSaleSelectors.selectCurrentSuspendSaleNo)
			.subscribe(suspendNo => {
				this.suspendNo = suspendNo;
			});
	}

	ngOnInit() {
		this.subscribeToState();
		//this.checkForSuspendedSales(); // Check if there are suspended sales when loading the page
		
		this.store.select(saleNoteSelectors.selectSaleNote)
			.subscribe(note => {
				this.saleComment = note;
			});
	}

	// getSuspendSaleItems(suspendNo: number): Observable<SuspendSaleLineDto[]> {
	// 	return this.suspendSaleClient.getLinesFromSuspendNo(suspendNo).pipe(
	// 		tap((response) => {
	// 			console.log("Suspended Sale Lines fetched:", response);
	// 		}),
	// 		catchError((error) => {
	// 			console.error("Error fetching suspended sale lines:", error);
	// 			return throwError(error); // Re-throw error for the caller to handle
	// 		})
	// 	);
	// }

	// //TODO use suspendSaleClient.get
	// checkForSuspendedSales() {
	// 	this.suspendSaleClient.getNextSuspendNo().subscribe(
	// 		(suspendNo) => {
	// 			this.getSuspendSaleItems(suspendNo).subscribe(
	// 				(suspendSaleItems) => {
	// 					if (suspendSaleItems.length > 1) {
	// 						// Automatically open the SaleSuspendHdrModal with the fetched items
	// 						this.openSuspendHdrModal(suspendSaleItems);
	// 					}
	// 				},
	// 				(error) => {
	// 					console.error(
	// 						"Error fetching suspended sale lines:",
	// 						error
	// 					);
	// 				}
	// 			);
	// 		},
	// 		(error) => {
	// 			console.error("Error fetching suspend number:", error);
	// 		}
	// 	);
	// }

	backButtonClick() {
		console.log("Back button clicked");
		this.modalService.dismissAll(); // Close any open modal

		// If items need to be suspended, open the modal
		if (this.goToSuspendSale && this.cart.total > 0) {
			console.log("Opening suspend sale modal with items:");
			this.openSuspendSaleModal(); // Open suspend sale modal if there are items to suspend
		} else {
			// Handle navigation based on the previous URL or clear staff login
			if (this.prevUrl) {
				console.log("Navigating back to:", this.prevUrl);
				this.router.navigateByUrl(this.prevUrl); // Navigate back to the previous URL
			} else {
				console.log("No previous URL, clearing staff login.");
				this.store.dispatch(clearStaffLogin()); // Log out if no previous URL
			}
		}
	}

	launchCustomerClubModal() {
		if (!this.canEditCart) {
			CreateErrorModal(
				this.modalService,
				false,
				"Cannot modify customer club member for customer special orders."
			);
			return;
		}

		const modalRef = this.modalService.open(CustomerClubModalComponent, {
			size: "xl",
			centered: true,
		});
		modalRef.componentInstance.name = "CustomerClubModal";
		modalRef.result
			.then((result) => {
				if (result) {
					console.log("result from modal:", result);
				}
			})
			.catch((error) => {
				console.log("Error occurred: ", error);
			});
	}

	openSuspendSaleModal() {
		if (!this.canEditCart) {
			CreateErrorModal(
				this.modalService,
				false,
				"Cannot suspend customer special orders."
			);
			return;
		}

		if (!this.cart || !this.cart.items || this.cart.items.length === 0) {
			this.store.dispatch(clearStaffLogin());
			this.router.navigateByUrl("/staff-login");
		} else {
			const modalRef = this.modalService.open(SaleSuspendModalComponent, {
				size: "lg",
				centered: true,
			});
			modalRef.componentInstance.name = "SaleSuspendModal";
			modalRef.result
				.then((result) => {
					if (result) {
						console.log("Result from suspend sale modal:", result);
					}
				})
				.catch((error) => {
					console.log("Error occurred:", error);
				});
		}
	}

	openSuspendHdrModal(suspendSaleItems: SuspendSaleLineDto[]) {
		if (this.goToSuspendSale) {
			console.log("trying to open Suspend Hdr Modal");
			const modalRef = this.modalService.open(
				SaleSuspendHdrModalComponent,
				{
					size: "xl",
					centered: true,
				}
			);
			modalRef.componentInstance.suspendSaleItems = suspendSaleItems; // Pass the suspended sale items
			modalRef.result
				.then((result) => {
					if (result) {
						console.log(
							"result from sale suspend header modal:",
							result
						);
					}
				})
				.catch((error) => {
					console.log("Error occurred: ", error);
				});
		}
	}

	openSundryModal() {
		if (!this.canEditCart) {
			CreateErrorModal(
				this.modalService,
				false,
				"Cannot add sundry items to customer special orders."
			);
			return;
		}

		const modalRef = this.modalService.open(SundryModalComponent, {
			centered: true
		});
		modalRef.componentInstance.name = 'SundryModal';
	}


	deselectCustomerClubMember() {
		this.store.dispatch(customerClubSearchActions.init());
	}

	nextBtnClick() {
		this.onNextClick.emit();
	}

	async launchCommentModal() {
		const { value: comment } = await Swal.fire({
			title: 'Sale Comment',
			input: 'textarea',
			text: 'Enter your comment',
			inputValue: this.saleComment || '',
			showCancelButton: true,
			inputValidator: (value) => {
				if ((value && value.length > 49)) {
					return 'Comment must be less than 50 characters';
				}
				return null;
			}
		});

		if (comment !== undefined && comment.length > 0) {
			this.saleComment = comment;
			this.store.dispatch(SaleNoteActions.setSaleNote({ note: comment }));
		}
	}

	removeComment() {
		this.saleComment = '';
		this.store.dispatch(SaleNoteActions.clearSaleNote());
	}
}
