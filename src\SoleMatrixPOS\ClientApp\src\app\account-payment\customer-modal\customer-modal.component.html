<div class="content-wrapper pt-10">
    <div class="m-2 d-flex justify-content-between">
        <div class="d-flex flex-column justify-content-between ml-3 mb-2">
            <table class="table  table-striped ml-2 mr-2 table-hover table-summary-font">
                <thead>
                    <tr>
                        <th scope="col-1">Account</th>
                        <th scope="col-1" [ngbTooltip]="'Balance is the amount outstanding on the account.
A positive value means the account needs to pay off the debt outstanding.
A negative value means the account has extra funds allocated'">
                            Balance
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div class="ml-5">{{customer.contactName}}</div>
                        </td>
                        <td>
                            <div class="ml-5" [ngClass]="sumBalances(customer) <= 0 ? 'text-success' : 'text-danger'">
                                {{toFinancialString(sumBalances(customer))}}
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
            <!-- <div class="column col-6">
                <pos-payment-cart-table (removePayment)="removePaymentOnDoubleClick($event)" [transaction$]="transaction$"></pos-payment-cart-table>
            </div> -->
            <form [formGroup]="paymentForm" (ngSubmit)="submitForm(customer)"
                class="d-flex flex-column align-items-center justify-content-between">
                <div class="d-flex flex-row align-items-center gap-3">
                    <div>
                        <label>Amount</label>
                        <div class="d-flex flex-row justify-content-center align-items-center">
                            <input [class.is-invalid]="paymentForm.get('amount').invalid 
                                && paymentForm.get('amount').touched" class="form-control" formControlName="amount"
                                type="number" />
                            <button *ngIf="sale" id="remainder" type='button' class="btn btn-outline-default mt-2"
                                (click)="useRemainder()" [translate]="'payment.modal.buttons.UseRemainder'">
                                Use Remainder
                            </button>
                        </div>
                    </div>

                    <div>
                        <label>New Balance</label>
                        <div [ngClass]="(isRefund || !sale ? calculateNewBalance() : calculateNewBalanceForSale()) <= 0 ? 'text-success' : 'text-danger'"
                            class="new-balance text-end">
                            {{toFinancialString(isRefund || !sale ? calculateNewBalance() : calculateNewBalanceForSale())}}
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-success mt-2"
                    [disabled]="!paymentForm.get('amount').value || paymentForm.get('amount').value <= 0">
                    PROCESS PAYMENT
                </button>
            </form>
        </div>
        <div>
            <button class="text-secondary float-right" (click)="dismiss('Cross Click')">X</button>
        </div>
    </div>
</div>