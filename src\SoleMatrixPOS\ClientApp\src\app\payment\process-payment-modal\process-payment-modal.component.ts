import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';

@Component({
	selector: 'pos-process-payment-modal',
	templateUrl: './process-payment-modal.component.html',
	styleUrls: ['./process-payment-modal.component.scss']
})
export class ProcessPaymentModalComponent implements OnInit {
	@Input() headerText: string = 'payment.Process.OkToProcessPayment';
	@Input() processButtonText: string = 'payment.Process.ProcessPayment';

	constructor(
		public activeModal: NgbActiveModal,
		private router: Router,
		private store: Store<AppState>
	) { }

	ngOnInit() {// Check if the current route is staff-login
		if (this.router.url.includes('/staff-login')) {
			this.activeModal.dismiss('Navigated away');
		}
	}

	goBack() {
		this.activeModal.dismiss();
	}

	process() {
		this.activeModal.close("Process");
		// console.log("transaction", this.transaction);
		//   this.store.dispatch(paymentActions.commitSplitTransaction({ payload: element }));
		// })
		// this.router.navigateByUrl('/home');
	}
}
