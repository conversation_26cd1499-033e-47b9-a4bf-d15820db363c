using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Layby;
using SoleMatrixPOS.Filters;
using System.Threading.Tasks;
using SoleMatrixPOS.Application.Layby.Commands;
using System.Collections.Generic;
using SoleMatrixPOS.Application.Transaction;
using System;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[RequireStaffCodeFilter]
	[ApiController]
	public class LaybyPaymentController : ControllerBase
	{
		private readonly IMediator _mediator;

		public LaybyPaymentController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[HttpPost("SubmitPayments")]
		public async Task<ActionResult<MakeLaybyPaymentResultDto>> SubmitPayments([FromBody] LaybyPaymentWithTransactionDto payload)
		{
			var result = await _mediator.Send(new MakeLaybyPaymentsCommand(payload.PaymentsDto, payload.TransactionDto));
			return Ok(result);
		}

		[HttpPost("CancelLayby")]
		public async Task CancelLayby([FromBody] MakeLaybyPaymentsDto payload)
		{
			await _mediator.Send(new MakeLaybyRefundCommand(payload));
		}
	}
}

public class LaybyPaymentWithTransactionDto
{
	public MakeLaybyPaymentsDto PaymentsDto { get; set; }
	public TransactionDto TransactionDto { get; set; }
}
