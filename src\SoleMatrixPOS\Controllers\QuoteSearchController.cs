using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Enums;
using SoleMatrixPOS.Application.OrderItem;
using SoleMatrixPOS.Application.OrderItem.Queries;
using SoleMatrixPOS.Application.QuoteItem;
using SoleMatrixPOS.Application.QuoteItem.Queries;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class QuoteSearchController : ControllerBase
	{
		private readonly IMediator _mediator;

		public QuoteSearchController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[HttpPost]
		public async Task<IEnumerable<CreateQuoteDto>> Get([FromBody] QuoteSearchRequestDto quoteSearchRequestDto)
		{
			return await _mediator.Send(new SearchQuotesQuery(quoteSearchRequestDto));
		}
	}
}
