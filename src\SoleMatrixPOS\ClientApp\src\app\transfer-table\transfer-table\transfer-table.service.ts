import { Injectable } from '@angular/core';
import { Size } from '../../stock-sale-table/classes/size';
import { AppState } from '../../stock-sale-table/redux/app.store';
//import { AppState, state } from '../../stock-sale-table/test-state';
import { Store } from '@ngrx/store';
import { getLocations } from '../../stock-sale-table/redux/location/location.selector'
import { getSizes } from '../../stock-sale-table/redux/size/size.selector'
import { Location } from '../../stock-sale-table/classes/location.model'
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TransferTableService {

  public sizes$: Observable<Size[]>;
  public locations$: Observable<Location[]>;

  constructor(private store: Store<AppState>) { 

    //this.sizes$ = of(state.sizes);
    //this.locations$ = of(state.locations);

    this.sizes$ = this.getSize();
    this.locations$ = this.getLocation();

  }

  public getSize(): Observable<Size[]> {
    return this.store.select(getSizes)
  }

  public getLocation(): Observable<Location[]> {
    return this.store.select(getLocations)
  }


}
