import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PaymentModalButtonComponent } from './payment-modal-button.component';

describe('PaymentModalButtonComponent', () => {
  let component: PaymentModalButtonComponent;
  let fixture: ComponentFixture<PaymentModalButtonComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PaymentModalButtonComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PaymentModalButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
