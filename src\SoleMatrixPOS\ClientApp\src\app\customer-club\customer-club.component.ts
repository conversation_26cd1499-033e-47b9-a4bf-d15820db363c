import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AppState } from '../reducers';
import * as customerClubSelectors from '../reducers/customer-club/club-search/customer-club.selectors';
import { CustomerClubDto } from '../pos-server.generated';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'customer-club',
  templateUrl: './customer-club.component.html',
  styleUrls: ['./customer-club.component.scss'],
  host: { class: 'wrapper' }
})
export class CustomerClubComponent implements OnInit {
  selectedCustomerClubMember: CustomerClubDto;

  constructor(private store: Store<AppState>, private router: Router) {}

  ngOnInit() {
    // Subscribe to the selected customer club member from the store
    this.store
      .select(customerClubSelectors.selectedCustomerClubMember)
      .pipe(
        filter((member) => !!member) // Ensure only non-null values are handled
      )
      .subscribe((member) => {
        this.selectedCustomerClubMember = member;
        console.log('Customer selected:', this.selectedCustomerClubMember);
      });
  }

  goToHistory() {
    if (this.selectedCustomerClubMember) {
      this.router.navigate(['/customer-club/history']);
    } else {
      alert('Please select a customer first.');
    }
  }

  onDoubleClickMember() {
    this.goToHistory();
  }
}
