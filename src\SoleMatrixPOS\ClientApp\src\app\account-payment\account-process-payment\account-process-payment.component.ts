import { Component, OnInit } from '@angular/core';
import { AppState } from 'src/app/reducers';
import { CustomerMateDto, EftposClient, GetReceiptDto, ReceiptTransactionDto, StockItemDto } from '../../pos-server.generated';
import { Store } from '@ngrx/store';
import * as staffActions from '../../reducers/staff/staff.actions';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import * as accountPaymentSelectors from '../../reducers/account-payments/account-payment.selectors';
import * as accountPaymentActions from '../../reducers/account-payments/account-payment.actions'
import { CustomerPayment } from '../customer-modal/customer-modal.component';
import { PaymentModalButton } from 'src/app/payment/payment-modal-button/payment-modal-button.component';
import { financialRound, Payment, PaymentType, Transaction } from 'src/app/payment/payment.service';
import { OpenCashDrawerAction } from 'src/app/printing/printing-definitions';
import { toFinancialString } from 'src/app/utility/math-helpers';
import { of } from 'rxjs';
import Swal from 'sweetalert2';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import * as transActions from '../../reducers/transaction/transaction.actions';
import * as transSelectors from '../../reducers/transaction/transaction.selectors';
import { CreateErrorModal } from 'src/app/error-modal/error-modal.component';
import { ProcessPaymentModalComponent } from 'src/app/payment/process-payment-modal/process-payment-modal.component';
import { TransactionDto, TranslogDto } from 'src/app/pos-server.generated';
import { EftposService, mapCartToLinklyBasket } from '../../eftpos/eftpos.service';
import * as sysSelectors from '../../reducers/sys-config/sys-config.selectors';
import * as receiptActions from '../../reducers/receipt-printing/receipt.actions'
import { WaitingForEftposModalComponent } from '../../payment/waiting-for-eftpos-modal/waiting-for-eftpos-modal.component';
import { tap } from 'rxjs/operators';
import { CartItem } from '../../reducers/sales/cart/cart.reducer';
import { ReceiptBatch, TextAction, CutAction, CutType, FeedAction, BarcodeAction } from '../../printing/printing-definitions';
import { EmailReceiptComponent } from '../../email-receipt/email-receipt.component';
import { PrintingService, SolemateReceiptOptions } from 'src/app/printing/printing.service';

const regularModalButtons: PaymentModalButton[] = [
	new PaymentModalButton("Cash", "fa-money-bill-wave text-success", PaymentType.Cash),
	new PaymentModalButton("Eftpos", "fa-credit-card text-success", PaymentType.Eftpos),
	//new PaymentModalButton("Cheque", "fa-money-check-edit-alt text-info", PaymentType.Cheque),
	new PaymentModalButton("Gift Card", "fa-gift text-info", PaymentType.GiftCard)
];

@Component({
	selector: 'pos-account-process-payment',
	templateUrl: './account-process-payment.component.html',
	styleUrls: ['./account-process-payment.component.scss']
})
export class AccountProcessPaymentComponent implements OnInit {
	readonly ACCOUNTPAYMENT_TRANSTYPE = 19;

	public selectedCustomer$: Observable<CustomerPayment>
	public selectedCustomer: CustomerPayment

	modalButtons: PaymentModalButton[] = regularModalButtons;
	public transaction: Transaction;
	public transaction$: Observable<Transaction>;
	private transactionSubject: BehaviorSubject<Transaction>;

	sysStatus: any;
	public sysStatus$: Observable<any>;

	transNo$: Observable<number>
	alwaysOpenCashTill: string = 'F';
	transNo: number

	intEftReceipts: GetReceiptDto[];

	alreadyAutoRequestedConfirmation: boolean = false;
	readyToProcess: boolean = false;

	constructor(private store: Store<AppState>, private modalService: NgbModal, private router: Router, private eftposService: EftposService, private printService: PrintingService
	) {

	}

	ngOnInit() {
		// Init 
		this.store.dispatch(transActions.init())

		this.store.dispatch(transActions.getTransactionNo());

		// Subscribe to the transaction number with proper error handling
		this.store.select(transSelectors.transNo)
			.pipe(
				tap(transNo => {
					this.transNo = transNo;
					console.log('Transaction number updated:', transNo);
				})
			)
			.subscribe();

		this.sysStatus$ = this.store.select(sysSelectors.selectSysConfig);
		this.sysStatus$.subscribe((sysconfig) => {
			this.sysStatus = sysconfig
		}
		);

		this.selectedCustomer$ = this.store.select(accountPaymentSelectors.storeSelectedCustomer)
		this.selectedCustomer$.subscribe((s) => this.selectedCustomer = s)
		this.transaction = new Transaction(this.selectedCustomer.amount as number);
		this.transactionSubject = new BehaviorSubject<Transaction>(this.transaction);
		this.transaction$ = this.transactionSubject.asObservable();

		this.transaction$.subscribe(t => {
			console.log("Transaction has changed: ", t);
			if (t.amountDue <= 0) {
				console.log("Let's check...");
				// If we haven't asked before
				if (!this.alreadyAutoRequestedConfirmation) {
					this.process();
					this.alreadyAutoRequestedConfirmation = true;
				}
			}
		});

		//   this.store.select(transSelectors.completed).subscribe(
		//		value => {if(value) this.accountPaymentTransactionCompleted()}
		//);

	}
	accountPaymentTransactionCompleted(): void {
		// Init the transaction reducer
		this.store.dispatch(transActions.init());
		this.store.dispatch(accountPaymentActions.init())
		this.store.dispatch(staffActions.clearStaffLogin());
		Swal.fire({
			title: "Account Payment Completed",
			text: `
			  The account was payed successfully.
			`
		});
	}
	handlePayment(payment: Payment) {
		console.log("Attempting to add payment: ", payment);

		let success = this.transaction.addPayment(payment);

		if (success) {
			// Emit the updated transaction to subscribers
			this.transactionSubject.next(this.transaction);

			// Payment completed, let's check if we've gone below 0
			if (this.transaction.amountDue <= 0) {
				if (!this.alreadyAutoRequestedConfirmation) {
					this.process();
					this.alreadyAutoRequestedConfirmation = true;
				}
			}
		}
		else {
			// Show warning / error popup
			Swal.fire({
				title: "Error",
				text: "You have attempted a payment which exceeds the due amount."
			});
		}

		this.readyToProcess = this.determineIfReadyToProcess();
	}

	process() {
		// Complete one final check 
		if (!this.determineIfReadyToProcess()) {
			CreateErrorModal(this.modalService, true, "Please complete the required payment.");
			return;
		}
		let modalRef = this.modalService.open(ProcessPaymentModalComponent, { size: 'xl', centered: true });
		modalRef.componentInstance.name = "Process Payment";
		modalRef.result.then((result) => {
			if (result === 'Process') {
				this.checkIntegratedEftpos();
			}
		}).catch(res => console.log("Error occurred: ", res));
	}


	checkIntegratedEftpos(): void {
		// const transNo = this.transNo.toString();
		// update the ctrans
		// const updatedCtrans = this.cTrans
		let trans = this.getAccountPaymentTransaction();

		console.log(this.transNo)
		let intEftPayment = new Payment;
		let intEft = false;
		let intAmount = 0;
		for (const payment of this.transaction.payments) {
			if (payment.desc === "Integrated Eftpos") {
				intEftPayment = payment;
				intEft = true;
				intAmount = payment.amount;
				break;
			}
		}

		if (intEft) {
			const modalRef = this.modalService.open(WaitingForEftposModalComponent, {
				size: 'md',
				centered: true,
				backdrop: 'static',
				keyboard: false
			});
			console.log(this.transNo)
			// Determine which integrated EFTPOS provider to use
			switch (this.sysStatus.integratedEFTProvider) {
				case "Linkly":
					modalRef.componentInstance.tenderAmount = intAmount;
					modalRef.componentInstance.totalAmount = this.transaction.total;
					modalRef.componentInstance.store = this.store;
					modalRef.componentInstance.discountAmt = 0; // TODO: calculate discount if needed
					modalRef.componentInstance.surchargeAmt = intAmount * 0; // TODO: adjust surcharge calculation if required
					modalRef.componentInstance.taxAmt = intAmount * 0; // TODO: adjust tax calculation based on config
					modalRef.componentInstance.transNo = this.transNo;

					const stockItem: StockItemDto = {
						barcode: "AccountPayment",
						price: intAmount,
						styleDescription: "Account Payment",
						colourName: "",
						size: ""
					}

					const cartItem: CartItem = {
						id: Math.random().toString(36).substr(2, 9),
						quantity: 1,
						stockItem: stockItem,
						bestValue: 0,
						originalPrice: 0, //NOT USED
						lineNo: 0 //NOT USED
					}

					// Map the current cart to the format required by Linkly
					const mappedItems = mapCartToLinklyBasket([cartItem]);
					modalRef.componentInstance.items = mappedItems;
					modalRef.componentInstance.transType = "Purchase";
					break;

				case "Tyro":
					// TODO: Implement Tyro integration logic here if needed.
					break;

				default:
					console.log("Integrated EFTPOS not configured");
					return;
			}

			// Handle the result from the waiting-for-EFTPOS modal.
			modalRef.result.then((result: { paymentResult: Payment, surchargePayment: Payment }) => {
				if (result) {
					console.log("EFTPOS payment result:", result);
					this.eftposService.getReceipts(this.transNo, false)
						.subscribe((receipts: GetReceiptDto[]) => {
							this.intEftReceipts = receipts;
							console.log(receipts);
							this.submitTransaction(trans)
							this.accountTransactionCompleted(trans)
							this.printService.printEftposReceipt(receipts, true);
						});
				} else {
					this.transaction.removePayment(intEftPayment);
					this.store.dispatch(transActions.resetTransactionConfirmation());
					this.store.dispatch(transActions.getTransactionNo());
					console.log("EFTPOS payment failed or was cancelled");
				}
			}).catch(error => {
				console.error("Error in waiting-for-EFTPOS modal:", error);
			});

		}
		else {
			this.submitTransaction(trans)
			this.accountTransactionCompleted(trans)

		}

	}

	submitTransaction(trans: TransactionDto): void {
		this.store.dispatch(transActions.submitTransaction({ payload: trans }));

		this.store.select(sysSelectors.OpenCashTill)
			.pipe()
			.subscribe(limit => {
				this.alwaysOpenCashTill = limit || 'F';
			});

		if (this.alwaysOpenCashTill === 'T') {
			this.store.dispatch(receiptActions.executeBatch({
				payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
			}));
		}
		else {
			if (trans.payments.some(payment => payment.paymentType === 'Cash')) {
				this.store.dispatch(receiptActions.executeBatch({
					payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
				}));
			}
		}
		// console.log("Now submitting Ctrans")
		// this.store.dispatch(accountPaymentActions.insertCtrans({payload:cTrans}))
		// do not use insertCtrans endpoint here, use updateCustomerBalance instead (which creates a ctrans of type 4, subtype 4, status U)
		this.store.dispatch(accountPaymentActions.updateCustomerBalance({ payload: { customer: this.selectedCustomer.customer, paymentAmount: this.transaction.total, saleNo: this.transNo } }))

	}

	accountTransactionCompleted(trans: TransactionDto): void {

		const saleDateTime = new Date();


		let receiptTrans: ReceiptTransactionDto = {
			logs: trans.translogs,
			pays: trans.payments,
			saleDateTime: saleDateTime,
			transType: this.ACCOUNTPAYMENT_TRANSTYPE,
		};

		Swal.fire({
			title: "Sale Completed",
			text: "The sale was successfully submitted.",
			type: "success",
			showCancelButton: true,
			cancelButtonText: "Email Receipt",  // Set "Email Receipt" as the confirm button
			confirmButtonText: "Print Receipt",         // Set "Go Home" as the cancel button  
		}).then(async (result) => {

			// If the "Print Receipt" button is clicked
			if (result.value) {
				await this.printService.printSolemateReceipt(
					"Account Payment",
					trans.translogs,
					trans.payments,
					this.ACCOUNTPAYMENT_TRANSTYPE,
					this.transNo.toString(),
					SolemateReceiptOptions.default(),
					receiptTrans,
					this.transaction.change,
					null,
					null,
					null,
					null,
					null,
					null,
					{ code: "", name: "", newBalance: this.updatedBalance(this.transaction.total, this.selectedCustomer.customer) } // only interested in the new Balance
				);

				//this.store.dispatch(receiptActions.printSale({ payload: receiptTrans }));
			}
			// If the "Email" button is clicked
			else if (result.dismiss === Swal.DismissReason.cancel) {
				this.openEmailModal(receiptTrans).then(() => {
					// Ensure the page redirects after sending the email
				});
			}
			this.accountPaymentTransactionCompleted();

		});
	}

	openEmailModal(receiptTrans: ReceiptTransactionDto): Promise<void> {
		return new Promise((resolve) => {
			const modalRef = this.modalService.open(EmailReceiptComponent, {
				size: 'lg',
				backdrop: 'static'
			});

			// Pass receiptTrans to the EmailReceiptComponent
			modalRef.componentInstance.receiptTrans = receiptTrans;

			// Check if a customer club member is selected and pass the email
			if (this.selectedCustomer.customer && this.selectedCustomer.customer.email) {
				modalRef.componentInstance.customerEmail = this.selectedCustomer.customer.email;
			}

			modalRef.result.then(() => {
				console.log('Email receipt sent.');
				resolve();  // Resolve the promise once the modal is closed
			}).catch(() => {
				resolve();  // Resolve the promise if the modal is dismissed
			});
		});
	}

	getAccountPaymentTransaction(): TransactionDto {
		// Get all translogs
		let resTranslogs: TranslogDto[] = [];
		resTranslogs.push({
			// TODO: Double check what to put for this
			styleCode: this.selectedCustomer.customer.customerCode,
			sellingPrice: this.transaction.total,
			quantity: 0,
			lineNo: 1,
		});
		return {
			payments: this.transaction.toTranspayDtos(),
			translogs: resTranslogs,
			transType: this.ACCOUNTPAYMENT_TRANSTYPE
		} as TransactionDto;
	}


	//Todo change to make better
	determineIfReadyToProcess(): boolean {
		return this.transaction.amountDue <= 0
	}

	toFinancialString(val) {
		if (val < 0) {
			return `-${toFinancialString(-1 * val)}`
		} else {
			return toFinancialString(val)
		}
	}

	sumBalances(customer: CustomerMateDto): number {
		return customer.currentBalance + customer.thirtyDayBalance + customer.sixtyDayBalance + customer.ninetyDayBalance;
	}

	updatedBalance(due: number, customer: CustomerMateDto): number {
		// console.log("-------")
		// console.log(due)
		// console.log(customer)
		return this.sumBalances(customer) - due
	}

}
