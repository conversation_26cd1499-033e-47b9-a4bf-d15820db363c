import { Action, createReducer, on } from '@ngrx/store';
import * as quoteSearchActions from './quote-item-search.actions';
import { CreateQuoteDto, QuoteSearchRequestDto } from '../../pos-server.generated';

export interface QuoteSearchState {
  isLoading: boolean;
  quotes: CreateQuoteDto[]; 
  options: QuoteSearchRequestDto;
  selected: CreateQuoteDto | null;
  error: any;
}

export const initialState: QuoteSearchState = {
  isLoading: false,
  quotes: [],
  options: {
    searchString: '',
    first: 0,
    skip: 0,
    orderSearchKeywordColumnDto: null,
    orderSearchOrderByColumnDto: null,
    orderSearchOrderByDirectionEnumDto: null,
  } as QuoteSearchRequestDto,
  selected: null,
  error: null,
};

export const quoteSearchReducer = createReducer(
  initialState,
  on(quoteSearchActions.init, () => ({
    ...initialState,
  })),
  on(quoteSearchActions.search, (state, action) => ({
    ...state,
    quotes: [], // Fixed: Changed from 'orders' to 'quotes'
    isLoading: true,
    options: action.searchParams,
  })),
  on(quoteSearchActions.searchResponse, (state, action) => ({
    ...state,
    isLoading: false,
    quotes: action.payload, // Fixed: Changed from 'orders' to 'quotes'
  })),
  on(quoteSearchActions.selectQuote, (state, action) => ({
    ...state,
    selected: action.payload,
  })),
  on(quoteSearchActions.cancelQuote, (state) => ({
    ...state,
    isLoading: true,
    error: null,
  })),
  on(quoteSearchActions.cancelQuoteSuccess, (state, action) => ({
    ...state,
    isLoading: false,
    quotes: state.quotes.filter(quote => quote.quoteHeader.quoteCode !== action.quoteCode),
    selected: null,
  })),
  on(quoteSearchActions.cancelQuoteFailure, (state, action) => ({
    ...state,
    isLoading: false,
    error: action.error,
  }))
);

export function reducer(state: QuoteSearchState | undefined, action: Action) {
  return quoteSearchReducer(state, action);
}