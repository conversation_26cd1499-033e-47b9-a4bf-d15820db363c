using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using SoleMatrixPOS.Application.Infrastructure;
using Microsoft.Extensions.DependencyInjection;

namespace SoleMatrixPOS.Filters
{
    public class RequireStaffCodeFilterAttribute : ActionFilterAttribute
    {
        public RequireStaffCodeFilterAttribute()
        {
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            var staffCodeContext = context.HttpContext.RequestServices.GetService<StaffCodeContext>();
            if (string.IsNullOrEmpty(staffCodeContext?.StaffCode))
            {
                context.Result = new UnauthorizedResult();
            }
            else
            {
                base.OnActionExecuting(context);
            }
        }

    }
}
