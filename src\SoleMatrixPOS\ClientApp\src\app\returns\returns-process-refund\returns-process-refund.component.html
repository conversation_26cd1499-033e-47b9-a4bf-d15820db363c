<div class="wrapper">
    <pos-nav-header pageName="Return - Refund"></pos-nav-header>
    <div class="content-wrapper flex-grow-1">
        <div class="container-fluid">

            <div class="row text-align-center pb-3">

                <div class="column col-6">
                    <pos-payment-cart-table (removePayment)="removePaymentOnDoubleClick($event)" [transaction$]="transaction$"></pos-payment-cart-table>
                </div>


                <div class="column col-6">
                    <pos-payment-modal-button-group [transType]="2" [amountDue]="(transaction$ | async).amountDue" (paymentResult)="handlePayment($event)" [modalButtons]="modalButtons"></pos-payment-modal-button-group>
                </div>
                

            </div>

        </div>
    </div>
    <pos-payment-footer [transaction$]="transaction$" (processPayment)="process()"></pos-payment-footer>
</div>