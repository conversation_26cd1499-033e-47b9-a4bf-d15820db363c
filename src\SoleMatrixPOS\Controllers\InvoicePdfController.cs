using System.ComponentModel;
using System.Net;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Invoice;

namespace SoleMatrixPOS.Controllers
{

    [Route("api/[Controller]")]
    public class InvoicePdfController : Controller
    {
        
        private readonly IMediator _mediator;

        public InvoicePdfController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [Route("test")]
        public async Task<IActionResult> Test()
        {
            var bytes = await _mediator.Send(new TestInvoicePdf());
            return File(bytes, "application/pdf");
        }

    }
}