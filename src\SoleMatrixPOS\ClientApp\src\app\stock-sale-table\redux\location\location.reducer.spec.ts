import { LocationReducer } from './location.reducer';
import * as LocationSelector from './location.selector';
import { state } from '../../test-state';
import { Location } from './location.model';

describe('LocationReducer', () => {

  describe('LocationActions.SWAP_STOCK', ()=> {
    it('should decrease the "from" location\'s stock', ()=> {

      const initialState = LocationSelector.getLocations.projector(state);
      const fromInitialQty = [...initialState][0].stock[0].qty;

      const transferQty = 1;

      const action = {
        type: "[LOCATION] swap", 
        id: 1,
        from: "Location 1",
        to: "Location 3",
        name: "BOUNCE",
        size: "1",
        qty: transferQty, 
        highlighted: "off"
      };

      const newState = LocationReducer([...initialState], action);
      const fromNewQty = [...newState][0].stock[0].qty;

      expect(fromNewQty).toEqual(fromInitialQty - transferQty);

    });

    it('should increase the "to" location\'s stock', ()=> {

      const initialState = LocationSelector.getLocations.projector(state);
      const toInitialQty = [...initialState][2].stock[0].qty;

      const transferQty = 1;

      const action = {
        type: "[LOCATION] swap", 
        id: 1,
        from: "Location 1",
        to: "Location 3",
        name: "BOUNCE",
        size: "1",
        qty: transferQty, 
        highlighted: "off"
      };

      const newState = LocationReducer([...initialState], action);
      const toNewQty = [...newState][2].stock[0].qty;

      expect(toNewQty).toEqual(toInitialQty + transferQty);

    });
  });

  describe('LocationActions.LOAD_LOCATIONS', ()=> {
    it('should replace the previous locations with the new locations', ()=> {

      const initialState = []; 
      const locations = LocationSelector.getLocations.projector(state);

      const action = {
        type: "[LOCATION] load", 
        locations: locations
      };

      const newState = LocationReducer(initialState, action);

      expect(newState).toEqual(locations);
    });
  });

  describe('LocationActions.HIGHLIGHT_TRANSFER', ()=> {
    it('should toggle the highlight state of the from location between "from" and "off"', ()=> {

      const initialState = LocationSelector.getLocations.projector(state);

      const action = {
        type: "[LOCATION] highlight", 
        id: 1,
        from: "Location 1",
        to: "Location 3",
        name: "BOUNCE",
        size: "1",
        qty: 1, 
        highlighted: "off"
      };

      // Turn on transfer with id '1'
      const onState = LocationReducer(initialState, action);
      expect(onState[0].stock[0].highlight).toEqual("from");

      // Turn off transfer with id '1'
      const offState = LocationReducer(initialState, action);
      expect(offState[0].stock[0].highlight).toEqual("off");

    });

    it('should toggle the highlight state of the from location between "to" and "off"', ()=> {

      const initialState = LocationSelector.getLocations.projector(state);

      const action = {
        type: "[LOCATION] highlight", 
        id: 1,
        from: "Location 1",
        to: "Location 3",
        name: "BOUNCE",
        size: "1",
        qty: 1, 
        highlighted: "off"
      };

      // Turn on transfer with id '1'
      const onState = LocationReducer(initialState, action);
      expect(onState[2].stock[0].highlight).toEqual("to");

      // Turn off transfer with id '1'
      const offState = LocationReducer(initialState, action);
      expect(offState[2].stock[0].highlight).toEqual("off");

    });
  })
});