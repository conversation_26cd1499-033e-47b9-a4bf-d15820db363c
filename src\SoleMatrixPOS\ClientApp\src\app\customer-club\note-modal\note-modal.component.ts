import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import {StyleItem} from '../../reducers/stock-search/styleItem';
import { FormBuilder, Validators } from '@angular/forms';

import { AppState } from 'src/app/reducers';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { map, first } from 'rxjs/operators';

import { NoteDto, NoteSearchRequestDto, StaffLoginDto } from 'src/app/pos-server.generated';
import * as noteActions from '../../reducers/customer-club/notes/notes.actions';
import * as staffSelectors from '../../reducers/staff/staff.selectors';
import { Timestamp } from 'rxjs/internal/operators/timestamp';
import { geteuid } from 'process';

@Component({
  selector: 'pos-stock-search-modal',
  templateUrl: './note-modal.component.html',
  styleUrls: ['./note-modal.component.scss']
})
export class NoteModalComponent implements OnInit {

	@Input() name;
	@Input() customerClubNumber;
	staff$: Observable<StaffLoginDto> ;

	public noteForm = this.formBuilder.group({
		Note: [],
		Date: [],
		Time: []
	});

	constructor(public activeModal: NgbActiveModal,
		private store: Store<AppState>,
		private formBuilder: FormBuilder){
			console.log("SHOT TO THE HEART! AND YOU'RE TOO LATE");
		}

	ngOnInit() {
		this.staff$ = this.store.select(staffSelectors.selectStaffLoginDto);
	}

	dismiss(reason: string) {
		this.activeModal.dismiss(reason);
	}

	onSubmit() {
		let obj = this.noteForm.get("Note");

		let date = new Date();

		this.staff$.pipe(first())
			.subscribe((staffLoginDto) => {
				const noteDto: NoteDto = {
					key: Math.floor(Math.random() * 999999), // need a better way of generating unique keys
					code: this.customerClubNumber,
					lineNo: 1,
					date: date.getFullYear() + "-" + (date.getMonth() + 1 /*Thanks javascript...*/) + "-" + date.getDay(),
					time: date.getUTCHours() + ":" + date.getUTCMinutes() + ":" + date.getUTCSeconds() + ":" + date.getUTCMilliseconds(),
					text: obj.value,
					staff: staffLoginDto.name
				}
				console.log(noteDto);
				this.store.dispatch(noteActions.createNote({ payload: noteDto }));
			});


		this.activeModal.close(obj);
	}
}
