import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable, Subscription } from 'rxjs';
import { AppState } from 'src/app/reducers';
import { StaffLoginDto } from 'src/app/pos-server.generated';
import * as staffSelectors from '../../reducers/staff/staff.selectors';
import * as staffActions from '../../reducers/staff/staff.actions';
import { PrintingService } from '../../printing/printing.service'; 
import { StaffLoginState } from 'src/app/reducers/staff/staff.reducer';
import { Router } from '@angular/router';
import { EftposService } from '../../eftpos/eftpos.service';
import { ClockOutComponent } from '../clock-out/clock-out.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';


@Component({
	selector: 'pos-home-header',
	templateUrl: './home-header.component.html',
	styleUrls: ['./home-header.component.scss']
})
export class HomeHeaderComponent implements OnInit {
	collapsed = true;
	showDownloadPrinterService = false;
	printers: string[] = [];
	selectedPrinter: string | null = null;
	printerWidth: number = 48;
	printerWidthOptions: number[] = [];

	refreshSub: Subscription;

	staff$: Observable<StaffLoginDto>;
	staffLoginStateSub: Subscription	

	constructor(private store: Store<AppState>, private router: Router, private printingService: PrintingService, private eftposService: EftposService, private modalService: NgbModal) {
		// Generate printer width options from 28 to 72 in increments of 4
		for (let width = 32; width <= 56; width += 8) {
			this.printerWidthOptions.push(width);
		}
	}

	ngOnInit() {
		this.showDownloadPrinterService = localStorage.getItem('dontAskDownloadPrinterService') === 'true';
		this.staff$ = this.store.select(staffSelectors.selectStaffLoginDto);
		this.staffLoginStateSub = this.store.select(s => s.staff.staffLoginState).subscribe(staffLoginState => {
			if(staffLoginState === StaffLoginState.LoggedOut){
				this.router.navigateByUrl('/staff-login')
			}
		})

		// Fetch the list of printers from the service
		this.fetchPrinters();

		this.refreshSub = this.printingService.refreshPrinters$.subscribe(() => {
			this.fetchPrinters();
		});

		// Prepopulate with the last selected printer
		const lastSelectedPrinter = localStorage.getItem('selectedPrinter');
		if (lastSelectedPrinter) {
			this.selectedPrinter = lastSelectedPrinter;
		} else {
			this.selectedPrinter = null; // Explicitly set to null if nothing is in localStorage
		}

		// Load saved printer width from localStorage
		const savedWidth = localStorage.getItem('printerWidth');
		if (savedWidth) {
			this.printerWidth = parseInt(savedWidth);
		}
	}

	fetchPrinters() {
		this.printingService.getPrinters().subscribe({
			next: (response: any) => {
				if (response && response.printers) { 
					this.printers = response.printers;

					// Ensure the last selected printer is still available
					if (this.selectedPrinter && this.printers.indexOf(this.selectedPrinter) === -1) {
						this.selectedPrinter = null;
					}
				} else {
					this.printers = [];
				}
			},
			error: (error) => {
				console.error('Failed to fetch printers:', error);
				this.printers = [];
			}
		});
	}

	onPrinterChange() {
		if (this.selectedPrinter) {
			localStorage.setItem('selectedPrinter', this.selectedPrinter);
		} else {
			localStorage.removeItem('selectedPrinter');
		}
	}

	PINPadLogon() {
		this.eftposService.logon();
	}

	openClockOutModal() {
		this.modalService.open(ClockOutComponent, {
			centered: true,
			backdrop: 'static'
		});
	}

	switchUser() {
		this.store.dispatch(staffActions.clearStaffLogin());
	}

	onPrinterWidthChange() {
		localStorage.setItem('printerWidth', this.printerWidth.toString());
		this.printingService.setPrinterWidth(this.printerWidth);
	}
}
