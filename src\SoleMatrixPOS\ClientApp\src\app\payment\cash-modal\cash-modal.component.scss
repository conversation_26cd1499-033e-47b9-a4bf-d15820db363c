.btn-circle {
  width: 70px;
  height: 70px;
  padding: 10px;
  border-radius: 50%;
  font-size: 20px;
  text-align: center;
  line-height: 1.428571429;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  
  &[data-value="0.05"], 
  &[data-value="0.10"],
  &[data-value="0.20"],
  &[data-value="0.50"] { 
    background: linear-gradient(145deg, #B8B8B8, #A0A0A0);
  }
  
  &[data-value="1.0"],
  &[data-value="2.0"] { 
    background: linear-gradient(145deg, #E6C34A, #D4AF37);
  }
}

.btn-block {
  font-size: 20px;
  padding: 15px;
  border: none;
  color: black;
  border-radius: 4px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  min-height: 70px;
  
  &[data-value="5"] { 
    background: #e0a2c0;
    &::after {
      content: "5";
      font-size: 20px;
    }
  }
  &[data-value="10"] { 
    background: #A3C4D7;
    &::after {
      content: "10";
      font-size: 20px;
    }
  }
  &[data-value="20"] { 
    background: #DB5549;
    &::after {
      content: "20";
      font-size: 20px;
    }
  }
  &[data-value="50"] { 
    background: #f0c95e;
    &::after {
      content: "50";
      font-size: 20px;
    }
  }
  &[data-value="100"] { 
    background: #70B07D;
    &::after {
      content: "100";
      font-size: 20px;
    }
  }
  
  &[data-value="0.05"],
  &[data-value="0.10"],
  &[data-value="0.20"],
  &[data-value="0.50"] { 
    background: #B8B8B8;
  }
  
  &[data-value="1.0"],
  &[data-value="2.0"] { 
    background: #d6c37b;
  }
  
  &[data-value="clear"] { 
    background: #808080;
    color: white;
  }
}

.coins-container {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
  margin: 20px 0;
}
