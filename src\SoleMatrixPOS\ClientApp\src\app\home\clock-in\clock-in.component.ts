import { Component, OnInit } from '@angular/core';
import { StaffLoginState, StaffState } from 'src/app/reducers/staff/staff.reducer';
import { Observable, Subscription } from 'rxjs';
import { AppState } from 'src/app/reducers';
import { Store } from '@ngrx/store';
import * as staffActions from '../../reducers/staff/staff.actions';
import { logging } from 'protractor';
import { Router } from '@angular/router';

@Component({
	selector: 'pos-clock-in',
	templateUrl: './clock-in.component.html',
	styleUrls: ['./clock-in.component.scss']
})
export class ClockInComponent implements OnInit {


	date: Date;
	staffLoginStateSub: Subscription
	staff$: Observable<StaffState>;

	constructor(
		private store: Store<AppState>,
		private router: Router
	) {
		this.date = new Date();
		// TODO: This gives error if month is december
		//this.date.setMonth(this.date.getMonth() + 1);
		console.log(`Date = ${this.date}`)
	}

	ngOnInit() {
		this.staffLoginStateSub = this.store.select(s => s.staff.staffLoginState).subscribe(staffLoginState => {
			if(staffLoginState === StaffLoginState.DailyFloat){
				return this.router.navigateByUrl('/daily-float-enter')
			}
			if(staffLoginState === StaffLoginState.Complete){
				return this.router.navigateByUrl('/home')
			}
			if(staffLoginState === StaffLoginState.LoggedOut){
				return this.router.navigateByUrl('/staff-login')
			}

		})

		this.staff$ = this.store.select(s => s.staff);
	}

	ngOnDestroy() {
		this.staffLoginStateSub.unsubscribe()
	}


	ClockIn() {
		this.store.dispatch(staffActions.clockIn());
	}


	Cancel() {
		this.store.dispatch(staffActions.clearStaffLogin());
	}

}
