import { Component, OnInit, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { Observable, Subscription } from 'rxjs';
import { filter, pluck } from 'rxjs/operators';
import { ValidateBarcodeResult } from '../../../pos-server.generated';
import { AppState } from '../../../reducers';
import * as receiveDocketScanActions from '../../../reducers/receive-stock/receive-docket-scan/receive-docket-scan.actions'
import { ReceiveDocketScanState } from '../../../reducers/receive-stock/receive-docket-scan/receive-docket-scan.reducer';
@Component({
  selector: 'pos-receive-docket-scan',
  templateUrl: './receive-docket-scan.component.html',
  styleUrls: ['./receive-docket-scan.component.scss']
})
export class ReceiveDocketScanComponent implements OnInit, AfterViewInit {

  @ViewChild('input', { static: false }) inputElement: ElementRef;

  subscriptions: Subscription[] = [];

  receiveDocketScanState$: Observable<ReceiveDocketScanState>;

  @Output() docketScannedEvent = new EventEmitter<DocketScanSuccessResult>();
  
  validationStatusMsg: string = null;

  docketBarcodeValid: boolean = false;

  docketValidationAttempted: boolean = false;

  constructor(private store: Store<AppState>) { }

  enterDocketBarcode = new FormControl('');

  subscribeToState(){
    this.receiveDocketScanState$.subscribe(state => {
      this.receiveDocketScanState$.pipe(filter(s => s.validationResult != null)).subscribe(state => {
        this.docketValidStatusChanged(state);
      });
    });
  }

  ngOnInit() {
    // Subscribe to state
    this.receiveDocketScanState$ = this.store.select(s => s.receiveDocketScan);

    this.store.dispatch(receiveDocketScanActions.init())

    this.subscribeToState();

    // Subscribe to text input
    this.subscriptions.push(this.enterDocketBarcode.valueChanges.subscribe((v: string) => {
      this.docketInputValueChanged(v);
    }))
  }

  ngAfterViewInit() {
    // Set focus to the input element
    setTimeout(() => {
      this.inputElement.nativeElement.focus();
    }, 0);
  }

  docketValidStatusChanged(state: ReceiveDocketScanState) {
    if(state.validationResult == ValidateBarcodeResult.Valid){
      this.docketBarcodeValid = true;
      // TODO emit event to parent
      this.docketScannedEvent.emit(new DocketScanSuccessResult(
        state.docketBarcode,
        state.senderStoreName
      ));
    }

    else{
      this.validationStatusMsg = getValidationMessage(state.validationResult);
    }

    this.docketValidationAttempted = true;

  }

  docketInputValueChanged(v: string) {
    
    if (v.length >= 13) this.submitDocketBarcode();
  }

  submitDocketBarcode() {
    let barcode = this.enterDocketBarcode.value;

  // Add a leading zero if the barcode length is 12
  if (barcode.length === 12) {
    barcode = '0' + barcode;

    // Update the input field to show the corrected barcode
    this.enterDocketBarcode.setValue(barcode);

    this.store.dispatch(
      receiveDocketScanActions.validateBarcode({ validation: { barcode: barcode } })
    );
    console.log('Corrected Barcode:', barcode);
  } else {
    // Call action with the original barcode
    this.store.dispatch(
      receiveDocketScanActions.validateBarcode({ validation: { barcode: this.enterDocketBarcode.value } })
    );
  }
  console.log('after ifelse Barcode:', barcode);
}
}

function getValidationMessage(result: ValidateBarcodeResult) : string{
  var msg: string;
  switch(result){
    case ValidateBarcodeResult.Valid:{
      msg = "Success.";
      break;
    }

    case ValidateBarcodeResult.InvalidAlreadyProcessed:{
      msg = "This transfer has already been processed!";
      break;
    }

    case ValidateBarcodeResult.InvalidBarcodeFormat:{
      msg = "Barcode format is incorrect!";
      break;
    }
      
    case ValidateBarcodeResult.InvalidReceiveStore:{
      msg = "This transfer is not for your store!";
      break;
    }
      
    case ValidateBarcodeResult.InvalidSendStore:{
      msg = "This transfer was not sent from a valid store!";
      break;
    }
      
    case ValidateBarcodeResult.InvalidSendReceiveSameStore:{
      msg = "Can not receive your own transfers!"
      break;
    }

    case null:{
      msg = "";
      break;
    }

    default: {
      msg = ValidateBarcodeResult[result];
      break;
    }
  }
  return msg;
}

export class DocketScanSuccessResult{
  constructor(public barcode: string, public senderStoreName: string){}
}