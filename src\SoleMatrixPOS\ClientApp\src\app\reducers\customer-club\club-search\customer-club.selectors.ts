import { AppState } from '../../index';
import { createSelector } from '@ngrx/store';

export const select = (state: AppState) => state.customerClubSearch;

export const searchedCustomerClubMembers = createSelector(select, (s) => s.members);
export const searchOptions = createSelector(select, (s) => s.options);
export const searchLoading = createSelector(select, (s) => s.isLoading);
export const selectedCustomerClubMember = createSelector(select, (s) => s.selected);
export const suburbSearchResults = createSelector(select, (s) => s.suburbs);
export const selectCustomerClubSearchTerm = createSelector(select, (s) => s.searchTerm);
export const selectCustomerClubSearchField = createSelector(select, (s) => s.searchField);
