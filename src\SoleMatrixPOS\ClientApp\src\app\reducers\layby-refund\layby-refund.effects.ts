import { Injectable } from '@angular/core';
import { Actions, ofType, createEffect } from '@ngrx/effects';
import { LaybyRefundClient } from 'src/app/pos-server.generated';
import { map, mergeMap, catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import * as laybyActions from './layby-refund.actions';

@Injectable()
export class LaybyRefundEffects {
  constructor(
    private actions$: Actions,
    private laybyRefundClient: LaybyRefundClient
  ) {}

  cancelLayby$ = createEffect(() =>
    this.actions$.pipe(
      ofType(laybyActions.cancelLayby),
      mergeMap((action) => {
        console.log("Effect triggered with action: ", action);

        const cancelLaybyDto = {
          laybyId: action.payload.laybyId, 
        };

        return this.laybyRefundClient.cancelLayby(cancelLaybyDto).pipe(
          map(() => laybyActions.laybyCancelledSuccess()),
          catchError((error) => {
            console.error("Error in cancelLayby effect: ", error);
            return of(laybyActions.laybyCancelledFailure({ error: error.message }));
          })
        );
      })
    )
  );
}
