<!--Solematrix Header logo etc..-->

<div class="modal-header text-ng-primary">
    <h5 class="modal-title">HOUSE KEEPING - Password Entry</h5>
    <button type="button" class="close" aria-label="Close" (click)="dismiss()">
        <i class="fa fa-times" style="color: red;"></i>
    </button>
</div>

<div class="modal-body">
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit(loginForm.value)">
        <div class="form-floating mb-3">
            <input type="password" formControlName="password" autocomplete="new-password"
                class="form-control form-control-sm" placeholder="********"
                [ngClass]="{ 'is-invalid': (submitted && f.password.errors) || wrongPassword }" autofocus />
            <div *ngIf="(submitted && f.password.errors) || wrongPassword" class="invalid-feedback">
                <div *ngIf="f.password.errors?.required">Password is required</div>
                <div *ngIf="wrongPassword">Incorrect password. Please try again.</div>
            </div>
        </div>

        <div class="alert alert-warning" role="alert" *ngIf="managerLoginMessage$ | async as message">
            {{message}}
        </div>
    </form>
</div>

<div class="modal-footer">
    <button [disabled]="loading" class="btn btn-info btn-sm" (click)="onSubmit(loginForm.value)">OK</button>
    <button type="button" class="btn btn-outline-secondary btn-sm" (click)="dismiss()">Cancel</button>
</div>