// import { async } from '@angular/core/testing';
// import { MysqlApiService } from './mysql-api.service';
// import { asyncData } from 'src/testing/async-observable-helper';
// import { HttpErrorResponse } from '@angular/common/http';
// import { asyncError } from 'src/testing/async-error-helper';

// /**
//  * Need to research how to properly test API response errors
//  */
// describe('MysqlApiService', () => {

//   let service: MysqlApiService;
//   let http: { get: jasmine.Spy } 

//   beforeEach(() => {

//     http = jasmine.createSpyObj('HttpClient', ['get']);
//     service = new MysqlApiService(<any> http)

//   });

//   describe('getHeader', ()=> {
//     it('should succesfully get the header', async(()=> {
//       const data: any[] = [{"data":"data"}]

//       http.get.and.returnValue(asyncData(data));

//       service.getHeader().subscribe(
//         data => expect(data).toEqual(data, 'data'),
//         fail
//       );

//       expect(http.get.calls.count()).toBe(1, 'one call');
//     }))

//     it('should return an empty array when the server returns a 404', () => {
//       const errorResponse = new HttpErrorResponse({
//         error: 'test 404 error',
//         status: 404, statusText: 'Not Found'
//       });
     
//       http.get.and.returnValue(asyncError(errorResponse));

//       service.getHeader().subscribe(
//         data => fail('expected an error, not data'),
//         error  => expect(error.message).toContain('test 404 error')
//       );

//       expect(http.get).toHaveBeenCalled();
      
//     });
    
//   });

//   describe('getFirstHeader', ()=> {
//     it('should succesfully get the header', async(()=> {
//       const data: any[] = [{"data":"data"}]

//       http.get.and.returnValue(asyncData(data));

//       service.getFirstHeader().subscribe(
//         data => expect(data).toEqual(data, 'data'),
//         fail
//       );

//       expect(http.get.calls.count()).toBe(1, 'one call');
//     }))

//     it('should return an error when the server returns a 404', () => {
//       const errorResponse = new HttpErrorResponse({
//         error: 'test 404 error',
//         status: 404, statusText: 'Not Found'
//       });
     
//       http.get.and.returnValue(asyncError(errorResponse));
     
//       service.getFirstHeader().subscribe(
//         data => fail('expected an error, not data'),
//         error  => expect(error.message).toContain('test 404 error')
//       );

//       expect(http.get).toHaveBeenCalled();

//     });
    
//   });

//   describe('getSecondHeader', ()=> {
//     it('should succesfully get the header', async(()=> {
//       const data: any[] = [{"data":"data"}]

//       http.get.and.returnValue(asyncData(data));

//       service.getSecondHeader().subscribe(
//         data => expect(data).toEqual(data, 'data'),
//         fail
//       );

//       expect(http.get.calls.count()).toBe(1, 'one call');
//     }))

//     it('should return an error when the server returns a 404', () => {
//       const errorResponse = new HttpErrorResponse({
//         error: 'test 404 error',
//         status: 404, statusText: 'Not Found'
//       });
     
//       http.get.and.returnValue(asyncError(errorResponse));
     
//       service.getSecondHeader().subscribe(
//         data => fail('expected an error, not data'),
//         error  => expect(error.message).toContain('test 404 error')
//       );

//       expect(http.get).toHaveBeenCalled();

//     });
    
//   });

//   describe('getSizes', ()=> {
//     it('should succesfully get the sizes', async(()=> {
//       const data: any[] = [{"data":"data"}]

//       http.get.and.returnValue(asyncData(data));

//       service.getSizes().subscribe(
//         data => expect(data).toEqual(data, 'data'),
//         fail
//       );

//       expect(http.get.calls.count()).toBe(1, 'one call');
//     }))

//     it('should return an error when the server returns a 404', () => {
//       const errorResponse = new HttpErrorResponse({
//         error: 'test 404 error',
//         status: 404, statusText: 'Not Found'
//       });
     
//       http.get.and.returnValue(asyncError(errorResponse));
     
//       service.getSizes().subscribe(
//         data => fail('expected an error, not data'),
//         error  => expect(error.message).toContain('test 404 error')
//       );

//       expect(http.get).toHaveBeenCalled();

//     });
    
//   });

//   describe('getLocations', ()=> {
//     it('should succesfully get the header', async(()=> {
//       const data: any[] = [{"data":"data"}]

//       http.get.and.returnValue(asyncData(data));

//       service.getLocations().subscribe(
//         data => expect(data).toEqual(data, 'data'),
//         fail
//       );

//       expect(http.get.calls.count()).toBe(1, 'one call');
//     }))

//     it('should return an error when the server returns a 404', () => {
//       const errorResponse = new HttpErrorResponse({
//         error: 'test 404 error',
//         status: 404, statusText: 'Not Found'
//       });
     
//       http.get.and.returnValue(asyncError(errorResponse));
     
//       service.getLocations().subscribe(
//         data => fail('expected an error, not data'),
//         error  => expect(error.message).toContain('test 404 error')
//       );

//       expect(http.get).toHaveBeenCalled();

//     });
    
//   });

//   describe('getStockSales', ()=> {
//     it('should succesfully get the header', async(()=> {
//       const data: any[] = [{"data":"data"}]

//       http.get.and.returnValue(asyncData(data));

//       service.getStockSales().subscribe(
//         data => expect(data).toEqual(data, 'data'),
//         fail
//       );

//       expect(http.get.calls.count()).toBe(1, 'one call');
//     }))

//     it('should return an error when the server returns a 404', () => {
//       const errorResponse = new HttpErrorResponse({
//         error: 'test 404 error',
//         status: 404, statusText: 'Not Found'
//       });
     
//       http.get.and.returnValue(asyncError(errorResponse));
     
//       service.getStockSales().subscribe(
//         data => fail('expected an error, not data'),
//         error  => expect(error.message).toContain('test 404 error')
//       );

//       expect(http.get).toHaveBeenCalled();

//     });
    
//   });

//   describe('getQtyByDate', ()=> {
//     it('should succesfully get the header', async(()=> {
//       const data: any[] = [{"data":"data"}]

//       http.get.and.returnValue(asyncData(data));

//       service.getQtyByDate().subscribe(
//         data => expect(data).toEqual(data, 'data'),
//         fail
//       );

//       expect(http.get.calls.count()).toBe(1, 'one call');
//     }))

//     it('should return an error when the server returns a 404', () => {
//       const errorResponse = new HttpErrorResponse({
//         error: 'test 404 error',
//         status: 404, statusText: 'Not Found'
//       });
     
//       http.get.and.returnValue(asyncError(errorResponse));
     
//       service.getQtyByDate().subscribe(
//         data => fail('expected an error, not data'),
//         error  => expect(error.message).toContain('test 404 error')
//       );

//       expect(http.get).toHaveBeenCalled();

//     });
    
//   });

//   describe('getTransferFirstHeader', ()=> {
//     it('should succesfully get the header', async(()=> {
//       const data: any[] = [{"data":"data"}]

//       http.get.and.returnValue(asyncData(data));

//       service.getTransferFirstHeader().subscribe(
//         data => expect(data).toEqual(data, 'data'),
//         fail
//       );

//       expect(http.get.calls.count()).toBe(1, 'one call');
//     }))

//     it('should return an error when the server returns a 404', () => {
//       const errorResponse = new HttpErrorResponse({
//         error: 'test 404 error',
//         status: 404, statusText: 'Not Found'
//       });
     
//       http.get.and.returnValue(asyncError(errorResponse));
     
//       service.getTransferFirstHeader().subscribe(
//         data => fail('expected an error, not data'),
//         error  => expect(error.message).toContain('test 404 error')
//       );

//       expect(http.get).toHaveBeenCalled();

//     });
    
//   });

//   describe('getLastReceiptedCost', ()=> {
//     it('should succesfully get the header', async(()=> {
//       const data: any[] = [{"data":"data"}]

//       http.get.and.returnValue(asyncData(data));

//       service.getLastReceiptedCost().subscribe(
//         data => expect(data).toEqual(data, 'data'),
//         fail
//       );

//       expect(http.get.calls.count()).toBe(1, 'one call');
//     }))

//     it('should return an error when the server returns a 404', () => {
//       const errorResponse = new HttpErrorResponse({
//         error: 'test 404 error',
//         status: 404, statusText: 'Not Found'
//       });
     
//       http.get.and.returnValue(asyncError(errorResponse));
     
//       service.getLastReceiptedCost().subscribe(
//         data => fail('expected an error, not data'),
//         error  => expect(error.message).toContain('test 404 error')
//       );

//       expect(http.get).toHaveBeenCalled();

//     });
    
//   });

// });

