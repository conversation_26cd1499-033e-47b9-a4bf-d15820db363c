using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Enums;
using SoleMatrixPOS.Application.Client;
using SoleMatrixPOS.Application.Client.Queries;
using Microsoft.AspNetCore.Authentication.JwtBearer;

namespace SoleMatrixPOS.Controllers
{
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController]
    public class ClientSearchController : ControllerBase
    {
        private readonly IMediator _mediator;

        public ClientSearchController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost]
        public async Task<IEnumerable<CustomerClubDto>> Get([FromBody] ClientSearchRequestDto clientSearchRequestDto)
        {
            return await _mediator.Send(new ClientSearchQuery(clientSearchRequestDto));
        }
    }
}
