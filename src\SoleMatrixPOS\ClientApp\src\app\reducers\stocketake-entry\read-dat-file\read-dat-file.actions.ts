import { createAction, props } from "@ngrx/store";
import { BarcodeResultDto, ReadDatFileDto } from "src/app/pos-server.generated";


export const init = createAction('[ReadDatFile] init');

export const readDatFile = createAction('[ReadDatFile] Read file', props<{fileName: string}>());
export const readDatFileResponse = createAction('[ReadDatFile] Read file Response', props<{payload: ReadDatFileDto[]}>());


export const validateBarcode = createAction('[ValidateBarcode] Validate Barcode', props<{barcode: string}>());
export const validateBarcodeResponse = createAction('[ValidateBarcode] Validate Barcode Reponse', props<{payload: BarcodeResultDto}>());
