import { AppState } from '../index';
import { createSelector } from '@ngrx/store';
import { createFeatureSelector } from '@ngrx/store';
import { AccountPaymentState } from './account-payment.reducers';

export const selectAccountPaymentState = createFeatureSelector<AccountPaymentState>('accountPayment');

export const selectCustomers = createSelector(
  selectAccountPaymentState,
  (state: AccountPaymentState) => state.customers
);

export const selectLoading = createSelector(
  selectAccountPaymentState,
  (state: AccountPaymentState) => state.loading
);
export const select = (state: AppState) => state.accountPayment;
export const storeFilteredCustomers = createSelector(select, (s)=> s.filteredCustomers);
export const isLoading = createSelector(select, (s)=> s.isLoading )
export const storeAllCustomers = createSelector(select, (s)=> s.allCustomers);
export const storeSelectedCustomer = createSelector(select, (s) => s.selectedCustomer)
export const insertCtransConfirmed = createSelector(select, (s) => s.ctransInserted)
