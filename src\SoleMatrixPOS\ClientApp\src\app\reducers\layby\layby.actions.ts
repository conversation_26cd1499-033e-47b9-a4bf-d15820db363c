import { createAction, props } from '@ngrx/store';
import { CreateLaybyDto, TransactionDto, CancelLaybyDto } from 'src/app/pos-server.generated';

export const init = createAction("[Layby] Init");

export const startLayby = createAction("[Layby] Start Layby", props<{minDeposit: number}>());

export const addToLaybyDeposit = createAction("[Layby] Add To Deposit", props<{amount: number}>());

export const submitLayby = createAction("[Layby] Create Layby", props<{payload: CreateLaybyDto}>());

export const laybyCreated = createAction("[Layby] Layby Created");

export const setLaybyOrderInProgress = createAction(
    "[Layby] Set Order In Progress",
    props<{inProgress: boolean}>()
);

export const getLaybyNumber = createAction(
    "[Layby] Get Layby Number"
  );
  
  export const getLaybyNumberSuccess = createAction(
    "[Layby] Get Layby Number Success",
    props<{ payload: string }>()
  );
  
  export const getLaybyNumberFailure = createAction(
    "[Layby] Get Layby Number Failure",
    props<{ error: any }>()
  );