import { createAction, props } from "@ngrx/store";
import { GiftVoucherCreationDto, GiftVoucherPurchaseRequestDto, GiftVoucherResultDto } from "src/app/pos-server.generated";
import { Payment } from "src/app/payment/payment.service";

export const init = createAction('[Gift-Voucher] init');

//get voucher number
export const getVoucherNo = createAction('[Gift-Voucher] Get Voucher No');
export const getVoucherNoResponse = createAction('[Gift-Voucher] Get Voucher No Response', props<{voucherNo: string}>());

//submit form value to giftCartItem cart
export const addGiftVoucher = createAction('[Gift-Voucher] Add Gift Card', props<{giftCard: GiftVoucherCreationDto }>());

export const addPayment = createAction('[Gift-Voucher] Add Payment', props<{payment: Payment}>());
export const removePayment = createAction('[Gift-Voucher] Remove Payment', props<{ payment: Payment }>());


//gift submit
export const submit = createAction('[Gift-Voucher] Submit Gift Voucher', props<{payload: GiftVoucherPurchaseRequestDto}>());
export const submitCompleted = createAction('[Gift-Voucher] Submit Gift Voucher Response', props<{payload: GiftVoucherResultDto}>());

// credit notes
export const creditNoteReceived = createAction('[Gift-Voucher] Credit Note Received', props<{payload: GiftVoucherResultDto}>());