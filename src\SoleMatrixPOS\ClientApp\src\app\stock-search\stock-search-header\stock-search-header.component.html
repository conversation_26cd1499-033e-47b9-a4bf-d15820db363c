<div class="container-fluid pb-3">
	<div class="row align-items-center">
	  <div class="d-flex">
		<span class="fa-stack">
		  <i class="fas fa-circle fa-stack-2x text-danger"></i>
		  <i class="fas fa-search fa-stack-1x fa-inverse"></i>
		</span>
	  </div>
	  <div class="col-auto text-right text-danger font-weight-bold">
		<span>Search by</span>
	  </div>
  
	  <div class="col-2">
		<select
		  class="form-control form-control-special"
		  [(ngModel)]="searchOptions.searchByField"
		  (change)="onSearchFieldChange()"
		>
		  <option
			*ngFor="let column of columns"
			[value]="column"
		  >
			{{ columnDisplayNames.get(column) }}
		  </option>
		</select>
	  </div>
  
	  <div class="col">
		<input
		  #searchInput
		  type="text"
		  class="form-control"
		  [(ngModel)]="searchOptions.searchString"
		  (ngModelChange)="onSearchStringChange($event)"
		  [attr.maxlength]="maxLength"
		/>
	  </div>
  
	  <div class="col-auto ml-auto">
		<button
		  type="button"
		  class="btn btn-outline-default"
		  (click)="searchByBarcode()"
		>
		  <i class="fas fa-barcode-alt text-info mr-2"></i>
		  Barcode
		</button>
	  </div>
	</div>
  </div>
