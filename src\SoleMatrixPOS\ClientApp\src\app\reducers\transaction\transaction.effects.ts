// transaction.effects.ts

import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { map, mergeMap, catchError, tap } from 'rxjs/operators';
import { of, from } from 'rxjs';
import { TransactionClient, FileResponse } from 'src/app/pos-server.generated';
import * as transActions from './transaction.actions';

@Injectable()
export class TransactionEffects {
  constructor(
    private actions$: Actions,
    private client: TransactionClient
  ) {}

  // Effect for submitting a transaction
  submitTransaction$ = createEffect(() =>
    this.actions$.pipe(
      ofType(transActions.submitTransaction),
      mergeMap((action) =>
        this.client.addTransaction(action.payload).pipe(
          map((response) =>
            transActions.transactionConfirmation({ payload: response })
          ),
          catchError((error) => {
            console.error('Add Transaction Error:', error);
            return of(
              transActions.submitTransactionFailure({ error: error.message || 'Unknown error' })
            );
          })
        )
      )
    )
  );

  getTransPayByTransNo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(transActions.getTransPayByTransNo),
      mergeMap((action) =>
        this.client.getTransPayByTransNo(action.transNo).pipe(
          map((response) => {
            // Log the response here
            console.log('TransPay response:', response);
            return transActions.getTransPayByTransNoSuccess({ payload: response });
          }),
          catchError((error) => {
            return of(
              transActions.getTransPayByTransNoFailure({ error: error.message || 'Unknown error' })
            );
          })
        )
      )
    )
  );  

  // Effect for getting the next transaction number
  getTransactionNo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(transActions.getTransactionNo),
      mergeMap(() =>
        this.client.getTransactionNo().pipe(
          mergeMap((response: FileResponse) => {
            if (response.status === 200 || response.status === 206) {
              return from(this.extractTransNo(response.data)).pipe(
                map((transNo) =>
                  transActions.getTransactionNoSuccess({ payload: transNo })
                ),
                catchError((error) => {
                  console.error('Extract Transaction No Error:', error);
                  return of(
                    transActions.getTransactionNoFailure({ error: 'Failed to extract transaction number.' })
                  );
                })
              );
            } else {
              return of(
                transActions.getTransactionNoFailure({
                  error: `Unexpected status code: ${response.status}`,
                })
              );
            }
          }),
          catchError((error) => {
            console.error('Get Transaction No Error:', error);
            return of(
              transActions.getTransactionNoFailure({ error: error.message || 'Unknown error' })
            );
          })
        )
      )
    )
  );

  // Helper method to extract transNo from Blob
  private extractTransNo(blob: Blob): Promise<number> {
    return new Promise<number>((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const text = reader.result as string;
        const transNo = parseInt(text, 10);
        if (!isNaN(transNo)) {
          resolve(transNo);
        } else {
          reject('Failed to parse transaction number.');
        }
      };
      reader.onerror = () => {
        reject('Failed to read blob data.');
      };
      reader.readAsText(blob);
    });
  }
}
