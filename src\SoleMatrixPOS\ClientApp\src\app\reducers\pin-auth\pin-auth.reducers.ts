import { createReducer, on } from '@ngrx/store';
import * as PINAuthActions from './pin-auth.actions';
import { GetPINAuthDto } from 'src/app/pos-server.generated';

//export enum UpdatePINAuthStatus {
//	Idle = 'idle',
//	InProgress = 'in_progress',
//	Success = 'success',
//	Error = 'error',
//}

export interface PINAuthState {
	pinAuth: GetPINAuthDto | null;
	loading: boolean;
	error: any;
	//updateStatus: UpdatePINAuthStatus;
}

export const initialState: PINAuthState = {
	pinAuth: null,
	loading: false,
	error: null,
	//updateStatus: UpdatePINAuthStatus.Idle,
};

export const pinAuthReducer = createReducer(
	initialState,
	// Read PINAuth Reducers
	on(PINAuthActions.readPINAuth, state => ({
		...state,
		loading: true,
		error: null,
	})),
	on(PINAuthActions.readPINAuthSuccess, (state, { pinAuth }) => ({
		...state,
		pinAuth,
		loading: false,
	})),
	on(PINAuthActions.readPINAuthFailure, (state, { error }) => ({
		...state,
		loading: false,
		error,
	})),
	//// Update PINAuth Reducers
	//on(PINAuthActions.updatePINAuth, state => ({
	//	...state,
	//	updateStatus: UpdatePINAuthStatus.InProgress,
	//})),
	//on(PINAuthActions.updatePINAuthSuccess, state => ({
	//	...state,
	//	updateStatus: UpdatePINAuthStatus.Success,
	//})),
	//on(PINAuthActions.updatePINAuthFailure, (state, { error }) => ({
	//	...state,
	//	updateStatus: UpdatePINAuthStatus.Error,
	//	error,
	//})),
	//on(PINAuthActions.resetUpdateStatus, state => ({
	//	...state,
	//	updateStatus: UpdatePINAuthStatus.Idle,
	//})),
);
