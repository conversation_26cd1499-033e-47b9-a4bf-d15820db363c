import { Action, createReducer, on } from '@ngrx/store';
import * as laybySearchActions from './layby-search.actions';
import { LaybylineDto, LaybySearchQueryDto, LaybySearchResultDto, SortDirection, StockItemDto, StockSearchFields, StockSearchRequestDto } from '../../pos-server.generated';

export class LaybySearchState {
  isLoading: boolean;
  results: LaybySearchResultDto[];
  query: LaybySearchQueryDto;
  selectedLayby: LaybySearchResultDto;
  // New property to store layby lines
  laybyLines: LaybylineDto[];
}

export const initialState: LaybySearchState = {
  isLoading: false,
  results: [],
  query: {},
  selectedLayby: null,
  laybyLines: [] // initialize as empty array
} as LaybySearchState;

export const laybySearchReducer = createReducer(
  initialState,
  on(laybySearchActions.init, (state, action) => ({ ...initialState })),
  on(laybySearchActions.selectLayby, (state, action) => ({
    ...state,
    selectedLayby: action.payload
  })),
  on(laybySearchActions.search, (state, action) => ({
    ...state,
    isLoading: true,
    query: { ...action.searchParams }
  })),
  on(laybySearchActions.searchResponse, (state, action) => ({
    ...state,
    isLoading: false,
    results: action.payload || []
  })),
  on(laybySearchActions.searchMore, (state, action) => ({ ...state, isLoading: true })),
  on(laybySearchActions.searchMoreResponse, (state, action) => ({
    ...state,
    isLoading: false,
    results: [...state.results, ...action.payload]
  })),
  // Handle the getLaybyLinesResponse action to update the laybyLines property
  on(laybySearchActions.getLaybyLinesResponse, (state, action) => ({
    ...state,
    laybyLines: action.laybyLines
  }))
);

export function reducer(state: LaybySearchState | undefined, action: Action) {
  return laybySearchReducer(state, action);
}
