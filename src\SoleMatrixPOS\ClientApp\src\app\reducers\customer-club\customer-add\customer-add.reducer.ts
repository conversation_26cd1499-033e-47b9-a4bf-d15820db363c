import { Action, createReducer, on } from '@ngrx/store';
import * as customerAddActions from './customer-add.actions';

import { CustomerClubDto } from '../../../pos-server.generated';

export enum MemberAddStatus {
	None,
	Loading,
	Success,
	Error,
	Initial
}

export interface CustomerClubAddMemberState {
	status: MemberAddStatus;
	error: string | null;
	barcodeExists: boolean;
	member: CustomerClubDto | null;
}

export const initialState: CustomerClubAddMemberState = {
	status: MemberAddStatus.Initial,
	error: null,
	barcodeExists: false,
	member: null
};

export const customerClubAddMemberReducer = createReducer(initialState,
	on(customerAddActions.init, () => ({ ...initialState })),
	on(customerAddActions.createMember, (state, action) => ({ ...state })),
	on(customerAddActions.createMemberSuccess, (state, action) => ({ ...state, status: MemberAddStatus.Success })),
	on(customerAddActions.createMemberError, (state, action) => ({ ...state, status: MemberAddStatus.Error })),
	on(customerAddActions.checkBarcodeSuccess, (state, { exists }) => ({
		...state,
		barcodeExists: exists
	})),
	on(customerAddActions.checkBarcodeError, (state, { error }) => ({
		...state,
		error
	}))
);

export function reducer(state: CustomerClubAddMemberState | undefined, action: Action) {
	return customerClubAddMemberReducer(state, action);
}
