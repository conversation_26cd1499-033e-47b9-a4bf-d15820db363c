import { Component, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-print-return-modal',
  template: `
    <div class="modal-header">
      <h4 class="modal-title">{{title}}</h4>
    </div>
    <div class="modal-body">
      <p>{{message}}</p>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-primary" (click)="activeModal.close('print')">
        Print Receipt
      </button>
      <button type="button" class="btn btn-secondary" (click)="activeModal.close('return')">
        No
      </button>
    </div>
  `
})
export class PrintReturnModalComponent {
  @Input() title: string = 'End of Day Complete';
  @Input() message: string = 'Would you like to print a receipt?';

  constructor(public activeModal: NgbActiveModal) {}
} 