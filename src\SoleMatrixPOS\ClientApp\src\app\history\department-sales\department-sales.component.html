<div class="d-flex justify-content-between align-items-center">
    <h3 *ngIf="currentView === 'sales'">Department Sales</h3>
    <h3 *ngIf="currentView === 'refunds'">Department Refunds</h3>
    <button class="btn btn-link" (click)="toggleView()">
        <i class="fas fa-exchange-alt"></i>
    </button>
</div>
<div class="table-responsive">
    <div *ngIf="currentView === 'sales'">
        <table class="table table-striped ml-2 mr-2 table-hover">
            <thead>
                <tr>
                    <th scope="col-1">Department</th>
                    <th scope="col-1">Amount</th>
                    <th scope="col-1"> </th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let departmentSale of departmentSales$|async; let i = index">
                    <td>
                        {{departmentSale.departmentName}}
                    </td>
                    <td>
                        {{toFinancialString(departmentSale.amount)}}
                    </td>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div *ngIf="currentView === 'refunds'">
        <table class="table table-striped ml-2 mr-2 table-hover">
            <thead>
                <tr>
                    <th scope="col-1">Department</th>
                    <th scope="col-1">Amount</th>
                    <th scope="col-1"> </th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let departmentRefund of departmentRefunds$|async; let i = index">
                    <td>
                        {{departmentRefund.departmentName}}
                    </td>
                    <td>
                        {{toFinancialString(departmentRefund.amount)}}
                    </td>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>