import { Component, Input, OnInit } from "@angular/core";
import { AbstractControl, FormBuilder, Validators } from "@angular/forms";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { Payment, PaymentType } from "../payment.service";
import { financialRound } from "src/app/utility/math-helpers";
import { Store } from '@ngrx/store';
import { AppState } from '../../reducers';
import * as customerClubSearchSelectors from '../../reducers/customer-club/club-search/customer-club.selectors';
import { CustomerClubDto } from '../../pos-server.generated';
import { CustomerClubModalComponent } from 'src/app/customer-club/customer-club-modal/customer-club-modal.component';

@Component({
	selector: "pos-credit-note-issuance-modal",
	templateUrl: "./credit-note-issuance-modal.component.html",
	styleUrls: ["./credit-note-issuance-modal.component.scss"],
})
export class CreditNoteIssuanceModalComponent implements OnInit {
	@Input() amountDue: number;
	@Input() isLayby: boolean = false;
	selectedCustomer: CustomerClubDto;
	public form;
	public customerError: string = null;

	constructor(
		public activeModal: NgbActiveModal,
		private formBuilder: FormBuilder,
		private store: Store<AppState>,
		private modalService: NgbModal
	) {
		// Initialize form in constructor
		this.form = this.formBuilder.group({
			Amount: [
				null,
				[
					Validators.required,
					Validators.min(0.01),
					Validators.pattern(/^\d*[.]{0,1}\d{0,2}$/),
				],
			],
		});
	}

	get amount() {
		return this.form.get("Amount");
	}

	ngOnInit() {
		// Subscribe to customer changes
		this.store.select(customerClubSearchSelectors.selectedCustomerClubMember)
			.subscribe(customer => {
				this.selectedCustomer = customer;
				if (this.selectedCustomer) {
					this.customerError = null;
					// Set initial amount when customer is selected
					if (!this.amount.value) {
						this.amount.setValue(this.amountDue);
					}
				}
			});

		// If no customer is selected
		if (!this.selectedCustomer) {
			if (this.isLayby) {
				// For layby, just show the error message
				this.customerError = 'No customer selected. Please select a customer to issue a credit note.';
			} else {
				// For non-layby, open the customer selection modal
				const modalRef = this.modalService.open(CustomerClubModalComponent, { size: 'xl', centered: true });
				modalRef.componentInstance.name = 'CustomerClubModal';
				modalRef.result.then((result) => {
					if (result) {
						// The customer selection will be handled by the store subscription above
						console.log('Customer selected from modal');
					} else {
						this.activeModal.dismiss('No customer selected');
					}
				}).catch((reason) => {
					this.activeModal.dismiss('Modal closed');
				});
			}
		}
	}

	dismiss(reason: string) {
		this.activeModal.dismiss(reason);
	}

	apply() {
		if (!this.selectedCustomer) {
			if (this.isLayby) {
				this.customerError = 'No customer selected. Please select a customer to issue a credit note.';
			} else {
				this.activeModal.dismiss('No customer selected');
			}
			return;
		}

		if (this.form.valid) {
			this.activeModal.close({
				type: PaymentType.CreditNote,
				amount: +this.amount.value,
				customer: this.selectedCustomer // Include the customer in the payment data
			} as Payment & { customer: CustomerClubDto });
		} else {
			this.form.markAsPristine();
		}
	}

	useRemainder() {
		this.amount.setValue(this.amountDue);
	}

	setAmount(value: number) {
		const currentAmount = Number(this.amount.value) || 0;
		this.amount.setValue(financialRound(currentAmount + value).toFixed(2));
	}

	clearAmount() {
		this.amount.setValue(null);
	}

	public fieldValidate(control: AbstractControl): boolean {
		return control.invalid;
	}
}
