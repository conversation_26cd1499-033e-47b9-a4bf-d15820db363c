// // #docplaster
// import { async, ComponentFixture, TestBed } from '@angular/core/testing';
// import { TransferFooterComponent } from './transfer-footer.component';

// import { Component, DebugElement, NO_ERRORS_SCHEMA } from '@angular/core';
// import { By } from '@angular/platform-browser';

// import { RouterLinkDirectiveStub } from 'src/testing/router-link-directive-stub';
// import { MainDisplayComponent } from '../../main-table/main-display.component';
// import { Store } from '@ngrx/store';
// //import { AppState } from '../../../../redux/app.store';
// import { AppState, state } from '../../stock-sale-table/test-state';

// // #docregion component-stubs

// @Component({ selector: 'app-main-display', template: '' })
// class MainDisplayStubComponent { }

// @Component({ selector: 'router-outlet', template: '' })
// class RouterOutletStubComponent { }

// // #enddocregion component-stubs

// let comp: MainDisplayComponent;
// let fixture: ComponentFixture<MainDisplayComponent>;
// let valueServiceSpy: jasmine.SpyObj<Store<AppState>>;


// describe('TransferFooterComponent & TestModule', () => {

//   beforeEach(async(() => {

//     const spy = jasmine.createSpyObj('Store', ['select']);

//     // #docregion testbed-stubs
//     TestBed.configureTestingModule({
//       declarations: [
//         TransferFooterComponent,
//         RouterLinkDirectiveStub,
//         MainDisplayStubComponent
//       ],
//       providers: [
//         TransferFooterComponent,
//         { provide: Store, useValue: spy }
//       ]
//     })
//       // #enddocregion testbed-stubs
//       .compileComponents().then(() => {
//         fixture = TestBed.createComponent(TransferFooterComponent);
//         comp = fixture.componentInstance;
//       });
//     comp = TestBed.get(TransferFooterComponent);
//     valueServiceSpy = TestBed.get(Store);
//   }));

//   tests();

// });

// describe('TransferFooterComponent & NO_ERRORS_SCHEMA', () => {

//   beforeEach(async(() => {

//     const spy = jasmine.createSpyObj('Store', ['select']);

//     TestBed.configureTestingModule({
//       declarations: [
//         TransferFooterComponent,
//         RouterLinkDirectiveStub,
//         MainDisplayStubComponent
//       ],
//       schemas: [NO_ERRORS_SCHEMA],
//       providers: [
//         TransferFooterComponent,
//         { provide: Store, useValue: spy }
//       ]
//     })
//       .compileComponents().then(() => {
//         fixture = TestBed.createComponent(TransferFooterComponent);
//         comp = fixture.componentInstance;
//       });
//     comp = TestBed.get(TransferFooterComponent);
//     valueServiceSpy = TestBed.get(Store);
//   }));

//   tests();
// });

// function tests() {
//   let routerLinks: RouterLinkDirectiveStub[];
//   let linkDes: DebugElement[];

//   // #docregion test-setup
//   beforeEach(() => {
//     fixture.detectChanges(); // trigger initial data binding

//     // find DebugElements with an attached RouterLinkStubDirective
//     linkDes = fixture.debugElement
//       .queryAll(By.directive(RouterLinkDirectiveStub));

//     // get attached link directive instances
//     // using each DebugElement's injector
//     routerLinks = linkDes.map(de => de.injector.get(RouterLinkDirectiveStub));
//   });
//   // #enddocregion test-setup

//   it('can instantiate the component', () => {
//     expect(comp).not.toBeNull();
//   });

//   // #docregion tests
//   it('can get RouterLinks from template', () => {
//     expect(routerLinks.length).toBe(1, 'should have 1 routerLinks');
//     // console.log("routerLinks: ", routerLinks)
//     expect(routerLinks[0].linkParams).toBe('/main');
//     // console.log("routerLinks[0].linkParams: ", routerLinks[0].linkParams)

//   });

// }
