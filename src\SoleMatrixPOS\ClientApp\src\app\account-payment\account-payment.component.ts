import { Component, OnInit } from '@angular/core';
import { AppState } from 'src/app/reducers';
import { CustomerMateDto } from '../pos-server.generated';
import { Store } from '@ngrx/store';
import { Observable, Subject } from 'rxjs';
import * as accountPaymentActions from '../reducers/account-payments/account-payment.actions'
import * as accountPaymentSelectors from '../reducers/account-payments/account-payment.selectors';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CustomerModalComponent } from './customer-modal/customer-modal.component';
import { Transaction } from '../payment/payment.service';
import { toFinancialString } from '../utility/math-helpers';
import { Router } from '@angular/router';
import { financialRound } from '../payment/payment.service';
import { CustomerPayment } from './customer-modal/customer-modal.component';
import { ClientSearchKeywordColumnDto, ClientSearchOrderByColumnDto } from '../pos-server.generated';
import { StockSearchOrderByDirectionEnumDto } from '../pos-server.generated';
@Component({
  selector: 'pos-account-payment',
  templateUrl: './account-payment.component.html',
  styleUrls: ['./account-payment.component.scss']
})
export class AccountPaymentComponent implements OnInit {
  field = 'Name'
  term = ''
  customers$: Observable<CustomerMateDto[]>;
  public loading: boolean = false;

  // Add pagination parameters
  first: number = 25;
  skip: number = 0;

  constructor(private store: Store<AppState>, private router: Router, private modalService: NgbModal) { }

  ngOnInit() {
    const savedField = localStorage.getItem('accountPaymentSearchField');
    if (savedField) {
      this.field = savedField;
    }
    // init state
    this.store.dispatch(accountPaymentActions.init())
    this.search() // Replace getAllCustomers with search
    this.customers$ = this.store.select(accountPaymentSelectors.storeFilteredCustomers)
  }

  search() {
    localStorage.setItem('accountPaymentSearchField', this.field);
    const searchQuery = {
      searchString: this.term.toLowerCase(),
      first: this.first,
      skip: this.skip,
      searchField: this.field as 'Phone' | 'Name' | 'Email'
    }

    this.store.dispatch(accountPaymentActions.searchCustomers({ payload: searchQuery }))
  }

  selectCust(cust) {
    console.log(`Selected ${JSON.stringify(cust)}`)
    this.openCustomerModal(cust)
  }



  openCustomerModal(cust: CustomerMateDto) {
    console.log("Open Modal")
    const modalRef = this.modalService.open(CustomerModalComponent, {
      windowClass: 'daily-modal-window',
      size: 'l',
      centered: true
    });
    modalRef.componentInstance.name = 'CustomerModal';
    modalRef.componentInstance.customer = cust;
    modalRef.componentInstance.sale = false;
    modalRef.result.then((result) => {
      if (result) {
        console.log('result from modal:', result);
      }
    }).catch((reason) => console.log(reason));
  }

  backButtonClick() {
    this.router.navigateByUrl("/home");
  }

}

export class AccountPaymentSearch {
  searchString: string;
  first: number;
  skip: number;
  searchField: 'Phone' | 'Name' | 'Email';
  field?: string;
  term?: string;

  constructor(init?: Partial<AccountPaymentSearch>) {
    Object.assign(this, init);
  }
}