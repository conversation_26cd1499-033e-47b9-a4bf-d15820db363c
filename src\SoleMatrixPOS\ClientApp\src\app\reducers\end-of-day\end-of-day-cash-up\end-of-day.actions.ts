import { createAction, props } from '@ngrx/store';
import { EndOfDayDTO } from 'src/app/pos-server.generated';

export const init = createAction("[EndOfDay] Init");

export const submitEndOfDay = createAction(
  "[EndOfDay] Submit End of Day",
  props<{ payload: EndOfDayDTO }>()
);

export const endOfDayConfirmation = createAction(
  "[EndOfDay] End of Day Confirmed",
  props<{ message: string }>()
);

export const endOfDayError = createAction(
  "[EndOfDay] End of Day Submission Failed",
  props<{ error: any }>()
);
