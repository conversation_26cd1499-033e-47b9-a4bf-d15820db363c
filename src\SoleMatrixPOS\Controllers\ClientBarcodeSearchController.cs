using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Enums;
using SoleMatrixPOS.Application.Client;
using SoleMatrixPOS.Application.Client.Queries;
using Microsoft.AspNetCore.Authentication.JwtBearer;

namespace SoleMatrixPOS.Controllers
{
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController]
    public class ClientBarcodeSearchController : ControllerBase
    {
        private readonly IMediator _mediator;

        public ClientBarcodeSearchController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost] // just following the clientSearchController, alternatively could have used Get with just a string rather than dto
        public async Task<ActionResult<CustomerClubDto>> Get([FromBody] ClientBarSearchRequestDto clientBarSearchRequestDto)
        {

			var res = await _mediator.Send(new ClientBarSearchQuery(clientBarSearchRequestDto));
			return Ok(res);

		}

    }
}
