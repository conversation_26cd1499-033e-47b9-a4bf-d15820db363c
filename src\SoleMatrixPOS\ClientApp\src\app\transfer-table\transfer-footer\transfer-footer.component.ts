import { Component } from '@angular/core';
import { AppState } from '../../stock-sale-table/redux/app.store';
//import { AppState, state } from '../../stock-sale-table/test-state';
import { Store } from '@ngrx/store';
import * as LocationActions from '../../stock-sale-table/redux/location/location.action';

@Component({
  selector: 'app-transfer-footer',
  templateUrl: './transfer-footer.component.html',
  styleUrls: ['./transfer-footer.component.scss']
})
export class TransferFooterComponent {

  constructor(private store: Store<AppState>) { }

  toggleSales() {
    // console.log("Click");
    const action = new LocationActions
      .ToggleSalesAction();

    this.store.dispatch(action);
  }

}
