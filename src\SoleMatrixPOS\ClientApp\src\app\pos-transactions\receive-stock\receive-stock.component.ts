import { Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Store } from '@ngrx/store'
import { AppState } from '../../reducers';
import * as receiveStockActions from '../../reducers/receive-stock/receive-stock.actions'
import {ValidateBarcodeRequestDto, ValidateBarcodeResult} from '../../pos-server.generated'
import { Observable, Subscription } from 'rxjs';
import { pluck } from 'rxjs/operators';
import {DocketScanSuccessResult} from './receive-docket-scan/receive-docket-scan.component'
@Component({
  selector: 'pos-receive-stock',
  templateUrl: './receive-stock.component.html',
  styleUrls: ['./receive-stock.component.scss']
})
export class ReceiveStockComponent {

  subscriptions: Subscription[] = [];

  docketValidated: boolean;

  docketBarcode: string = null;

  senderStoreName: string = null;

  constructor() { 
    
  }

  docketScanned(result: DocketScanSuccessResult){
    // Finished - set valid
    this.senderStoreName = result.senderStoreName;
    this.docketBarcode = result.barcode;
    this.docketValidated = true;
  }

}
