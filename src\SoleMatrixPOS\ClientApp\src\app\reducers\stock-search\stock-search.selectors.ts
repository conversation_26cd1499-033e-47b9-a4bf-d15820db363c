import { AppState } from '../index';
import { createSelector } from '@ngrx/store';

export const select = (state: AppState) => state.stockSearch;

export const searchedItems = createSelector(select, (s) => s.items);
export const searchOptions = createSelector(select, (s) => s.options);
export const searchedItemsCount = createSelector(searchedItems, (s) => s.length);
export const isLoading = createSelector(select, (s) => s.isLoading);
