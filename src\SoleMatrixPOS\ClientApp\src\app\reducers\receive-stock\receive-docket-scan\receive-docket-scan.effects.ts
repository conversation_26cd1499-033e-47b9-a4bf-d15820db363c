import { Injectable } from '@angular/core';
import { Actions, Effect, ofType, createEffect } from '@ngrx/effects';
import * as receiveDocketScanActions from './receive-docket-scan.actions';
import {catchError, filter, map, mergeMap, tap, withLatestFrom} from 'rxjs/operators';
import {GenerateTransferNumberRequest, ReceiveStockClient} from '../../../pos-server.generated';
import {EMPTY} from 'rxjs';
import {Store} from '@ngrx/store';
import {AppState} from '../../index';

@Injectable()
export class ReceiveDocketScanEffects {
	constructor(
		private actions$: Actions,
		private receiveStockClient: ReceiveStockClient
	) {}

	validate$ = createEffect(() => this.actions$.pipe(
		ofType(receiveDocketScanActions.validateBarcode),
		mergeMap(
			(action)=>this.receiveStockClient.validateBarcode(action.validation)
			.pipe(
				map(
					(response) => receiveDocketScanActions.retrieveBarcodeValidation({response})
				)
			)
		)
	))
}
