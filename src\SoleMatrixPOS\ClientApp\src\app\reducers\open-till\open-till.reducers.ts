import { createReducer, on, Action } from '@ngrx/store';
import * as openTillActions from './open-till.actions'
export class TransactionState {
    transactionCompleted: boolean;
}

export const initialState: TransactionState = {
    transactionCompleted: false
}

export const transactionReducer = createReducer(initialState,
    on(openTillActions.init, (state) => initialState),
    on(openTillActions.transactionConfirmation, (state) => {
        return {...state, transactionCompleted: true}
    }),
)
