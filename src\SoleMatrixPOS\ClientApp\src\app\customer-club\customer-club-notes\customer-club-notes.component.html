<div class="row text-align-center mt-2 mb-4">

    <h3 class="mt-3 mb-0 mr-2 ml-4 mr-4 text-secondary">
        Search</h3> <!-- TODO Add translation -->

    <div class="col-3 pl-0">
        <!--(keyup)='keyUp.next($event)'-->
        <input type="text" class="form-control" [(ngModel)]="term" (keyup)="search()" />
    </div>

    <div class="col-auto ml-auto mr-4">
        <button type="button" class="btn btn-outline-default" (click)="addNote()">
            <i class="fas fa-plus text-info mr-2"></i>
            Add Note</button> <!-- TODO Add translation -->
    </div>

</div>

<div class="row m-2">
    <div class="table-responsive">
        <table class="table table-striped ml-2 mr-2 table-hover">
            <thead>
                <tr>
                    <th scope="col-1">Date</th>
                    <th scope="col-2">Time</th>
                    <th scope="col-8">Notes</th>
                    <th scope="col-2">Staff</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let note of notes$|async; let i = index">
                    <td>
                        <ngb-highlight [result]="note.date" [highlightClass]="'search-highlight'">
                        </ngb-highlight>
                    </td>
                    <td>
                        <ngb-highlight [result]="note.time" [highlightClass]="'search-highlight'">
                        </ngb-highlight>
                    </td>
                    <td>
                        <ngb-highlight [result]="note.text" [highlightClass]="'search-highlight'">
                        </ngb-highlight>
                    </td>
                    <td>
                        <ngb-highlight [result]="note.staff" [highlightClass]="'search-highlight'">
                        </ngb-highlight>
                    </td>
                    <td><i style="cursor: pointer;" class="fa fa-trash" (click)="deleteNote(note)"></i>
                    <td>
                </tr>
            </tbody>
        </table>
    </div>
</div>