import { FloatDto, FloatType } from '../../pos-server.generated';
import { FormBuilder, Validators, AbstractControl } from '@angular/forms';
import { AppState } from 'src/app/reducers';
import { Store } from '@ngrx/store';
import { Component, OnInit } from '@angular/core';
import * as staffSelectors from '../../reducers/staff/staff.selectors';
import { Observable } from 'rxjs';
import * as receiptActions from '../../reducers/receipt-printing/receipt.actions'
import { ReceiptBatch, OpenCashDrawerAction } from 'src/app/printing/printing-definitions';
import { map, first } from 'rxjs/operators';
import { Router } from '@angular/router';
import { startWith } from 'rxjs/operators';
import * as endOfDayCashDrawerActions from '../../reducers/end-of-day/end-of-day-cash-drawer/end-of-day-cash-up.actions';
import { EventEmitter, Output } from '@angular/core';
import { storeEndFloatData } from '../../reducers/end-of-day/end-of-day-float/end-of-day-float.actions';
import * as openTillActions from "../../reducers/open-till/open-till.actions"
import Swal from 'sweetalert2';
import { EndOfDayClient } from '../../pos-server.generated';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationModalComponent } from '../../shared/components/confirmation-modal/confirmation-modal.component';
import * as staffActions from '../../reducers/staff/staff.actions';

@Component({
  selector: 'pos-end-of-day',
  templateUrl: './end-of-day-float.component.html',
  styleUrls: ['./end-of-day-float.component.scss']
})
export class EndOfDayFloatComponent implements OnInit {
  @Output() closeModal: EventEmitter<void> = new EventEmitter<void>();

  constructor(
    private store: Store<AppState>,
    private formBuilder: FormBuilder,
    private router: Router,
    private endOfDayClient: EndOfDayClient,
    private modalService: NgbModal
  ) { }

  total$: Observable<number>;

  public floatForm = this.formBuilder.group({
    Denomination100: [0, [Validators.min(0), Validators.required]],
    Denomination50: [0, [Validators.min(0), Validators.required]],
    Denomination20: [0, [Validators.min(0), Validators.required]],
    Denomination10: [0, [Validators.min(0), Validators.required]],
    Denomination5: [0, [Validators.min(0), Validators.required]],
    Denomination2: [0, [Validators.min(0), Validators.required]],
    Denomination1: [0, [Validators.min(0), Validators.required]],
    Denomination50c: [0, [Validators.min(0), Validators.required]],
    Denomination20c: [0, [Validators.min(0), Validators.required]],
    Denomination10c: [0, [Validators.min(0), Validators.required]],
    Denomination5c: [0, [Validators.min(0), Validators.required]]
  });

  ngOnInit() {
    // Load saved values from local storage if they exist
    this.loadFormValuesFromLocalStorage();

    this.total$ = this.floatForm.valueChanges.pipe(
      startWith(this.calculateTotal()),
      map(() => this.calculateTotal())
    );

    this.store.select(staffSelectors.selectStaffLoginDto)
      .pipe(first())
      .subscribe(staffLoginDto => {
        this.endOfDayClient.checkEndOfDayFloat().subscribe(
          exists => {
            if (exists) {
              const modalRef = this.modalService.open(ConfirmationModalComponent);
              modalRef.componentInstance.title = 'Warning';
              modalRef.componentInstance.message = 'A daily float has already been submitted for today. Would you like to delete it and continue?';
              modalRef.componentInstance.confirmButtonText = 'Delete and Continue';
              modalRef.componentInstance.cancelButtonText = 'Cancel';

              modalRef.result.then(
                (result) => {
                  if (result === 'confirm') {
                    this.endOfDayClient.deleteDailyFloat().subscribe(
                      () => {
                        console.log('Daily float deleted successfully');
                      },
                      error => {
                        console.error('Error deleting daily float:', error);
                        this.store.dispatch(staffActions.clearStaffLogin());
                        this.modalService.dismissAll();
                      }
                    );
                  } else {
                    this.store.dispatch(staffActions.clearStaffLogin());
                    this.modalService.dismissAll();
                  }
                },
                () => {
                  this.store.dispatch(staffActions.clearStaffLogin());
                  this.modalService.dismissAll();
                }
              );
            }
          }
        );
      });
  }

  private loadFormValuesFromLocalStorage(): void {
    const savedValues = localStorage.getItem('endOfDayFloatFormValues');
    if (savedValues) {
      try {
        const parsedValues = JSON.parse(savedValues);
        this.floatForm.patchValue(parsedValues);
        console.log('Loaded float form values from local storage');
      } catch (error) {
        console.error('Error loading float form values from local storage:', error);
        // Clear invalid data
        localStorage.removeItem('endOfDayFloatFormValues');
      }
    }
  }

  private saveFormValuesToLocalStorage(): void {
    const formValues = this.floatForm.value;
    localStorage.setItem('endOfDayFloatFormValues', JSON.stringify(formValues));
    console.log('Saved float form values to local storage');
  }

  calculateTotal(): number {
    return (
      this.floatForm.value.Denomination100 * 100 +
      this.floatForm.value.Denomination50 * 50 +
      this.floatForm.value.Denomination20 * 20 +
      this.floatForm.value.Denomination10 * 10 +
      this.floatForm.value.Denomination5 * 5 +
      this.floatForm.value.Denomination2 * 2 +
      this.floatForm.value.Denomination1 * 1 +
      this.floatForm.value.Denomination50c * 0.5 +
      this.floatForm.value.Denomination20c * 0.2 +
      this.floatForm.value.Denomination10c * 0.1 +
      this.floatForm.value.Denomination5c * 0.05
    );
  }

  static getDateForCSharp(): Date {
    return new Date();
  }

  public onSubmit(): void {
    // Save form values to local storage before proceeding
    this.saveFormValuesToLocalStorage();

    this.store.select(staffSelectors.selectStaffLoginDto)
      .pipe(first()) // fetch value once and complete
      .subscribe(staffLoginDto => {
        const dailyFloatPayload: FloatDto = {
          storeId: staffLoginDto.storeId,
          tillNo: staffLoginDto.tillId,
          transactionDate: EndOfDayFloatComponent.getDateForCSharp(),
          floatType: FloatType.PM,
          hundreds: this.floatForm.value.Denomination100,
          fifties: this.floatForm.value.Denomination50,
          twenties: this.floatForm.value.Denomination20,
          tens: this.floatForm.value.Denomination10,
          fives: this.floatForm.value.Denomination5,
          twos: this.floatForm.value.Denomination2,
          ones: this.floatForm.value.Denomination1,
          fiftyCents: this.floatForm.value.Denomination50c,
          twentyCents: this.floatForm.value.Denomination20c,
          tenCents: this.floatForm.value.Denomination10c,
          fiveCents: this.floatForm.value.Denomination5c
        };
        this.store.dispatch(storeEndFloatData({ payload: dailyFloatPayload }));
        const total = this.calculateTotal();
        this.store.dispatch(endOfDayCashDrawerActions.saveCashDrawerTotal({ total }));
        this.router.navigate(['/end-of-day-cash-drawer']);
      });
  }

  public openTill(): void {
    this.store.dispatch(receiptActions.executeBatch({
        payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
    }));
  }


  public fieldValidate(control: AbstractControl): boolean {
    // TODO handle errors
    return control.invalid;
  }
}
