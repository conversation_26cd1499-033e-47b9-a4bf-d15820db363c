import { createAction, props } from '@ngrx/store';
import { StockEnquiryGetAllColorDto, StockEnquiryRequestDto, StockEnquiryResultDto, StockItemDto } from 'src/app/pos-server.generated';

export const init = createAction('[StockEnquiry] Init');
export const enquiryQuery = createAction('[StockEnquiry] EnquiryQuery', props<{ stockItem: StockItemDto }>())
export const enquiryResponse = createAction('[StockEnquiry] Enquiry Response', props<{ payload: StockEnquiryResultDto }>());

export const enquiryColor = createAction('[StockEnquiry] EnquiryQuery Filter', props<{itemStock: StockEnquiryGetAllColorDto}>());
export const enquiryFilterResponse = createAction('[StockEnquiry] Enquiry Filter Response', props<{ payload: StockEnquiryResultDto }>());


//export const enquiryFilter = createAction('[StockEnquiry] FilterByColor', props<{filter: {filterBy: string[]}}>());


