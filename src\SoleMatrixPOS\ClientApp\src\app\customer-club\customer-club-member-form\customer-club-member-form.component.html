<form [formGroup]="customerClubNewMemberForm" autocapitalize="on" autocomplete="one-time-code">
    <div class="card mx-auto" style="margin-top: 1rem; margin-bottom: 1rem;">
        <div class="card-body">
            <div class="container">
                <div class="row">
                    <label class="col">
                        <div class="mb-2" [translate]="'customer-club.ClubNumber'">Club Number</div>
                        <input type="text" class="form-control ng-untouched ng-pristine ng-valid" [readonly]="true"
                            formControlName="ClubNumber">
                    </label>

                    <label class="col">
                        <div class="mb-2 text-danger" [translate]="'customer-club.Points'">Points</div>
                        <input type="number" class="form-control ng-untouched ng-pristine ng-valid" readonly
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('Points'))}"
                            formControlName="Points">
                        <div class="invalid-feedback" [translate]="'customer-club-error.PointsValidationMessage'"
                            *ngIf="fieldValidate(customerClubNewMemberForm.get('Points'))">
                            invalid
                        </div>
                    </label>

                    <div class="col-4"></div>
                </div>

                <div class="row">
                    <label class="col-2">
                        <div class="mb-2" [translate]="'customer-club.Title'">Title</div>
                        <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                            [ngClass]="{'is-invalid': customerClubNewMemberForm.get('Title').invalid}"
                            formControlName="Title">
                    </label>

                    <label class="col">
                        <div class="mb-2" [translate]="'customer-club.FirstName'">First Name</div>
                        <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('FirstName'))}"
                            formControlName="FirstName" autocomplete="one-time-code">
                        <div class="invalid-feedback" [translate]="'customer-club-error.NameValidationMessage'"
                            *ngIf="fieldValidate(customerClubNewMemberForm.get('FirstName'))">
                            invalid
                        </div>
                    </label>

                    <label class="col">
                        <div class="mb-2" [translate]="'customer-club.LastName'">Last Name</div>
                        <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('LastName'))}"
                            formControlName="LastName" autocomplete="one-time-code">
                        <div class="invalid-feedback" [translate]="'customer-club-error.NameValidationMessage'"
                            *ngIf="fieldValidate(customerClubNewMemberForm.get('LastName'))">
                            invalid
                        </div>
                    </label>

                    <label class="col-4">
                        <div class="mb-2" [translate]="'customer-club.CareOf'">Care of</div>
                        <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                            [ngClass]="{'is-invalid': customerClubNewMemberForm.get('CareOf').invalid}"
                            formControlName="CareOf">
                    </label>
                </div>

                <div class="row">
                    <label class="col">
                        <div class="mb-2" [translate]="'customer-club.Street'">Street</div>
                        <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('Street'))}"
                            formControlName="Street">
                        <div class="invalid-feedback" [translate]="'customer-club-error.StreetValidationMessage'"
                            *ngIf="fieldValidate(customerClubNewMemberForm.get('Street'))">
                            invalid
                        </div>
                    </label>

                    <label class="col">
                        <div class="mb-2" [translate]="'customer-club.Suburb'">Suburb</div>
                        <input type="text" class="form-control" formControlName="Suburb" id="Suburb"
                            [ngClass]="{'is-invalid': customerClubNewMemberForm.get('Suburb').invalid && customerClubNewMemberForm.get('Suburb').touched}"
                            autocomplete="one-time-code">
                        <!-- Suburb suggestions dropdown -->
                        <div class="suburb-suggestions" *ngIf="(suburbs$ | async)?.length > 0">
                            <div class="suburb-item" *ngFor="let suburb of suburbs$ | async"
                                (click)="onSuburbSelect(suburb)">
                                {{ suburb.suburb }}, {{ suburb.state }} {{ suburb.postcode }}
                            </div>
                        </div>
                        <div class="invalid-feedback" [translate]="'customer-club-error.SuburbValidationMessage'"
                            *ngIf="fieldValidate(customerClubNewMemberForm.get('Suburb'))">
                            invalid
                        </div>
                    </label>

                    <label class="col-2">
                        <div class="mb-2" [translate]="'customer-club.State'">State</div>
                        <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('State'))}"
                            formControlName="State">
                        <div class="invalid-feedback" [translate]="'customer-club-error.StateValidationMessage'"
                            *ngIf="fieldValidate(customerClubNewMemberForm.get('State'))">
                            invalid
                        </div>
                    </label>
                </div>

                <div class="row">
                    <label class="col">
                        <div class="mb-2" [translate]="'customer-club.Country'">Country</div>
                        <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('State'))}"
                            formControlName="Country">
                        <div class="invalid-feedback" [translate]="'customer-club-error.CountryValidationMessage'"
                            *ngIf="fieldValidate(customerClubNewMemberForm.get('Country'))">
                            invalid
                        </div>
                    </label>


                    <label class="col-2">
                        <div class="mb-2" [translate]="'customer-club.Postcode'">Postcode</div>
                        <input type="number" class="form-control ng-untouched ng-pristine ng-valid"
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('Postcode'))}"
                            formControlName="Postcode">
                        <div class="invalid-feedback" [translate]="'customer-club-error.PostcodeValidationMessage'"
                            *ngIf="fieldValidate(customerClubNewMemberForm.get('Postcode'))">
                            invalid
                        </div>
                    </label>

                    <div class="col-4"></div>

                </div>

                <div class="row">
                    <label class="col">
                        <div class="mb-2" [translate]="'customer-club.Email'">Email</div>
                        <input type="text" class="form-control"
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('Email')) || hasEmailOrMobileError()}"
                            formControlName="Email" autocomplete="one-time-code">
                        <div class="invalid-feedback" *ngIf="fieldValidate(customerClubNewMemberForm.get('Email'))">
                            Please enter a valid email address
                        </div>
                    </label>

                    <label class="col-3">
                        <div class="mb-2">Phone No.</div>
                        <input type="text" class="form-control"
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('Phone')) || hasEmailOrMobileError()}"
                            formControlName="Phone" autocomplete="one-time-code">
                        <div class="invalid-feedback" *ngIf="fieldValidate(customerClubNewMemberForm.get('Phone'))">
                            Please enter a valid phone number
                        </div>
                    </label>

                    <label class="col-3">
                        <div class="mb-2">Mobile</div>
                        <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('Mobile'))}"
                            formControlName="Mobile" autocomplete="one-time-code">
                        <div class="invalid-feedback" [translate]="'customer-club-error.PhoneValidationMessage'"
                            *ngIf="fieldValidate(customerClubNewMemberForm.get('Mobile'))">
                            invalid
                        </div>
                    </label>

                    <div class="col-12" *ngIf="hasEmailOrMobileError()">
                        <div class="text-danger">Either Email or Phone is required</div>
                    </div>
                </div>

                <div class="row">
                    <label class="col">
                        <div class="mb-2">
                            <i class="fas fa-birthday-cake mr-2 text-primary"></i>Birthday
                        </div>
                        <div class="row">
                            <div class="col">
                                <input type="number" class="form-control"
                                    [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('Bday'))}"
                                    formControlName="Bday" placeholder="Day">
                                <div class="invalid-feedback">Day must be between 0-31</div>
                            </div>
                            <div class="col">
                                <input type="number" class="form-control"
                                    [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('Bmth'))}"
                                    formControlName="Bmth" placeholder="Month">
                                <div class="invalid-feedback">Month must be between 0-12</div>
                            </div>
                            <div class="col">
                                <input type="number" class="form-control"
                                    [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('Byr'))}"
                                    formControlName="Byr" placeholder="Year">
                                <div class="invalid-feedback">Invalid year</div>
                            </div>
                        </div>
                    </label>

                    <label class="col-1">
                        <div class="mb-2" [translate]="'customer-club.NoMail'">No mail please</div>
                        <input type="checkbox" class="col mb-2 form-check-input" formControlName="NoMail">
                    </label>
                </div>

                <hr>
                <div class="row">
                    <h3 class="col" [translate]="'customer-club.VIP'">VIP</h3>
                    <div *ngIf="vipHidden">
                        <h5 class="col text-primary pr-4" style="text-align: right; cursor: pointer"
                            (click)="vipHidden = !vipHidden" [translate]="'customer-club.buttons.Show'">Show</h5>
                    </div>
                    <div *ngIf="!vipHidden">
                        <h5 class="col text-primary pr-4" style="text-align: right; cursor: pointer"
                            (click)="vipHidden = !vipHidden" [translate]="'customer-club.buttons.Hide'">Hide</h5>
                    </div>
                </div>
                <div class="row" [hidden]="vipHidden">
                    <label class="col-4">
                        <div class="mb-2" [translate]="'customer-club.BarNo'">Bar No.</div>
                        <input type="text" class="form-control"
                            [ngClass]="{'is-invalid': customerClubNewMemberForm.get('BarNo').errors?.barcodeExists || fieldValidate(customerClubNewMemberForm.get('BarNo'))}"
                            formControlName="BarNo">
                        <div class="invalid-feedback" *ngIf="customerClubNewMemberForm.get('BarNo').errors?.maxlength">
                            Maximum 13 characters allowed
                        </div>
                        <div class="invalid-feedback"
                            *ngIf="customerClubNewMemberForm.get('BarNo').errors?.barcodeExists">
                            This barcode is already in use
                        </div>
                    </label>
                    <label class="col-2">
                        <div class="mb-2" [translate]="'customer-club.Discount'">Discount %</div>
                        <input type="number" class="form-control"
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('Discount'))}"
                            formControlName="Discount">
                        <div class="invalid-feedback" *ngIf="fieldValidate(customerClubNewMemberForm.get('Discount'))">
                            Discount must be between 0 and 100
                        </div>
                    </label>
                    <label class="col">
                        <div class="mb-2" [translate]="'customer-club.Issued'">Issued</div>
                        <input type="text" class="form-control"
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('Issued'))}"
                            placeholder="dd/MM/yyyy" formControlName="Issued">
                        <div class="invalid-feedback" *ngIf="fieldValidate(customerClubNewMemberForm.get('Issued'))">
                            Date format must be dd/MM/yyyy
                        </div>
                    </label>
                    <label class="col">
                        <div class="mb-2" [translate]="'customer-club.Expires'">Expires</div>
                        <input type="text" class="form-control"
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('Expires'))}"
                            placeholder="dd/MM/yyyy" formControlName="Expires">
                        <div class="invalid-feedback" *ngIf="fieldValidate(customerClubNewMemberForm.get('Expires'))">
                            Date format must be dd/MM/yyyy
                        </div>
                    </label>
                </div>

                <hr>
                <div class="row">
                    <h3 class="col" [translate]="'customer-club.StaffDetails'">Staff Details</h3>
                    <div *ngIf="staffDetailsHidden">
                        <h5 class="col text-primary pr-4" style="text-align: right; cursor: pointer"
                            (click)="staffDetailsHidden = !staffDetailsHidden"
                            [translate]="'customer-club.buttons.Show'">
                            Show</h5>
                    </div>
                    <div *ngIf="!staffDetailsHidden">
                        <h5 class="col text-primary pr-4" style="text-align: right; cursor: pointer"
                            (click)="staffDetailsHidden = !staffDetailsHidden"
                            [translate]="'customer-club.buttons.Hide'">
                            Hide</h5>
                    </div>
                </div>
                <div class="row" [hidden]="staffDetailsHidden">
                    <label class="col">
                        <div class="mb-2" [translate]="'customer-club.Created'">Created</div>
                        <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                            formControlName="Created" [readonly]="true" [value]="_member.creationDate">
                    </label>
                    <label class="col">
                        <div class="mb-2" [translate]="'customer-club.Altered'">Altered</div>
                        <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                            formControlName="Altered" [readonly]="true" [value]="_member.lastAlterDate">
                    </label>
                    <label class="col">
                        <div class="mb-2" [translate]="'customer-club.FirstSale'">Last Sale</div>
                        <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                            formControlName="FirstSale" [readonly]="true" [value]="_member.firstSaleDate">
                    </label>
                    <label class="col">
                        <div class="mb-2" [translate]="'customer-club.LastSale'">First Sale</div>
                        <input type="text" class="form-control ng-untouched ng-pristine ng-valid"
                            formControlName="LastSale" [readonly]="true" [value]="_member.lastSaleDate">
                    </label>
                </div>

                <hr>
                <div class="row">
                    <h3 class="col">Other Details</h3>
                    <div *ngIf="otherDetailsHidden">
                        <h5 class="col text-primary pr-4" style="text-align: right; cursor: pointer"
                            (click)="otherDetailsHidden = !otherDetailsHidden"
                            [translate]="'customer-club.buttons.Show'">Show</h5>
                    </div>
                    <div *ngIf="!otherDetailsHidden">
                        <h5 class="col text-primary pr-4" style="text-align: right; cursor: pointer"
                            (click)="otherDetailsHidden = !otherDetailsHidden"
                            [translate]="'customer-club.buttons.Hide'">Hide</h5>
                    </div>
                </div>

                <div class="row" [hidden]="otherDetailsHidden">
                    <label class="col">
                        <div class="mb-2">Business Number</div>
                        <input type="text" class="form-control"
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('BusinessNumber'))}"
                            formControlName="BusinessNumber">
                        <div class="invalid-feedback">Maximum 25 characters allowed</div>
                    </label>

                    <label class="col">
                        <div class="mb-2">Licence Number</div>
                        <input type="text" class="form-control"
                            [ngClass]="{'is-invalid': fieldValidate(customerClubNewMemberForm.get('LicenceNo'))}"
                            formControlName="LicenceNo">
                        <div class="invalid-feedback">Maximum 12 characters allowed</div>
                    </label>
                </div>

                <div class="row" [hidden]="otherDetailsHidden">
                    <label class="col">
                        <div class="mb-2">Reference Type</div>
                        <input type="text" class="form-control" formControlName="RefType">
                    </label>

                    <label class="col">
                        <div class="mb-2">Family Number</div>
                        <input type="text" class="form-control" formControlName="FamilyNo">
                    </label>

                    <label class="col">
                        <div class="mb-2">Referrer Number</div>
                        <input type="text" class="form-control" formControlName="ReferrerNo">
                    </label>
                </div>

                <div class="row" [hidden]="otherDetailsHidden">
                    <label class="col">
                        <div class="mb-2">Relationship</div>
                        <input type="text" class="form-control" formControlName="Relationship">
                    </label>
                </div>

                <hr>
                <div class="row mt-5">

                    <div class="col-6">
                        <div><button class="btn btn-primary back-button" type="button" (click)="onCancel()"
                                [translate]="'customer-club.buttons.Back'">
                                <i class="fas fa-arrow-left mr-2"></i>Go Back to Member Search
                            </button>
                        </div>
                    </div>
                    <div class="col-6 text-right">
                        <div><button class="btn btn-outline-default" type="button" (click)="onSubmit()"
                                [translate]="'customer-club.buttons.Save'">
                                <i class="fas fa-lg fa-fw fa-check-circle text-success mr-2"></i>
                                Save Changes</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>