using MediatR;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Client;
using SoleMatrixPOS.Application.Client.Queries;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SoleMatrixPOS.Controllers
{
	[ApiController]
	[Route("api/[controller]")]
	public class ClientSubController : ControllerBase
	{
		private readonly IMediator _mediator;

		public ClientSubController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[HttpGet("search")]
		public async Task<ActionResult<IEnumerable<CustomerSuburbDto>>> SearchSuburbs([FromQuery] string search)
		{
			var query = new ClientSubSearchQuery { SuburbSearch = search };
			var result = await _mediator.Send(query);
			return Ok(result);
		}
	}
}
