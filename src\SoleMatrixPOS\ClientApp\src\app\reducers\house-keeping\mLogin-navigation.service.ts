import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { Store } from "@ngrx/store";
import { Observable } from "rxjs";
import { distinctUntilChanged } from "rxjs/operators";
import { AppState } from "..";
import { ManagerLoginState } from "./mLogin.reducers";
import { SysConfigState, UpdateConfigStatus } from "../sys-config/sys-config.reducers"; // Updated import
import { UpdateReceiptStatus } from "./updateReceipt/receipt.reducers";
import * as houseAction from './mLogin.actions';
import * as receiptAction from './updateReceipt/receipt.actions';
import * as sysConfigActions from '../sys-config/sys-config.actions'; // Updated import
import * as SysConfigSelectors from '../sys-config/sys-config.selectors'; // Import selectors


@Injectable({
	providedIn: 'root'
})
export class HouseKeepingStateNavigationService {

	managerStateChange$: Observable<ManagerLoginState>;
	receiptStateChange$: Observable<UpdateReceiptStatus>;
	updateConfigStateChange$: Observable<UpdateConfigStatus>;

	constructor(private store: Store<AppState>, private router: Router) {

		this.managerStateChange$ = store.select(s => s.manager.mLoginState)
			.pipe(distinctUntilChanged());

		this.receiptStateChange$ = store.select(s => s.receipt.updateRecStatus)
			.pipe(distinctUntilChanged());

		// Updated selector to use 'sysConfig' state
		this.updateConfigStateChange$ = this.store.select(SysConfigSelectors.selectUpdateStatus)
            .pipe(distinctUntilChanged());

		// Manager state change subscription remains the same
		this.managerStateChange$.subscribe(newState => {
			console.log("Manager login State: " + newState);

			if (newState === ManagerLoginState.Complete) {
				router.navigateByUrl('/house-keeping');
			}
			// Note can't do below this as it will send the site to login on every refresh
			// } else if (newState === ManagerLoginState.LoggedOut) {
			// 	router.navigateByUrl('/');
			// }
		});

		// Receipt state change subscription remains the same
		this.receiptStateChange$.subscribe(newState => {
			console.log("Receipt State: " + newState);

			if (newState === UpdateReceiptStatus.Success) {
				this.store.dispatch(houseAction.mLogout());
				this.store.dispatch(receiptAction.loggedout());
			}
		});

		// Update config state change subscription updated
		this.updateConfigStateChange$.subscribe(newState => {
			console.log("Config State: " + newState);

			if (newState === UpdateConfigStatus.Success) {
				this.store.dispatch(houseAction.mLogout());
				this.store.dispatch(sysConfigActions.resetUpdateStatus()); // Updated action
			}
		});
	}
}
