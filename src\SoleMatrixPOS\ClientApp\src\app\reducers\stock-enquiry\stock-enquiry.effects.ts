import { Injectable } from "@angular/core";
import { Actions, ofType, createEffect, act } from '@ngrx/effects';
import { Store } from "@ngrx/store";
import { EMPTY } from "rxjs";
import { mergeMap, map, catchError } from "rxjs/operators";
import { StockEnquiryClient } from "src/app/pos-server.generated";
import { AppState } from "..";
import * as StockEnquiryAction from './stock-enquiry.actions';


@Injectable()
export class StockEnquiryEffect {

    constructor(
        private store: Store<AppState>,
        private actions$: Actions,
        private stockSearchClient: StockEnquiryClient
    ) { }

    //get Stock enquiry from the api server
    getEnquiryQuery$ = createEffect(() => this.actions$.pipe(
        ofType(StockEnquiryAction.enquiryQuery),
        mergeMap(
            (action) => this.stockSearchClient.getStockEnquiry({ 
                styleCode: action.stockItem.styleCode,
                colourCode: action.stockItem.colourCode
            })
            .pipe(
                map(
                    stockEnquiryResult => StockEnquiryAction.enquiryResponse({payload: stockEnquiryResult}),
                    catchError(() => EMPTY)
                )
            ))));


    getEnquiryByColorQuery$ = createEffect(() => this.actions$.pipe(
        ofType(StockEnquiryAction.enquiryColor),
        mergeMap(
            (action) => this.stockSearchClient.getStockEnquiry({ 
                styleCode: action.itemStock.styleCode,
                colourCode: action.itemStock.colorCode
            })
            .pipe(
                map(
                    stockEnquiryResult => StockEnquiryAction.enquiryFilterResponse({payload: stockEnquiryResult}),
                    catchError(() => EMPTY)
                )
            ))));
}