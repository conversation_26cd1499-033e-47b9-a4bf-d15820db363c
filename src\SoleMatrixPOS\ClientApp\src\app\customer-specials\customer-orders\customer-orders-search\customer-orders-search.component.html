<div class="content-wrapper flex-grow-1 pt-10">
	<div class="container-fluid">
	  <div class="row text-align-center mt-2 mb-4">
		<i class="fas fa-lg fa-fw fa-shopping-cart text-danger mt-3 mr-2 ml-4 text-shadow"></i>
		<h3 class="mt-3 mb-0 mr-2 text-danger">Order Search By</h3>
  
		<div class="col-2 pr-0">
		  <select class="form-control form-control-special" [(ngModel)]="field" (change)="search()">
			<option value="FirstName">Full Name</option>
			<option value="OrderCode">Order Code</option>
			<option value="ClientCode">Client Code</option>
			<option value="OrderStaff">Order Staff</option>
		  </select>
		</div>
  
		<div class="col-3 pl-0">
		  <input type="text" class="form-control" [(ngModel)]="term" (keyup)="search()" autofocus />
		</div>
	  </div>
  
	  <div *ngIf="loading">
		<mat-spinner style="margin:0 auto;" mode="indeterminate"></mat-spinner>
	  </div>
  
	  <div class="row m-2" *ngIf="!loading">
		<div class="table-responsive">
		  <table class="table table-striped table-hover table-fit">
			<thead>
			  <tr>
				<th>Order Code</th>
				<th>Client Code</th>
				<th>Full Name</th>
				<th>Order Created</th>
				<th>Order Placed</th>
				<th>ETA</th>
				<th>Style</th>
				<th>Size</th>
				<th>Selling Price</th>
				<th>Quantity</th>
				<th>Cancel Order</th>
			  </tr>
			</thead>
			<tbody>
			  <ng-container *ngFor="let order of orders$ | async; let i = index">
				<!-- Main Order Row -->
				<tr (click)="selectOrder(order)"
					[ngClass]="{'selectedOrder': selectedOrder && order.orderHeader.orderCode === selectedOrder.orderHeader.orderCode}">
				  <td><ngb-highlight [result]="order.orderHeader.orderCode || 'N/A'" [term]="term" [highlightClass]="'search-highlight'"></ngb-highlight></td>
				  <td><ngb-highlight [result]="order.orderHeader.clientCode || 'N/A'" [term]="term" [highlightClass]="'search-highlight'"></ngb-highlight></td>
				  <td><ngb-highlight [result]="order.fullName || 'N/A'" [term]="term" [highlightClass]="'search-highlight'"></ngb-highlight></td>
				  <td>{{ order.orderHeader.orderCreated | date: 'dd/MM/yy' }}</td>
				  <td>{{ order.orderHeader.orderPlaced ? 'Yes' : 'No' }}</td>
				  <td>{{ order.orderHeader.eta | date: 'short' }}</td>
				  <td>{{ order.orderLines && order.orderLines.length > 0 ? order.orderLines[0].orderStyle.trim() : 'N/A' }}</td>
				  <td>{{ order.orderLines && order.orderLines.length > 0 ? order.orderLines[0].orderSize.trim() : 'N/A' }}</td>
				  <td>{{ order.orderLines && order.orderLines.length > 0 ? order.orderLines[0].sellingPrice : 'N/A' }}</td>
				  <td>{{ order.orderLines && order.orderLines.length > 0 ? order.orderLines[0].quantity : 'N/A' }}</td>
				  <td>
					<button
					class="btn btn-danger"
					(click)="cancelOrder(order); $event.stopPropagation()"
					[disabled]="loading || order.orderHeader.cancelled"
				  >
					Cancel Order
				  </button>				  
				  </td>
				</tr>
				<!-- Sub-Rows for Additional Order Lines -->
				<tr *ngFor="let line of order.orderLines.slice(1)">
				  <td></td>
				  <td></td>
				  <td></td>
				  <td></td>
				  <td></td>
				  <td></td>
				  <td>{{ line.orderStyle || 'N/A' }}</td>
				  <td>{{ line.orderSize || 'N/A' }}</td>
				  <td>{{ line.sellingPrice || 'N/A' }}</td>
				  <td>{{ line.quantity || 'N/A' }}</td>
				  <td></td>
				</tr>
			  </ng-container>
			</tbody>
		  </table>
		</div>
	  </div>
	</div>
  </div>
  