import * as TransferSelector from './transfer.selector';
import { state } from '../../test-state'

describe('TransferSelectors', () => {
  
  describe('getLocations', ()=> {

    it('should return the locations slice', () => {
      expect(TransferSelector.getLocations.projector(state)).toEqual(state.locations);
    });

  });

  describe('getTransfers', () => {

    it('should return the transfer slice', ()=> {
      expect(TransferSelector.getTransfers.projector(state)).toEqual(state.transfers);
    });

  });

});
