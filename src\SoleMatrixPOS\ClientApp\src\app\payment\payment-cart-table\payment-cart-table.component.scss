.table {

    th,
    td {

        // Set a consistent width for each column
        &:nth-child(1) {
            width: 25%;
        }

        // Items/StyleCode
        &:nth-child(2) {
            width: 20%;
        }

        // Colour
        &:nth-child(3) {
            width: 15%;
        }

        // Size
        &:nth-child(4) {
            width: 10%;
        }

        // Qty
        &:nth-child(5) {
            width: 15%;
        }

        // Price
        &:nth-child(6) {
            width: 15%;
        }

        // Total
    }

    // Right-align numeric columns
    td,
    th {

        &:nth-child(4),
        // Qty
        &:nth-child(5),
        // Price
        &:nth-child(6) {
            // Total
            text-align: right;
        }
    }
}

// Ensure consistent table layout
.table-responsive table {
    table-layout: fixed;
    width: 100%;
}

// Style for the scrollable body
#sale-table-body {
    .table {
        margin-bottom: 0;
    }
}

// Ensure footer values align with table columns
tfoot {
    td {
        white-space: nowrap;
    }
}