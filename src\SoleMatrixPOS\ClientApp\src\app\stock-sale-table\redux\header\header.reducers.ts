import * as HeaderType from './header.actions';
import { Header } from '../../classes/header.model';

const initialState: Header[] = []; 

export function HeaderReducer(state: Header[] = initialState, action: any) {

    // console.log("I am being called: " + action.type, action.payload)

    switch (action.type) {
        case HeaderType.LOAD_HEADER_REQUESTED: {
            // console.log("HeaderReducer: LOAD_HEADER_REQUESTED", action.type);
            return state;
        }
        case HeaderType.LOAD_HEADER: {
            // console.log("HeaderReducer: LOAD_HEADER", action);
            state = action.headers;
            // console.log("HeaderReducer: LOAD_HEADER", state);
            return state;
        }
        default: {
            // console.log("HeaderReducer: DEFAULT- recevied ", action.type);
            return state;
        }
    }
}