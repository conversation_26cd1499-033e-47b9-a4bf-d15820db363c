import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import * as StoreSelector from '../../../reducers/store-info/store-info.selectors';
import * as StaffSelector from '../../../reducers/staff/staff.selectors';
import { AppState } from 'src/app/reducers';
import { Store } from '@ngrx/store';
import { Observable, Subscription, timer } from 'rxjs';
import { StaffLoginDto, StoreDetailsDto } from 'src/app/pos-server.generated';
import { map, share } from 'rxjs/operators';
import { NavigationEvent } from '@ng-bootstrap/ng-bootstrap/datepicker/datepicker-view-model';

@Component({
  selector: 'pos-nav-header',
  templateUrl: './nav-header.component.html',
  styleUrls: ['./nav-header.component.scss']
})
export class NavHeaderComponent implements OnInit, OnDestroy {

  collapsed: boolean;
  storeInfo$: Observable<StoreDetailsDto>;
  staff$: Observable<StaffLoginDto>;
  time = new Date();
  rxTime = new Date();
  intervalId;
  subscription: Subscription;
  staffSubscription: Subscription;

   _pageName = '';
   currentRoute = '';

  @Input()
  set pageName(pageName: string){
    this._pageName = (pageName && pageName.trim()) || "<no name set>";
  }
  
  constructor(private router: Router, private store: Store<AppState>) { 
    this.router.events.subscribe(event => {
      if(event instanceof NavigationEnd){
        this.currentRoute = event.url
      }
    })
  }


  ngOnInit() {

    this.storeInfo$ = this.store.select(StoreSelector.storeInfo);
    // this.storeInfo$.subscribe(store => {
    //   console.log("Store info: " +JSON.stringify(store));
    // })

    this.staff$ = this.store.select(StaffSelector.selectStaffLoginDto);
    this.staffSubscription = this.staff$.subscribe();

    //Displaying time
    // this.intervalId = setInterval(() => {
    //   this.time = new Date();
    // },1000);

    //using Rxjs Timer
    this.subscription = timer(0, 1000)
    .pipe(
      map(() => new Date()),
      share()
    )
    .subscribe(time => {
      this.rxTime = time;
    });
  }

  ngOnDestroy(): void {
    clearInterval(this.intervalId);
    if(this.subscription){
      this.subscription.unsubscribe();
    }

    if (this.staffSubscription) { // Add this block
      this.staffSubscription.unsubscribe();
    }
  }


  goHome() {
		this.router.navigateByUrl('/home');
  }
}
