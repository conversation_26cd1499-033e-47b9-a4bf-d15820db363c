
<div class="footer-wrapper">
	<div class="container-fluid">
		<div class="row align-items-center">
			<div class="col-auto back-button">

                <button type="button" class="btn-hidden">
                    <div class="btn btn-lg btn-circle btn-default" (click)="backButtonClick()">
                        <i class="fas fa-caret-left fa-4x color-gradient-grey mr-2"></i>
                    </div>
                </button>
            </div>

			<div class="col-auto ml-auto">
                <div class="total-container">
                    <div class="total-items">
                        <h5 class="items-label">
                            TOTAL
                        </h5>
                        <h5 class="items-label">
                            UNITS
                        </h5>
                    </div>
                    <h1 class="total">
                        <span>
                            {{totalUnits$ | async }}
                        </span>
                    </h1>
                </div>
            </div>


			<div class="col-auto next-button">
                <button type="button" [disabled]="!enableProcessButton" class="btn-hidden" (click)="okToProcess()">
                    <div class="btn btn-lg btn-circle btn-default">
                        <i class="fas fa-caret-right fa-4x color-gradient-green ml-2"></i>
                    </div>
                    <h4 class="text-light">OK to Process</h4>
                </button>
            </div>

		</div>


	</div>

</div>
