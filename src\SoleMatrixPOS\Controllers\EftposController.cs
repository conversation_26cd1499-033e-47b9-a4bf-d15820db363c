using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SoleMatrixPOS.Application.HouseKeeping.Queries;
using Microsoft.AspNetCore.Authorization;
using SoleMatrixPOS.Domain.Models;
using SoleMatrixPOS.Application.HouseKeeping;
using SoleMatrixPOS.Application.Eftpos;
using SoleMatrixPOS.Application.ReceiptPrinting;
using System.Linq;
using AutoMapper;
using SoleMatrixPOS.Domain.Service;
using MediatR; 
using Microsoft.AspNetCore.Authentication.JwtBearer;
using SoleMatrixPOS.Application.Infrastructure;
using Org.BouncyCastle.Asn1.Ocsp;
using System.Threading;
using SoleMatrixPOS.Domain.RepositoryInterfaces;
using Microsoft.AspNetCore.DataProtection;
using System.Net.Sockets;
using SoleMatrixPOS.Domain.Interfaces;
using PostmarkDotNet.Model;
using System.Diagnostics;
using Serilog;

namespace SoleMatrixPOS.Controllers
{
	[ApiController]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[Route("api/[controller]")]
	public class EftposController : ControllerBase
	{
		private readonly IMediator _mediator;
		private readonly ILinklyService _linklyService;
		private readonly ILogger<EftposController> _logger;
		private readonly IMapper _mapper;
		private readonly ISuspendSaleRepository _suspendSaleRepository;
		private readonly ITyroService _tyroService;

		//added 02/02/2025
		private readonly StaffCodeContext _staffCodeContext;

		public EftposController(IMediator mediator, ILinklyService linklyService, ITyroService tyroService, ILogger<EftposController> logger, IMapper mapper, StaffCodeContext staffCodeContext, ISuspendSaleRepository suspendSaleRepository)
		{
			_linklyService = linklyService;
			_mediator = mediator;
			_logger = logger;
			_mapper = mapper;
			_staffCodeContext = staffCodeContext;
			_suspendSaleRepository = suspendSaleRepository;
			_tyroService = tyroService;
		}

		[Route("pairPINPad")]
		[HttpPost]
		public async Task<ActionResult<string>> PairPINPad([FromBody] EftPairPinPadDto dto)
		{
			var sysStatusQuery = new GetSysStatusQuery(_staffCodeContext.StoreDetailsDto.StoreId);
			var sysStatus = await _mediator.Send(sysStatusQuery);

			switch (sysStatus.IntegratedEFTProvider)
			{
				case "Linkly":
					try
					{
						var secret = await _linklyService.PairPINPadAsync(dto.username, dto.password, dto.pairCode, _staffCodeContext.StoreDetailsDto.StoreId);
						if (secret == null)
						{
							return StatusCode(500, "Failed to pair PIN pad.");
						}

						GetPINAuthDto newLinklyPinAuth = new GetPINAuthDto { StoreId = _staffCodeContext.StoreDetailsDto.StoreId, StoreCommonName = "StoreCommonName", PINPadNo = _staffCodeContext.PINPadNo, PINPadId = Guid.NewGuid().ToString(), PINPadCommonName = dto.pinPadCommonName, IntegratedEFTSecret = secret, TerminalId = "", MerchantId = "" };

						//Update pinpad secret
						var updatePINAuthQuery = new UpdatePINAuthQuery(newLinklyPinAuth);
						var updatePINAuth = await _mediator.Send(updatePINAuthQuery, HttpContext.RequestAborted);
						return Ok(secret);
					}
					catch (Exception ex)
					{
						_logger.LogError("Error pairing PIN pad: {Exception}", ex);
						return StatusCode(500, $"Error pairing PIN pad: {ex.Message}");
					}
				case "Tyro": // Send key through paircode, tid through pinpadId, mid through storeId.

					GetPINAuthDto newTyroPinAuth = new GetPINAuthDto { StoreId = _staffCodeContext.StoreDetailsDto.StoreId, StoreCommonName = "StoreCommonName", PINPadNo = _staffCodeContext.PINPadNo, PINPadId = Guid.NewGuid().ToString(), PINPadCommonName = dto.pinPadCommonName, IntegratedEFTSecret = dto.pairCode, TerminalId = dto.pinPadId, MerchantId = dto.storeId };
					//Update pinpad secret
					var updateTyroPINAuthQuery = new UpdatePINAuthQuery(newTyroPinAuth);
					var updateTyroPINAuth = await _mediator.Send(updateTyroPINAuthQuery, HttpContext.RequestAborted);
					return Ok();
				default:
					return StatusCode(422, "Error pairing PIN pad: EFTPOS provider not supported");
			}
		}

		//[Route("/getAuthToken")]
		//[HttpPost]
		//public async Task<IActionResult> GetAuthTokenAsync(string posId, string storeId)
		//{
		//	var sysStatusQuery = new GetSysStatusQuery(storeId);
		//	var sysStatus = await _getSysStatusQueryHandler.Handle(sysStatusQuery, HttpContext.RequestAborted);

		//	if (sysStatus.IntegratedEFTProvider != "Linkly")
		//	{
		//		return BadRequest("Linkly is not the configured EFT provider.");
		//	}

		//	try
		//	{
		//		var authToken = await _linklyService.GetAuthTokenAsync(secret, posId);
		//		if (string.IsNullOrEmpty(authToken.AuthToken))
		//		{
		//			return StatusCode(500, "Failed to retrieve auth token.");
		//		}

		//		return Ok(authToken);
		//	}
		//	catch (Exception ex)
		//	{
		//		_logger.LogError("Error retrieving auth token: {Exception}", ex);
		//		return StatusCode(500, $"Error retrieving auth token: {ex.Message}");
		//	}
		//}

		[Route("processPurchase")]
		[HttpPost]
		public async Task<ActionResult<LinklyPurchaseResponseDto>> ProcessPurchase([FromBody] LinklyPurchaseRequestDto requestDto)
		{
			var sysStatusQuery = new GetSysStatusQuery(_staffCodeContext.StoreDetailsDto.StoreId);
			var sysStatus = await _mediator.Send(sysStatusQuery);
			var pinAuthQuery = new GetPINAuthByStoreAndTillQuery(_staffCodeContext.StoreDetailsDto.StoreId, _staffCodeContext.PINPadNo);
			var pinAuthList = await _mediator.Send(pinAuthQuery, HttpContext.RequestAborted);
			var pinAuth = pinAuthList.FirstOrDefault();

			switch (sysStatus.IntegratedEFTProvider)
			{
				case "Linkly":
					try
					{
						var authToken = await _linklyService.GetAuthTokenAsync(secret: pinAuth.IntegratedEFTSecret, posId: pinAuth.PINPadId);
						// TODO save auth token, don't get everytime
						var response = await _linklyService.ProcessPurchaseAsync(
							requestDto.totalAmount,
							requestDto.tenderAmount,
							requestDto.totalEftAmount,
							requestDto.taxAmt,
							requestDto.discountAmt,
							requestDto.surchargeAmt,
							requestDto.transNo.ToString(),
							requestDto.currencyCode,
							requestDto.cutReceipt,
							requestDto.receiptAutoPrint,
							requestDto.operatorReference,
							requestDto.hasBarCodeScanner,
							requestDto.items,
							authToken.AuthToken,
							_staffCodeContext.StoreDetailsDto.StoreId,
							_staffCodeContext.PINPadNo
						);

						return Ok(new
						{
							SessionId = response.SessionId,
							Status = response.status
						});
					}
					catch (Exception ex)
					{
						_logger.LogError("Error processing purchase transaction: {Exception}", ex);
						return StatusCode(500, $"Error processing purchase transaction: {ex.Message}");
					}
				case "Tyro":
				// Something has gone horribly wrong
				default:
					return BadRequest("Integrated EFT is disabled or not configured correctly.");
			}
		}

		[Route("processRefund")]
		[HttpPost]
		public async Task<ActionResult<LinklyRefundResponseDto>> ProcessRefund([FromBody] LinklyRefundRequestDto requestDto)
		{
			var sysStatusQuery = new GetSysStatusQuery(_staffCodeContext.StoreDetailsDto.StoreId);
			var sysStatus = await _mediator.Send(sysStatusQuery);
			var pinAuthQuery = new GetPINAuthByStoreAndTillQuery(_staffCodeContext.StoreDetailsDto.StoreId, _staffCodeContext.PINPadNo);
			var pinAuthList = await _mediator.Send(pinAuthQuery, HttpContext.RequestAborted);
			var pinAuth = pinAuthList.FirstOrDefault();

			switch (sysStatus.IntegratedEFTProvider)
			{
				case "Linkly":
					try
					{
						var authToken = await _linklyService.GetAuthTokenAsync(secret: pinAuth.IntegratedEFTSecret, posId: pinAuth.PINPadId);

						var response = await _linklyService.ProcessRefundAsync(
												requestDto.amtRefund,
												requestDto.txnRef.ToString(),
												requestDto.currencyCode,
												requestDto.receiptAutoPrint,
												authToken.AuthToken,
												_staffCodeContext.StoreDetailsDto.StoreId,
												_staffCodeContext.PINPadNo);

						return Ok(new
						{
							SessionId = response.SessionId,
							Status = response.status
						});
					}
					catch (Exception ex)
					{
						_logger.LogError("Error processing refund transaction: {Exception}", ex);
						return StatusCode(500, $"Error processing refund transaction: {ex.Message}");
					}

				case "Tyro":
				// Something has gone horribly wrong
				default:
					return BadRequest("Integrated EFT is disabled or not configured correctly.");
			}
		}


		[Route("getReceipts/{intEft}/{transNo}/{historical}")]
		[HttpGet]
		public async Task<ActionResult<GetReceiptDto[]>> GetReceipts([FromRoute] int transNo, [FromRoute] string intEft, [FromRoute] bool historical = false)
		{
			var sysStatusQuery = new GetSysStatusQuery(_staffCodeContext.StoreDetailsDto.StoreId);
			var sysStatus = await _mediator.Send(sysStatusQuery);
			var pinAuthQuery = new GetPINAuthByStoreAndTillQuery(_staffCodeContext.StoreDetailsDto.StoreId, _staffCodeContext.PINPadNo);
			var pinAuthList = await _mediator.Send(pinAuthQuery, HttpContext.RequestAborted);
			var pinAuth = pinAuthList.FirstOrDefault();

			try
			{
				LinklySession[] sessions = await _linklyService.GetSessionsAsync(transNo);
				if (sessions == null || sessions.Length == 0)
					return Ok(Array.Empty<GetReceiptDto>());

				var receiptDtos = new List<GetReceiptDto>();

				foreach (var session in sessions)
				{
					var updatedSession = session;

					if (historical && string.IsNullOrEmpty(updatedSession.MerchReceipt))
					{
						// Get auth token
						var authToken = await _linklyService.GetAuthTokenAsync(
							secret: pinAuth.IntegratedEFTSecret,
							posId: pinAuth.PINPadId
						);

						// Call ReprintReceipt to update the receipt in the DB.
						await _linklyService.ReprintReceipt(
							authToken.AuthToken,
							_staffCodeContext.StoreDetailsDto.StoreId,
							_staffCodeContext.PINPadNo,
							updatedSession.TxnRef
						);

						// Update the session using its sessionId.
						updatedSession = await _linklyService.GetSessionByIdAsync(updatedSession.SessionId);

						// Wait until the MerchReceipt is updated (with a cancellation after 30 seconds).
						using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
						while (string.IsNullOrEmpty(updatedSession.MerchReceipt))
						{
							await Task.Delay(1000, cts.Token);
							updatedSession = await _linklyService.GetSessionByIdAsync(updatedSession.SessionId);
						}
					}

					// Map updated session to GetReceiptDto.
					receiptDtos.Add(new GetReceiptDto
					{
						merchReceipt = updatedSession.MerchReceipt,
						custReceipt = updatedSession.CustReceipt
					});
				}
				Console.WriteLine(receiptDtos);
				return Ok(receiptDtos.ToArray());
			}
			catch
			{
				throw new Exception("Integrated Eftpos not set up");
			}
		}


		[AllowAnonymous]
		[Route("linklyNotification/{sessionid}/{type}")]
		[HttpPost]
		public async Task<IActionResult> ReceiveNotification(
			[FromRoute] string sessionid,
			[FromRoute] string type,
			[FromBody] LinklyNotificationRequestDto notification
			//,[FromHeader(Name = "Authorization")] string authorizationHeader
			)
		{
			// Log incoming notification for debugging
			_logger.LogInformation("Received Linkly notification for Session ID: {sessionid}, Type: {Type}", sessionid, type);

			//// Validate the Authorization header if necessary
			//if (!ValidateAuthorizationHeader(authorizationHeader))
			//{
			//	_logger.LogWarning("Invalid Authorization Header.");
			//	return Unauthorized("Invalid Authorization Header.");
			//}



			// Validate notification data
			if (notification == null)
			{
				_logger.LogError("Invalid notification payload received.");
				return BadRequest("Invalid notification payload.");
			}

			var not = _mapper.Map<LinklyNotificationRequest>(notification);

			// Process notification based on type
			await _linklyService.ProcessNotificationAsync(sessionid, type, not);

			// Return a 200 OK to acknowledge receipt of notification
			return Ok();
		}

		private bool ValidateAuthorizationHeader(string authorizationHeader)
		{
			// Add logic to validate the authorization token, if provided
			return string.IsNullOrEmpty(authorizationHeader) || authorizationHeader.StartsWith("Bearer ");
		}



		[Route("sendKey/{intEft}/{transNo}/{key}")]
		[HttpPost]
		public async Task<ActionResult<bool>> SendKey([FromRoute] int transNo, [FromRoute] string intEft, [FromRoute] string key)
		{
			try
			{
				var sysStatusQuery = new GetSysStatusQuery(_staffCodeContext.StoreDetailsDto.StoreId);
				var sysStatus = await _mediator.Send(sysStatusQuery);
				var pinAuthQuery = new GetPINAuthByStoreAndTillQuery(_staffCodeContext.StoreDetailsDto.StoreId, _staffCodeContext.PINPadNo);
				var pinAuthList = await _mediator.Send(pinAuthQuery, HttpContext.RequestAborted);
				var pinAuth = pinAuthList.FirstOrDefault();

				switch (intEft.ToLower())
				{
					case "linkly":
						var authToken = await _linklyService.GetAuthTokenAsync(secret: pinAuth.IntegratedEFTSecret, posId: pinAuth.PINPadId);

						var session = (await _linklyService.GetSessionsAsync(transNo)).Last();
						if (session == null)
							return NotFound(new { Message = "Session not found" });
						var success = await _linklyService.SendKey(session.SessionId, authToken.AuthToken, key);
						if (success)
						{
							return Ok(true);
						}
						else
						{
							throw new Exception();
						}
					default:
						return BadRequest(new { Message = "Unsupported integrated EFT provider." });
				}
			}
			catch
			{
				return NotFound(new { });
			}
		}

		[Route("getLast/{intEft}")]
		[HttpPost]
		public async Task<ActionResult<GetLast>> GetLast([FromRoute] string intEft)
		{
			try
			{
				var sysStatusQuery = new GetSysStatusQuery(_staffCodeContext.StoreDetailsDto.StoreId);
				var sysStatus = await _mediator.Send(sysStatusQuery);
				var pinAuthQuery = new GetPINAuthByStoreAndTillQuery(_staffCodeContext.StoreDetailsDto.StoreId, _staffCodeContext.PINPadNo);
				var pinAuthList = await _mediator.Send(pinAuthQuery, HttpContext.RequestAborted);
				var pinAuth = pinAuthList.FirstOrDefault();

				switch (intEft.ToLower())
				{
					case "linkly":
						var authToken = await _linklyService.GetAuthTokenAsync(secret: pinAuth.IntegratedEFTSecret, posId: pinAuth.PINPadId);

						var (receipts, accepted, amount) = await _linklyService.GetLast(authToken.AuthToken, _staffCodeContext.StoreDetailsDto.StoreId, _staffCodeContext.PINPadNo);
						if (receipts != null)
						{
							var results = new GetLast
							{
								Receipts = receipts,

							};

							return Ok((receipts, accepted, amount));
						}
						else
						{
							return NotFound();
						}
					default:
						return BadRequest(new { Message = "Unsupported integrated EFT provider." });
				}
			}
			catch
			{
				return NotFound(new { });
			}
		}

		[Route("logon/{intEft}")]
		[HttpPost]
		public async Task<ActionResult<bool>> Logon([FromRoute] string intEft)
		{
			try
			{
				var sysStatusQuery = new GetSysStatusQuery(_staffCodeContext.StoreDetailsDto.StoreId);
				var sysStatus = await _mediator.Send(sysStatusQuery);
				var pinAuthQuery = new GetPINAuthByStoreAndTillQuery(_staffCodeContext.StoreDetailsDto.StoreId, _staffCodeContext.PINPadNo);
				var pinAuthList = await _mediator.Send(pinAuthQuery, HttpContext.RequestAborted);
				var pinAuth = pinAuthList.FirstOrDefault();

				switch (intEft.ToLower())
				{
					case "linkly":
						var authToken = await _linklyService.GetAuthTokenAsync(secret: pinAuth.IntegratedEFTSecret, posId: pinAuth.PINPadId);

						var success = await _linklyService.Logon(authToken.AuthToken, _staffCodeContext.StoreDetailsDto.StoreId, _staffCodeContext.PINPadNo);
						if (success)
						{
							return Ok(true);
						}
						else
						{
							throw new Exception();
						}
					default:
						return BadRequest(new { Message = "Unsupported integrated EFT provider." });
				}
			}
			catch
			{
				return NotFound(new { });
			}
		}

		[Route("transactionStatus/{transNo:int}")]
		[HttpGet]
		public async Task<ActionResult<TransactionStatusResponseDto>> GetTransactionStatus([FromRoute] int transNo, string integratedEFTProvider)
		{ // TODO JASON see if this works as it should given multi eft
			try
			{
				switch (integratedEFTProvider.ToLower())
				{
					case "linkly":
						// Call the service to get the session
						var session = (await _linklyService.GetSessionsAsync(transNo)).Last();

						if (session == null)
							return NotFound(new { Message = "Session not found" });

						// Return the formatted session data
						return Ok(new
						{
							session.SessionId,
							session.Status,
							session.CreatedAt
						});

					case "tyro":
						// Set the timeout duration to 3 minutes.
						TimeSpan timeout = TimeSpan.FromMinutes(1); // TODO set to 3
						Stopwatch stopwatch = Stopwatch.StartNew();

						while (stopwatch.Elapsed < timeout)
						{
							var tyroSession = (await _linklyService.GetSessionsAsync(transNo)).Last();

							// If the session status is no longer "initiated", return it.
							if ((tyroSession != null) && (tyroSession.Status != "initiated" && tyroSession.Status != "Initiating..."))
							{
								return Ok(new
								{
									tyroSession.SessionId,
									tyroSession.Status,
									tyroSession.CreatedAt,
									tyroSession.SurchargeAmount
								});
							}

							// Wait for before polling again.
							await Task.Delay(3000);
						}

						// After 3 minutes, return the latest session data (even if it's still "initiated")
						var finalSession = (await _linklyService.GetSessionsAsync(transNo)).Last();
						return Ok(new
						{
							finalSession.SessionId,
							finalSession.Status,
							finalSession.CreatedAt,
							finalSession.SurchargeAmount
						});

					default:
						return BadRequest(new { Message = "Unsupported EFT provider." });
				}
			}
			catch (Exception ex)
			{
				// Log and handle errors
				_logger.LogError(ex, "Error retrieving session data for session {TransNo}", transNo);
				return StatusCode(500, new { Message = ex.Message });
			}
		}

		[Route("getTyroAuth")]
		[HttpGet]
		public async Task<ActionResult<GetPINAuthDto>> GetTyroAuth()
		{
			try
			{
				var auth = await _tyroService.GetTyroAuth(_staffCodeContext.StoreDetailsDto.StoreId, _staffCodeContext.PINPadNo);
				return Ok(auth);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "Error retrieving Tyro Auth");
				return StatusCode(500, new { Message = ex.Message });
			}
		}

		[Route("saveTyroReceipt")]
		[HttpPost]
		public async Task<ActionResult> SaveTyroReceipt(string receipt, bool merchant, string transNo)
		{
			try
			{
				await _tyroService.SaveTyroReceipt(receipt, merchant, transNo);
				return Ok();
			}
			catch (Exception ex)
			{
				return StatusCode(500, new { Message = ex.Message });
			}
		}

		[Route("createTyroSession")]
		[HttpPost]
		public async Task<ActionResult<string>> CreateTyroSession(string transNo)
		{
			try
			{
				await _tyroService.CreateTyroSession(transNo);
			}
			catch (Exception ex)
			{
				return StatusCode(500, new { Message = ex.Message });

			}
			return Ok();
		}

		[Route("updateStatus")]
		[HttpPost]
		public async Task<ActionResult> UpdateStatus(string status, string transNo)
		{
			try
			{
				await _tyroService.UpdateStatus(status, transNo);
				return Ok();
			}
			catch (Exception ex)
			{
				return StatusCode(500, new { Message = ex.Message });
			}
		}

		[Route("updateSurcharge")]
		[HttpPost]
		public async Task<ActionResult> UpdateSurcharge(decimal surchargeAmount, string transNo)
		{
			try
			{
				await _tyroService.UpdateSurcharge(surchargeAmount, transNo);
				return Ok();
			}
			catch (Exception ex)
			{
				return StatusCode(500, new { Message = ex.Message });
			}
		}

		[Route("log")]
		[HttpPost]
		public async Task<ActionResult> Log(string message)
		{
			_logger.LogDebug(message);
			return Ok();
		}

	}
}
