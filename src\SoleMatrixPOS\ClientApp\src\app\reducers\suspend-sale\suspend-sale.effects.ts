import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { HttpClient } from "@angular/common/http";
import { catchError, map, mergeMap, tap } from "rxjs/operators";
import { of } from "rxjs";
import * as SuspendSaleActions from "./suspend-sale.actions";
import {
	SuspendSaleHdrDto,
	CartItemDto,
	SuspendSaleClient,
	SuspendTransToCartDto,
	SuspendSaleLineDto,
	SuspendSaleLoadDto,
	CustomerClubDto
} from "src/app/pos-server.generated";
import * as cartActions from "../sales/cart/cart.actions";
import * as customerClubSearchActions from "../customer-club/club-search/customer-club.actions"

@Injectable()
export class SuspendSaleEffects {
	constructor(
		private actions$: Actions,
		private client: SuspendSaleClient,
		private http: HttpClient
	) { }

	loadSuspendSaleHeaders$ = createEffect(() =>
		this.actions$.pipe(
			ofType(SuspendSaleActions.loadSuspendSaleHeaders),
			tap(() => console.log("loadSuspendSaleHeaders effect")),
			mergeMap(() =>
				// this.http
				// 	.get<SuspendSaleHdrDto[]>("api/suspendsale/headers")
				this.client.getSuspendSaleHeaders().pipe(
					map((headers) =>
						SuspendSaleActions.loadSuspendSaleHeadersSuccess({
							headers,
						})
					),
					catchError((error) =>
						of(
							SuspendSaleActions.loadSuspendSaleHeadersFailure({
								error,
							})
						)
					)
				)
			)
		)
	);

	deleteSuspendSale$ = createEffect(() =>
		this.actions$.pipe(
			ofType(SuspendSaleActions.deleteSuspendSale),
			mergeMap((action) =>
				this.http.delete(`api/suspendsale/${action.suspendNo}`).pipe(
					map(() =>
						SuspendSaleActions.deleteSuspendSaleSuccess({
							suspendNo: action.suspendNo,
						})
					),
					catchError((error) => {
						console.error('❌ SUSPEND SALE DELETION FAILED:', error);
						return of(
							SuspendSaleActions.deleteSuspendSaleFailure({
								error,
							})
						);
					})
				)
			)
		)
	);

	selectSuspendSale$ = createEffect(() =>
		this.actions$.pipe(
			ofType(SuspendSaleActions.selectSuspendSale),
			mergeMap(({ suspendNo }) =>
				this.client.getSuspendSaleLines(suspendNo).pipe(
					mergeMap((res: SuspendSaleLoadDto) => {
						let lines: SuspendTransToCartDto[] = res.lines;
						let customer: CustomerClubDto = res.client;
						if (!lines || lines.length === 0) {
							return of(SuspendSaleActions.selectSuspendSaleFailure({ 
								error: new Error('No lines found in response') 
							}));
						}
						const actions: any[] = [
							cartActions.init(),
							SuspendSaleActions.selectSuspendSaleSuccess({ lines }),
							
							...lines.map(line => cartActions.addSuspendedSaleItem({ 
								cartItem: line,
								reason: line.discountReason,
								reasonCode: line.discountCode
							}))
						];

						if (customer != null) actions.push(
							// Restore the customer club
							customerClubSearchActions.selectCustomerClubMember({ payload: res.client })
						);

						return actions;
					}),
					catchError((error) => {
						console.error('Error loading suspend sale:', error);
						return of(SuspendSaleActions.selectSuspendSaleFailure({ error }));
					})
				)
			)
		)
	);
}
