import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable()
export class LocalTimeInterceptor implements HttpInterceptor {
    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
        const now = new Date();
        const localTimeString = now.toISOString();
        const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

        const modifiedRequest = request.clone({
            headers: request.headers
                .set('ClientTime', localTimeString)
                .set('ClientTimezone', timeZone)
        });

        return next.handle(modifiedRequest);
    }
}