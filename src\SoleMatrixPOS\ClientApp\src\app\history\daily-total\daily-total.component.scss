.daily-table-font {
    font-size: 1.8rem;
}

.table-summary-font {
    font-size: 1.4rem;
}

th,td{
    width:auto;
    white-space: nowrap;
} 

.justify-self-end {
    justify-self: end;
}

::ng-deep {
    .p-datepicker {
      z-index: 9999 !important; // Very high z-index to appear above everything
    }
  }
  
  // Ensure the calendar position is correct
  :host {
    .position-relative {
      position: relative;
    }
    
    .position-absolute {
      position: absolute;
    }
  }

.calendar-container {
  position: relative;
  display: flex;
  align-items: center;
}

.calendar-toggle-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  z-index: 2;
  
  &:hover {
    color: var(--accent-color);
  }
  
  i {
    font-size: 1.2rem;
  }
}

.close-btn {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  transition: opacity 0.2s;
  
  &:hover {
    opacity: 0.7;
  }
}