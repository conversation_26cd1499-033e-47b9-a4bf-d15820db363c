import { Component , OnInit} from "@angular/core";
import { ActivatedRoute, Params, Router } from "@angular/router";

@Component({
	selector: "app-password-reset",
	templateUrl: "./password-reset.component.html",
	styleUrls: ["./password-reset.component.scss"],
})
export class PasswordResetComponent implements OnInit {
	id: string | null = null;
	token: string | null = null;
	exp: string | null = null;


	constructor(private route: ActivatedRoute, private router: Router) {}

	ngOnInit(){
		this.token = this.route.snapshot.queryParamMap.get('token')
		this.id = this.route.snapshot.queryParamMap.get('id')
		this.exp = this.route.snapshot.queryParamMap.get('exp')

		if(!this.token || !this.id || !this.exp ){
			this.router.navigateByUrl('/404')	
			return
		}

		const expDate = new Date(Number(this.exp) * 1000)

		if(isNaN(expDate.getTime()) || expDate.getTime() < new Date().getTime() ){
			this.router.navigateByUrl('/404')	
			return
		}
	}

}

