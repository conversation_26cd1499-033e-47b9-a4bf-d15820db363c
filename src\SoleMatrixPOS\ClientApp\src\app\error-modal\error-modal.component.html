<div class="modal-body text-center p-4">
    <div>
        <i *ngIf="!isCritical" class="fas fa-exclamation-triangle fa-3x text-warning"></i>
        <i *ngIf="isCritical" class="fas fa-times-circle    fa-3x text-danger"></i>
    </div>
    <div class="my-4">
        <strong>{{ description }}</strong>
    </div>
</div>
<div class="modal-footer justify-content-center">
    <button type="button" class="btn btn-info" (click)="onOk()">Okay</button>
    <button *ngIf="showConfirm"
            type="button"
            class="btn btn-danger"
            (click)="onConfirmClick()">
        {{ confirmButtonText }}
    </button>
</div>
