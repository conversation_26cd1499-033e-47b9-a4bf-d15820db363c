using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MediatR; 
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SoleMatrixPOS.Application.Enums;
using SoleMatrixPOS.Application.Infrastructure;
using SoleMatrixPOS.Application.Stock;
using SoleMatrixPOS.Application.Stock.Dtos;
using SoleMatrixPOS.Application.Stock.Queries;
using SoleMatrixPOS.Application.Stock.ReceiveStock.Queries;

namespace SoleMatrixPOS.Controllers
{
    [Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
    public class StockSearchController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<StockSearchController> _logger;
		private readonly StaffCodeContext _staffCodeContext;

        public StockSearchController(IMediator mediator, StaffCodeContext staffCodeContext)
        {
            _mediator = mediator;
			_staffCodeContext = staffCodeContext;
        }

        [HttpPost]
        public async Task<IEnumerable<StockItemDto>> Get([FromBody] StockSearchRequestDto stockSearchRequestDto)
        {

            return await _mediator.Send(new StockSearchQuery(stockSearchRequestDto));
        }

		[Route("StockEnquiry")]
		[HttpPost]
		public async Task<IEnumerable<StockItemDto>> GetNoSize([FromBody] StockSearchRequestDto stockSearchRequestDto)
		{

			return await _mediator.Send(new StockEnquirySearchQuery(stockSearchRequestDto));
		}


		[Route("BestPrice")]
		[HttpPost]
        public async Task<CartItemDto> GetCartItem([FromBody] BestPriceSearchRequestDto bestPriceSearchRequestDto)
        {
			var storeId = _staffCodeContext.StoreDetailsDto.StoreId;
			bestPriceSearchRequestDto.LocationCode = storeId;
			var a = await _mediator.Send(new BestPriceQuery(bestPriceSearchRequestDto));
			return a; 
        }

		[Route("GetItemByBarcode")]
		[HttpPost]
		public async Task<StockItemDto> GetItemByBarcode([FromBody] ItemBarcodeSearchRequestDto request, CancellationToken ct)
		{
			return await _mediator.Send(new ItemBarcodeSearchQuery(request), ct);
		}

	}
}
