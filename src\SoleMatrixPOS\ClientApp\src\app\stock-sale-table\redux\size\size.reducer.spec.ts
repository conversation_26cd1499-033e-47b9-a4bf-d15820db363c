import * as SizeSelector from './size.selector';
import { state } from '../../test-state';
import { SizeReducer } from './size.reducer';
import { Size } from './size.model';

describe('SizeReducer', () => {

  let initialState: Size[];

  beforeEach(()=>{
    initialState = SizeSelector.getSizes.projector(state);
  });

  describe('SizeType.LOAD_SIZES', ()=> {
    it('should add sizes to the state', ()=> {

      const action = {
        type: "[SIZE] load", 
        sizes: [
            {size: "1"},
            {size: "2"},
            {size: "3"},
            {size: "4"},
        ]
      };

      const newState = SizeReducer(initialState, action);

      expect(newState).toEqual([
        {size: "1"},
        {size: "2"},
        {size: "3"},
        {size: "4"},
      ]);
    });
  });
});