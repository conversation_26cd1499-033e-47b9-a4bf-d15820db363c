import { Component, } from '@angular/core';
import { TotalAmountService } from './total-amount.service';
import { Observable, of } from 'rxjs';
import { Location } from '../classes/location.model';
import { Total } from '../classes/total';

@Component({
  selector: 'app-total-amount',
  templateUrl: './total-amount.component.html',
  styleUrls: ['./total-amount.component.scss']
})
export class TotalAmountComponent {

  constructor(public totalAmountService: TotalAmountService) { 
  }

  
 

}
