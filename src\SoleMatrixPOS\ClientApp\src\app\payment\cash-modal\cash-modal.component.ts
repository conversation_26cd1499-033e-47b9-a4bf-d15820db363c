import { Component, Input, OnInit } from "@angular/core";
import { AbstractControl, FormBuilder, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { Payment, PaymentType } from "../payment.service";
import { financialRound } from "src/app/utility/math-helpers";

@Component({
	selector: "pos-cash-modal",
	templateUrl: "./cash-modal.component.html",
	styleUrls: ["./cash-modal.component.scss"],
})
export class CashModalComponent implements OnInit {
	@Input() amountDue: number;
	public form;
	private firstClick: boolean = true;

	get amount() {
		return this.form.get("Amount");
	}

	constructor(
		public activeModal: NgbActiveModal,
		private formBuilder: FormBuilder
	) {}

	ngOnInit() {
		this.form = this.formBuilder.group({
			Amount: [
				this.amountDue,
				[
					Validators.required,
					Validators.min(0.01),
					Validators.pattern(/^\d*[.]{0,1}\d{0,2}$/),
				],
			],
		});
	}

	dismiss(reason: string) {
		this.activeModal.dismiss(reason);
	}

	apply() {
		console.log(this.amount);
		if (this.form.valid) {
			// Set the payment type directly to PaymentType.Cash
			this.activeModal.close({
				type: PaymentType.Cash,
				amount: +this.amount.value,
			} as Payment);
		} else {
			this.form.markAsPristine();
		}
	}

	useRemainder() {
		this.amount.setValue(this.amountDue);
	}

	setAmount(value: number) {
		if (this.firstClick) {
			this.amount.setValue(financialRound(value).toFixed(2));
			this.firstClick = false;
		} else {
			const currentAmount = Number(this.amount.value) || 0;
			this.amount.setValue(financialRound(currentAmount + value).toFixed(2));
		}
	}

	clearAmount() {
		this.amount.setValue(null);
	}

	public fieldValidate(control: AbstractControl): boolean {
		return control.invalid;
	}
}
