import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { StockItemDto } from '../pos-server.generated';
import { HttpClient } from '@angular/common/http';
import * as cartActions from '../reducers/sales/cart/cart.actions';
import { Store } from '@ngrx/store';
import { AppState } from '../reducers';
import { DiscountConfirmModalComponent } from '../discount-confirm-modal/discount-confirm-modal.component';
interface DiscountType {
    discountCode: string;
    discountReason: string;
    maxDiscPercent: number;
    visible: string;
}

@Component({
    selector: 'pos-transaction-line-adjust-modal',
    templateUrl: './transaction-line-adjust-modal.component.html',
    styleUrls: ['./transaction-line-adjust-modal.component.scss']
})
export class TransactionLineAdjustModalComponent implements OnInit {
    @Input() stockItem: StockItemDto;
    @Input() quantity: number;
    @Input() originalPrice: number;
    @Input() discountReason: string;
    @Input() discountPercent: number;
    @Input() discountAmount: number;
    @Input() currentPrice: number;
    @Input() isRefundAdjust: boolean = false;

    Math = Math;

    adjustForm: FormGroup;
    discountTypes: DiscountType[] = [];
    selectedDiscountType: DiscountType | null = null;
    public formValid = false;

    constructor(
        private activeModal: NgbActiveModal,
        private modalService: NgbModal,
        private fb: FormBuilder,
        private http: HttpClient,
        private store: Store<AppState>
    ) { }

    ngOnInit() {
        this.originalPrice = this.originalPrice || this.stockItem.price;
        this.currentPrice = this.currentPrice || this.originalPrice;
        this.discountAmount = this.discountAmount || 0;
        this.discountPercent = this.discountPercent || 0;
        this.adjustForm = this.fb.group({
            reason: [null],
            discountPercent: [this.discountPercent],
            discountAmount: [this.discountAmount],
            price: [this.currentPrice, [Validators.required, Validators.min(0)]],
            quantity: [this.quantity, [Validators.required,
            this.isRefundAdjust ? Validators.max(-1) : Validators.min(1)
            ]]
        });

        // Add value changes subscription for reason
        this.adjustForm.get('reason').valueChanges.subscribe((selectedDiscount: DiscountType) => {
            if (selectedDiscount) {
                this.selectedDiscountType = selectedDiscount;

                // Update max discount percent if needed
                const maxPercent = selectedDiscount.maxDiscPercent;
                this.adjustForm.get('discountPercent').setValidators([
                    Validators.required,
                    Validators.min(0),
                    Validators.max(maxPercent)
                ]);
                this.adjustForm.get('discountPercent').updateValueAndValidity({ emitEvent: false });
            }
        });

        // Subscribe to discount percent changes
        this.adjustForm.get('discountPercent').valueChanges.subscribe(percent => {
            const selectedDiscount = this.selectedDiscountType;
            const maxAllowedPercent = selectedDiscount ? selectedDiscount.maxDiscPercent : 100;
            // Mark as touched immediately when value changes
            this.adjustForm.get('discountPercent').markAsTouched();
            if (percent >= 0 && percent <= maxAllowedPercent) {
                const newPrice = Number((this.originalPrice * (1 - (percent / 100))).toFixed(2));
                const discountAmt = Number((this.originalPrice - newPrice).toFixed(2));

                this.adjustForm.patchValue({
                    price: newPrice,
                    discountAmount: discountAmt
                }, { emitEvent: false });

                this.updateReasonValidation();
            }
        });

        // Subscribe to discount amount changes
        this.adjustForm.get('discountAmount').valueChanges.subscribe(amount => {
            if (amount >= 0 && amount != this.originalPrice) {
                const newPrice = Number((this.originalPrice - amount).toFixed(2));
                const percent = Number(((amount / this.originalPrice) * 100).toFixed(2));

                this.adjustForm.patchValue({
                    price: newPrice,
                    discountPercent: percent
                }, { emitEvent: false });

                this.updateReasonValidation();
            }
        });

        // Subscribe to price changes
        this.adjustForm.get('price').valueChanges.subscribe(price => {
            if (price > 0) {
                const discountAmt = Number((this.originalPrice - price).toFixed(2));
                const percent = Number(((discountAmt / this.originalPrice) * 100).toFixed(2));

                this.adjustForm.patchValue({
                    discountAmount: discountAmt,
                    discountPercent: percent
                }, { emitEvent: false });

                this.updateReasonValidation();
            }
        });

        // Load discount types and set initial selected type
        this.loadDiscountTypes().then(() => {
            if (this.discountReason) {
                const matchingDiscount = this.discountTypes.find(d => d.discountReason === this.discountReason);
                if (matchingDiscount) {
                    this.adjustForm.patchValue({ reason: matchingDiscount });
                }
            }
        });

        // Subscribe to form changes to update validity
        this.adjustForm.statusChanges.subscribe(() => {
            this.formValid = this.calculateFormValidity();
        });
    }

    private async loadDiscountTypes() {
        try {
            this.discountTypes = await this.http.get<DiscountType[]>('/api/Discounts/GetDiscountTypes')
                .toPromise();
            console.log('Available discount types:', this.discountTypes);

            if (this.discountReason) {
                this.selectedDiscountType = this.discountTypes.find(d => d.discountReason === this.discountReason);
                console.log('Selected discount type from reason:', this.selectedDiscountType);
            } 
            else if (this.discountTypes && this.discountTypes.length > 0) {
                this.selectedDiscountType = this.discountTypes[0];
                this.adjustForm.patchValue({ reason: this.selectedDiscountType });
            }
        } catch (error) {
            console.error('Error loading discount types:', error);
        }
    }

    resetPrice() {
        this.adjustForm.patchValue({
            price: this.originalPrice,
            discountPercent: 0,
            discountAmount: 0
        });
    }

    public cancel() {
        this.activeModal.close(false);
    }

    public save() {
        if (this.isFormValid()) {
            const selectedDiscount = this.adjustForm.get('reason').value as DiscountType;
            const price = this.adjustForm.get('price').value;
            const rawQuantity = this.adjustForm.get('quantity').value;
            // Ensure quantity is negative for refunds
            const quantity = this.isRefundAdjust 
                ? -Math.abs(rawQuantity) 
                : rawQuantity;
            const result = {
                price: price,
                quantity: quantity,
                originalPrice: this.originalPrice,
                reason: price !== this.originalPrice ? (selectedDiscount ? selectedDiscount.discountReason : '') : '',
                discountPercent: this.adjustForm.get('discountPercent').value,
                discountCode: price !== this.originalPrice ? (selectedDiscount ? selectedDiscount.discountCode : '') : ''
            };
            this.activeModal.close(result);
        }
    }

    public dismiss() {
        this.activeModal.dismiss();
    }

    isPriceModified(): boolean {
        return this.adjustForm.get('price').value !== this.originalPrice;
    }

    getRemainingCharacters(): number {
        const selectedDiscount = this.adjustForm.get('reason').value as DiscountType;
        const reason = selectedDiscount ? selectedDiscount.discountReason : '';
        return 45 - (reason ? reason.length : 0);
    }

    isFormValid(): boolean {
        const form = this.adjustForm;
        const price = form.get('price').value;
        const selectedDiscount = form.get('reason').value as DiscountType;

        if (price !== this.originalPrice) {
            return form.get('quantity').valid && 
                   form.get('price').valid && 
                   selectedDiscount != null;
        }

        return form.get('quantity').valid && form.get('price').valid;
    }

    getFinalTotal(): number {
        const price = this.adjustForm.get('price').value;
        const quantity = this.adjustForm.get('quantity').value;
        return Math.round((price * quantity) * 100) / 100;
    }

    getOriginalTotal(): number {
        return Math.round((this.originalPrice * this.adjustForm.get('quantity').value) * 100) / 100;
    }

    getTotalSavings(): number {
        return Math.round((this.getOriginalTotal() - this.getFinalTotal()) * 100) / 100;
    }

    async confirmApplyToAll(type: 'percent' | 'amount') {
        const value = type === 'percent' 
            ? this.adjustForm.get('discountPercent').value 
            : this.adjustForm.get('discountAmount').value;

        const selectedDiscount = this.adjustForm.get('reason').value as DiscountType;
        if (!selectedDiscount) {
            return;
        }

        const confirmModal = this.modalService.open(DiscountConfirmModalComponent);
        confirmModal.componentInstance.discountType = type;
        confirmModal.componentInstance.value = value;
        confirmModal.componentInstance.reason = selectedDiscount.discountReason;

        try {
            const result = await confirmModal.result;
            if (result) {
                if (type === 'percent') {
                    this.store.dispatch(cartActions.applyDiscountToAll({
                        discountPercent: value,
                        reason: selectedDiscount.discountReason,
                        discountCode: selectedDiscount.discountCode
                    }));
                } else {
                    this.store.dispatch(cartActions.applyAmountToAll({
                        discountAmount: value,
                        reason: selectedDiscount.discountReason,
                        discountCode: selectedDiscount.discountCode
                    }));
                }
                this.activeModal.close();
            }
        } catch {
        }
    }

    private updateReasonValidation() {
        const price = this.adjustForm.get('price').value;
        const reasonControl = this.adjustForm.get('reason');

        if (price !== this.originalPrice) {
            reasonControl.setValidators([Validators.required]);
        } else {
            reasonControl.clearValidators();
        }
        reasonControl.updateValueAndValidity({ emitEvent: false });
    }

    private calculateFormValidity(): boolean {
        const form = this.adjustForm;
        const price = form.get('price').value;
        const quantity = form.get('quantity').value;
        const selectedDiscount = form.get('reason').value as DiscountType;

        const isQuantityValid = this.isRefundAdjust ? 
            quantity <= -1 :  // For refunds, quantity must be negative
            quantity >= 1;   // For normal adjustments, quantity must be positive

        const isDiscountValid = price > this.originalPrice || this.isDiscountValid;

        if (price !== this.originalPrice) {
            return isQuantityValid && 
                form.get('price').valid && 
                (price > this.originalPrice || selectedDiscount != null) &&
                isDiscountValid;
        }

        return isQuantityValid && form.get('price').valid;
    }

    get isDiscountValid(): boolean {
        const discountControl = this.adjustForm.get('discountPercent');
        // Only check discount validation if it's actually a discount as opposed to a price increase
        if (this.adjustForm.get('price').value <= this.originalPrice) {
            return discountControl.valid && !(discountControl.errors && discountControl.errors['max']);
        }
        return true;
    }

    // Add getter for absolute quantity display
    get displayQuantity(): number {
        return Math.abs(this.adjustForm.get('quantity').value);
    }

    // Add getter for modal title
    get modalTitle(): string {
        return this.isRefundAdjust ? 'Adjust' : 'Discount';
    }

    adjustQuantity(change: number): void {
        const currentQty = this.adjustForm.get('quantity').value;
        const newQty = currentQty + change;
        if (this.isRefundAdjust) {
            // For refunds, ensure quantity stays negative and doesn't go above -1
            if (newQty <= -1) {
                this.adjustForm.patchValue({ quantity: newQty });
            }
        } else {
            // Original behavior for normal adjustments
            if (newQty >= 1) {
                this.adjustForm.patchValue({ quantity: newQty });
            }
        }
    }

    // Add a dedicated method for absolute value
    getAbsoluteValue(value: number): number {
        return Math.abs(value || 0);
    }

    onQuantityChange(value: string) {
        const numValue = parseFloat(value) || 0;
        const adjustedValue = this.isRefundAdjust ? -Math.abs(numValue) : Math.abs(numValue);
        this.adjustForm.patchValue({ quantity: adjustedValue }, { emitEvent: false });
    }
}