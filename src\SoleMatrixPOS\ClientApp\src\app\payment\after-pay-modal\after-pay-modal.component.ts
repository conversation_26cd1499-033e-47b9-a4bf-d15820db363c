import { Component, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import { Payment, Transaction } from '../payment.service';

@Component({
  selector: 'pos-after-pay-modal',
  templateUrl: './after-pay-modal.component.html',
  styleUrls: ['./after-pay-modal.component.scss']
})
export class AfterPayModalComponent implements OnInit {

  @Input() type;
  @Input() amountDue: number;

  get amount() { return this.form.get('Amount'); }

  constructor(
    public activeModal: NgbActiveModal,
		private store: Store<AppState>,
    private formBuilder: FormBuilder
    ) { }

  public form = this.formBuilder.group({
      Amount: [undefined,[
        Validators.required,
        Validators.min(0.01),
        Validators.pattern(/^\d*[.]{0,1}\d{0,2}$/)
      ]]
  });

  ngOnInit() {

  }

  dismiss(reason: string) {
		this.activeModal.dismiss(reason);
	}

  apply() {
    console.log(this.amount);
    if(this.form.valid) {
      this.activeModal.close({ type: this.type, amount: +this.amount.value } as Payment);
    } else {
      this.form.markAsPristine();
    }
  }

  useRemainder() {
    this.amount.setValue(this.amountDue);
  }

  public fieldValidate(control: AbstractControl): boolean {
    return control.invalid;
  }
}