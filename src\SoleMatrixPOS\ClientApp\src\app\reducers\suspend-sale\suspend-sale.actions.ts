import { createAction, props } from "@ngrx/store";
import { SuspendSaleHdrDto, SuspendTransToCartDto } from "src/app/pos-server.generated";

export const loadSuspendSaleHeaders = createAction(
	"[Suspend Sale] Load Suspend Sale Headers"
);
export const loadSuspendSaleHeadersSuccess = createAction(
	"[Suspend Sale] Load Suspend Sale Headers Success",
	props<{ headers: SuspendSaleHdrDto[] }>()
);
export const loadSuspendSaleHeadersFailure = createAction(
	"[Suspend Sale] Load Suspend Sale Headers Failure",
	props<{ error: any }>()
);
export const deleteSuspendSale = createAction(
	"[Suspend Sale] Delete Suspend Sale",
	props<{ suspendNo: number }>()
);

export const deleteSuspendSaleSuccess = createAction(
	"[Suspend Sale] Delete Suspend Sale Success",
	props<{ suspendNo: number }>()
);

export const deleteSuspendSaleFailure = createAction(
	"[Suspend Sale] Delete Suspend Sale Failure",
	props<{ error: any }>()
);

export const selectSuspendSale = createAction(
	'[Suspend Sale] Select Suspend Sale',
	props<{ suspendNo: number }>()
);

export const selectSuspendSaleSuccess = createAction(
	'[Suspend Sale] Select Suspend Sale Success',
	props<{ lines: SuspendTransToCartDto[] }>()
);

export const selectSuspendSaleFailure = createAction(
	'[Suspend Sale] Select Suspend Sale Failure',
	props<{ error: any }>()
);
