import { Injectable } from '@angular/core';
import { Actions, Effect, ofType, createEffect } from '@ngrx/effects';
import { catchError, map, mergeMap, tap } from 'rxjs/operators';
import { FileResponse, LaybyClient, TransactionClient } from 'src/app/pos-server.generated';
import * as laybyActions from './layby.actions'
import { of } from 'rxjs';
import { from } from 'rxjs';
@Injectable()
export class LaybyEffects {
	constructor(
		private actions$: Actions,
		private client: LaybyClient
	) {}

	submitLayby$ = createEffect(() => this.actions$.pipe(
		ofType(laybyActions.submitLayby),
		tap(() => console.log("hmmm")),
		mergeMap(
			(action)=>this.client.addLayby(action.payload)
			.pipe(
				map(
					(response) => {
						return laybyActions.laybyCreated();
					}
				)
			)
		)
	));

	getLaybyNumber$ = createEffect(() =>
		this.actions$.pipe(
		  ofType(laybyActions.getLaybyNumber),
		  mergeMap(() =>
			this.client.getLaybyNo().pipe(
			  mergeMap((response: FileResponse) => {
				if (response.status === 200 || response.status === 206) {
				  return from(this.extractLaybyNo(response.data)).pipe(
					map((laybyNo) =>
					  laybyActions.getLaybyNumberSuccess({ payload: laybyNo })
					),
					catchError((error) => {
					  console.error('Extract Layby Number Error:', error);
					  return of(
						laybyActions.getLaybyNumberFailure({ error: 'Failed to extract layby number.' })
					  );
					})
				  );
				} else {
				  return of(
					laybyActions.getLaybyNumberFailure({
					  error: `Unexpected status code: ${response.status}`,
					})
				  );
				}
			  }),
			  catchError((error) => {
				console.error('Get Layby Number Error:', error);
				return of(
				  laybyActions.getLaybyNumberFailure({ error: error.message || 'Unknown error' })
				);
			  })
			)
		  )
		)
	  );
	
	// Helper method to extract laybyNo from Blob
	private extractLaybyNo(blob: Blob): Promise<string> {
	return new Promise<string>((resolve, reject) => {
		const reader = new FileReader();
		reader.onload = () => {
		const text = reader.result as string;
		const laybyNo = text;
		if (laybyNo) {
			resolve(laybyNo);
		} else {
			reject('Failed to parse layby number.');
		}
		};
		reader.onerror = () => {
		reject('Failed to read blob data.');
		};
		reader.readAsText(blob);
	});
	}
}