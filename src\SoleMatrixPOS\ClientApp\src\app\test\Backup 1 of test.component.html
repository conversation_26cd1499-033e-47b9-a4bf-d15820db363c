<nav class="navbar navbar-light navbar-expand-lg bg-light">
	<div class="container-fluid">
		<div class="navbar-brand" (click)="goHome()">
			<img src="assets/logo.svg" alt="Solemate Logo" />
		</div>
		<button class="navbar-toggler" type="button" (click)="collapsed = !collapsed"
			aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
			<span class="navbar-toggler-icon"></span>
		</button>

		<div class="navbar-collapse collapsed" [class.collapse]="collapsed" id="navbarSupportedContent">
			<ul class="navbar-nav ml-auto">
				<li class="nav-item">
					<span class="navbar-text">VIP</span>
					<span class="navbar-text">Store EC</span>
					<span class="navbar-text">Till 01</span>
					<span class="navbar-text">24/06/19</span>
					<span class="navbar-text">1:48:33PM</span>
				</li>

				<li class="nav-item active">
					<a class="nav-link" href="#">Support <span class="sr-only">(current)</span></a>
				</li>

			</ul>
		</div>
	</div>
</nav>
<div class="content-wrapper flex-grow-1">
	<div class="container-fluid">

		<div class="row text-align-center pb-3">

			<div class="col-3">
				<select class="form-control form-control-special" id="exampleFormControlSelect1">
					<option>Retail</option>
					<option>2</option>
					<option>3</option>
					<option>4</option>
					<option>5</option>
				</select>
			</div>
			<div class="col">
				<button type="button" class="btn btn-outline-default">
					<i class="fas fa-tag mr-2"></i>
					Special Order</button>
			</div>

			<div class="col-auto ml-auto">
				<button type="button" class="btn btn-outline-default">
					<i class="fas fa-barcode-alt text-info mr-2"></i>
					Barcode</button>
			</div>

			<div class="col-auto d-flex align-items-center">
				<button type="button" class="btn btn-link" (click)="openSearch()">
					<span class="fa-stack">
						<i class="fas fa-circle fa-stack-2x text-danger"></i>
						<i class="fas fa-search fa-stack-1x fa-inverse"></i>
					</span>
				</button>
			</div>

			<!-- Stock Sale Table -->
			<div class="col-auto d-flex align-items-center">
				<button type="button" class="btn btn-link" (click)="openStockSaleTable()">
					<span class="fa-stack">
						<i class="fas fa-circle fa-stack-2x text-danger"></i>
						<i class="fas fa-table fa-stack-1x fa-inverse"></i>
					</span>
				</button>
			</div>

		</div>
		<div class="row">
			<div class="table-responsive">
				<table class="table table-striped">
					<thead>
						<tr>
							<th scope="col">Style</th>
							<th scope="col">Description</th>
							<th scope="col">Colour</th>
							<th scope="col">Size</th>
							<th scope="col" style="width:100px;">Qty</th>
							<th scope="col">Value</th>
							<th scope="col">Price</th>
							<th scope="col"></th>
						</tr>
					</thead>
					<tbody>
						<tr *ngFor="let item of cart$ | async">
							<td (click)="removeItemOnDoubleClick(item.stockItem) ">{{item.stockItem.styleCode}}</td>
							<td (click)="removeItemOnDoubleClick(item.stockItem) ">{{item.stockItem.styleDescription}}
							</td>
							<td (click)="removeItemOnDoubleClick(item.stockItem) ">{{item.stockItem.colourName}}</td>
							<td (click)="removeItemOnDoubleClick(item.stockItem) ">{{item.stockItem.size}}</td>
							<td class="table-input">
								<input type="number" class="form-control" value="{{item.quantity}}"
									(blur)="setNumberOfItems(item.stockItem, $event.target.value)"
									(input)="setNumberOfItems(item.stockItem, $event.target.value)">
							</td>
							<td (click)="removeItemOnDoubleClick(item.stockItem) ">{{item.stockItem.price | currency}}
							</td>
							<td *ngIf="item.bestValue < item.stockItem.price"
								(click)="removeItemOnDoubleClick(item.stockItem) " class="text-warning">{{item.bestValue
								| currency}}
							</td>
							<td *ngIf="item.bestValue < item.stockItem.price" class="text-right">
								<a href="">
									<i class="fas fa-star text-warning"></i>
								</a>
							</td>

							<td *ngIf="item.bestValue == item.stockItem.price"
								(click)="removeItemOnDoubleClick(item.stockItem) ">{{item.bestValue | currency}}
							</td>
							<td *ngIf="item.bestValue == item.stockItem.price" class="text-right">
								<a href="">
									<i class="fas fa-star text-gray"></i>
								</a>
							</td>

						</tr>
					</tbody>
				</table>
			</div>
		</div>
		<p>
			This is a temporary test page used to demonstrate incomplete features, etc.
		</p>

		<h3>Test PDF Download</h3>
		<p>
			Click below to download a proof-of-concept, server-generated PDF.
		</p>
		<div><a href="/api/invoicepdf/test" class="btn btn-outline-default">Download Pdf</a></div>


	</div>

</div>
<div class="footer-wrapper">


	<div class="container-fluid">

		<div class="row align-items-center">

			<div class="col-auto back-button">

				<button type="button" class="btn-hidden" disabled>
					<div class="btn btn-lg btn-circle btn-default">
						<i class="fas fa-caret-left fa-4x color-gradient-grey mr-2"></i>
					</div>
				</button>


			</div>

			<div class="col-auto">

				<button *ngIf="selectedCustomerClubMember != null" type="button" (click)="launchCustomerClubModal()"
					class="btn-lg btn-default btn d-flex align-items-center">
					<i class="fas fa-crown fa-lg text-danger"></i>
					<h4 class="ml-3">{{selectedCustomerClubMember.firstname}}<br />
						{{selectedCustomerClubMember.surname}}</h4>
				</button>
				<button *ngIf="selectedCustomerClubMember == null" type="button" (click)="launchCustomerClubModal()"
					class="btn-lg btn-default btn d-flex align-items-center">
					<i class="fas fa-crown fa-lg"></i>
					<h4 class="ml-3">Customer<br />
						Club</h4>
				</button>

			</div>

			<div class="col-auto">

				<button type="button" class="btn-lg btn-default btn d-flex align-items-center">
					<i class="fas fa-watch fa-lg text-gray"></i>
					<h4 class="ml-3">Layby</h4>
				</button>

			</div>


			<div class="col-auto ml-auto">

				<div class="total-container">

					<div class="total-items">
						<h6 class="items-label">
							{{(cart$ | async).length}} items
						</h6>
						<h6 class="total-label">
							Total
						</h6>
					</div>
					<h1 class="total">
						<span *ngIf="total$">
							{{(total$ | async) | currency}}
						</span>
					</h1>

				</div>


			</div>

			<div class="col-auto next-button">

				<button type="button" class="btn-hidden" [routerLink]="['/payment']">
					<div class="btn btn-lg btn-circle btn-default">
						<i class="fas fa-caret-right fa-4x color-gradient-green ml-2"></i>
					</div>
					<h4 class="text-light">Payment</h4>
				</button>


			</div>

		</div>


	</div>

</div>