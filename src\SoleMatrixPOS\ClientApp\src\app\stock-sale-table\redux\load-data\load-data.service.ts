import { Injectable } from '@angular/core';
import { flatMap } from 'rxjs/operators';

// // Services
import { ApiResponseFactory } from '../../factories/api-response-factory';
import { MysqlApiService } from '../../services/mysql-api.service';

// Classes
import { Sale } from '../../classes/sale';
import { Size } from '../../classes/size';
import { Stock } from '../../classes/stock';
import { StockSale } from '../../classes/stock-sale';
import { Location } from '../location/location.model'
import { Observable, of } from 'rxjs';


// Ngrx
import { AppState } from '../../redux/app.store';
import { Store } from '@ngrx/store';
import * as LocationActions from '../location/location.action';
import * as ImmutableLocationActions from '../location/immutable-location.action';
import * as SizeActions from '../../redux/size/size.action';

@Injectable({
  providedIn: 'root'
})
export class LoadDataService {

  // presentation data
  private stockSale: StockSale[] = [];

  private locations: Location[] = [];
  private sizes: Size[] = [];
  private immutableLocations: Location[] = [];
  private costs: number[];

  private factory: ApiResponseFactory = new ApiResponseFactory();

  constructor(private api: MysqlApiService, private store: Store<AppState>) {
    this.getData();
  }

  /**
   * For storing Location data from ngrx store
   * Avoid using for anything other than effects
   * @returns {Observable<Location[]>}
   */
  public getCosts(): Observable<number[]> {
    return of(this.costs)
  }

  /**
   * For storing Location data from ngrx store
   * Avoid using for anything other than effects
   * @returns {Observable<Location[]>}
   */
  public getLocations(): Observable<Location[]> {
    return of(this.locations)
  }

  /**
   * For storing size data from ngrx store
   * Avoid using for anything other than effects
   * @returns {Observable<Size[]>}
   */
  public getSizes(): Observable<Size[]> {
    return of(this.sizes)
  }

  /**
   * For storing immutableLocation data from ngrx store
   * Avoid using for anything other than effects
   * @returns {Observable<Location[]>}
   */
  public getImmutableLocations(): Observable<Location[]> {
    return of(this.immutableLocations)
  }
  
///////////////
// API CALLS //
///////////////

  /**
   * The beginning of our API call chain
   * Some calls depend on other calls being
   * completed so this is a convenient way 
   * of ensuring everyhting gets finished
   * 
   * If a new method is to be added to the chain,
   * replace the current method and call it on 
   * completion of the new method.
   * 
   * NOTE: Might need to research pros/cons of this approach
   */
  private getData() {
    this.getStockSale();
  }

  /**
   * Get the stock and sales table from the api and 
   * assign it to our StockSales array
   */
  private getStockSale() {

    this.api
      .getStockSales()
      .subscribe({
        next: stockSale => {
          this.stockSale = this.factory.buildStockSale(stockSale);
        },
        error: err => {
          console.log("LoadDataService.getStockSale(): Trouble getting data in stock sales: " + err)
        },
        complete: () => {   // complete allows us to call other methods upon completion
          this.packShops();
        }
      })
  }

  /**
   * Pack shops makes an api call to get all locations and assigns
   * each response to a new object inside the shops array
   */
  private packShops() {
    this.api
      .getLocations()
      .pipe(
        flatMap((x) => { return x }), // Flat map is used to allow foreach traversal
      )
      .subscribe({
        next: loc => {
          this.locations.push(this.factory.buildLocation(loc, this.stockSale))
          this.immutableLocations.push(this.factory.buildLocation(loc, this.stockSale))
        },
        error: err => {
          console.log("LoadDataService.packShops(): Error in packing shops: " + err)
        },
        complete: () => {
          this.getSizesFromAPI(); // Now that the locations have been nicely ordered, assign their stock
        }
      })
  }

  /**
   * Get the stock table from the api and 
   * assign it to our Stock array
   */ 
  private getSizesFromAPI() {
    this.api
      .getSizes()
      .subscribe({
        next: sizes => {
          this.sizes = this.factory.buildSizes(sizes);
        },
        error: err => {
          console.log("LoadDataService.getSizesFromAPI(): Problem getting sizes: " + err)
        },
        complete: () => {
          this.cleanData();
        }
      });
  }

  ////////////////////
  // PREPARING DATA //
  ////////////////////

  /**
   * This is a wrapper for cleaning up any missing data
   * and making sure it's ordered and formatted correctly
   */
  private cleanData(){
 
    this.fillMissingData(this.locations, this.sizes);
    this.fillMissingData(this.immutableLocations, this.sizes);

    this.orderBySize(this.locations, this.sizes);
    this.orderBySize(this.immutableLocations, this.sizes);

    this.store.dispatch(new SizeActions.LoadSizesRequested());
    this.store.dispatch(new LocationActions.LoadLocationsRequested());
    this.store.dispatch(new ImmutableLocationActions.LoadImmutableLocationsRequested());

  }

  /**
   * This method finds any gaps in the data and fills it with blanl
   * eg no entry exists for shoe size '10',
   * it will create an entry in the array where size = 10 and qty = 0
   * 
   * @param {Location[]} Locations
   * @param {Size[]} sizes
   */
  private fillMissingData(Locations: Location[], sizes: Size[]) {

    Locations.forEach(Location => {           // Look at every store
      
      let stockSizes = [];
      Location.stock.forEach(stock => {   // Extract a list of stock sizes for this store
        stockSizes.push(stock.size);
      });
     
      let saleSizes = [];
      Location.sales.forEach(sale => {    // Extract a list of sale sizes for this store
        saleSizes.push(sale.size);
      });
 
      sizes.forEach(size => {                         // Identify any missing size entires
        if (!stockSizes.includes(size.size)) {
          Location.stock.push(new Stock(size.size, 0));   // Add this size to this store's stock array
        };
        if (!saleSizes.includes(size.size)) {         
          Location.sales.push(new Sale(size.size, 0));    // Add this size to this store's sales array
        };
      });
    });
  }

  /**
   * Ensures that each store has their stock and sales
   * ordered by size. This is neccessary for the current 
   * view implementation that relies on data being in 
   * order of size for accurate data representation
   * 
   * @param {Location[]} Locations 
   * @param {Size[]} sizes 
   */
  private orderBySize(Locations: Location[], sizes: Size[]){

    Locations.forEach(Location => {     // Look at every store

      let orderedStock = [];    // Make some temp arrays for the ordered stock/sales
      let orderedSales = [];

      for(let i=0; i < sizes.length; i++){  // Look at every size
        Location.stock.forEach((stock)=>{       
          if(stock.size === sizes[i].size){ // Find the size in the stock array
            orderedStock.push(stock);       // Append to temp array
          }
        });
        Location.sales.forEach((sales)=>{       
          if(sales.size === sizes[i].size){ // Find the size in the sales array
            orderedSales.push(sales);       // Append to temp array
          }
        });
      }

      Location.stock = orderedStock;
      Location.sales = orderedSales;
     
    });

  }

}
