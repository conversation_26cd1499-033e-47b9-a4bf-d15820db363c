.row {
    display: flex;
    justify-content: center;
    padding: 1.5rem;
    gap: 2rem;

    div {
        display: flex;
        align-items: center;
        gap: 1rem;

        label {
            font-size: 1.1rem;
            font-weight: 500;
            color: #333;
        }

        input {
            padding: 0.75rem 1rem;
            font-size: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            transition: all 0.3s ease;
            width: 200px;

            &:focus {
                outline: none;
                border-color: #2196f3;
                box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
            }
        }

        .btn-link {
            padding: 0.5rem;
            border: none;
            background: none;
            cursor: pointer;
            transition: transform 0.2s ease;

            &:focus {
                outline: none;
                box-shadow: none;
            }

            &::-moz-focus-inner {
                border: 0;
            }

            &:hover {
                transform: scale(1.1);
            }

            &:active {
                transform: scale(0.95);

                .fa-stack {
                    opacity: 0.8;
                }
            }

            .fa-stack {
                font-size: 2rem;
                transition: opacity 0.2s ease;

                .fa-circle {
                    color: #f44336;
                    opacity: 0.9;
                }

                .fa-inverse {
                    color: white;
                }
            }
        }
    }
}