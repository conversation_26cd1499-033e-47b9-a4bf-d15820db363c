<div class="container">
        <ng-container *ngIf="managerSys$| async let result">
                <div class="row justify-content-center bg-primary p-2 mb-3">
                        <h5 class="justify-content-center">Control Panel</h5>
                </div>
                <button type="button" class="btn btn-link" (click)="openPairing()">Integrated Eftpos Settings</button>
                <form #configForm="ngForm" (ngSubmit)="saveConfig(configForm.value)">
                        <div class="row justify-content-center mt-3 mb-3">
                                <!--control panel-->

                                <div class="col">
                                        <table class="table table-striped">
                                                <thead>
                                                        <tr>
                                                                <td></td>
                                                                <td>Yes</td>
                                                                <td>No</td>
                                                        </tr>
                                                </thead>
                                                <tbody>
                                                        <tr>
                                                                <td>Customer Accounts</td>
                                                                <td><input type="radio" id="cust-account-on"
                                                                                name="customer_Account" value="T"
                                                                                [ngModel]="result.customerAccount" />
                                                                </td>
                                                                <td><input type="radio" id="cust-account-off"
                                                                                name="customer_Account" value="F"
                                                                                [ngModel]="result.customerAccount" />
                                                                </td>
                                                        </tr>
                                                        <tr>
                                                                <td>Always Open Cash Drawer</td>
                                                                <td><input type="radio" id="always-openCashDrawer-on"
                                                                                name="always_Open_Cash_Drawer" value="T"
                                                                                [ngModel]="result.alwaysOpenCashDrawer" />
                                                                </td>
                                                                <td><input type="radio" id="always-openCashDrawer-off"
                                                                                name="always_Open_Cash_Drawer" value="F"
                                                                                [ngModel]="result.alwaysOpenCashDrawer" />
                                                                </td>
                                                        </tr>
                                                        <tr>
                                                                <td>Open Layby</td>
                                                                <td><input type="radio" id="open-layby-on"
                                                                                name="open_Layby" value="T"
                                                                                [ngModel]="result.openLayby" />
                                                                </td>
                                                                <td><input type="radio" id="open-layby-off"
                                                                                name="open_Layby" value="F"
                                                                                [ngModel]="result.openLayby" />
                                                                </td>
                                                        </tr>
                                                        <tr>
                                                                <td>Earn Points On All Sales</td>
                                                                <td><input type="radio" id="point-onAllSales-on"
                                                                                name="point_On_All_Sales" value="T"
                                                                                [ngModel]="result.pointOnAllSales" />
                                                                </td>
                                                                <td><input type="radio" id="point-onAllSales-off"
                                                                                name="point_On_All_Sales" value="F"
                                                                                [ngModel]="result.pointOnAllSales" />
                                                                </td>
                                                        </tr>
                                                        <tr>
                                                            <td>Basic Cash Up</td>
                                                            <td>
                                                                <input type="radio" id="basic-cashUp-on"
                                                                       name="basic_Cash_Up" value="T"
                                                                       [ngModel]="result.basicCashUp" />
                                                            </td>
                                                            <td>
                                                                <input type="radio" id="basic-cashUp-off"
                                                                       name="basic_Cash_Up" value="F"
                                                                       [ngModel]="result.basicCashUp" />
                                                            </td>
                                                        </tr>
                                                </tbody>
                                        </table>
                                </div>
                                <div class="col">
                                        <table class="table table-striped">
                                                <thead>
                                                        <tr>
                                                                <td></td>
                                                                <td>Yes</td>
                                                                <td>No</td>
                                                        </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>Integrated EFT Provider</td>
                                                        <td>
                                                            <ng-container *ngIf="managerSys$ | async as result">
                                                                <select id="integrated_EFT_Provider" name="integrated_Eft_Provider"
                                                                        [(ngModel)]="selectedEFTProvider" required>
                                                                    <option *ngFor="let provider of eftProviders" [value]="provider">{{ provider }}</option>
                                                                </select>
                                                            </ng-container>

                                                        </td>
                                                        <td></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Soft Credit Limit</td>
                                                        <td>
                                                            <input type="radio" id="soft-creditLimit-on"
                                                                   name="soft_Credit_Limit" value="T"
                                                                   [ngModel]="result.softCreditLimit" />
                                                        </td>
                                                        <td>
                                                            <input type="radio" id="soft-creditLimit-off"
                                                                   name="soft_Credit_Limit" value="F"
                                                                   [ngModel]="result.softCreditLimit" />
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>No Rounding</td>
                                                        <td>
                                                            <input type="radio" id="no-rounding-on"
                                                                   name="no_Rounding" value="T"
                                                                   [ngModel]="result.noRounding" />
                                                        </td>
                                                        <td>
                                                            <input type="radio" id="no-rounding-off"
                                                                   name="no_Rounding" value="F"
                                                                   [ngModel]="result.noRounding" />
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>Display weeks to pay off layby</td>
                                                        <td>
                                                            <input type="radio" id="num-weekLayBy-on"
                                                                   name="layby_Number_Of_Weeks" value="T"
                                                                   [ngModel]="result.laybyNumberOfWeeks" />
                                                        </td>
                                                        <td>
                                                            <input type="radio" id="num-weekLayBy-off"
                                                                   name="layby_Number_Of_Weeks" value="F"
                                                                   [ngModel]="result.laybyNumberOfWeeks" />
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>Customer Club Birthday</td>
                                                        <td>
                                                            <input type="radio" id="cust-clubBirthday-on"
                                                                   name="customer_Club_Birthday" value="T"
                                                                   [ngModel]="result.customerClubBirthday" />
                                                        </td>
                                                        <td>
                                                            <input type="radio" id="cust-clubBirthday-off"
                                                                   name="customer_Club_Birthday" value="F"
                                                                   [ngModel]="result.customerClubBirthday" />
                                                        </td>
                                                    </tr>
                                                </tbody>
                                        </table>
                                </div>
                        </div>
                        <div class="row justify-content-center mt-3 mb-3">
                                <div class="col">
                                        <table class="table table-striped">
                                                <tbody>
                                                        <tr>
                                                            <td>Dollars Spent to earn 1 Point</td>
                                                            <td><input type="text" id="points_Per_Dollar"
                                                                            name="points_Per_Dollar" value=""
                                                                            [ngModel]="result.pointPerDollar" />
                                                            </td>
                                                            <td></td>
                                                        </tr>
                                                        <tr>
                                                            <td>Dollars off for 1 point</td>
                                                            <td>{{result.dollarPerPoints}}</td>
                                                            <td></td>
                                                        </tr>
                                                        <tr>
                                                                <td>Auto Discount %</td>
                                                                <td><input type="number" id="auto_Disc" name="auto_Disc"
                                                                                value=""
                                                                                [ngModel]="result.automaticDiscount" />
                                                                </td>
                                                                <td></td>
                                                        </tr>
                                                        <tr>
                                                                <td>Layby Deposit %</td>
                                                                <td><input type="text" id="layby_deposit"
                                                                                name="layby_Deposit" value=""
                                                                                [ngModel]="result.laybyDeposit"
                                                                                
                                                                                posTwoDigitInput />
                                                                </td>
                                                                <td></td>
                                                        </tr>
                                                        <tr>
                                                            <td>Order Deposit %</td>
                                                            <td><input type="text" id="order_deposit"
                                                                            name="order_Deposit" value=""
                                                                            [ngModel]="result.orderDeposit"
                                                                            
                                                                            posTwoDigitInput />
                                                            </td>
                                                            <td></td>
                                                        </tr>
                                                        <tr>
                                                            <td>On Hold Deposit %</td>
                                                            <td><input type="text" id="on_hold_deposit"
                                                                            name="on_Hold_Deposit" value=""
                                                                            [ngModel]="result.onHoldDeposit"
                                                                            
                                                                            posTwoDigitInput />
                                                            </td>
                                                            <td></td>
                                                        </tr>
                                                </tbody>
                                        </table>
                                </div>
                        </div>
                        <div class="row justify-content-center mt-3 mb-3">
                                <button type="button" class="btn btn-secondary mb-2" (click)="toggleOtherOptions()">
                                        {{ isOtherOptionsVisible ? 'Hide' : 'Show' }} Other Options
                                </button>
                                <div [hidden]="!isOtherOptionsVisible" class="col-12">
                                        <div class="row">
                                                <div class="col">
                                                        <table class="table table-striped">
                                                                <thead>
                                                                        <tr>
                                                                                <td></td>
                                                                                <td>Yes</td>
                                                                                <td>No</td>
                                                                        </tr>
                                                                </thead>
                                                                <tbody>
                                                                        <tr>
                                                                                <td>Client Database</td>
                                                                                <td><input type="radio" id="client-database-on"
                                                                                                name="client_Database" value="T"
                                                                                                [ngModel]="result.clientDatabase" />
                                                                                </td>
                                                                                <td><input type="radio" id="client-database-off"
                                                                                                name="client_Database" value="F"
                                                                                                [ngModel]="result.clientDatabase" />
                                                                                </td>
                                                                        </tr>
                                                                        <tr>
                                                                                <td>Demo Question 1</td>
                                                                                <td><input type="radio" id="demo-q1-on" name="demo_Q1"
                                                                                                value="T" [ngModel]="result.demoQ1" />
                                                                                </td>
                                                                                <td><input type="radio" id="demo-q1-off" name="demo_Q1"
                                                                                                value="F" [ngModel]="result.demoQ1" />
                                                                                </td>
                                                                        </tr>
                                                                        <tr>
                                                                                <td>Demo Question 2</td>
                                                                                <td><input type="radio" id="demo-q2-on" name="demo_Q2"
                                                                                                value="T" [ngModel]="result.demoQ2" />
                                                                                </td>
                                                                                <td><input type="radio" id="demo-q2-off" name="demo_Q2"
                                                                                                value="F" [ngModel]="result.demoQ2" />
                                                                                </td>
                                                                        </tr>
                                                                        <tr>
                                                                                <td>Demo Question 3</td>
                                                                                <td><input type="radio" id="demo-q3-on" name="demo_Q3"
                                                                                                value="T" [ngModel]="result.demoQ3" />
                                                                                </td>
                                                                                <td><input type="radio" id="demo-q3-off" name="demo_Q3"
                                                                                                value="F" [ngModel]="result.demoQ3" />
                                                                                </td>
                                                                        </tr>
                                                                        <tr>
                                                                                <td>Demo Question 4</td>
                                                                                <td><input type="radio" id="demo-q4-on" name="demo_Q4"
                                                                                                value="T" [ngModel]="result.demoQ4" />
                                                                                </td>
                                                                                <td><input type="radio" id="demo-q4-off" name="demo_Q4"
                                                                                                value="F" [ngModel]="result.demoQ4" />
                                                                                </td>
                                                                        </tr>
                                                                        <tr>
                                                                                <td>Suspend Sale</td>
                                                                                <td><input type="radio" id="suspend-sale-on"
                                                                                                name="suspend_Sale" value="T"
                                                                                                [ngModel]="result.suspendSale" />
                                                                                </td>
                                                                                <td><input type="radio" id="suspend-sale-off"
                                                                                                name="suspend_Sale" value="F"
                                                                                                [ngModel]="result.suspendSale" />
                                                                                </td>
                                                                        </tr>
                                                                        <tr>
                                                                                <td>Suspend RTN</td>
                                                                                <td><input type="radio" id="suspend-trn-on"
                                                                                                name="suspend_Rtn" value="T"
                                                                                                [ngModel]="result.suspendRTN" />
                                                                                </td>
                                                                                <td><input type="radio" id="suspend-trn-off"
                                                                                                name="suspend_Rtn" value="F"
                                                                                                [ngModel]="result.suspendRTN" />
                                                                                </td>
                                                                        </tr>
                                                                        <tr>
                                                                                <td>Suspend TFR</td>
                                                                                <td><input type="radio" id="suspend-tfr-on"
                                                                                                name="suspend_Tfr" value="T"
                                                                                                [ngModel]="result.suspendTFR" />
                                                                                </td>
                                                                                <td><input type="radio" id="suspend-tfr-off"
                                                                                                name="suspend_Tfr" value="F"
                                                                                                [ngModel]="result.suspendTFR" />
                                                                                </td>
                                                                        </tr>
                                                                        <tr>
                                                                                <td>Suspend LayBy Pay</td>
                                                                                <td><input type="radio" id="suspend-laypay-on"
                                                                                                name="suspend_Laypay" value="T"
                                                                                                [ngModel]="result.suspendLayPay" />
                                                                                </td>
                                                                                <td><input type="radio" id="suspend-laypay-off"
                                                                                                name="suspend_Laypay" value="F"
                                                                                                [ngModel]="result.suspendLayPay" />
                                                                                </td>
                                                                        </tr>
                                                                        <tr>
                                                                                <td>Tender Centrev</td>
                                                                                <td><input type="radio" id="tender-centrev-on"
                                                                                                name="tender_Centrev" value="T"
                                                                                                [ngModel]="result.tenderCentrev" />
                                                                                </td>
                                                                                <td><input type="radio" id="tender-centrev-off"
                                                                                                name="tender_Centrev" value="F"
                                                                                                [ngModel]="result.tenderCentrev" />
                                                                                </td>
                                                                        </tr>
                                                                        <tr>
                                                                                <td>Non Colour Size</td>
                                                                                <td><input type="radio" id="non-colorsize-on"
                                                                                                name="non_Coloursize" value="T"
                                                                                                [ngModel]="result.nonColorSize" />
                                                                                </td>
                                                                                <td><input type="radio" id="non-colorsize-off"
                                                                                                name="non_Coloursize" value="F"
                                                                                                [ngModel]="result.nonColorSize" />
                                                                                </td>
                                                                        </tr>
                                                                        <tr>
                                                                                <td>Customer Order No Stock
                                                                                        Movement</td>
                                                                                <td><input type="radio" id="cust-orderNoStkMovement-on"
                                                                                                name="customer_Order_No_Stock_Movement" value="T"
                                                                                                [ngModel]="result.customerOrderNoStockMovement" />
                                                                                </td>
                                                                                <td><input type="radio" id="cust-orderNoStkMovement-off"
                                                                                                name="customer_Order_No_Stock_Movement" value="F"
                                                                                                [ngModel]="result.customerOrderNoStockMovement" />
                                                                                </td>
                                                                        </tr>
                                                                </tbody>
                                                        </table>
                                                </div>
                                                <div class="col">
                                                        <table class="table table-striped">
                                                                <thead>
                                                                        <tr>
                                                                                <td></td>
                                                                                <td>Yes</td>
                                                                                <td>No</td>
                                                                        </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <tr>
                                                                        <td>Price Prompt</td>
                                                                        <td>
                                                                            <input type="radio" id="price-prompt-on"
                                                                                   name="price_Prompt" value="T"
                                                                                   [ngModel]="result.pricePrompt" />
                                                                        </td>
                                                                        <td>
                                                                            <input type="radio" id="price-prompt-off"
                                                                                   name="price_Prompt" value="F"
                                                                                   [ngModel]="result.pricePrompt" />
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Block Exchange Button</td>
                                                                        <td>
                                                                            <input type="radio" id="block-exchangeButton-on"
                                                                                   name="block_Exchange_Button" value="T"
                                                                                   [ngModel]="result.blockExchangeButton" />
                                                                        </td>
                                                                        <td>
                                                                            <input type="radio" id="block-exchangeButton-off"
                                                                                   name="block_Exchange_Button" value="F"
                                                                                   [ngModel]="result.blockExchangeButton" />
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Header Image</td>
                                                                        <td>
                                                                            <input type="radio" id="header-image-on"
                                                                                   name="header_Image" value="T"
                                                                                   [ngModel]="result.headerImage" />
                                                                        </td>
                                                                        <td>
                                                                            <input type="radio" id="header-image-off"
                                                                                   name="header_Image" value="F"
                                                                                   [ngModel]="result.headerImage" />
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Footer Image</td>
                                                                        <td>
                                                                            <input type="radio" id="footer-image-on"
                                                                                   name="footer_Image" value="T"
                                                                                   [ngModel]="result.footerImage" />
                                                                        </td>
                                                                        <td>
                                                                            <input type="radio" id="footer-image-off"
                                                                                   name="footer_Image" value="F"
                                                                                   [ngModel]="result.footerImage" />
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Consolidate Ctran</td>
                                                                        <td>
                                                                            <input type="radio" id="consolidate-ctran-on"
                                                                                   name="consolidate_C_Tran" value="T"
                                                                                   [ngModel]="result.consolidateCTran" />
                                                                        </td>
                                                                        <td>
                                                                            <input type="radio" id="consolidate-ctran-off"
                                                                                   name="consolidate_C_Tran" value="F"
                                                                                   [ngModel]="result.consolidateCTran" />
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>
                                                                            Customer Name On Receipt
                                                                        </td>
                                                                        <td>
                                                                            <input type="radio" id="cust-nameOnReceipt-on"
                                                                                   name="customer_Name_On_Receipt" value="T"
                                                                                   [ngModel]="result.customerNameOnReceipt" />
                                                                        </td>
                                                                        <td>
                                                                            <input type="radio" id="cust-nameOnReceipt-off"
                                                                                   name="customer_Name_On_Receipt" value="F"
                                                                                   [ngModel]="result.customerNameOnReceipt" />
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>
                                                                            Print Customer Account On A4
                                                                        </td>
                                                                        <td>
                                                                            <input type="radio" id="cust-accountprintA4-on"
                                                                                   name="customer_Account_Print_On_A4" value="T"
                                                                                   [ngModel]="result.customerAccountPrintOnA4" />
                                                                        </td>
                                                                        <td>
                                                                            <input type="radio" id="cust-accountprintA4-off"
                                                                                   name="customer_Account_Print_On_A4" value="F"
                                                                                   [ngModel]="result.customerAccountPrintOnA4" />
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>
                                                                            Allow Enter OrderNo and No
                                                                            Deposit
                                                                        </td>
                                                                        <td>
                                                                            <input type="radio" id="allowed-userOrderNoDep-on"
                                                                                   name="enter_Order_No_And_No_Deposit" value="T"
                                                                                   [ngModel]="result.enterOrderNoAndNoDeposit" />
                                                                        </td>
                                                                        <td>
                                                                            <input type="radio" id="allowed-userOrderNoDep-off"
                                                                                   name="enter_Order_No_And_No_Deposit" value="F"
                                                                                   [ngModel]="result.enterOrderNoAndNoDeposit" />
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>
                                                                            Demographic Question Ask
                                                                        </td>
                                                                        <td>
                                                                            <input type="radio" id="demo-qAsk-on"
                                                                                   name="demographic_Question_Ask" value="T"
                                                                                   [ngModel]="result.demographicQuestionAsk" />
                                                                        </td>
                                                                        <td>
                                                                            <input type="radio" id="demo-qAsk-off"
                                                                                   name="demographic_Question_Ask" value="F"
                                                                                   [ngModel]="result.demographicQuestionAsk" />
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Customer Club Mandatory</td>
                                                                        <td>
                                                                            <input type="radio" id="cust-clubMandatory-on"
                                                                                   name="customer_Club_Mandatory" value="T"
                                                                                   [ngModel]="result.customerClubMandatory" />
                                                                        </td>
                                                                        <td>
                                                                            <input type="radio" id="cust-clubMandatory-off"
                                                                                   name="customer_Club_Mandatory" value="F"
                                                                                   [ngModel]="result.customerClubMandatory" />
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                        </table>
                                                </div>
                                        </div>
                                </div>
                        </div>
                        <div class="text-right">
                                <button class="btn btn-outline-primary mb-5" type="submit"><i
                                        class="fas fa-lg fa-fw fa-check-circle text-success mr-2"></i>Save
                                Config</button>
                        </div>

                </form>
        </ng-container>
</div>
