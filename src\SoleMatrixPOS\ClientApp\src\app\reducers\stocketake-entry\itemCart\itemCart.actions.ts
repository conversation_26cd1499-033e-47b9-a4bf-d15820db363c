
import { createAction, props } from "@ngrx/store";
import { BarcodeResultDto, PrintInvalidDto, StockItemCartDto, StockItemDto } from "src/app/pos-server.generated";

export const init = createAction('[StocktakeEntry] init');

export const addStockItem = createAction('[StocktakeEntry] Add Stock Item', props<{ stockItem: StockItemDto }>());
export const addStockItemResponse = createAction('[StocktakeEntry] AddStockItemResponse', props<{ stockCartItem: StockItemCartDto }>());

export const removeItem = createAction('[StocktakeEntry] Remove Item In Cart', props<{ stockItem: StockItemDto }>());
export const setNumberOfItems = createAction('[StocktakeEntry] SetNum Items', props<{ stockItem: StockItemDto, quantity: number }>());


export const addReason = createAction("[StocktakeEntry] Add Reason", props<{ barcode: string, reason: string }>());
export const removeReason = createAction("[StocktakeEntry] Remove Reason", props<{ barcode: string, reasonId: number }>())
export const removeAllReasons = createAction("[StocktakeEntry] Remove All Reasons", props<{ barcode: string }>());

export const negateCart = createAction("[StocktakeEntry] Negate");

export const submitBarcode = createAction('[StocktakeEntry] Submit Barcode', props<{ itembarcode: StockItemDto }>());
export const submitBarcodeResponse = createAction('[StocktakeEntry] Submit Barcode Response', props<{ addCartItem: StockItemCartDto }>());

//Hello Gift Voucher
