import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { Store, select } from '@ngrx/store';
import { first, map, filter } from 'rxjs/operators';
import Swal from 'sweetalert2';
import { mergeMap } from 'rxjs/operators';
import { of } from 'rxjs';
import { merge, Observable, Subject } from 'rxjs';
import * as laybyActions from 'src/app/reducers/layby/layby.actions';
import * as laybyPaymentActions from 'src/app/reducers/layby-payment/layby-payment.actions';
import { Payment, PaymentType, Transaction } from 'src/app/payment/payment.service';
import * as laybyRefundActions from 'src/app/reducers/layby-refund/layby-refund.actions';
import { takeUntil, take } from 'rxjs/operators';
import { PaymentModalButton } from 'src/app/payment/payment-modal-button/payment-modal-button.component';
import * as transactionSelectors from 'src/app/reducers/transaction/transaction.selectors';
import * as cartSelectors from 'src/app/reducers/sales/cart/cart.selectors';
import * as transactionActions from 'src/app/reducers/transaction/transaction.actions';
import { AppState } from 'src/app/reducers';
import { CashModalComponent } from 'src/app/payment/cash-modal/cash-modal.component';
import { EftposModalComponent } from 'src/app/payment/eftpos-modal/eftpos-modal.component';
import * as receiptActions from 'src/app/reducers/receipt-printing/receipt.actions';
import { CancelLaybyDto, EftposClient, GetReceiptDto, LaybylineDto, LaybyPaymentDto, LaybyPaymentWithTransactionDto, LaybySearchResultDto, MakeLaybyPaymentsDto, TransactionDto, TranslogDto, TranspayDto, LaybyPaymentEmailRequest, GiftVoucherResultDto, MakeLaybyPaymentResultDto } from 'src/app/pos-server.generated';
import * as transActions from 'src/app/reducers/transaction/transaction.actions';
import { EmailReceiptComponent } from 'src/app/email-receipt/email-receipt.component';
import { ReceiptTransactionDto } from 'src/app/pos-server.generated';
import { tap } from 'rxjs/operators';
import * as sysSelectors from '../../reducers/sys-config/sys-config.selectors';
import * as staffActions from 'src/app/reducers/staff/staff.actions';
import { CreateLaybyDto } from 'src/app/pos-server.generated';
import * as laybySearchSelectors from 'src/app/reducers/layby-search/layby-search.selectors';
import { EftposService, mapCartToLinklyBasket } from '../../eftpos/eftpos.service';
import { WaitingForEftposModalComponent } from '../../payment/waiting-for-eftpos-modal/waiting-for-eftpos-modal.component';
import { ReceiptBatch, TextAction, CutAction, CutType, FeedAction, BarcodeAction, OpenCashDrawerAction } from '../../printing/printing-definitions';
import { PrintingService, SolemateReceiptOptions } from 'src/app/printing/printing.service';
import { LaybyZeroRefundConfirmationComponent } from './layby-zero-refund-confirmation/layby-zero-refund-confirmation.component';
import { financialRound } from 'src/app/payment/payment.service';
import * as customerClubSearchActions from 'src/app/reducers/customer-club/club-search/customer-club.actions';
import * as customerClubUpdateActions from 'src/app/reducers/customer-club/customer-update/customer-update.actions';
import * as customerClubSearchSelectors from 'src/app/reducers/customer-club/club-search/customer-club.selectors';
import { PointsUpdatePayload } from 'src/app/reducers/customer-club/customer-update/customer-update.actions';
import { CustomerClubDto } from 'src/app/pos-server.generated';
import { CreditNoteIssuanceModalComponent } from 'src/app/payment/credit-note-issuance-modal/credit-note-issuance-modal.component';
import { GiftVoucherIssueComponent } from 'src/app/gift-voucher/gift-voucher-issue/gift-voucher-issue.component';
import * as voucherActions from 'src/app/reducers/gift-voucher/gift-voucher.actions';
import * as laybyPaymentSelectors from 'src/app/reducers/layby-payment/layby-payment.selectors';
@Component({
  selector: 'pos-cancel-layby',
  templateUrl: './cancel-layby.component.html',
  styleUrls: ['./cancel-layby.component.scss']
})
export class CancelLaybyComponent implements OnInit, OnDestroy {
  @Input() clientCode: string | null = null;
  @Input() clientName: string | null = null;
  private destroy$ = new Subject<void>();
  modalButtons: PaymentModalButton[];

  sysStatus: any;
  alwaysOpenCashTill: string = 'T'; // Default to always open cash till
  public sysStatus$: Observable<any>;

  public intEftAmount = 0;
  intEftReceipts: GetReceiptDto[];

  creditNote: MakeLaybyPaymentResultDto = null; // Store credit note once generated
  totalCreditNoteAmount: number = 0; // Track the total credit note amount

  selectedLayby$: Observable<LaybySearchResultDto | null>;
  selectedLaybyLines$: Observable<LaybylineDto[] | null>;

  // Variables to hold the TransPay data
  payAmount: number | null = null;
  paymentType: string | null = null;
  transNo: number | null = null;
  voucherNumber: string | null = null;
  totalRefundedInModals: number = 0;  // Total amount refunded via payment modals
  transaction: Transaction; // Transaction to handle payments
  laybyCode: string | null = null;

  totalCashRefunded: number = 0;
  totalEftposRefunded: number = 0;
  laybyLines$: Observable<LaybylineDto[]>;

  totalPaid: number = 0; // Total amount paid for the layby
  amountDue: number = 0;
  cancellationError: string | null = null; // Error message if the refund amount is invalid

  // Added properties for points calculation
  PointsPerDollar: number = 0;
  private selectedCustomerClubMember$: Observable<CustomerClubDto>;
  public selectedCustomerClubMember: CustomerClubDto = null;
  private customerClubMemberForPoints: CustomerClubDto | null = null; // To store fetched customer details
  newCustomerPointsTotal: number | null = null;
  pointsDeducted: number = 0; // Will store negative value for display
  totalItemsValue: number = 0;

  constructor(
    public activeModal: NgbActiveModal,
    private store: Store<AppState>,
    private router: Router,
    private modalService: NgbModal,
    private eftposService: EftposService,
    private printService: PrintingService  // Add this line
  ) {
    this.selectedLayby$ = this.store.pipe(select(laybySearchSelectors.selectedLayby));
    this.selectedLaybyLines$ = this.store.pipe(select(laybySearchSelectors.searchedLaybyLines));
    this.laybyLines$ = this.selectedLaybyLines$;
    this.selectedCustomerClubMember$ = this.store.pipe(select(customerClubSearchSelectors.selectedCustomerClubMember)); // Added selector
  }

  ngOnInit() {

    this.store.dispatch(transActions.getTransactionNo());

    // Subscribe to the transaction number with proper error handling
    this.store.select(transactionSelectors.transNo)
      .pipe(
        takeUntil(this.destroy$),
        tap(transNo => {
          this.transNo = transNo;
          console.log('Transaction number updated:', transNo);
        })
      )
      .subscribe();

    // Subscribe to the credit note state
    this.store.select(laybyPaymentSelectors.selectLastPaymentResult)
      .pipe(takeUntil(this.destroy$))
      .subscribe(creditNote => {
        this.creditNote = creditNote;
        console.log('Credit note received:', creditNote);
      });

    this.store.select(sysSelectors.PointsPerDollar)
      .pipe(takeUntil(this.destroy$))
      .subscribe(limit => {
        this.PointsPerDollar = limit || 0;
      });

    this.sysStatus$ = this.store.select(sysSelectors.selectSysConfig);
    this.sysStatus$.subscribe((sysconfig) => {
      this.sysStatus = sysconfig
      this.alwaysOpenCashTill = sysconfig.alwaysOpenCashTill || 'F'; // Ensure default
    });
    this.alwaysOpenCashTill = this.sysStatus.alwaysOpenCashTill;
    // Set up payment modal buttons for Cash and EFTPOS refunds.
    this.modalButtons = [
      new PaymentModalButton('Cash', 'fas fa-money-bill-wave', PaymentType.Cash, false),
      new PaymentModalButton('EFTPOS', 'fas fa-credit-card', PaymentType.Eftpos, false),
      new PaymentModalButton('Credit Note', 'fa-sticky-note', PaymentType.CreditNote, false)
    ];

    if (this.selectedLaybyLines$ && this.selectedLayby$) {
      this.selectedLaybyLines$.pipe(
        takeUntil(this.destroy$)
      ).subscribe(lines => {
        console.log("Layby lines:", lines);
        if (lines) {
          console.log("Layby lines:", lines);

          // 1. Calculate the total value of items on layby.
          // Add the values for transtypes 1 and 5
          const stockPlaced = lines
            .filter(line => line.transType === 1)
            .reduce((sum, line) => sum + (line.extendedValue || 0), 0);
          console.log("Stock placed on layby (transType 1):", stockPlaced);

          const additionalItems = lines
            .filter(line => line.transType === 5)
            .reduce((sum, line) => sum + (line.extendedValue || 0), 0);
          console.log("Additional items added (transType 5):", additionalItems);

          // Subtract the values for transtypes 7 (items removed)
          const stockRemoved = lines
            .filter(line => line.transType === 7)
            .reduce((sum, line) => sum + (line.extendedValue || 0), 0);
          console.log("Stock removed (transType 7):", stockRemoved);

          // Total value of items placed on layby:
          const totalItemsValue = stockPlaced + additionalItems - (-1 * stockRemoved);
          console.log("Total items value (1 + 5 minus 7):", totalItemsValue);

          // 2. Calculate the total amount due (sum of all extended values)
          const amountDue = lines.reduce((sum, line) => sum + (line.extendedValue || 0), 0);
          console.log("Total amount due (sum of all extended values):", amountDue);

          // 3. Calculate the total paid as (total value of items - amount due)
          const totalPaid = totalItemsValue - amountDue;
          console.log("Total paid:", totalPaid);

          // Save these calculated values in your component
          this.totalPaid = parseFloat(totalPaid.toFixed(2));
          this.amountDue = parseFloat(amountDue.toFixed(2));
          this.totalItemsValue = totalItemsValue;
        }
      });
    }

    // Fetch customer details if clientCode is provided
    if (this.clientCode) {
      this.store.dispatch(customerClubSearchActions.search({
        searchParams: {
          searchString: this.clientCode,
          customerClubSearchKeywordColumnDto: 3, // Search by client code
          customerClubSearchOrderByColumnDto: 3,
          customerClubOrderByDirectionEnumDto: 0,
          first: 1,
          skip: 0
        }
      }));

      // Subscribe to the search results to select the first member
      this.store.select(customerClubSearchSelectors.searchedCustomerClubMembers)
        .pipe(
          takeUntil(this.destroy$),
          tap(searchResults => {
            if (searchResults && searchResults.length > 0) {
              console.log('Customer club search results:', searchResults);
              // Select the first member from the search results
              this.store.dispatch(customerClubSearchActions.selectCustomerClubMember({
                payload: searchResults[0]
              }));
            }
          })
        )
        .subscribe();

      // Subscribe to the selected customer club member
      this.selectedCustomerClubMember$
        .pipe(takeUntil(this.destroy$))
        .subscribe(member => {
          if (member) {
            this.selectedCustomerClubMember = member;
            this.customerClubMemberForPoints = member;
            // Use input clientName if available, otherwise construct from member details
            this.clientName = this.clientName || `${member.firstname || ''} ${member.surname || ''}`.trim();
            console.log('Customer Club Member found:', member);
            console.log('Using Client Name:', this.clientName);
          }
        });
    }

    this.selectedLayby$
      .pipe(takeUntil(this.destroy$))
      .subscribe(layby => {
        if (layby) {
          this.laybyCode = layby.laybyCode;
          console.log('Layby code set:', this.laybyCode);
        }
      });
  }

  dismissModal() {
    this.activeModal.dismiss('laybyCancelled');
    this.destroy$.next();
    this.destroy$.complete();
  }

  validateCancellationAmount() {
    if (this.totalRefundedInModals > this.totalPaid) {
      this.cancellationError = 'Refund amount cannot exceed the total paid amount.';
    } else if (this.totalRefundedInModals < 0) {
      this.cancellationError = 'Refund amount cannot be negative.';
    } else {
      this.cancellationError = null;
    }
  }

  canProcessCancellation(): boolean {
    return this.totalRefundedInModals >= 0 && this.totalRefundedInModals <= this.totalPaid;
  }

  getAmountRemaining(): number {
    return Math.max(this.totalPaid - this.totalRefundedInModals, 0);
  }

  handleLaybyCancellation() {
    if (this.canProcessCancellation()) {
      console.log("Refund amount", this.totalRefundedInModals);

      if (this.totalRefundedInModals === 0) {
        const confirmModalRef = this.modalService.open(LaybyZeroRefundConfirmationComponent, {
          centered: true,
          backdrop: 'static'
        });

        confirmModalRef.result.then((result) => {
          if (result === 'confirm') {
            this.checkIntegratedEftpos();
          }
        }).catch(() => {
        });
      } else {
        this.checkIntegratedEftpos();
      }
    }
  }

  checkIntegratedEftpos(): void {
    if (this.intEftAmount != 0) {
      const modalRef = this.modalService.open(WaitingForEftposModalComponent, {
        size: 'md',
        centered: true,
        backdrop: 'static',
        keyboard: false
      });
      console.log(this.transNo)
      // Determine which integrated EFTPOS provider to use
      switch (this.sysStatus.integratedEFTProvider) {
        case "Linkly":
          console.log(this.intEftAmount);
          modalRef.componentInstance.tenderAmount = this.intEftAmount;
          modalRef.componentInstance.totalAmount = this.totalRefundedInModals;
          modalRef.componentInstance.store = this.store;
          modalRef.componentInstance.discountAmt = 0; // TODO: calculate discount if needed
          modalRef.componentInstance.surchargeAmt = this.intEftAmount * 0; // TODO: adjust surcharge calculation if required
          modalRef.componentInstance.taxAmt = this.intEftAmount * 0; // TODO: adjust tax calculation based on config
          modalRef.componentInstance.transNo = this.transNo;

          // Map the current cart to the format required by Linkly
          const mappedItems = mapCartToLinklyBasket([]); // TODO fix if needed
          modalRef.componentInstance.items = mappedItems;
          modalRef.componentInstance.transType = "Refund";
          break;

        case "Tyro":
          // TODO: Implement Tyro integration logic here if needed.
          break;

        default:
          console.log("Integrated EFTPOS not configured");
          return;
      }

      // Handle the result from the waiting-for-EFTPOS modal.
      modalRef.result.then((result: { paymentResult: Payment, surchargePayment: Payment }) => {
        if (result) {
          console.log("EFTPOS payment result:", result);
          this.eftposService.getReceipts(this.transNo, false)
            .subscribe((receipts: GetReceiptDto[]) => {
              this.intEftReceipts = receipts;

              // Process the transaction with store
              this.processLaybyTransaction();

              this.printService.printEftposReceipt(receipts, true);
            });
        } else {
          // this.totalRefundedInModals -= this.intEftAmount;
          // this.totalEftposRefunded = 0;
          // this.intEftAmount = 0;

          this.store.dispatch(transActions.resetTransactionConfirmation());
          this.store.dispatch(transActions.getTransactionNo());

          console.log("EFTPOS payment failed or was cancelled");
        }
      }).catch(error => {
        console.error("Error in waiting-for-EFTPOS modal:", error);
      });
    }
    else {
      // Process the transaction without the need for integrated EFTPOS
      this.processLaybyTransaction();
    }
  }

  // Separate the transaction processing into its own method
  processLaybyTransaction(): void {
    
    this.calculateAndSubtractPoints(this.totalItemsValue);
    let dispatchedLaybyPaymentDto: LaybyPaymentWithTransactionDto;

    this.getLaybyPaymentDto().pipe(
      take(1),
      tap(dto => dispatchedLaybyPaymentDto = dto),
      mergeMap(laybyPaymentWithTransactionDto => {
        if (!laybyPaymentWithTransactionDto) {
          throw new Error("Failed to generate layby payment DTO for dispatch.");
        }
        this.store.dispatch(laybyPaymentActions.submitPayments({ payload: laybyPaymentWithTransactionDto }));

        return this.store.pipe(
          select(laybyPaymentSelectors.selectLastPaymentResult),
          filter(result => result !== null && result.transactionNumber === this.transNo),
          take(1)
        );
      }),
      mergeMap((paymentResult: MakeLaybyPaymentResultDto) => {
        return this.laybyLines$.pipe(
          take(1),
          map(currentLaybyLines => ({ paymentResult, currentLaybyLines, originalTransactionDto: dispatchedLaybyPaymentDto.transactionDto }))
        );
      })
    ).subscribe(
      ({ paymentResult, currentLaybyLines, originalTransactionDto }) => {
        this.activeModal.dismiss('laybyCancelled');

        this.transNo = paymentResult.transactionNumber;
        const actualCreditNoteDto: GiftVoucherResultDto | null = paymentResult.creditNote;

        console.log('CancelLaybyComponent: Processing successful payment result:', paymentResult);
        console.log('CancelLaybyComponent: Actual creditNote DTO for modal:', actualCreditNoteDto);

        const saleDateTime = new Date();
        const trans: ReceiptTransactionDto = {
          logs: originalTransactionDto.translogs,
          pays: originalTransactionDto.payments,
          saleDateTime: saleDateTime,
          transType: 2,
        };

        const translogForEmail: ReceiptTransactionDto = {
          logs: currentLaybyLines ? [...currentLaybyLines, ...originalTransactionDto.translogs] : originalTransactionDto.translogs,
          pays: originalTransactionDto.payments,
          saleDateTime: saleDateTime,
          transType: 5,
        };

        if (translogForEmail.logs && translogForEmail.logs.length > 0) {
          const summaryLogIndex = translogForEmail.logs.findIndex(log => log.styleCode === '');
          if (summaryLogIndex !== -1 && translogForEmail.logs[summaryLogIndex]) {
            translogForEmail.logs[summaryLogIndex].styleCode = this.laybyCode;
          } else if (translogForEmail.logs[translogForEmail.logs.length - 1]) {
            translogForEmail.logs[translogForEmail.logs.length - 1].styleCode = this.laybyCode;
          }
        }

        const laybyEmailRequest: LaybyPaymentEmailRequest = {
          email: null,
          receiptDto: trans,
          receiptType: '2',
          logs: translogForEmail.logs,
          pays: translogForEmail.pays,
          transNo: paymentResult.transactionNumber,
          amountDue: 0,
          change: 0
        };

        if (this.alwaysOpenCashTill === 'T' || originalTransactionDto.payments.some(p => p.paymentType === 'Cash')) {
          this.store.dispatch(receiptActions.executeBatch({
            payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
          }));
        }

        Swal.fire({
          title: "Layby Cancelled",
          text: "The layby has been successfully cancelled and refunded.",
          showCancelButton: true,
          cancelButtonText: "Email Receipt",
          confirmButtonText: "Print Receipt",
          allowOutsideClick: false,
        }).then(async (swalResult) => {
          const handleCreditNoteModal = () => {
            if (actualCreditNoteDto) {
              console.log('CancelLaybyComponent: Credit note exists. Dispatching voucherActions.creditNoteReceived with:', actualCreditNoteDto);
              this.store.dispatch(voucherActions.creditNoteReceived({ payload: actualCreditNoteDto }));
              this.modalService.open(GiftVoucherIssueComponent, {
                windowClass: 'daily-modal-window', size: 'l', centered: true,
                backdrop: 'static', keyboard: false
              });
            } else {
              console.log('CancelLaybyComponent: No credit note to issue.');
              this.store.dispatch(staffActions.clearStaffLogin());
            }
          };

          if (swalResult.value) {
            await this.printService.printLaybyReturnReceipt(
              "Layby Cancellation",
              translogForEmail.logs,
              translogForEmail.pays,
              trans.transType,
              paymentResult.transactionNumber.toString(),
              SolemateReceiptOptions.default(),
              trans, 0, 0, undefined,
              this.clientCode, this.clientName, this.laybyCode,
              undefined, this.newCustomerPointsTotal, this.pointsDeducted
            );
            handleCreditNoteModal();
          } else if (swalResult.dismiss === Swal.DismissReason.cancel) {
            await this.openEmailModal(laybyEmailRequest, this.pointsDeducted, this.newCustomerPointsTotal);
            handleCreditNoteModal();
          } else {
            handleCreditNoteModal();
          }
        });
      },
      error => {
        console.error("Error during layby cancellation processing:", error);
        this.store.dispatch(laybyPaymentActions.submitPaymentsFailure({ error: error.message || "Layby cancellation failed." }));
        Swal.fire("Error", "Layby cancellation failed. Please try again.", "error");
        this.activeModal.dismiss('cancellationFailed');
      }
    );
  }

  laybyTransactionCompleted(): void {
    // Now just delegate to the processLaybyTransaction method
    this.processLaybyTransaction();
  }

  openEmailModal(laybyReceipt: LaybyPaymentEmailRequest, pointsDeducted: number, newCustomerPointsTotal: number | null): Promise<void> {
    return new Promise((resolve) => {
      const modalRef = this.modalService.open(EmailReceiptComponent, {
        size: 'lg',
        backdrop: 'static'
      });

      // Pass receiptTrans to the EmailReceiptComponent.
      modalRef.componentInstance.laybyReceipt = laybyReceipt;
      modalRef.componentInstance.laybyType = 'Cancel';
      modalRef.componentInstance.customerSelected = this.customerClubMemberForPoints;
      modalRef.componentInstance.pointsEarned = pointsDeducted; // Pass deducted points (negative value)
      modalRef.componentInstance.newCustomerPointsTotal = newCustomerPointsTotal;

      modalRef.result.then(() => {
        console.log('Email receipt sent.');
        resolve();  // Resolve the promise once the modal is closed.
      }).catch(() => {
        resolve();  // Resolve the promise if the modal is dismissed.
      });
    });
  }

  launchPaymentModal(type: PaymentType) {
    let modalRef;

    switch (type) {
      case PaymentType.Cash:
        modalRef = this.modalService.open(CashModalComponent, { size: 'xl', centered: true });
        break;
      case PaymentType.CreditNote:
        modalRef = this.modalService.open(CreditNoteIssuanceModalComponent, { size: 'xl', centered: true });
        modalRef.componentInstance.isLayby = true;
        break;
      case PaymentType.Eftpos:
        this.store.select(sysSelectors.selectSysConfig).pipe(
          first(), // Get the first emitted value and complete
          map(sysConfig => sysConfig && sysConfig.integratedEFTProvider !== 'None' ? sysConfig.integratedEFTProvider : null)
        ).subscribe(integrated => {
          if (integrated && integrated != "None") {
            modalRef = this.modalService.open(EftposModalComponent, { size: 'xl', centered: true });
            modalRef.componentInstance.intEft = true
          }
          else {
            modalRef = this.modalService.open(EftposModalComponent, { size: 'xl', centered: true });
            modalRef.componentInstance.intEft = false
          }
        });
        break;
    }
    if (modalRef) {
      modalRef.componentInstance.amountDue = this.getAmountRemaining();
      modalRef.result.then((result: Payment) => {
        if (result) {
          if (type === PaymentType.Cash) {
            this.totalCashRefunded += result.amount;
            this.totalRefundedInModals += result.amount;
          } else if (type === PaymentType.Eftpos) {
            this.totalEftposRefunded += result.amount;
            this.totalRefundedInModals += result.amount;
            if (result.desc == "Integrated Eftpos") {
              this.intEftAmount += result.amount;
            }
          } else if (type === PaymentType.CreditNote) {
            // Handle credit note payment
            this.totalCreditNoteAmount += result.amount;
            this.totalRefundedInModals += result.amount;
            console.log("Added credit note payment:", result.amount);
            console.log("Total credit note amount:", this.totalCreditNoteAmount);
          }
          // Optionally revalidate the cancellation amount after a payment modal completes.
          this.validateCancellationAmount();
        }
      }).catch((error) => {
        console.log('Payment Cancelled:', error);
      });
    }
  }

  getLaybyCancellationTransaction(): Observable<TransactionDto> {
    return this.selectedLaybyLines$.pipe(
      take(1),
      map(lines => {
        const payments: TranspayDto[] = [];

        if (this.totalRefundedInModals > 0) {
          if (this.totalCashRefunded > 0) {
            payments.push({
              paymentType: 'Cash',
              payAmount: -this.totalCashRefunded,
              voucherNumber: null,
            });
          }

          if (this.totalEftposRefunded > 0) {
            payments.push({
              paymentType: 'Eftpos',
              payAmount: -this.totalEftposRefunded,
              voucherNumber: this.voucherNumber,
            });
          }

          // Add credit note payment if necessary
          const creditNoteAmount = this.totalRefundedInModals - (this.totalCashRefunded + this.totalEftposRefunded);
          if (creditNoteAmount > 0) {
            payments.push({
              paymentType: 'Credit Note',
              payAmount: -creditNoteAmount,
              voucherNumber: null, // The voucher number will be generated by the backend
            });
          }
        }

        const itemTranslogs: TranslogDto[] = (lines || [])
          .filter(line => line.transType === 1) // Filter for items placed on layby
          .map((line, index): TranslogDto => ({
            styleCode: line.styleCode,
            colourCode: line.colourCode,
            sizeCode: line.sizeCode,
            quantity: - (line.quantity || 0), // Negate the quantity for return
            sellingPrice: line.sellingPrice, // Keep original selling price per item
            clientCode: this.clientCode, // Assuming clientCode is available in the component scope
            transNo: this.transNo,
            lineNo: index + 1, // Sequential line numbers starting from 1
          }));

        // Create the final translog entry for the total refund amount summary
        const refundTranslog: TranslogDto = {
          styleCode: '', // Use a descriptive code for the summary refund line
          colourCode: '',
          sizeCode: '',
          quantity: 0, // Representing a single refund summary transaction
          sellingPrice: this.totalRefundedInModals, // Total amount being refunded
          clientCode: this.clientCode,
          transNo: this.transNo,
          lineNo: itemTranslogs.length + 1, // Line number follows item lines
        };

        const allTranslogs = [...itemTranslogs, refundTranslog];
        console.log("Generated Translogs for Cancellation:", allTranslogs);

        return {
          transType: 2, // Transaction type for Sale/Refund
          translogs: allTranslogs, // Combined item and refund translogs
          payments: payments,
        };
      })
    );
  }

  parseDate(dateString: string): Date {
    const parts = dateString.split('/');
    // Assuming "24" means "2024". You might need to adjust if the century is different.
    const day = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1; // JavaScript months are 0-based.
    const year = parseInt(parts[2], 10) + 2000;
    return new Date(year, month, day);
  }

  /**
 * Gets a descriptive name for the item
 * For true items (transType 1 and 5), return the style code
 * For payments and deposits, return a formatted date if available
 */
  getItemDescription(line: LaybylineDto): string {
    if (line.transType === 1 || line.transType === 5) {
      return line.styleCode || 'Unknown Item';
    }
    return '';
  }

  /**
   * Gets the payment method for a payment line
   * This is a placeholder since the actual payment method 
   * might not be available in the layby lines
   */
  getPaymentMethod(line: LaybylineDto): string {
    // This is a placeholder - in a real implementation,
    // you might need to look up the payment method from transaction records
    if (line.transType === 2) {
      return 'Initial Deposit';
    } else if (line.transType === 3) {
      return 'Layby Payment';
    }
    return '';
  }

  /**
   * Calculates the total value of items on layby
   */
  getTotalItemValue(): number {
    let total = 0;

    // We'll calculate this on demand from the layby lines
    this.laybyLines$.pipe(take(1)).subscribe(lines => {
      if (lines) {
        // Sum the values for transTypes 1 and 5 (items placed on layby)
        const stockPlaced = lines
          .filter(line => line.transType === 1)
          .reduce((sum, line) => sum + (line.extendedValue || 0), 0);

        const additionalItems = lines
          .filter(line => line.transType === 5)
          .reduce((sum, line) => sum + (line.extendedValue || 0), 0);

        // Subtract the values for transType 7 (items removed)
        const stockRemoved = lines
          .filter(line => line.transType === 7)
          .reduce((sum, line) => sum + (line.extendedValue || 0), 0);

        // Total value of items placed on layby
        total = stockPlaced + additionalItems - (-1 * stockRemoved);
      }
    });

    return total;
  }

  getLaybyPaymentDto(): Observable<LaybyPaymentWithTransactionDto> {
    return this.selectedLayby$.pipe(
      take(1), // Take the first emission of selectedLayby$
      mergeMap(layby => {
        // Prepare the payments array
        const payments: LaybyPaymentDto[] = [];

        if (this.totalRefundedInModals > 0) {
          if (this.totalCashRefunded > 0) {
            payments.push({
              type: 'Cash',
              amount: this.totalCashRefunded,
            });
          }

          if (this.totalEftposRefunded > 0) {
            payments.push({
              type: 'Eftpos',
              amount: this.totalEftposRefunded,
            });
          }

          // Add credit note payment if necessary
          const creditNoteAmount = this.totalRefundedInModals - (this.totalCashRefunded + this.totalEftposRefunded);
          if (creditNoteAmount > 0) {
            payments.push({
              type: 'Credit Note',
              amount: creditNoteAmount,
            });
          }
        }
        if (this.totalRefundedInModals === 0) {
          payments.push({
            type: 'Cash',
            amount: 0
          })
        }

        // Create the payments DTO
        const paymentsDto: MakeLaybyPaymentsDto = {
          laybyCode: layby.laybyCode,
          payments: payments.length > 0 ? payments : null,
          transType: 9
        };

        // Get the transaction DTO
        return this.getLaybyCancellationTransaction().pipe(
          map(transactionDto => ({
            paymentsDto: paymentsDto,
            transactionDto: transactionDto, // Now this is a plain TransactionDto

          }))
        );
      })
    );
  }

  calculateAndSubtractPoints(refundAmount: number): void {
    const roundedRefundAmount = financialRound(refundAmount);

    console.log("Points Per Dollar", this.PointsPerDollar)
    console.log("Client Code", this.clientCode)
    console.log("Refund Amount", roundedRefundAmount)
    console.log("Selected Customer Club Member", this.selectedCustomerClubMember)

    if (this.clientCode && roundedRefundAmount > 0 && this.PointsPerDollar > 0 && this.customerClubMemberForPoints) {
      const pointsToDeduct = Math.floor(roundedRefundAmount / this.PointsPerDollar);
      console.log("Points To Deduct", pointsToDeduct);
      if (pointsToDeduct > 0) {
        try {
          const currentPoints = this.customerClubMemberForPoints.clientPoints != null ? this.customerClubMemberForPoints.clientPoints : 0;
          this.pointsDeducted = -pointsToDeduct; // Store as negative for display
          this.newCustomerPointsTotal = currentPoints - pointsToDeduct;
          console.log("Points Deducted", this.pointsDeducted);
          console.log("New Customer Points Total", this.newCustomerPointsTotal);

          const pointsUpdatePayload: PointsUpdatePayload = {
            clientCode: this.clientCode,
            pointsToAdjust: -pointsToDeduct // Negative value to subtract points
          };
          console.log('Dispatching points update:', pointsUpdatePayload);
          this.store.dispatch(customerClubUpdateActions.updatePoints({ payload: pointsUpdatePayload }));

        } catch (error) {
          console.error('Error processing points deduction:', error);
          this.pointsDeducted = 0;
          this.newCustomerPointsTotal = this.customerClubMemberForPoints ? this.customerClubMemberForPoints.clientPoints : null;
        }
      } else {
        this.pointsDeducted = 0;
        this.newCustomerPointsTotal = this.customerClubMemberForPoints.clientPoints;
      }
    } else {
      this.pointsDeducted = 0;
      this.newCustomerPointsTotal = this.customerClubMemberForPoints ? this.customerClubMemberForPoints.clientPoints : null;
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
