import { Injectable } from "@angular/core";
import { Actions, ofType, createEffect } from '@ngrx/effects';
import { HistoryClient } from 'src/app/pos-server.generated';
import * as historyActions from './history.actions';
import { mergeMap, map, catchError, tap } from 'rxjs/operators';
import { EMPTY } from 'rxjs';

@Injectable()
export class HistoryEffects {

    matchingHistory$ = createEffect(
        ()=> this.actions$.pipe(
            ofType(historyActions.search),
            mergeMap(
                (action) => this.historyClient.get(action.searchParams).pipe(
                    tap((res) => { console.log("matchingHistory", res) }),
                    map(
                        foundHistory => historyActions.searchResponse({payload: foundHistory, loadMore: action.loadMore})
                    ),
                    catchError(()=> EMPTY)
                )
            )
        )
    );

    matchingHistoryForClient$ = createEffect(
        ()=> this.actions$.pipe(
            ofType(historyActions.searchClient),
            mergeMap(
                (action) => this.historyClient.getByClientNo(action.searchParams).pipe(
                    tap((res) => { console.log("matchingHistory", res) }),
                    map(
                        foundHistory => historyActions.searchResponse({payload: foundHistory, loadMore: action.loadMore})
                    ),
                    catchError(()=> EMPTY)
                )
            )
        )
    );




    constructor(
        private actions$: Actions, 
        private historyClient: HistoryClient){}
}