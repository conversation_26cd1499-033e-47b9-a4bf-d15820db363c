import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import * as appActions from './app.actions';
import { map, mapTo, concatMapTo } from 'rxjs/operators';

@Injectable()
export class AppEffects {


  // concatMapTo all the state init actions to raise on application start
  // appInit$ = createEffect(() => this.actions$.pipe(
	// ofType(appActions.appInit),
	// concatMapTo([
	// 	identityActions.fetchIdentity()
	// ])
  // ));


  constructor(private actions$: Actions) {}
}
