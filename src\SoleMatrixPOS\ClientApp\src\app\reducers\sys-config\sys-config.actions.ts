import { createAction, props } from '@ngrx/store';
import { GetSystemStatusDto, SysControlUpdateDto } from 'src/app/pos-server.generated';

// Read Actions
export const readSysConfig = createAction('[SysConfig] Read SysConfig');
export const readSysConfigSuccess = createAction(
	'[SysConfig] Read SysConfig Success',
	props<{ sysConfig: GetSystemStatusDto }>()
);
export const readSysConfigFailure = createAction(
	'[SysConfig] Read SysConfig Failure',
	props<{ error: any }>()
);

// Update Actions
export const updateSysConfig = createAction(
	'[SysConfig] Update SysConfig',
	props<{ updateSys: SysControlUpdateDto }>()
);

export const updateSysConfigSuccess = createAction(
	'[SysConfig] Update SysConfig Success'
);

export const updateSysConfigFailure = createAction(
	'[SysConfig] Update SysConfig Failure',
	props<{ error: any }>()
);

export const resetUpdateStatus = createAction('[SysConfig] Reset Update Status');

