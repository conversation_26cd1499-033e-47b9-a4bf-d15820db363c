import { createAction, props } from '@ngrx/store';

import { CustomerClubDto } from '../../../pos-server.generated';

export const init = createAction('[CustomerClub] Init');
export const createMember = createAction('[CustomerClub] Add', props<{ payload: CustomerClubDto }>());
export const createMemberSuccess = createAction('[CustomerClub] AddMemberSuccess', props<{ result: CustomerClubDto }>());
export const createMemberError = createAction('[CustomerClub] AddMemberError', props<{ error: string }>());
export const checkBarcode = createAction(
    '[CustomerClub] Check Barcode',
    props<{ barcode: string }>()
);

export const checkBarcodeSuccess = createAction(
    '[CustomerClub] Check Barcode Success',
    props<{ exists: boolean }>()
);

export const checkBarcodeError = createAction(
    '[CustomerClub] Check Barcode Error',
    props<{ error: string }>()
);
