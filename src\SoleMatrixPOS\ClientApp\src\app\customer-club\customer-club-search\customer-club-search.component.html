<div class="content-wrapper flex-grow-1 pt-10">
	<div class="container-fluid">

		<div class="row text-align-center mt-2 mb-4">

			<i class="fas fa-lg fa-fw fa-crown text-danger mt-3 mr-2 ml-4 text-shadow"></i>
			<h3 class="mt-3 mb-0 mr-2 text-danger">
				Club Search by</h3>

			<div class="col-2 pr-0">
				<select class="form-control form-control-special" [(ngModel)]="field" (change)="search()">
					<option value="Phone" selected>PHONE</option>
					<option value="Mobile">MOBILE</option>
					<option value="Email">EMAIL</option>
					<option value="Id">CLIENT CODE</option>
					<option value="First">FIRST NAME</option>
					<option value="Surname">SURNAME</option>
					<option value="CareOf">CARE OF</option>
					<option value="Street">STREET</option>
					<option value="Suburb">SUBURB</option>
					<option value="Postcode">POSTCODE</option>
				</select>
			</div>

			<div class="col-3 pl-0">
				<!--(keyup)='keyUp.next($event)'-->
				<input type="text" class="form-control" [(ngModel)]="term" (keyup)="search()" autofocus #searchInput />
			</div>

			<div class="col-3 pl-0">
				<div class="input-group">
					<input type="text" class="form-control" [(ngModel)]="barcode" (keyup.enter)="searchByBarcode()"
						placeholder="Scan Barcode..." #barcodeInput />
					<div class="input-group-append">
						<button class="btn btn-outline-secondary" type="button" (click)="searchByBarcode()">
							<i class="fas fa-barcode"></i>
						</button>
					</div>
				</div>
			</div>

			<div class="col-auto ml-auto mr-4">
				<button type="button" class="btn btn-outline-default" (click)="addMember()">
					<i class="fas fa-user-plus text-info mr-2"></i>
					New Member</button>
			</div>

		</div>
		<div *ngIf="loading && !(customerClubMembers$ | async)?.length" class="text-center">
			<mat-spinner style="margin:0 auto;" mode="indeterminate"></mat-spinner>
		</div>
		<div class="row m-2" *ngIf="(customerClubMembers$ | async)?.length > 0">
			<div #scrollContainer class="table-responsive" style="max-height: 62vh; overflow-y: auto;" (scroll)="onScroll($event)">
				<table class="table table-striped ml-2 mr-2 table-hover">
					<thead>
						<tr>
							<th style="width: 11%">Phone</th>
							<th style="width: 7%">Title</th>
							<th style="width: 12%">First</th>
							<th style="width: 14%">Surname</th>
							<th style="width: 23%">Email</th>
							<th style="width: 15%">Suburb</th>
							<th style="width: 8%">State</th>
							<th style="width: 7%">PCode</th>
							<th style="width: 3%"> </th>
						</tr>
					</thead>
					<tbody>
						<tr #memberItem *ngFor="let member of customerClubMembers$|async; let i = index"
							(click)="selectMember(member)" (dblclick)="onDoubleClick(member)"
							[ngClass]="{'selectedMember': selectedMember && member.clientCode == selectedMember.clientCode}">
							<td>
								<ngb-highlight [result]="member.telephone" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td>
								<ngb-highlight [result]="member.title" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td>
								<ngb-highlight [result]="member.firstname" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td>
								<ngb-highlight [result]="member.surname" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td class="small-text">
								<ngb-highlight [result]="member.email" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td class="small-text">
								<ngb-highlight [result]="member.suburb" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td>
								<ngb-highlight [result]="member.state" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td>
								<ngb-highlight [result]="member.postcode" [highlightClass]="'search-highlight'">
								</ngb-highlight>
							</td>
							<td><i style="cursor: pointer;" class="fa fa-pencil" (click)="editMember(member)"></i>
							<td>
						</tr>

					</tbody>
				</table>
				<div *ngIf="isLoadingMore" class="text-center py-2" style="width: 100%;">
					<mat-spinner diameter="30" style="margin: 0 auto;"></mat-spinner>
				</div>
			</div>
		</div>

	</div>

</div>