// src/app/reducers/order-item-search/order-item-search.actions.ts

import { createAction, props } from '@ngrx/store';
import { CreateOrderDto } from 'src/app/pos-server.generated';
import { OrderSearchRequestDto } from 'src/app/pos-server.generated';

export const init = createAction('[OrderSearch] Init');

export const search = createAction(
  '[OrderSearch] Search',
  props<{ searchParams: OrderSearchRequestDto }>()
);

export const searchResponse = createAction(
  '[OrderSearch] Response',
  props<{ payload: CreateOrderDto[] }>()
);

export const selectOrder = createAction(
  '[OrderSearch] SelectOrder',
  props<{ payload: CreateOrderDto }>()
);

// New Actions for Payment and Cancellation
export const processPayment = createAction(
  '[OrderDetails] Process Payment',
  props<{ paymentInfo: any }>() // Define a proper type for paymentInfo
);

export const cancelOrder = createAction(
  '[OrderDetails] Cancel Order',
  props<{ orderCode: string }>()
);

export const cancelOrderSuccess = createAction(
  '[OrderDetails] Cancel Order Success',
  props<{ orderCode: string }>()
);

export const cancelOrderFailure = createAction(
  '[OrderDetails] Cancel Order Failure',
  props<{ error: any }>()
);
