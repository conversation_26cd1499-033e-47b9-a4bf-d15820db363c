<div class="table-responsive">
	<table class="table table-striped" style="margin: 0">
		<thead>
			<tr>
				<!-- Todo: reorder these to fit -->
				<th scope="col">Suspend No</th>
				<th scope="col">Staff Code</th>
				<th scope="col">Date</th>
				<th scope="col">Time</th>
				<th scope="col">Total Quantity</th>
				<th scope="col">Total Value</th>
				<th scope="col"></th>
			</tr>
		</thead>
		<tbody *ngFor="let suspendSale of suspendSaleItems; let i = index">
			<tr>
				<td>
					<button
						class="btn btn-link"
						(click)="onSuspendSaleClick(suspendSale)"
					>
						{{ suspendSale.suspendNo }}
					</button>
				</td>
				<td>{{ suspendSale.staffCode }}</td>
				<td>
					{{ suspendSale.suspendDate | date : "shortDate" }}
				</td>
				<td>{{ suspendSale.suspendTime }}</td>
				<td>{{ suspendSale.total_Quantity }}</td>
				<td>{{ suspendSale.total_Value | currency }}</td>
				<td>
					<span
						class="fas fa-trash fa-lg deleteButton"
						(click)="onTableItemDeleteClicked(i)"
					></span>
				</td>
			</tr>
		</tbody>
	</table>
</div>

<div class="modal-footer">
	<button type="button" class="btn btn-secondary" (click)="close()">
		Close
	</button>
</div>
