using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.KeyVault.Models;
using SoleMatrixPOS.Application.Stock;
using SoleMatrixPOS.Application.Stock.Dtos;
using SoleMatrixPOS.Application.Stock.SendStock.Commands;
using SoleMatrixPOS.Application.Stock.SendStock.Queries;
using SoleMatrixPOS.Filters;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	//[RequireStaffCodeFilter]
	[ApiController]
	public class SendStockController: ControllerBase
	{
		private readonly IMediator _mediator;

		public SendStockController(IMediator mediator)
		{
			_mediator = mediator;
		}


		[Route("TransferReason")]
		[HttpGet]
		public async Task<IEnumerable<TransferReasonDto>> GetTransferReasonList(CancellationToken ct)
		{
			var result = await _mediator.Send(new GetTransferReasonListQuery(), ct);
			return result;
		}


		[Route("Location")]
		[HttpGet]
		public async Task<TransferDestinationResponseDto> GetLocationList(CancellationToken ct,
			string transferReasonCode)
		{
			return await _mediator.Send(new GetTransferDestinationQuery(transferReasonCode), ct);
		}


		[Route("TransferNumber")]
		[HttpPost]
		public async Task<string> GenerateTransferNumber(
			[FromBody] GenerateTransferNumberRequest generateTransferNumberRequest, CancellationToken ct)
		{
			string result = await _mediator.Send(new GenerateTransferNumberCommand(generateTransferNumberRequest), ct);
			return result;
		}


		[Route("Proceed")]
		[HttpPost]
		public async Task<IActionResult> Proceed(
			[FromBody] SendStockRequestDto sendStockRequestDto, CancellationToken ct)
		{
			try
			{
				var result = await _mediator.Send(new ProceedSendStockCommand(sendStockRequestDto), ct);
				return Ok();
			}
			catch
			{
				return BadRequest("Something wrong occured");
			}
		}
	}
}
