import {Component, Input, OnInit} from '@angular/core' 
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from 'src/app/core/services/auth.service';



@Component({
    selector: "app-password-reset-form",
    templateUrl: "./password-reset-form.component.html",
    styleUrls: ["./password-reset-form.component.scss"]
})
export class PasswordResetFormComponent implements OnInit{

    submitted = false;  
    id: string = "";
    token: string = "";
  

    serverError: string = "";
 
    constructor(
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private router: Router,
        private authService: AuthService
    ){}



    ngOnInit(){
		this.token = this.route.snapshot.queryParamMap.get('token')
		this.id = this.route.snapshot.queryParamMap.get('id')
    }

    resetPasswordForm = this.fb.group({
        password: ['', [Validators.required, Validators.pattern(/^(?=.*[A-Z]{1,})(?=.*[a-z]{1,})(?=.*[0-9]{1,})(?=.*[\W]{1,}).{8,}$/)]],
        passwordConfirm: ['', [Validators.required ]],
    },{
        validators: [this.confirmPasswordValidator]
    })

    get f() {
        return this.resetPasswordForm.controls
    }

    confirmPasswordValidator(formGroup: FormGroup): void {
    const password = formGroup.controls['password']
    const passwordConfirm = formGroup.controls['passwordConfirm']
        if (passwordConfirm.errors && !passwordConfirm.errors['passwordMismatch']) {
    return;
  }
        if(password.value !== passwordConfirm.value) {
            passwordConfirm.setErrors({passwordMismatch: true})
        } else {
            passwordConfirm.setErrors(null)
        }
    }

    onSubmit(){
        this.submitted = true;
        if(this.resetPasswordForm.invalid){
            return
        }

        this.authService.resetPassword({
            id: this.id, 
            token: this.token, 
            password : this.resetPasswordForm.controls.password.value
        })
        .subscribe(
            () =>{
                this.authService.logout()
                this.router.navigateByUrl('/password-reset-success')
            }  , 
            error => { 
                if(error.status === 400 || error.status === 401){
                    this.serverError = 'Reset link is invalid or expired'
                    return
                }
            }
        )
    }
}