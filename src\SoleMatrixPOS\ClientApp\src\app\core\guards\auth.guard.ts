import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree } from "@angular/router";
import { AuthService } from "../services/auth.service";
import { AuthContextProvider } from "../context/auth.context";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { Injectable } from "@angular/core";



@Injectable()
export class AuthGuard implements CanActivate {
    constructor(private authContextProvider: AuthContextProvider, private router: Router){}

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean | UrlTree> {
        return this.authContextProvider.getAuthContext().pipe(map(authContext => authContext.isAuthenticated || this.router.parseUrl('/login')))
    }
    
}
