using System;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Serilog;
using Serilog.Events;
using Microsoft.AspNetCore.Server.Kestrel.Https;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore;

namespace SoleMatrixPOS
{
    public class Program
    {
        public static int Main(string[] args)
        {
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
                .Enrich.FromLogContext()
                .WriteTo.Console()
				// Not using seq - besides, this is development only
                // .WriteTo.Seq("http://localhost:5341")
                .CreateLogger();

			try
			{
				Log.Information("Starting host");
				CreateWebHostBuilder(args).Build().Run();
				return 0;
			}
			catch (Exception ex)
			{
				Log.Fatal(ex, "Host terminated unexpectedly");
				return 1;
			}
			finally
			{
				Log.CloseAndFlush();
			}
		}


        public static IWebHostBuilder CreateWebHostBuilder(string[] args) =>
            WebHost.CreateDefaultBuilder(args)
                .UseStartup<Startup>()
                .UseSerilog()
                .ConfigureAppConfiguration((ctx, builder) =>
                {
                    Log.Information("Loading config for env {EnvironmentName}", ctx.HostingEnvironment.EnvironmentName);
                    builder.AddCommandLine(args);
                    builder.AddJsonFile($"appsettings.{ctx.HostingEnvironment.EnvironmentName}.json", true, true);
                    //builder.AddJsonFile("appsettings.local.json", true, true);
				})
                .ConfigureKestrel((ctx, options) =>
                {
                    // when running kestrel on local dev, configure client certs. 
                    // on azure app services, ssl is terminated at the load balancer

                    if (ctx.HostingEnvironment.EnvironmentName == "Development")
                    {
                        options.ListenAnyIP(443, listenOptions =>
                        {
                            listenOptions.UseHttps((httpsOptions) =>
                            {
                                httpsOptions.ClientCertificateMode = ClientCertificateMode.AllowCertificate;
                                httpsOptions.CheckCertificateRevocation = false;
                                httpsOptions.ClientCertificateValidation = (clientCert, validationChain, policyErrors) =>
                                {
                                    // accept all client certs here - we have middleware that will check client certs later
                                    // supporting both kestrel-terminated and app service load-balancer-terminated scenarios
                                    return true;
                                };
                            });
                        });
                    }
                });
    }
}
