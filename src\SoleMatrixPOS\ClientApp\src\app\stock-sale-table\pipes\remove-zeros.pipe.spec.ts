import { RemoveZerosPipe } from './remove-zeros.pipe';

describe('RemoveZeroesPipe', () => {
  
  it('create an instance', () => {
    const pipe = new RemoveZerosPipe();
    expect(pipe).toBeTruthy();
  });

  it('should replace the number "0" with "" ', ()=> {
    const pipe = new RemoveZerosPipe();
    expect(pipe.transform(0)).toBe('');
  })

  it('should not replace the number "0" if it is part of another number', ()=> {
    const pipe = new RemoveZerosPipe();
    expect(pipe.transform(10)).toBe('10');
  })

});
