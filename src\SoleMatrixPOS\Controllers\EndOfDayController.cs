using System;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Transaction;
using SoleMatrixPOS.Application.Transaction.Commands;
using SoleMatrixPOS.Application.Float.Queries;
using SoleMatrixPOS.Application.Float.Commands;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class EndOfDayController : ControllerBase
	{
		private readonly IMediator _mediator;

		public EndOfDayController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[HttpPut]
		public async Task<string> AddTransaction([FromBody] EndOfDayDTO endOfDayDTO)
		{
			return await _mediator.Send(new CreateEndOfDayCommand(endOfDayDTO));
		}

		[HttpGet("check-end-of-day-float")]
		public async Task<bool> CheckEndOfDayFloat()
		{
			return await _mediator.Send(new CheckEndOfDayFloatExistsQuery());
		}

		[HttpDelete("delete-daily-float")]
		public async Task DeleteDailyFloat()
		{
			await _mediator.Send(new DeleteEndOfDayFloatCommand());
		}
	}
}
