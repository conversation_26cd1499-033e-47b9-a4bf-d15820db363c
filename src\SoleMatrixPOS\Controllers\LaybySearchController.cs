using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SoleMatrixPOS.Application.Layby;
using SoleMatrixPOS.Filters;
using System.Threading.Tasks;
using SoleMatrixPOS.Application.Layby.Commands;
using System.Collections.Generic;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[RequireStaffCodeFilter]
	[ApiController]
	public class LaybySearchController : ControllerBase
	{
		private readonly IMediator _mediator;

		public LaybySearchController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[HttpPost("SearchLayby")]
		public async Task<IEnumerable<LaybySearchResultDto>> SearchLayby([FromBody] LaybySearchQueryDto laybySearch)
		{
			return await _mediator.Send(new LaybySearchCommand(laybySearch));
		}

		[HttpPost("GetLaybyLines")]
		public async Task<IEnumerable<LaybylineDto>> GetLaybyLines([FromBody] string laybyCode)
		{
			return await _mediator.Send(new GetLaybyLinesCommand(laybyCode));
		}

		[HttpPost("ThisIsADummy")]
		// This exists to ensure the LaybyItemDto class is generated by NSwag
		public async Task<IActionResult> ThisIsDummy([FromBody] LaybyItemDto dto)
		{
			return await Task.FromResult(Ok());
		}
	}
}
