<div class="modal-header">
  <h4 class="modal-title">Order Refund</h4>
  <button type="button" class="close" aria-label="Close" (click)="dismissModal()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <div class="form-group">
    <label for="totalPaid">Total Paid Amount</label>
    <input
      type="text"
      id="totalPaid"
      class="form-control"
      [value]="totalPaid | currency"
      readonly
    />
  </div>

  <div class="form-group">
    <label for="refundAmount">Refund Amount</label>
    <input
      type="number"
      id="refundAmount"
      class="form-control"
      [value]="totalRefundedInModals"
      readonly
    />
  </div>

  <div class="form-group">
    <label for="refundMethod">Select Refund Method</label>
    <div class="d-flex justify-content-around py-3">
      <!-- Use Payment Modal Button component for Cash and EFTPOS -->
      <pos-payment-modal-button
      *ngFor="let button of modalButtons" 
      [buttonInfo]="button" 
      (openModal)="launchPaymentModal($event)">
      </pos-payment-modal-button>
    </div>
  </div>
</div>

<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="dismissModal()">Cancel</button>
  <button
    type="button"
    class="btn btn-success"
    [disabled]="!canProcessRefund()"
    (click)="handleRefund()"
  >
    Process Refund
  </button>
</div>
