import {Component} from '@angular/core'
import { FormBuilder, Validators } from '@angular/forms';
import { error } from 'console';
import { catchError, tap } from 'rxjs/operators';
import { AuthService } from 'src/app/core/services/auth.service';
import { TokenStore } from 'src/app/core/store/token.store';

@Component({
    selector: "app-login-form",
    templateUrl: "./login-form.component.html",
    styleUrls: ["./login-form.component.scss"]
})
export class LoginFormComponent{
constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private tokenStore: TokenStore,
){}

serverError: string | null = null;


loginForm = this.fb.group({
    id: ['', [Validators.required]] ,
    password: ['', [Validators.required]],
})

get f() {
    return this.loginForm.controls
}



onSubmit(){
    if(this.loginForm.invalid){
        return
    }

    this.authService.login({
        id: this.loginForm.value.id,
        password: this.loginForm.value.password
    }).pipe(
        tap(loginData => {
        this.tokenStore.setAccessToken(loginData.accessToken)
        this.tokenStore.setRefreshToken(loginData.refreshToken)
        this.serverError = null
        }),
        catchError( error => this.serverError = error.error)
    ).subscribe()
}


}