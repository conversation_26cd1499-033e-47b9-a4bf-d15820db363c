using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR; using Microsoft.AspNetCore.Authentication.JwtBearer;
using SoleMatrixPOS.Application.Layby.Commands;
using SoleMatrixPOS.Application.Layby.Queries;
using SoleMatrixPOS.Application.Layby;

namespace SoleMatrixPOS.Controllers
{
	[Route("api/[controller]")]
	[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
	[ApiController]
	public class LaybyController : ControllerBase
	{
		private readonly IMediator _mediator;

		public LaybyController(IMediator mediator)
		{
			_mediator = mediator;
		}

		[HttpPut]
		public async Task<IActionResult> AddLayby([FromBody] CreateLaybyDto laybyDto)
		{
			await _mediator.Send(new CreateLaybyCommand(laybyDto));
			return Ok();
		}

		[HttpGet("getLaybyNo")]
		public async Task<IActionResult> GetLaybyNo()
		{
			string laybyNo = await _mediator.Send(new GetLaybyNumberQuery());
			return Ok(laybyNo);
		}

		[HttpGet("getLaybyCodeByTransNo/{transNo}")]
		[Produces("application/json")]
		public async Task<ActionResult<string>> GetLaybyCodeByTransNo([FromRoute] int transNo)
		{
			string laybyCode = await _mediator.Send(new GetLaybyCodeByTransNoQuery(transNo));
			return Ok(laybyCode);
		}
	}
}
