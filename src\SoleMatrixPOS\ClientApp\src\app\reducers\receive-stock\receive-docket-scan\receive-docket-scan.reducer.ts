import { createReducer, on, Action } from '@ngrx/store';
import { ValidateBarcodeResult } from '../../../pos-server.generated';
import * as receiveDocketScanActions from "./receive-docket-scan.actions"

export class ReceiveDocketScanState {
    validationInProgress: boolean;
    validationResult: ValidateBarcodeResult;
    docketBarcode: string;
    senderStoreName: string;
}

export const initialState: ReceiveDocketScanState = {
    validationInProgress: false,
    validationResult: null,
    docketBarcode: null,
    senderStoreName: null
}

export const receiveDocketScanReducer = createReducer(initialState,
    on(receiveDocketScanActions.init, (state) => initialState),
    on(
        receiveDocketScanActions.validateBarcode,
        (state, action) => {
            return { ...state, validationInProgress: true, docketBarcode: action.validation.barcode}
        }
    ),
    on(
        receiveDocketScanActions.retrieveBarcodeValidation,
        (state, action) => {
            return { ...state, validationInProgress: false, validationResult: action.response.verifyResult, senderStoreName:  action.response.senderStoreName}
        }
    )

)