import { TestBed } from '@angular/core/testing';

import { StockSaleTableFooterService } from './stock-sale-table-footer.service';
import { HttpClientTestingModule, HttpTestingController } from "@angular/common/http/testing"
import { HttpClient, HttpHandler } from '@angular/common/http';

describe('MainFooterService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [ HttpClientTestingModule ],
    providers: [ HttpClient, HttpHandler, HttpClientTestingModule, HttpTestingController ],
  }));

  it('should be created', () => {
    const service: StockSaleTableFooterService = TestBed.get(StockSaleTableFooterService);
    expect(service).toBeTruthy();
  });
});
