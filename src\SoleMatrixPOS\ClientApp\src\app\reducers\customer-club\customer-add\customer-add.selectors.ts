import { AppState } from '../../index';
import { createSelector } from '@ngrx/store';
import { CustomerClubAddMemberState } from './customer-add.reducer';

export const select = (state: AppState) => state.customerAdd;

export const status = createSelector(select, s => s.status);

export const customer = createSelector(select, s => s.member);

export const barcodeExists = createSelector(
    select,
    (state: CustomerClubAddMemberState) => state.barcodeExists
);