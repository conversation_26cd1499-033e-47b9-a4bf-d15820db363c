import { createFeatureSelector, createSelector } from '@ngrx/store';
import { QuoteState } from './quote.reducers';

export const selectQuoteState = createFeatureSelector<QuoteState>('quote');

export const selectSelectedQuote = createSelector(
  selectQuoteState,
  (state: QuoteState) => state.selectedQuote
);

export const selectQuoteLoading = createSelector(
  selectQuoteState,
  (state: QuoteState) => state.loading
);

export const selectQuoteError = createSelector(
  selectQuoteState,
  (state: QuoteState) => state.error
);

export const selectUploadedQuoteCode = createSelector(
    selectQuoteState,
    (state: QuoteState) => state.uploadedQuoteCode
);

export const selectQuoteNo = createSelector(
    selectQuoteState,
    (state: QuoteState) => state.quoteNo
);