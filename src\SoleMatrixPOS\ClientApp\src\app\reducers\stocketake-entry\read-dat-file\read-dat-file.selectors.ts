import { createSelector } from "@ngrx/store";
import { AppState } from "../..";


export const select = (state: AppState) => state.readDatFile;
export const readDatFile = createSelector(select, (s) => s.items)

export const searchLoading = createSelector(select, (s) => s.isLoading);

export const getvalidateBarcode = createSelector(select, (s) => s.itemBar);
export const ischecking = createSelector(select, (s) => s.ischeckingBarcode);

