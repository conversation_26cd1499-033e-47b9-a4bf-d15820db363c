import { createAction, props } from '@ngrx/store';
import { CreateQuoteDto, CreateQuoteRequestDto, QuoteHeaderDTO, QuoteLineDTO } from 'src/app/pos-server.generated';

export const createQuote = createAction(
  '[Quote] Create Quote',
  props<{ payload: CreateQuoteRequestDto }>()
);

export const createQuoteSuccess = createAction(
  '[Quote] Create Quote Success'
);

export const createQuoteFailure = createAction(
  '[Quote] Create Quote Failure',
  props<{ error: any }>()
);

export const cancelQuote = createAction(
  '[Quote] Cancel Quote',
  props<{ quoteNumber: string }>()
);

export const cancelQuoteSuccess = createAction(
  '[Quote] Cancel Quote Success'
);

export const cancelQuoteFailure = createAction(
  '[Quote] Cancel Quote Failure',
  props<{ error: any }>()
);

export const completeQuote = createAction(
  '[Quote] Complete Quote',
  props<{ quoteCode: string }>()
);

export const completeQuoteSuccess = createAction(
  '[Quote] Complete Quote Success'
);

export const completeQuoteFailure = createAction(
  '[Quote] Complete Quote Failure',
  props<{ error: any }>()
);

export const uploadQuoteCode = createAction(
  '[Order] Upload Order Code', 
  props<{ quoteCode: string }>()
);
export const clearUploadedQuoteCode = createAction(
  '[Quote] Clear Uploaded Quote Code'
);

// Add new actions for getting quote number
export const getQuoteNo = createAction(
  '[Quote] Get Quote Number'
);

export const getQuoteNoSuccess = createAction(
  '[Quote] Get Quote Number Success',
  props<{ quoteNo: string }>()
);

export const getQuoteNoFailure = createAction(
  '[Quote] Get Quote Number Failure',
  props<{ error: any }>()
);