import { Component, OnInit, Input } from "@angular/core";
import { Router } from "@angular/router";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { Store } from "@ngrx/store";
import { AppState } from "src/app/reducers";
import {
	clearStaffLogin,
	staffLogin,
} from "src/app/reducers/staff/staff.actions";
import Swal from "sweetalert2";
import { timer } from "rxjs";
import { take } from "rxjs/operators";
import {
	CartItem,
	cartItemToTranslog,
} from "src/app/reducers/sales/cart/cart.reducer";
import * as staffSelectors from "../../reducers/staff/staff.selectors";
import * as customerClubSelectors from "../../reducers/customer-club/club-search/customer-club.selectors";
import {
	SuspendSaleDto,
	SuspendSaleHdrDto,
	SuspendSaleClient,
	SuspendSaleLineDto,
	StaffLoginDto,
	CustomerClubDto,
} from "src/app/pos-server.generated";
import { Observable } from "rxjs";
import * as cartSelectors from "../../reducers/sales/cart/cart.selectors";
import { time, timeStamp } from "console";
import { items } from "src/app/reducers/layby-payment/layby-payment.selectors";
import * as suspendSaleSelectors from "../../reducers/suspend-sale/suspend-sale.selectors";

@Component({
	selector: "pos-sale-suspend-modal",
	templateUrl: "./sale-suspend-modal.component.html",
	styleUrls: ["./sale-suspend-modal.component.scss"],
})
export class SaleSuspendModalComponent implements OnInit {
	@Input() suspendSaleItems: SuspendSaleDto[] = [];
	cart$: Observable<CartItem[]>;
	cart: CartItem[];
	staff$: Observable<StaffLoginDto>;
	staff: StaffLoginDto;

	client$: Observable<CustomerClubDto>;
	client: CustomerClubDto;

	totalValue$: Observable<number>;
	totalValue: number;
	totalQuantity$: Observable<number>;
	totalQuantity: number;
	suspendType$: Observable<number>;
	suspendType: number;

	constructor(
		private router: Router,
		private store: Store<AppState>,
		public activeModal: NgbActiveModal,
		private suspendSaleClient: SuspendSaleClient
	) { }

	ngOnInit() {
		this.cart$ = this.store.select(cartSelectors.cart);
		this.cart$.subscribe((value) => (this.cart = value));
		this.staff$ = this.store.select(staffSelectors.selectStaffLoginDto);
		this.staff$.subscribe((value) => (this.staff = value));
		this.totalValue$ = this.store.select(cartSelectors.total);

		this.client$ = this.store.select(customerClubSelectors.selectedCustomerClubMember);
		this.client$.subscribe((value) => this.client = value);

		// TODO: delete the lines below. Pretty sure this isn't doing anything
		this.totalValue$.subscribe((value) => this.totalValue$);
		this.totalQuantity$ = this.store.select(cartSelectors.total);
		this.totalQuantity$.subscribe((value) => this.totalQuantity$);
		this.suspendType$ = this.store.select(cartSelectors.total);
		this.suspendType$.subscribe((value) => this.suspendType$);
	}

	backButtonClick() {
		console.log("Hello, Back button clicked");
		this.store.dispatch(clearStaffLogin());
		this.router.navigateByUrl("/staff-login");
		this.activeModal.dismiss();
	}

	async process() {
		console.log("!!!!>!>>!>!>!>!>");
		this.suspendSaleClient
			.getNextSuspendNo()
			.subscribe((suspendNo: number) => {
				const suspendSale: SuspendSaleDto = {
					header: {
						suspendNo: suspendNo,
						suspendType: 1,
						suspendDate: new Date(),
						suspendTime: new Date().toTimeString().split(" ")[0],
						staffCode: this.staff.code,
						total_Quantity: this.cart.reduce(
							(total, item) => total + item.quantity,
							0
						),
						total_Value: this.cart.reduce(
							(total, item) => total + (item.bestValue * item.quantity),
							0
						),
						active_Sale: "F",
						eft_Attempted: "F",

						// Save the client code so it can be restored later
						clientCode: this.client == null ? null : this.client.clientCode
					},
					lines: this.cart.map((item, index) => {
						const discountedPrice = item.bestValue !== null && item.bestValue !== undefined
							? item.bestValue
							: item.stockItem.price;
						return {
							suspendNo: suspendNo,
							lineNo: index + 1,
							barcode: item.stockItem.barcode,
							styleCode: item.stockItem.styleCode,
							styleDescription: item.stockItem.styleDescription,
							sizeCode: item.stockItem.size,
							colourCode: item.stockItem.colourCode,
							colourName: item.stockItem.colourName,
							quantity: item.quantity,
							recommendedSell: item.stockItem.rrp,
							discountCode: item.discountCode || '',
							discountPcent: item.discountPercent || 0,
							discountValue: item.stockItem.price - discountedPrice,
							sellingPrice: discountedPrice,
							extendedValue: item.quantity * discountedPrice,
						};
					}),
				};

				// Send the SuspendSaleDto payload to the backend
				this.suspendSaleClient.addSuspendSale(suspendSale).subscribe(
					() => {
						console.log("Sale suspended successfully");
						this.activeModal.dismiss(); // Close the modal
					},
					(error) => {
						console.error("Error suspending sale", error);
					}
				);

				console.log("Before Swal call");
				Swal.fire({
					title: "Sale Suspended",
					text: "The sale was successfully suspended.",
					timer: 1200, // 1.2 seconds
					showConfirmButton: false, // Hide the confirm button
				}).then(() => {
					this.activeModal.close("process");
					this.store.dispatch(clearStaffLogin());
					this.router.navigateByUrl("/staff-login");
				});
				console.log("After Swal call");

				// TODO: can't you delete this if SWAL timer works correctly?
				// Wait for timer before closing the Swal popup
				timer(1200)
					.pipe(take(1))
					.subscribe(() => {
						Swal.close();
					});
			});
	}
}
