import { Injectable } from "@angular/core"
import { Actions, createEffect, ofType } from "@ngrx/effects"
import { EMPTY } from "rxjs";
import { catchError, map, mergeMap, tap } from "rxjs/operators";
import { StockSearchClient, StocktakeEntryClient, StocktakeEntryRequestDto } from "src/app/pos-server.generated";
import * as stockCartAction from '../itemCart/itemCart.actions';


@Injectable()
export class StocktakeEntryEffect {
    constructor(private actions$: Actions, private stocktakeClient: StocktakeEntryClient, private stockSearchClient: StockSearchClient,) { }

    stockTakeEntry$ = createEffect(
        () => this.actions$.pipe(
            ofType(stockCartAction.addStockItem),
            tap(() => console.log("stockTakeEntry$")),
            mergeMap(
                (action) => this.stocktakeClient.stockTakeCartItem({
                    styleCode: action.stockItem.styleCode,
                    colourCode: action.stockItem.colourCode,
                    sizeCode: action.stockItem.size,
                    locationCode: 'OO', // need to find this somewhere... TODO investigate
                    clientCode: '', // INVESTIGATION VERDICT BELOW,
                    // clientCode and locationCode are not needed for this... 
                    // they are only needed for the best price search
                } as StocktakeEntryRequestDto).pipe(
                    map(itemCart => stockCartAction.addStockItemResponse({ stockCartItem: itemCart }),
                        catchError(() => EMPTY)
                    )
                )
            )
        )
    );

    submitBarcode$ = createEffect(
        () => this.actions$.pipe(
            ofType(stockCartAction.submitBarcode),
            tap(() => console.log("submitBarcode$")),
            mergeMap(
                (action) => this.stockSearchClient.getItemByBarcode({
                    barcode: action.itembarcode.barcode
                }).pipe(
                    map(itemCart => stockCartAction.submitBarcodeResponse({ addCartItem: itemCart }),
                        catchError(() => EMPTY)
                    )
                )
            )
        )
    );
}
