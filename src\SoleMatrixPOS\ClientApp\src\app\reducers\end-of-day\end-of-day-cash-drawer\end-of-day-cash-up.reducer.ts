import { createReducer, on } from '@ngrx/store';
import * as EndOfDayActions from './end-of-day-cash-up.actions';
import { FloatDto } from 'src/app/pos-server.generated';

export interface EndOfDayState {
  floatData: FloatDto | null;
  cashDrawerTotal: number | null;
}

export const initialState: EndOfDayState = {
  floatData: null,
  cashDrawerTotal: null
};

export const endOfDayReducer = createReducer(
  initialState,
  on(EndOfDayActions.saveEndOfDayFloat, (state, { payload }) => ({
    ...state,
    endFloatData: payload
  })),
  on(EndOfDayActions.saveCashDrawerTotal, (state, { total }) => ({
    ...state,
    cashDrawerTotal: total
  }))
);
