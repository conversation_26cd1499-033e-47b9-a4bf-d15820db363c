import { FloatDto, FloatType } from '../../pos-server.generated';
import { FormBuilder, Validators, AbstractControl } from '@angular/forms';
import { AppState } from 'src/app/reducers';
import { Store } from '@ngrx/store';
import * as receiptActions from '../../reducers/receipt-printing/receipt.actions'
import { ReceiptBatch, OpenCashDrawerAction } from 'src/app/printing/printing-definitions';
import { Component, OnInit } from '@angular/core';
import * as staffActions from '../../reducers/staff/staff.actions';
import * as staffSelectors from '../../reducers/staff/staff.selectors';
import { Observable, PartialObserver, Subscription } from 'rxjs';
import { map, first } from 'rxjs/operators';
import { Router } from '@angular/router';
import { StaffLoginState } from 'src/app/reducers/staff/staff.reducer';

@Component({
	selector: 'pos-float-record-enter',
	templateUrl: './float-record-enter.component.html',
	styleUrls: ['./float-record-enter.component.scss']
})
export class FloatRecordEnterComponent implements OnInit {

	constructor(
		private store: Store<AppState>,
		private formBuilder: FormBuilder,
		private router: Router
	) { }

	staffLoginStateSub: Subscription
	total$: Observable<number>;

	public floatForm = this.formBuilder.group({
		Denomination100: [0,
			[Validators.min(0), Validators.required]
		],
		Denomination50: [0,
			[Validators.min(0), Validators.required]
		],
		Denomination20: [0,
			[Validators.min(0), Validators.required]
		],
		Denomination10: [0,
			[Validators.min(0), Validators.required]
		],
		Denomination5: [0,
			[Validators.min(0), Validators.required]
		],
		Denomination2: [0,
			[Validators.min(0), Validators.required]
		],
		Denomination1: [0,
			[Validators.min(0), Validators.required]
		],
		Denomination50c: [0,
			[Validators.min(0), Validators.required]
		],
		Denomination20c: [0,
			[Validators.min(0), Validators.required]
		],
		Denomination10c: [0,
			[Validators.min(0), Validators.required]
		],
		Denomination5c: [0,
			[Validators.min(0), Validators.required]
		]
	});


	ngOnInit() {
		this.staffLoginStateSub = this.store.select(s => s.staff.staffLoginState).subscribe(staffLoginState => {
			if(staffLoginState === StaffLoginState.Complete) {
				this.router.navigateByUrl('/home')
			}
		})

		this.total$ = this.floatForm.valueChanges.pipe(
			map(f => this.floatForm.value.Denomination100 * 100 +
				this.floatForm.value.Denomination50 * 50 +
				this.floatForm.value.Denomination20 * 20 +
				this.floatForm.value.Denomination10 * 10 +
				this.floatForm.value.Denomination5 * 5 +
				this.floatForm.value.Denomination2 * 2 +
				this.floatForm.value.Denomination1 * 1 +
				this.floatForm.value.Denomination50c * 0.5 +
				this.floatForm.value.Denomination20c * 0.2 +
				this.floatForm.value.Denomination10c * 0.1 +
				this.floatForm.value.Denomination5c * 0.05
			)
		);
	}

	ngOnDestroy() {
		this.staffLoginStateSub.unsubscribe()
	}

	public onCancel(): void {
		this.store.dispatch(staffActions.clearStaffLogin());
	}

	static getDateForCSharp(): Date {
		return new Date();

		//date.setMonth(date.getMonth());
		//date.setMonth(date.getMonth() + 1); // Added 22/11 reverting back to +1
		//return date;//date.setMonth(date.getMonth());
		//date.setMonth(date.getMonth() + 1); // Added 22/11 reverting back to +1
		//return date;
		//date.setMonth(date.getMonth());
		//date.setMonth(date.getMonth() + 1); // Added 22/11 reverting back to +1

		//return date;
	}

	public onSubmit(): void {
		this.store.select(staffSelectors.selectStaffLoginDto)
			.pipe(first()) // fetch value once and complete
			.subscribe(staffLoginDto => {
				const dailyFloatPayload: FloatDto = {
					storeId: staffLoginDto.storeId,
					tillNo: staffLoginDto.tillId,
					transactionDate: FloatRecordEnterComponent.getDateForCSharp(),
					floatType: FloatType.AM,
					hundreds: this.floatForm.value.Denomination100,
					fifties: this.floatForm.value.Denomination50,
					twenties: this.floatForm.value.Denomination20,
					tens: this.floatForm.value.Denomination10,
					fives: this.floatForm.value.Denomination5,
					twos: this.floatForm.value.Denomination2,
					ones: this.floatForm.value.Denomination1,
					fiftyCents: this.floatForm.value.Denomination50c,
					twentyCents: this.floatForm.value.Denomination20c,
					tenCents: this.floatForm.value.Denomination10c,
					fiveCents: this.floatForm.value.Denomination5c
				};
				this.store.dispatch(staffActions.dailyFloatEnter({ payload: dailyFloatPayload }));	
			})
	}

	public fieldValidate(control: AbstractControl): boolean {
		// TODO handle errors
		return control.invalid;
	}


	public openTill(): void {
		this.store.dispatch(receiptActions.executeBatch({
			payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
		}));
	}
}
