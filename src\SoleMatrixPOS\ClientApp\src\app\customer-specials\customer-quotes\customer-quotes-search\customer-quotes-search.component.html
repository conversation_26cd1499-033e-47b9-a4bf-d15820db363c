<div class="content-wrapper flex-grow-1 pt-10">
	<div class="container-fluid">
	  <div class="row text-align-center mt-2 mb-4">
		<i class="fas fa-lg fa-fw fa-shopping-cart text-danger mt-3 mr-2 ml-4 text-shadow"></i>
		<h3 class="mt-3 mb-0 mr-2 text-danger">Quote Search By</h3>
  
		<div class="col-2 pr-0">
		  <select class="form-control form-control-special" [(ngModel)]="field" (change)="search()">
			<option value="FirstName">Full Name</option>
			<option value="QuoteCode">Quote Code</option>
			<option value="ClientCode">Client Code</option>
			<option value="QuoteStaff">Quote Staff</option>
		  </select>
		</div>
  
		<div class="col-3 pl-0">
		  <input type="text" class="form-control" [(ngModel)]="term" (keyup)="search()" autofocus />
		</div>
	  </div>
  
	  <div *ngIf="loading">
		<mat-spinner style="margin:0 auto;" mode="indeterminate"></mat-spinner>
	  </div>
  
	  <div class="row m-2" *ngIf="!loading">
		<div class="table-responsive">
		  <table class="table table-striped table-hover table-fit">
			<thead>
			  <tr>
				<th>Quote Code</th>
				<th>Client Code</th>
				<th>Full Name</th>
				<th>Quote Date</th>
				<th>Quote Staff</th>
				<th>Style Code</th>
				<th>Size</th>
				<th>Color</th>
				<th>Selling Price</th>
				<th>Quantity</th>
				<th>Cancel Quote</th>
			  </tr>
			</thead>
			<tbody>
			  <ng-container *ngFor="let quote of quotes$ | async; let i = index">
				<!-- Main Quote Row -->
				<tr (click)="selectQuote(quote)" 
					[ngClass]="{'selectedQuote': selectedQuote && quote.quoteHeader.quoteCode === selectedQuote.quoteHeader.quoteCode}">
				  <td><ngb-highlight [result]="quote.quoteHeader.quoteCode" [term]="term" [highlightClass]="'search-highlight'"></ngb-highlight></td>
				  <td><ngb-highlight [result]="quote.quoteHeader.clientCode" [term]="term" [highlightClass]="'search-highlight'"></ngb-highlight></td>
				  <td><ngb-highlight [result]="quote.fullName" [term]="term" [highlightClass]="'search-highlight'"></ngb-highlight></td>
				  <td>{{ quote.quoteHeader.quoteDate | date: 'dd/MM/yy' }}</td>
				  <td>{{ quote.quoteHeader.quoteStaff }}</td>
				  <td>{{ quote.quoteLines[0]?.styleCode }}</td>
				  <td>{{ quote.quoteLines[0]?.sizeCode }}</td>
				  <td>{{ quote.quoteLines[0]?.colourCode }}</td>
				  <td>{{ quote.quoteLines[0]?.sellingPrice | currency }}</td>
				  <td>{{ quote.quoteLines[0]?.quantity }}</td>
				  <td>
					<button
					  class="btn btn-danger btn-sm"
					  (click)="cancelQuote(quote); $event.stopPropagation()"
					  [disabled]="loading || quote.quoteHeader.quoteCancelled">
					  Cancel Quote
					</button>
				  </td>
				</tr>
				<!-- Additional Quote Lines -->
				<tr *ngFor="let line of quote.quoteLines.slice(1)" class="table-secondary">
				  <td colspan="5"></td>
				  <td>{{ line.styleCode }}</td>
				  <td>{{ line.sizeCode }}</td>
				  <td>{{ line.colourCode }}</td>
				  <td>{{ line.sellingPrice | currency }}</td>
				  <td>{{ line.quantity }}</td>
				  <td></td>
				</tr>
			  </ng-container>
			</tbody>
		  </table>
		</div>
	  </div>
	</div>
  </div>