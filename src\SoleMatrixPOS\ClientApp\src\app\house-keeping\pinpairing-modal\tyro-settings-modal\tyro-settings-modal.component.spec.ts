import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TyroSettingsModalComponent } from './tyro-settings-modal.component';

describe('TyroSettingsModalComponent', () => {
  let component: TyroSettingsModalComponent;
  let fixture: ComponentFixture<TyroSettingsModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ TyroSettingsModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TyroSettingsModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
