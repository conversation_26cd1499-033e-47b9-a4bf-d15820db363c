//import { LinklyBasketItem, LinklyService } from '../eftpos/linkly.service';
//import { Store } from '@ngrx/store';
//import { first, switchMap, map, catchError, withLatestFrom, tap } from 'rxjs/operators';
//import { GetPINAuthDto, GetSystemStatusDto } from 'src/app/pos-server.generated';
//import * as UpdatePINAuthActions from '../reducers/pin-auth/pin-auth.actions';
//import { AppState } from 'src/app/reducers';
//import * as PINAuthSelector from '../reducers/pin-auth/pin-auth.selectors';
//import * as SysSelector from '../reducers/house-keeping/mLogin.selectors';
//import { Observable, of } from 'rxjs';

//export function pairPINPad(linklyService: LinklyService, store: Store<AppState>, username: string, password: string, pairCode: string): Observable<any> {
//	// Fetch SysConfig and PINAuth details
//	return store.select(SysSelector.selectSysConfig).pipe(
//		first(),
//		switchMap(sysConfig => {
//			return store.select(PINAuthSelector.selectPINAuth).pipe(
//				first(),
//				switchMap(pinAuth => {
//					// Check if Linkly is the configured EFT provider
//					if (sysConfig.integratedEFTProvider === 'Linkly') {
//						return linklyService.linklyPairPINPad(username, password, pairCode).pipe(
//							map((secret: string) => {
//								// Update PINAuth with the new secret
//								const updatedPINAuth: GetPINAuthDto = {
//									...pinAuth,
//									integratedEFTSecret: secret,
//								};
//								store.dispatch(UpdatePINAuthActions.updatePINAuth({ updatePINAuth: updatedPINAuth }));
//								return secret;
//							}),
//							catchError(error => {
//								console.error('Error pairing PIN Pad:', error);
//								return of(error);  // Return error as an observable
//							})
//						);
//					} else {
//						console.log('Linkly is not the configured EFT provider.');
//						return of(null); // Return an empty observable if Linkly is not used
//					}
//				})
//			);
//		})
//	);
//}

//export function getAuthToken(linklyService: LinklyService, store: Store<AppState>, posName: string, posVersion: string, posId: string): Observable<string> {
//	// Fetch SysConfig and PINAuth details
//	return store.select(SysSelector.selectSysConfig).pipe(
//		first(),
//		switchMap(sysConfig => {
//			return store.select(PINAuthSelector.selectPINAuth).pipe(
//				first(),
//				switchMap(pinAuth => {
//					const secret = pinAuth && pinAuth.integratedEFTSecret ? pinAuth.integratedEFTSecret : null;

//					if (!secret) {
//						console.error('Secret not found in PINAuth.');
//						return of(''); // Return an empty string if the secret is not available
//					}

//					// Check if Linkly is being used
//					if (sysConfig.integratedEFTProvider === 'Linkly') {
//						return linklyService.linklyGetAuthToken(secret, posId = 'ed252847-3169-4db7-98cd-8d9493b1d483').pipe(
//							map(({ authToken, expiryTime }) => {
//								const updatedPINAuth: GetPINAuthDto = {
//									...pinAuth,
//									integratedEFTToken: authToken,
//									integratedEFTTokenExpiry: expiryTime
//								};
//								store.dispatch(UpdatePINAuthActions.updatePINAuth({ updatePINAuth: updatedPINAuth }));
//								return authToken;
//							}),
//							catchError(error => {
//								console.error('Error getting Auth Token:', error);
//								return of(''); // Return an empty string in case of error
//							})
//						);
//					} else {
//						console.log('Linkly is not the configured EFT provider.');
//						return of('');
//					}
//				})
//			);
//		})
//	);
//}

//export function AuthToken(linklyService: LinklyService, sysconfig$: Observable<GetSystemStatusDto>, store: Store<AppState>, posId: string): Observable<string> {
//	return sysconfig$.pipe(
//		first(),
//		withLatestFrom(store.select(PINAuthSelector.selectPINAuth).pipe(
//		)),
//		switchMap(([sysConfig, pinAuth]) => {
//			if (!sysConfig) {
//				console.error('SysConfig is unexpectedly null.');
//				return of(''); // Return empty or handle as needed
//			}

//			const currentToken = pinAuth.integratedEFTToken;
//			const expiryTimeString = pinAuth.integratedEFTTokenExpiry;

//			let shouldRefreshToken = false;

//			if (!currentToken || !expiryTimeString) {
//				shouldRefreshToken = true;
//			} else {
//				const expiryTime = new Date(expiryTimeString);
//				const currentTime = new Date();
//				const oneHourFromNow = new Date(currentTime.getTime() + 60 * 60 * 1000);

//				if (expiryTime <= currentTime || expiryTime <= oneHourFromNow) {
//					shouldRefreshToken = true;
//				}
//			}

//			if (shouldRefreshToken) {
//				if (sysConfig.integratedEFTProvider === 'Linkly') {
//					console.log('Token is either missing or needs to be refreshed.');
//					return linklyService.linklyGetAuthToken(pinAuth.integratedEFTSecret, posId).pipe(
//						map(({ authToken, expiryTime }) => {
//							const updatedPINAuth: GetPINAuthDto = {
//								...pinAuth,
//								integratedEFTToken: authToken,
//								integratedEFTTokenExpiry: expiryTime
//							};
//							store.dispatch(UpdatePINAuthActions.updatePINAuth({ updatePINAuth: updatedPINAuth }));
//							return authToken;
//						}),
//						catchError(error => {
//							console.error('Error refreshing Auth Token:', error);
//							return of(''); // Return empty string in case of error
//						})
//					);
//				} else {
//					console.log('Linkly is not the configured EFT provider.');
//					return of('');
//				}
//			} else {
//				return of(currentToken);
//			}
//		})
//	);
//}
//// TODO typing for items
//export function Purchase(sysConfig$: Observable<GetSystemStatusDto>, store: Store<AppState>, linklyService: LinklyService, amtPurchase: number, taxAmt: number, discountAmt: number, surchargeAmt: number, transNo: string, currencyCode: string = 'AUD', cutReceipt: string, receiptAutoPrint: string, operatorReference: string, hasBarCodeScanner: string, items: LinklyBasketItem[]): Observable<any> {
//	return sysConfig$.pipe(
//		first(),
//		switchMap((sysConfig) => {
//			const authToken$ = AuthToken(linklyService, sysConfig$, store, 'ed252847-3169-4db7-98cd-8d9493b1d483');
//			// console.log(sysConfig);
//			if (sysConfig.integratedEFT === 'T') {
//				if (sysConfig.integratedEFTProvider === 'Linkly') {
//					return authToken$.pipe(
//						first(),
//						switchMap((authToken) => {
//							return linklyService.linklyProcessPurchase(
//								amtPurchase,
//								taxAmt,
//								discountAmt,
//								surchargeAmt,
//								transNo,
//								currencyCode,
//								cutReceipt,
//								receiptAutoPrint,
//								operatorReference,
//								hasBarCodeScanner,
//								items,
//								authToken
//							).pipe(
//								switchMap(response => {
//									console.log('Purchase transaction successful:', response);
//									return of(response);  // Return successful transaction response
//								}),
//								catchError(error => {
//									console.error('Error processing purchase transaction:', error);
//									return of(error);  // Return error for the transaction
//								})
//							);
//						})
//					);
//				}
//			}
//			console.log('Integrated EFT is disabled.');
//			return of(null);  // Return a null or empty observable if EFT is disabled
//		})
//	);
//}
